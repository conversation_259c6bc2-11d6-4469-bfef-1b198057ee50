import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
// import ViewOverflow from 'react-native-view-overflow';
import SideBar from './SideBar';
import Footer from './footer';
import Icon from 'react-native-vector-icons/Feather';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
  isTablet
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import RadioButtonRN from 'radio-buttons-react-native';
import ReservationTableScreen from './ReservationTableScreen';

const FloorScreen = (props) => {
  const { navigation } = props;
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  // textinput usestate
  const [ReservationSearch, setReservationSearch] = useState('');

  // show usestate
  const [showReservations, setShowReservation] = useState(false);
  const [showFinished, setShowFinished] = useState(false);
  const [showWaitlist, setShowWaitlist] = useState(false);
  const [showTempCustomerDetails, setShowTempCustomerDetails] = useState(false);
  const [showMemberCustomerDetails, setShowMemberCustomerDetails] =
    useState(false);
  const [showMemberReservationRes, setShowMemberReservationRes] =
    useState(false);
  const [showMemberGuestRes, setShowMemberGuestRes] = useState(false);
  const [showMemberReservationFin, setShowMemberReservationFin] =
    useState(false);
  const [showMemberGuestFin, setShowMemberGuestFin] = useState(false);
  const [showMemberReservationWait, setShowMemberReservationWait] =
    useState(false);
  const [showMemberGuestWait, setShowMemberGuestWait] = useState(false);
  const [showLeftSide, setShowLeftSide] = useState(false);
  const [showGuestProfileRes, setShowGuestProfileRes] = useState(false);

  // use to expand
  const [showReservationsSideTag, setShowReservationsSideTag] = useState({});

  // to prompt the customer is selected
  const [customerUniqueId, setCustomerUniqueId] = useState('');

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // useEffect start
  // show reservation first always
  useEffect(() => {
    setShowReservation(true);
    setShowFinished(false);
    setShowWaitlist(false);
    setShowLeftSide(true);
    setShowTempCustomerDetails(false);
    setShowMemberCustomerDetails(false);
    // console.log('Refreshed');
  }, []);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);

  //dummy data
  const dummyData = [
    {
      uniqueId: '1',
      name: 'guest',
      guestDetails: 'temporary',
    },
    {
      uniqueId: '2',
      name: 'jason',
      guestDetails: 'temporary',
    },
    {
      uniqueId: '3',
      name: 'ali',
      guestDetails: 'member',
    },
  ];

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }} style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          }, 
          // Dimensions.get('screen').width >= 768 && switchMerchant ? { right: Dimensions.get('screen').width * 0.1 } : {}, Dimensions.get('screen').width <= 768 ? { right: Dimensions.get('screen').width / 40 } : {}
          ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Floor
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
    if (global.currUserRole === 'admin') {
        navigation.navigate('Setting');
    }
}}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  //when press this function will work
  const expandReservationSideTag = (param) => {
    if (showReservationsSideTag[param.item.uniqueId]) {
      setShowReservationsSideTag({
        ...showReservationsSideTag,
        [param.item.uniqueId]: false,
      });
    } else {
      setShowReservationsSideTag({
        // ...showReservationsSideTag,
        [param.item.uniqueId]: true,
      });
    }
  };

  const renderTemporaryGuestDetails = (item, index) => {
    return (
      <View>
        <View
          style={{
            width: '100%',
            paddingVertical: Dimensions.get('screen').height * 0.01,
            paddingHorizontal: Dimensions.get('screen').width * 0.01,
            flexDirection: 'row',
          }}>
          <View
            style={{
              flex: 2,
            }}>
            <Text>Status Change:</Text>
          </View>
          <View
            style={{
              flex: 1,
            }}>
            <Text>Reserved</Text>
          </View>
          <View
            style={{
              flex: 1,
            }}>
            <Text>2:38 PM, 07/12/2021</Text>
          </View>
          <View
            style={{
              flex: 0.5,
            }}>
            <Text
              style={{
                paddingVertical: 5,
                paddingHorizontal: 7,
                borderRadius: 5,
                borderWidth: 1,
                textAlign: 'center',
              }}>
              In-house
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderSummary = (item, index) => {
    // console.log(item);
    return (
      <View
        style={{
          height: Dimensions.get('screen').height * 0.18,
          borderWidth: 1,
          marginBottom: 10,
          marginHorizontal: 10,
          backgroundColor: Colors.fieldtBgColor2,
          borderRadius: 15,
          zIndex: 1000,
          overflow: 'visible',
        }}>
        {/* top part */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            // borderWidth: 1,
            paddingVertical: 5,
            paddingLeft: 10,
            paddingRight: Dimensions.get('screen').width * 0.01,
            backgroundColor: Colors.fieldtTxtColor,
            borderTopLeftRadius: 15,
            borderTopRightRadius: 15,
            height: '25%',
          }}>
          <View>
            <Text>3.00 P.M.</Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              //   borderWidth: 1,
              width: '40%',
            }}>
            <View
              style={{
                marginRight: Dimensions.get('screen').width * 0.05,
              }}>
              {/* icon here */}
              <Text>2</Text>
            </View>
            <View>
              {/* icon here */}
              <Text>1</Text>
            </View>
          </View>
        </View>
        {/* bottom part */}
        <TouchableOpacity
          onPress={() => {
            if (item.item.guestDetails === 'member') {
              setShowMemberCustomerDetails(!showMemberCustomerDetails);
              setShowMemberReservationRes(true);
              setShowMemberGuestRes(false);
              setShowTempCustomerDetails(false);
            } else {
              setShowTempCustomerDetails(!showTempCustomerDetails);
              setShowMemberCustomerDetails(false);
            }
          }}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingHorizontal: 10,
              paddingTop: Dimensions.get('screen').height * 0.015,
            }}>
            <View>
              <Text
                style={{
                  marginBottom: Dimensions.get('screen').height * 0.007,
                }}>
                {item.item.guestDetails === 'member'
                  ? item.item.name
                  : 'Temporary guest'}
              </Text>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  marginBottom: Dimensions.get('screen').height * 0.02,
                  //   borderWidth: 1,
                  width: Dimensions.get('screen').width * 0.136,
                }}>
                <Text>
                  {item.item.guestDetails === 'member'
                    ? 'Indoor'
                    : 'Temporary guest'}
                </Text>
                <View>
                  {/* icon here */}
                  <Text>2</Text>
                </View>
              </View>
              <Text>Indoor</Text>
            </View>
            <TouchableOpacity
              onPress={() => {
                expandReservationSideTag(item);
                // console.log(item);
              }}
              style={{
                borderWidth: 1,
                height: '80%',
                width: '30%',
                justifyContent: 'center',
                borderRadius: 10,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                }}>
                {/* icon here */}
                <View
                  style={{
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      textAlign: 'center',
                    }}>
                    6
                  </Text>
                  <Text>Seated</Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
        {showReservationsSideTag[item.item.uniqueId] == true &&
          showReservations ? (
          <View
            style={{
              position: 'absolute',
              zIndex: 1000,
              height: Dimensions.get('screen').height * 0.177,
              backgroundColor: Colors.fieldtBgColor2,
              width: Dimensions.get('screen').width * 0.17,
              borderRadius: 15,
              padding: 10,
            }}>
            <View>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 9999,
                }}
                onPress={() => { }}>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <Plus
                    name="chevron-left"
                    size={20}
                    color={Colors.darkBgColor}
                    style={{}}
                  />
                  <Text>Change Back To Reserved</Text>
                </View>
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: 'row',
                width: '100%',
                marginTop: Dimensions.get('screen').height * 0.015,
                justifyContent: 'space-between',
              }}>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 10,
                  width: '40%',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => { }}>
                <View
                  style={{
                    alignSelf: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Feather
                    name="phone-call"
                    size={30}
                    color={Colors.darkBgColor}
                    style={{ marginTop: 5 }}
                  />
                  <Text>Call</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.whiteColor,
                  width: '40%',
                  borderRadius: 10,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => { }}>
                <View
                  style={{
                    alignSelf: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Feather
                    name="alert-triangle"
                    size={30}
                    color={Colors.darkBgColor}
                    style={{ marginTop: 5 }}
                  />
                  <Text>No-Show</Text>
                </View>
              </TouchableOpacity>
            </View>
            <View
              style={{
                marginTop: Dimensions.get('screen').height * 0.015,
              }}>
              <TouchableOpacity
                style={{
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 9999,
                }}
                onPress={() => { }}>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <Plus
                    name="chevron-left"
                    size={20}
                    color={Colors.darkBgColor}
                    style={{}}
                  />
                  <Text>Finished</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        ) : null}
      </View>
    );
  };

  return (
    <View
      style={[
        styles.container,
        !isTablet()
          ? {
            transform: [{ scaleX: 1 }, { scaleY: 1 }],
          }
          : {},
      ]}>
      {/* <View
        style={[
          styles.sidebar,
          !isTablet()
            ? {
              width: Dimensions.get('screen').width * 0.08,
            }
            : {},
          switchMerchant
            ? {
              // width: '10%'
            }
            : {},
        ]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={1}
          expandOperation={true}
        />
      </View> */}
      <View
        style={{
          flex: 1,
          position: 'absolute',
          bottom: Dimensions.get('screen').height * 0.1,
          zIndex: -20,
        }}>
        <Footer />
      </View>

      <View
        style={{
          flex: 1,
          height: Dimensions.get('screen').height * 0.755,
          // backgroundColor: Colors.primaryColor,
          flexDirection: 'row',
        }}>

        {showLeftSide ? (
          <View
            style={{
              height: '100%',
              backgroundColor: Colors.highlightColor,
              width: Dimensions.get('screen').width * 0.25,
              zIndex: 1000,
            }}>
            <View
              style={{
                flexDirection: 'row',
                // borderWidth: 1,
                height: '8%',
              }}>
              <TouchableOpacity
                onPress={() => {
                  setShowReservation(true);
                  setShowFinished(false);
                  setShowWaitlist(false);
                  setShowTempCustomerDetails(false);
                }}
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: showReservations
                    ? Colors.descriptionColor
                    : null,
                }}>
                <Text>Reservations</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setShowReservation(false);
                  setShowFinished(true);
                  setShowTempCustomerDetails(false);
                  setShowWaitlist(false);
                }}
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: showFinished
                    ? Colors.descriptionColor
                    : null,
                }}>
                <Text>Finished/CSL</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  setShowReservation(false);
                  setShowFinished(false);
                  setShowTempCustomerDetails(false);
                  setShowWaitlist(true);
                }}
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: showWaitlist
                    ? Colors.descriptionColor
                    : null,
                }}>
                <Text>Waitlist</Text>
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: 'row',
                // borderWidth: 1,
                height: '8%',
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingHorizontal: Dimensions.get('screen').width * 0.005,
                backgroundColor: Colors.descriptionColor,
              }}>
              <View
                style={[
                  {
                    width: showReservations
                      ? '45%'
                      : showFinished
                        ? '70%'
                        : '100%',
                    height: '80%',
                    backgroundColor: 'white',
                    borderRadius: 5,
                    flexDirection: 'row',
                    alignContent: 'center',
                    alignItems: 'center',
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                  },
                ]}>
                <Feather
                  name="search"
                  size={switchMerchant ? 13 : 18}
                  color={Colors.primaryColor}
                  style={{ marginLeft: switchMerchant ? 8 : 15 }}
                />
                <TextInput
                  underlineColorAndroid={Colors.whiteColor}
                  style={
                    switchMerchant
                      ? {
                        width: showReservations
                          ? '70%'
                          : showFinished
                            ? '80%'
                            : '85%',
                        fontSize: 10,
                        // borderWidth: 1,
                        fontFamily: 'NunitoSans-Regular',
                        height: '100%',
                        height: 45,
                      }
                      : {
                        width: showReservations
                          ? '70%'
                          : showFinished
                            ? '80%'
                            : '85%',
                        fontSize: 15,
                        fontFamily: 'NunitoSans-Regular',
                        paddingLeft: 5,
                        height: 45,
                      }
                  }
                  clearButtonMode="while-editing"
                  placeholder=" Search"
                  placeholderTextColor={Platform.select({
                    ios: '#a9a9a9',
                  })}
                  onChangeText={(text) => {
                    setReservationSearch(text);
                  }}
                  value={ReservationSearch}
                />
              </View>
              {showFinished || showReservations ? (
                <View
                  style={{
                    width: '50%',
                    height: '80%',
                    flexDirection: 'row',
                    justifyContent: showReservations
                      ? 'flex-end'
                      : 'flex-start',
                    // borderWidth: 1,
                    marginLeft: Dimensions.get('screen').width * 0.01,
                  }}>
                  <TouchableOpacity
                    style={{
                      width: '45%',
                      height: '100%',
                      marginRight: Dimensions.get('screen').width * 0.005,
                      marginLeft: showFinished
                        ? Dimensions.get('screen').width * 0.008
                        : null,
                    }}>
                    <View
                      style={[
                        {
                          height: '100%',
                          backgroundColor: 'white',
                          borderRadius: 5,
                          flexDirection: 'row',
                          alignContent: 'center',
                          alignItems: 'center',
                          justifyContent: 'center',
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        },
                      ]}>
                      <Text>Filter</Text>
                    </View>
                  </TouchableOpacity>
                  {showReservations ? (
                    <TouchableOpacity
                      style={{
                        width: '45%',
                        height: '100%',
                      }}>
                      <View
                        style={[
                          {
                            height: '100%',
                            backgroundColor: 'white',
                            borderRadius: 5,
                            flexDirection: 'row',
                            alignContent: 'center',
                            alignItems: 'center',
                            justifyContent: 'center',
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                          },
                        ]}>
                        <Text>Now</Text>
                      </View>
                    </TouchableOpacity>
                  ) : null}
                </View>
              ) : null}
            </View>
            <View
              style={{
                flexDirection: 'row',
                // borderWidth: 1,
                backgroundColor: Colors.descriptionColor,
                height: '8%',
                paddingVertical: Dimensions.get('screen').height * 0.01,
                paddingHorizontal: Dimensions.get('screen').width * 0.01,
              }}>
              <TouchableOpacity
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderWidth: 1,
                  borderRadius: 9999,
                }}>
                <Text>
                  {showReservations
                    ? 'Seated'
                    : showFinished
                      ? 'Finished'
                      : 'Waitlist'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  borderWidth: 1,
                  alignItems: 'center',
                  borderRadius: 9999,
                  marginHorizontal: Dimensions.get('screen').width * 0.01,
                }}>
                <Text>
                  {showReservations
                    ? 'Late'
                    : showFinished
                      ? 'No Show'
                      : 'Cancelled'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  borderWidth: 1,
                  borderRadius: 9999,
                  alignItems: 'center',
                }}>
                <Text>
                  {showReservations
                    ? 'Reserved'
                    : showFinished
                      ? 'Cancelled'
                      : 'Expired'}
                </Text>
              </TouchableOpacity>
            </View>
            <FlatList
              style={{
                marginTop: 10,
                overflow: 'visible',
                zIndex: 1000,
              }}
              nestedScrollEnabled={true}
              data={dummyData}
              renderItem={renderSummary}
              keyExtractor={(item, index) => String(index)}
            />
          </View>
        ) : null}

        {/* render custom table screen here */}
        <ReservationTableScreen navigation={navigation} showLeftSide={showLeftSide} />

        {!showTempCustomerDetails ? (
          <TouchableOpacity
            onPress={() => {
              setShowLeftSide(!showLeftSide);
            }}
            style={{
              backgroundColor: Colors.highlightColor,
              height: Dimensions.get('screen').height * 0.05,
              width: Dimensions.get('screen').width * 0.015,
              borderTopRightRadius: 2,
              borderBottomRightRadius: 2,
              justifyContent: 'center',
              alignItems: 'center',
              position: 'absolute',
              left: !showLeftSide ? 0 : Dimensions.get('screen').width * 0.25,
              top: Dimensions.get('screen').height * 0.4,
            }}>
            <View
              style={{
                backgroundColor: Colors.highlightColor,
              }}>
              {showLeftSide ? (
                <Plus
                  name="chevron-left"
                  size={20}
                  color={Colors.darkBgColor}
                  style={{}}
                />
              ) : (
                <Plus
                  name="chevron-right"
                  size={20}
                  color={Colors.darkBgColor}
                  style={{}}
                />
              )}
            </View>
          </TouchableOpacity>
        ) : null}

        {showTempCustomerDetails && showReservations ? (
          <View
            style={{
              backgroundColor: Colors.highlightColor,
              position: 'absolute',
              right: 0,
              height: '100%',
              width: Dimensions.get('screen').width * 0.67,
            }}>
            {/* top */}
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                height: '8%',
                flexDirection: 'row',
                padding: 10,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 100,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Seated
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 100,
                      marginLeft: 10,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Seated
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 100,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    No Show
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 170,
                      marginLeft: 10,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Cancel Reservation
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            {/* bottom part */}
            <View
              style={{
                padding: 10,
              }}>
              <ScrollView
                style={{
                  backgroundColor: Colors.whiteColor,
                  height: '89.5%',
                  width: '100%',
                }}>
                <View
                  style={{
                    paddingTop: Dimensions.get('screen').height * 0.03,
                    // borderWidth: 1,
                    flexDirection: 'row',
                    paddingBottom: Dimensions.get('screen').height * 0.03,
                  }}>
                  <AsyncImage
                    style={{
                      width: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      height: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      borderRadius: 9999,
                      marginLeft: Dimensions.get('screen').width * 0.01,
                      borderWidth: 1,
                    }}
                    // source={{
                    //   uri: item.avatar
                    // }}
                    // item={item}
                    hideLoading={true}
                  />
                  <View
                    style={{
                      marginLeft: Dimensions.get('screen').width * 0.02,
                      justifyContent: 'space-between',
                    }}>
                    <Text>Temporary Guest</Text>
                    <Text>-</Text>
                    <Text>-</Text>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: Dimensions.get('screen').width * 0.13,
                            height: Dimensions.get('screen').height * 0.08,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        +3rd Party Booker
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      position: 'absolute',
                      right: 10,
                      top: Dimensions.get('screen').height * 0.03,
                    }}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: Dimensions.get('screen').width * 0.13,
                            height: Dimensions.get('screen').height * 0.08,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Change Guest
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    width: '100%',
                    paddingVertical: Dimensions.get('screen').height * 0.008,
                    alignItems: 'center',
                    marginVertical: Dimensions.get('screen').height * 0.02,
                    backgroundColor: Colors.tabRed,
                  }}>
                  <Text style={{ color: Colors.whiteColor }}>
                    It is 5 minutes past the reservation start time
                  </Text>
                </View>
                <View
                  style={[
                    {
                      flexDirection: 'row',
                      justifyContent: 'space-evenly',
                      width: '100%',
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.84,
                        alignSelf: 'center',
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Party Size
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            color: Colors.whiteColor,
                            textAlign: 'center',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        2
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        3:00PM
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Indoor
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        6
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Turn Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        1h 30m
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    marginTop: Dimensions.get('screen').height * 0.03,
                    paddingHorizontal: 10,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Tags</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation tags have been added
                      </Text>
                    </View>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Notes</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation notes have been added
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Created</Text>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                            // borderWidth: 1,
                            textAlignVertical: 'center',
                          }}>
                          2:24 PM 3 Dec 2021
                        </Text>
                        <Text
                          style={{
                            paddingVertical: 5,
                            paddingHorizontal: 7,
                            borderRadius: 5,
                            borderWidth: 1,
                          }}>
                          In-house
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Manual Entry</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                          }}>
                          Bill amount manual entry
                        </Text>
                        <Text style={{}}>RM0.00</Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      borderWidth: 1,
                      borderRadius: 10,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        borderBottomWidth: 1,
                        width: '100%',
                        paddingHorizontal:
                          Dimensions.get('screen').width * 0.01,
                        paddingVertical:
                          Dimensions.get('screen').height * 0.002,
                        flexDirection: 'row',
                      }}>
                      <View
                        style={{
                          flex: 2,
                        }}>
                        <Text>Update to this reservation</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Details</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Time of change</Text>
                      </View>
                      <View
                        style={{
                          flex: 0.5,
                        }}>
                        <Text>Source</Text>
                      </View>
                    </View>
                    <FlatList
                      style={{
                        overflow: 'visible',
                        zIndex: 1000,
                        marginBottom: 10,
                      }}
                      nestedScrollEnabled={true}
                      data={dummyData}
                      renderItem={renderTemporaryGuestDetails}
                      keyExtractor={(item, index) => String(index)}
                    />
                  </View>
                </View>
              </ScrollView>
            </View>
          </View>
        ) : null}
        {showTempCustomerDetails && showFinished ? (
          <View
            style={{
              backgroundColor: Colors.highlightColor,
              position: 'absolute',
              right: 0,
              height: '100%',
              width: Dimensions.get('screen').width * 0.67,
            }}>
            {/* top */}
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                height: '8%',
                flexDirection: 'row',
                padding: 10,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 250,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Change Back To Seated
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            {/* bottom part */}
            <View
              style={{
                padding: 10,
              }}>
              <ScrollView
                style={{
                  backgroundColor: Colors.whiteColor,
                  height: '89.5%',
                  width: '100%',
                }}>
                <View
                  style={{
                    paddingTop: Dimensions.get('screen').height * 0.03,
                    // borderWidth: 1,
                    flexDirection: 'row',
                  }}>
                  <AsyncImage
                    style={{
                      width: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      height: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      borderRadius: 9999,
                      marginLeft: Dimensions.get('screen').width * 0.01,
                      borderWidth: 1,
                    }}
                    // source={{
                    //   uri: item.avatar
                    // }}
                    // item={item}
                    hideLoading={true}
                  />
                  <View
                    style={{
                      marginLeft: Dimensions.get('screen').width * 0.02,
                      justifyContent: 'space-between',
                      height: Dimensions.get('screen').height * 0.15,
                    }}>
                    <Text>Temporary Guest</Text>
                    <Text>-</Text>
                    <Text>-</Text>
                    <Text>-</Text>
                  </View>
                  <View
                    style={{
                      position: 'absolute',
                      right: 10,
                      top: Dimensions.get('screen').height * 0.03,
                    }}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: Dimensions.get('screen').width * 0.13,
                            height: Dimensions.get('screen').height * 0.08,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Change Guest
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={[
                    {
                      flexDirection: 'row',
                      justifyContent: 'space-evenly',
                      width: '100%',
                      marginTop: Dimensions.get('screen').height * 0.03,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.84,
                        alignSelf: 'center',
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Party Size
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            color: Colors.whiteColor,
                            textAlign: 'center',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        2
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={true}
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        3:00PM
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={true}
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Indoor
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        6
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Turn Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        1h 30m
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    marginTop: Dimensions.get('screen').height * 0.03,
                    paddingHorizontal: 10,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Tags</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation tags have been added
                      </Text>
                    </View>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Notes</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation notes have been added
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Created</Text>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                            // borderWidth: 1,
                            textAlignVertical: 'center',
                          }}>
                          2:24 PM 3 Dec 2021
                        </Text>
                        <Text
                          style={{
                            paddingVertical: 5,
                            paddingHorizontal: 7,
                            borderRadius: 5,
                            borderWidth: 1,
                          }}>
                          In-house
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Manual Entry</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                          }}>
                          Bill amount manual entry
                        </Text>
                        <Text style={{}}>RM0.00</Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      borderWidth: 1,
                      borderRadius: 10,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        borderBottomWidth: 1,
                        width: '100%',
                        paddingHorizontal:
                          Dimensions.get('screen').width * 0.01,
                        paddingVertical:
                          Dimensions.get('screen').height * 0.002,
                        flexDirection: 'row',
                      }}>
                      <View
                        style={{
                          flex: 2,
                        }}>
                        <Text>Update to this reservation</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Details</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Time of change</Text>
                      </View>
                      <View
                        style={{
                          flex: 0.5,
                        }}>
                        <Text>Source</Text>
                      </View>
                    </View>
                    <FlatList
                      style={{
                        overflow: 'visible',
                        zIndex: 1000,
                        marginBottom: 10,
                      }}
                      nestedScrollEnabled={true}
                      data={dummyData}
                      renderItem={renderTemporaryGuestDetails}
                      keyExtractor={(item, index) => String(index)}
                    />
                  </View>
                </View>
              </ScrollView>
            </View>
          </View>
        ) : null}
        {showTempCustomerDetails && showWaitlist ? (
          <View
            style={{
              backgroundColor: Colors.highlightColor,
              position: 'absolute',
              right: 0,
              height: '100%',
              width: Dimensions.get('screen').width * 0.67,
            }}>
            {/* top */}
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                height: '8%',
                flexDirection: 'row',
                padding: 10,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 250,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Wait First
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            {/* bottom part */}
            <View
              style={{
                padding: 10,
              }}>
              <ScrollView
                style={{
                  backgroundColor: Colors.whiteColor,
                  height: '89.5%',
                  width: '100%',
                }}>
                <View
                  style={{
                    paddingTop: Dimensions.get('screen').height * 0.03,
                    // borderWidth: 1,
                    flexDirection: 'row',
                  }}>
                  <AsyncImage
                    style={{
                      width: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      height: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      borderRadius: 9999,
                      marginLeft: Dimensions.get('screen').width * 0.01,
                      borderWidth: 1,
                    }}
                    // source={{
                    //   uri: item.avatar
                    // }}
                    // item={item}
                    hideLoading={true}
                  />
                  <View
                    style={{
                      marginLeft: Dimensions.get('screen').width * 0.02,
                      justifyContent: 'space-between',
                      height: Dimensions.get('screen').height * 0.15,
                    }}>
                    <Text>Temporary Guest</Text>
                    <Text>-</Text>
                    <Text>-</Text>
                    <Text>-</Text>
                  </View>
                  <View
                    style={{
                      position: 'absolute',
                      right: 10,
                      top: Dimensions.get('screen').height * 0.03,
                    }}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: Dimensions.get('screen').width * 0.13,
                            height: Dimensions.get('screen').height * 0.08,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Change Guest
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={[
                    {
                      flexDirection: 'row',
                      justifyContent: 'space-evenly',
                      width: '100%',
                      marginTop: Dimensions.get('screen').height * 0.03,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.84,
                        alignSelf: 'center',
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Party Size
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            color: Colors.whiteColor,
                            textAlign: 'center',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        2
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={true}
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        3:00PM
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={true}
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Indoor
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        6
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Turn Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        1h 30m
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    marginTop: Dimensions.get('screen').height * 0.03,
                    paddingHorizontal: 10,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Tags</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation tags have been added
                      </Text>
                    </View>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Notes</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation notes have been added
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Created</Text>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                            // borderWidth: 1,
                            textAlignVertical: 'center',
                          }}>
                          2:24 PM 3 Dec 2021
                        </Text>
                        <Text
                          style={{
                            paddingVertical: 5,
                            paddingHorizontal: 7,
                            borderRadius: 5,
                            borderWidth: 1,
                          }}>
                          In-house
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Manual Entry</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                          }}>
                          Bill amount manual entry
                        </Text>
                        <Text style={{}}>RM0.00</Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      borderWidth: 1,
                      borderRadius: 10,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        borderBottomWidth: 1,
                        width: '100%',
                        paddingHorizontal:
                          Dimensions.get('screen').width * 0.01,
                        paddingVertical:
                          Dimensions.get('screen').height * 0.002,
                        flexDirection: 'row',
                      }}>
                      <View
                        style={{
                          flex: 2,
                        }}>
                        <Text>Update to this reservation</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Details</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Time of change</Text>
                      </View>
                      <View
                        style={{
                          flex: 0.5,
                        }}>
                        <Text>Source</Text>
                      </View>
                    </View>
                    <FlatList
                      style={{
                        overflow: 'visible',
                        zIndex: 1000,
                        marginBottom: 10,
                      }}
                      nestedScrollEnabled={true}
                      data={dummyData}
                      renderItem={renderTemporaryGuestDetails}
                      keyExtractor={(item, index) => String(index)}
                    />
                  </View>
                </View>
              </ScrollView>
            </View>
          </View>
        ) : null}
        {showMemberCustomerDetails && showReservations ? (
          <View
            style={{
              backgroundColor: Colors.highlightColor,
              position: 'absolute',
              right: 0,
              height: '100%',
              width: Dimensions.get('screen').width * 0.67,
            }}>
            {/* top */}
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                height: '8%',
                flexDirection: 'row',
                padding: 10,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 100,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Seated
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 100,
                      marginLeft: 10,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Seated
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 100,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    No Show
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 170,
                      marginLeft: 10,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Cancel Reservation
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            {/* bottom part */}
            <View
              style={{
                padding: 10,
                flex: 1,
              }}>
              <View
                style={{
                  width: '100%',
                  flexDirection: 'row',
                  height: '10%',
                  marginBottom: 3,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setShowMemberGuestRes(false);
                    setShowMemberReservationRes(true);
                  }}
                  style={{
                    flex: 1,
                    height: '100%',
                    justifyContent: 'center',
                    borderTopLeftRadius: 5,
                    backgroundColor: showMemberReservationRes
                      ? Colors.whiteColor
                      : Colors.fieldtBgColor,
                    alignItems: 'center',
                  }}>
                  <Text>Reservation Details</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setShowMemberGuestRes(true);
                    setShowMemberReservationRes(false);
                    setShowGuestProfileRes(true);
                  }}
                  style={{
                    flex: 1,
                    height: '100%',
                    justifyContent: 'center',
                    borderTopRightRadius: 5,
                    backgroundColor: showMemberGuestRes
                      ? Colors.whiteColor
                      : Colors.fieldtBgColor,
                    alignItems: 'center',
                  }}>
                  <Text>Guest Details</Text>
                </TouchableOpacity>
              </View>
              {showMemberGuestRes ? (
                <ScrollView
                  style={{
                    backgroundColor: Colors.whiteColor,
                    height: '89.5%',
                    width: '100%',
                  }}>
                  <View
                    style={{
                      paddingTop: Dimensions.get('screen').height * 0.03,
                      // borderWidth: 1,
                      flexDirection: 'row',
                      paddingBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <AsyncImage
                      style={{
                        width: switchMerchant
                          ? 35
                          : Dimensions.get('screen').height * 0.18,
                        height: switchMerchant
                          ? 35
                          : Dimensions.get('screen').height * 0.18,
                        borderRadius: 9999,
                        marginLeft: Dimensions.get('screen').width * 0.01,
                        borderWidth: 1,
                      }}
                      // source={{
                      //   uri: item.avatar
                      // }}
                      // item={item}
                      hideLoading={false}
                    />
                    <View
                      style={{
                        marginLeft: Dimensions.get('screen').width * 0.02,
                        justifyContent: 'space-between',
                      }}>
                      <Text>Ryan</Text>
                      <Text></Text>
                      <Text>+60 17-979 2022</Text>
                    </View>
                  </View>
                  <View
                    style={{
                      width: '100%',
                      paddingVertical: Dimensions.get('screen').height * 0.008,
                      paddingHorizontal: Dimensions.get('screen').width * 0.01,
                      alignItems: 'center',
                      marginVertical: Dimensions.get('screen').height * 0.02,
                    }}>
                    <View
                      style={{
                        width: '100%',
                        flexDirection: 'row',
                      }}>
                      <Text
                        style={{
                          flex: 1,
                          textAlign: 'center',
                        }}>
                        Total spend
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        Visits
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        Avg/visit
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        Avg/cover
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        No-shows
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        Cancellation
                      </Text>
                    </View>
                    <View
                      style={{
                        width: '100%',
                        flexDirection: 'row',
                      }}>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        RM10,000.35
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        139
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        RM76.71
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        RM77.27
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        4
                      </Text>
                      <Text
                        style={{
                          textAlign: 'center',
                          flex: 1,
                        }}>
                        14
                      </Text>
                    </View>
                  </View>
                  <View
                    style={[
                      {
                        flexDirection: 'row',
                        width: '100%',
                        paddingHorizontal:
                          Dimensions.get('screen').width * 0.05,
                      },
                      switchMerchant
                        ? {
                          width: Dimensions.get('screen').width * 0.84,
                          alignSelf: 'center',
                        }
                        : {},
                    ]}>
                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          height: 30,
                          backgroundColor: showGuestProfileRes
                            ? Colors.tabMint
                            : Colors.whiteColor,
                          borderTopLeftRadius: 10,
                          borderBottomLeftRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 30,
                          paddingTop: 16,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setShowGuestProfileRes(true);
                      }}>
                      <View
                        style={[
                          {
                            marginTop: -15,
                          },
                          switchMerchant
                            ? {
                              marginTop: 15,
                              height: 60,
                            }
                            : {},
                        ]}>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              color: showGuestProfileRes
                                ? Colors.whiteColor
                                : Colors.tabMint,
                            },
                            switchMerchant ? { fontSize: 20 } : {},
                          ]}>
                          Profile
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        {
                          flex: 1,
                          height: 30,
                          backgroundColor: !showGuestProfileRes
                            ? Colors.tabMint
                            : Colors.whiteColor,
                          borderTopRightRadius: 10,
                          borderBottomRightRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 30,
                          paddingTop: 16,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                      ]}
                      onPress={() => {
                        setShowGuestProfileRes(false);
                      }}>
                      <View
                        style={[
                          {
                            marginTop: -15,
                          },
                          switchMerchant
                            ? {
                              marginTop: 15,
                              height: 60,
                            }
                            : {},
                        ]}>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              color: !showGuestProfileRes
                                ? Colors.whiteColor
                                : Colors.tabMint,
                            },
                            switchMerchant ? { fontSize: 20 } : {},
                          ]}>
                          History
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                  {showGuestProfileRes ? (
                    <View
                      style={{
                        marginTop: Dimensions.get('screen').height * 0.03,
                        paddingHorizontal: 10,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          height: 50,
                          justifyContent: 'space-evenly',
                        }}>
                        <View>
                          <Text>Current Balance</Text>
                          <Text>RM762.10</Text>
                        </View>
                        <View>
                          <Text>Credit redeemed</Text>
                          <Text>RM447.00</Text>
                        </View>
                        <View>
                          <Text>Campaigns available</Text>
                          <Text>0</Text>
                        </View>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          marginBottom: Dimensions.get('screen').height * 0.03,
                        }}>
                        <View
                          style={{
                            borderWidth: 1,
                            borderRadius: 10,
                            width: '45%',
                          }}>
                          <View
                            style={{
                              borderBottomWidth: 1,
                              width: '100%',
                              paddingHorizontal:
                                Dimensions.get('screen').width * 0.01,
                              paddingVertical:
                                Dimensions.get('screen').height * 0.002,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text>Guest Tags</Text>
                            <TouchableOpacity>
                              <Text>Edit</Text>
                            </TouchableOpacity>
                          </View>
                          <Text
                            style={{
                              paddingVertical:
                                Dimensions.get('screen').height * 0.05,
                              textAlign: 'center',
                            }}>
                            No tags have been added
                          </Text>
                        </View>
                        <View
                          style={{
                            borderWidth: 1,
                            borderRadius: 10,
                            width: '45%',
                          }}>
                          <View
                            style={{
                              borderBottomWidth: 1,
                              width: '100%',
                              paddingHorizontal:
                                Dimensions.get('screen').width * 0.01,
                              paddingVertical:
                                Dimensions.get('screen').height * 0.002,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text>Guest Notes</Text>
                            <TouchableOpacity>
                              <Text>Edit</Text>
                            </TouchableOpacity>
                          </View>
                          <Text
                            style={{
                              paddingVertical:
                                Dimensions.get('screen').height * 0.05,
                              textAlign: 'center',
                            }}>
                            Chicken is raw
                          </Text>
                        </View>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          marginBottom: Dimensions.get('screen').height * 0.03,
                        }}>
                        <View
                          style={{
                            borderWidth: 1,
                            borderRadius: 10,
                            width: '45%',
                          }}>
                          <View
                            style={{
                              borderBottomWidth: 1,
                              width: '100%',
                              paddingHorizontal:
                                Dimensions.get('screen').width * 0.01,
                              paddingVertical:
                                Dimensions.get('screen').height * 0.002,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text>Personal</Text>
                          </View>
                          <View
                            style={{
                              paddingHorizontal:
                                Dimensions.get('screen').width * 0.01,
                              paddingVertical:
                                Dimensions.get('screen').height * 0.03,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <RadioButtonRN
                              data={[
                                {
                                  label: 'Male',
                                },
                                {
                                  label: 'Female',
                                },
                                {
                                  label: 'Other'
                                }
                              ]}
                              style={{
                                borderWidth: 0,
                                height: 20,
                                width: 60,
                                flexDirection: 'row',
                              }}
                              selectedBtn={(e) => console.log(e)}
                              icon={
                                <FontAwesome
                                  name="check-circle"
                                  size={10}
                                  color="#2c9dd1"
                                />
                              }
                            />
                            <Text
                              style={{
                                paddingVertical: 5,
                                paddingHorizontal: 7,
                                borderRadius: 5,
                                borderWidth: 1,
                              }}>
                              In-house
                            </Text>
                          </View>
                        </View>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          marginBottom: Dimensions.get('screen').height * 0.03,
                        }}>
                        <View
                          style={{
                            borderWidth: 1,
                            borderRadius: 10,
                            width: '45%',
                          }}>
                          <View
                            style={{
                              borderBottomWidth: 1,
                              width: '100%',
                              paddingHorizontal:
                                Dimensions.get('screen').width * 0.01,
                              paddingVertical:
                                Dimensions.get('screen').height * 0.002,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text>Manual Entry</Text>
                            <TouchableOpacity>
                              <Text>Edit</Text>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              paddingHorizontal:
                                Dimensions.get('screen').width * 0.01,
                              paddingVertical:
                                Dimensions.get('screen').height * 0.03,
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            }}>
                            <Text
                              style={{
                                textAlign: 'center',
                              }}>
                              Bill amount manual entry
                            </Text>
                            <Text style={{}}>RM0.00</Text>
                          </View>
                        </View>
                      </View>
                      <View
                        style={{
                          borderWidth: 1,
                          borderRadius: 10,
                          width: '100%',
                        }}>
                        <View
                          style={{
                            borderBottomWidth: 1,
                            width: '100%',
                            paddingHorizontal:
                              Dimensions.get('screen').width * 0.01,
                            paddingVertical:
                              Dimensions.get('screen').height * 0.002,
                            flexDirection: 'row',
                          }}>
                          <View
                            style={{
                              flex: 2,
                            }}>
                            <Text>Update to this reservation</Text>
                          </View>
                          <View
                            style={{
                              flex: 1,
                            }}>
                            <Text>Details</Text>
                          </View>
                          <View
                            style={{
                              flex: 1,
                            }}>
                            <Text>Time of change</Text>
                          </View>
                          <View
                            style={{
                              flex: 0.5,
                            }}>
                            <Text>Source</Text>
                          </View>
                        </View>
                        <FlatList
                          style={{
                            overflow: 'visible',
                            zIndex: 1000,
                            marginBottom: 10,
                          }}
                          nestedScrollEnabled={true}
                          data={dummyData}
                          renderItem={renderTemporaryGuestDetails}
                          keyExtractor={(item, index) => String(index)}
                        />
                      </View>
                    </View>
                  ) : (
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '100%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                        }}>
                        <View
                          style={{
                            flex: 2,
                          }}>
                          <Text>Update to this reservation</Text>
                        </View>
                        <View
                          style={{
                            flex: 1,
                          }}>
                          <Text>Details</Text>
                        </View>
                        <View
                          style={{
                            flex: 1,
                          }}>
                          <Text>Time of change</Text>
                        </View>
                        <View
                          style={{
                            flex: 0.5,
                          }}>
                          <Text>Source</Text>
                        </View>
                      </View>
                      <FlatList
                        style={{
                          overflow: 'visible',
                          zIndex: 1000,
                          marginBottom: 10,
                        }}
                        nestedScrollEnabled={true}
                        data={dummyData}
                        renderItem={renderTemporaryGuestDetails}
                        keyExtractor={(item, index) => String(index)}
                      />
                    </View>
                  )}
                </ScrollView>
              ) : showMemberReservationRes ? (
                <ScrollView
                  style={{
                    backgroundColor: Colors.whiteColor,
                    height: '89.5%',
                    width: '100%',
                  }}>
                  <View
                    style={{
                      paddingTop: Dimensions.get('screen').height * 0.03,
                      // borderWidth: 1,
                      flexDirection: 'row',
                      paddingBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <AsyncImage
                      style={{
                        width: switchMerchant
                          ? 35
                          : Dimensions.get('screen').height * 0.18,
                        height: switchMerchant
                          ? 35
                          : Dimensions.get('screen').height * 0.18,
                        borderRadius: 9999,
                        marginLeft: Dimensions.get('screen').width * 0.01,
                        borderWidth: 1,
                      }}
                      // source={{
                      //   uri: item.avatar
                      // }}
                      // item={item}
                      hideLoading={true}
                    />
                    <View
                      style={{
                        marginLeft: Dimensions.get('screen').width * 0.02,
                        justifyContent: 'space-between',
                      }}>
                      <Text>Temporary Guest</Text>
                      <Text>-</Text>
                      <Text>-</Text>
                      <TouchableOpacity
                        style={[
                          {
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0F1A3C',
                            borderRadius: 5,
                            width: 160,
                            paddingHorizontal: 10,
                            height: 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          },
                          switchMerchant
                            ? {
                              width: Dimensions.get('screen').width * 0.13,
                              height: Dimensions.get('screen').height * 0.08,
                            }
                            : {},
                        ]}
                        onPress={() => { }}>
                        <Text
                          style={[
                            {
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          +3rd Party Booker
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View
                      style={{
                        position: 'absolute',
                        right: 10,
                        top: Dimensions.get('screen').height * 0.03,
                      }}>
                      <TouchableOpacity
                        style={[
                          {
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0F1A3C',
                            borderRadius: 5,
                            width: 160,
                            paddingHorizontal: 10,
                            height: 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          },
                          switchMerchant
                            ? {
                              width: Dimensions.get('screen').width * 0.13,
                              height: Dimensions.get('screen').height * 0.08,
                            }
                            : {},
                        ]}
                        onPress={() => { }}>
                        <Text
                          style={[
                            {
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Change Guest
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                  <View
                    style={{
                      width: '100%',
                      paddingVertical: Dimensions.get('screen').height * 0.008,
                      alignItems: 'center',
                      marginVertical: Dimensions.get('screen').height * 0.02,
                      backgroundColor: Colors.tabRed,
                    }}>
                    <Text style={{ color: Colors.whiteColor }}>
                      It is 5 minutes past the reservation start time
                    </Text>
                  </View>
                  <View
                    style={[
                      {
                        flexDirection: 'row',
                        justifyContent: 'space-evenly',
                        width: '100%',
                      },
                      switchMerchant
                        ? {
                          width: Dimensions.get('screen').width * 0.84,
                          alignSelf: 'center',
                        }
                        : {},
                    ]}>
                    <TouchableOpacity
                      style={[
                        {
                          width: '15%',
                          height: 70,
                          backgroundColor: Colors.tabMint,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 30,
                          paddingTop: 16,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                        switchMerchant
                          ? {
                            width: '22%',
                            height: 60,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <View
                        style={[
                          {
                            marginTop: -15,
                          },
                          switchMerchant
                            ? {
                              marginTop: 15,
                              height: 60,
                            }
                            : {},
                        ]}>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              color: Colors.whiteColor,
                            },
                            switchMerchant ? { fontSize: 20 } : {},
                          ]}>
                          Party Size
                        </Text>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Regular',
                              color: Colors.whiteColor,
                              textAlign: 'center',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          2
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        {
                          width: '15%',
                          height: 70,
                          backgroundColor: Colors.tabMint,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 30,
                          paddingTop: 16,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                        switchMerchant
                          ? {
                            width: '22%',
                            height: 60,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <View
                        style={[
                          {
                            marginTop: -15,
                          },
                          switchMerchant
                            ? {
                              marginTop: 15,
                              height: 60,
                            }
                            : {},
                        ]}>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              textAlign: 'center',
                              color: Colors.whiteColor,
                            },
                            switchMerchant ? { fontSize: 20 } : {},
                          ]}>
                          Time
                        </Text>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                              color: Colors.whiteColor,
                            },
                            switchMerchant ? { fontSize: 10 } : {},
                          ]}>
                          3:00PM
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        {
                          width: '15%',
                          height: 70,
                          backgroundColor: Colors.tabMint,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 30,
                          paddingTop: 16,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                        switchMerchant
                          ? {
                            width: '22%',
                            height: 60,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <View
                        style={[
                          {
                            marginTop: -15,
                          },
                          switchMerchant
                            ? {
                              marginTop: 15,
                              height: 60,
                            }
                            : {},
                        ]}>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              color: Colors.whiteColor,
                            },
                            switchMerchant ? { fontSize: 20 } : {},
                          ]}>
                          Indoor
                        </Text>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                              color: Colors.whiteColor,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          6
                        </Text>
                      </View>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        {
                          width: '15%',
                          height: 70,
                          backgroundColor: Colors.tabMint,
                          borderRadius: 10,
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          paddingHorizontal: 30,
                          paddingTop: 16,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        },
                        switchMerchant
                          ? {
                            width: '22%',
                            height: 60,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <View
                        style={[
                          {
                            marginTop: -15,
                          },
                          switchMerchant
                            ? {
                              marginTop: 15,
                              height: 60,
                            }
                            : {},
                        ]}>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Bold',
                              color: Colors.whiteColor,
                            },
                            switchMerchant ? { fontSize: 20 } : {},
                          ]}>
                          Turn Time
                        </Text>
                        <Text
                          style={[
                            {
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                              color: Colors.whiteColor,
                            },
                            switchMerchant ? { fontSize: 10 } : {},
                          ]}>
                          1h 30m
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      marginTop: Dimensions.get('screen').height * 0.03,
                      paddingHorizontal: 10,
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginBottom: Dimensions.get('screen').height * 0.03,
                      }}>
                      <View
                        style={{
                          borderWidth: 1,
                          borderRadius: 10,
                          width: '45%',
                        }}>
                        <View
                          style={{
                            borderBottomWidth: 1,
                            width: '100%',
                            paddingHorizontal:
                              Dimensions.get('screen').width * 0.01,
                            paddingVertical:
                              Dimensions.get('screen').height * 0.002,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text>Reservation Tags</Text>
                          <TouchableOpacity>
                            <Text>Edit</Text>
                          </TouchableOpacity>
                        </View>
                        <Text
                          style={{
                            paddingVertical:
                              Dimensions.get('screen').height * 0.05,
                            textAlign: 'center',
                          }}>
                          No reservation tags have been added
                        </Text>
                      </View>
                      <View
                        style={{
                          borderWidth: 1,
                          borderRadius: 10,
                          width: '45%',
                        }}>
                        <View
                          style={{
                            borderBottomWidth: 1,
                            width: '100%',
                            paddingHorizontal:
                              Dimensions.get('screen').width * 0.01,
                            paddingVertical:
                              Dimensions.get('screen').height * 0.002,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text>Reservation Notes</Text>
                          <TouchableOpacity>
                            <Text>Edit</Text>
                          </TouchableOpacity>
                        </View>
                        <Text
                          style={{
                            paddingVertical:
                              Dimensions.get('screen').height * 0.05,
                            textAlign: 'center',
                          }}>
                          No reservation notes have been added
                        </Text>
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginBottom: Dimensions.get('screen').height * 0.03,
                      }}>
                      <View
                        style={{
                          borderWidth: 1,
                          borderRadius: 10,
                          width: '45%',
                        }}>
                        <View
                          style={{
                            borderBottomWidth: 1,
                            width: '100%',
                            paddingHorizontal:
                              Dimensions.get('screen').width * 0.01,
                            paddingVertical:
                              Dimensions.get('screen').height * 0.002,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text>Created</Text>
                        </View>
                        <View
                          style={{
                            paddingHorizontal:
                              Dimensions.get('screen').width * 0.01,
                            paddingVertical:
                              Dimensions.get('screen').height * 0.03,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text
                            style={{
                              textAlign: 'center',
                              // borderWidth: 1,
                              textAlignVertical: 'center',
                            }}>
                            2:24 PM 3 Dec 2021
                          </Text>
                          <Text
                            style={{
                              paddingVertical: 5,
                              paddingHorizontal: 7,
                              borderRadius: 5,
                              borderWidth: 1,
                            }}>
                            In-house
                          </Text>
                        </View>
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginBottom: Dimensions.get('screen').height * 0.03,
                      }}>
                      <View
                        style={{
                          borderWidth: 1,
                          borderRadius: 10,
                          width: '45%',
                        }}>
                        <View
                          style={{
                            borderBottomWidth: 1,
                            width: '100%',
                            paddingHorizontal:
                              Dimensions.get('screen').width * 0.01,
                            paddingVertical:
                              Dimensions.get('screen').height * 0.002,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text>Manual Entry</Text>
                          <TouchableOpacity>
                            <Text>Edit</Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            paddingHorizontal:
                              Dimensions.get('screen').width * 0.01,
                            paddingVertical:
                              Dimensions.get('screen').height * 0.03,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text
                            style={{
                              textAlign: 'center',
                            }}>
                            Bill amount manual entry
                          </Text>
                          <Text style={{}}>RM0.00</Text>
                        </View>
                      </View>
                    </View>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '100%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                        }}>
                        <View
                          style={{
                            flex: 2,
                          }}>
                          <Text>Update to this reservation</Text>
                        </View>
                        <View
                          style={{
                            flex: 1,
                          }}>
                          <Text>Details</Text>
                        </View>
                        <View
                          style={{
                            flex: 1,
                          }}>
                          <Text>Time of change</Text>
                        </View>
                        <View
                          style={{
                            flex: 0.5,
                          }}>
                          <Text>Source</Text>
                        </View>
                      </View>
                      <FlatList
                        style={{
                          overflow: 'visible',
                          zIndex: 1000,
                          marginBottom: 10,
                        }}
                        nestedScrollEnabled={true}
                        data={dummyData}
                        renderItem={renderTemporaryGuestDetails}
                        keyExtractor={(item, index) => String(index)}
                      />
                    </View>
                  </View>
                </ScrollView>
              ) : null}
            </View>
          </View>
        ) : null}
        {showTempCustomerDetails && showFinished ? (
          <View
            style={{
              backgroundColor: Colors.highlightColor,
              position: 'absolute',
              right: 0,
              height: '100%',
              width: Dimensions.get('screen').width * 0.67,
            }}>
            {/* top */}
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                height: '8%',
                flexDirection: 'row',
                padding: 10,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 250,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Change Back To Seated
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            {/* bottom part */}
            <View
              style={{
                padding: 10,
              }}>
              <ScrollView
                style={{
                  backgroundColor: Colors.whiteColor,
                  height: '89.5%',
                  width: '100%',
                }}>
                <View
                  style={{
                    paddingTop: Dimensions.get('screen').height * 0.03,
                    // borderWidth: 1,
                    flexDirection: 'row',
                  }}>
                  <AsyncImage
                    style={{
                      width: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      height: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      borderRadius: 9999,
                      marginLeft: Dimensions.get('screen').width * 0.01,
                      borderWidth: 1,
                    }}
                    // source={{
                    //   uri: item.avatar
                    // }}
                    // item={item}
                    hideLoading={true}
                  />
                  <View
                    style={{
                      marginLeft: Dimensions.get('screen').width * 0.02,
                      justifyContent: 'space-between',
                      height: Dimensions.get('screen').height * 0.15,
                    }}>
                    <Text>Temporary Guest</Text>
                    <Text>-</Text>
                    <Text>-</Text>
                    <Text>-</Text>
                  </View>
                  <View
                    style={{
                      position: 'absolute',
                      right: 10,
                      top: Dimensions.get('screen').height * 0.03,
                    }}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: Dimensions.get('screen').width * 0.13,
                            height: Dimensions.get('screen').height * 0.08,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Change Guest
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={[
                    {
                      flexDirection: 'row',
                      justifyContent: 'space-evenly',
                      width: '100%',
                      marginTop: Dimensions.get('screen').height * 0.03,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.84,
                        alignSelf: 'center',
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Party Size
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            color: Colors.whiteColor,
                            textAlign: 'center',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        2
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={true}
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        3:00PM
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={true}
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Indoor
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        6
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Turn Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        1h 30m
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    marginTop: Dimensions.get('screen').height * 0.03,
                    paddingHorizontal: 10,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Tags</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation tags have been added
                      </Text>
                    </View>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Notes</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation notes have been added
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Created</Text>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                            // borderWidth: 1,
                            textAlignVertical: 'center',
                          }}>
                          2:24 PM 3 Dec 2021
                        </Text>
                        <Text
                          style={{
                            paddingVertical: 5,
                            paddingHorizontal: 7,
                            borderRadius: 5,
                            borderWidth: 1,
                          }}>
                          In-house
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Manual Entry</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                          }}>
                          Bill amount manual entry
                        </Text>
                        <Text style={{}}>RM0.00</Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      borderWidth: 1,
                      borderRadius: 10,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        borderBottomWidth: 1,
                        width: '100%',
                        paddingHorizontal:
                          Dimensions.get('screen').width * 0.01,
                        paddingVertical:
                          Dimensions.get('screen').height * 0.002,
                        flexDirection: 'row',
                      }}>
                      <View
                        style={{
                          flex: 2,
                        }}>
                        <Text>Update to this reservation</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Details</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Time of change</Text>
                      </View>
                      <View
                        style={{
                          flex: 0.5,
                        }}>
                        <Text>Source</Text>
                      </View>
                    </View>
                    <FlatList
                      style={{
                        overflow: 'visible',
                        zIndex: 1000,
                        marginBottom: 10,
                      }}
                      nestedScrollEnabled={true}
                      data={dummyData}
                      renderItem={renderTemporaryGuestDetails}
                      keyExtractor={(item, index) => String(index)}
                    />
                  </View>
                </View>
              </ScrollView>
            </View>
          </View>
        ) : null}
        {showTempCustomerDetails && showWaitlist ? (
          <View
            style={{
              backgroundColor: Colors.highlightColor,
              position: 'absolute',
              right: 0,
              height: '100%',
              width: Dimensions.get('screen').width * 0.67,
            }}>
            {/* top */}
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                height: '8%',
                flexDirection: 'row',
                padding: 10,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 250,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.13,
                        height: Dimensions.get('screen').height * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => { }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Wait First
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            {/* bottom part */}
            <View
              style={{
                padding: 10,
              }}>
              <ScrollView
                style={{
                  backgroundColor: Colors.whiteColor,
                  height: '89.5%',
                  width: '100%',
                }}>
                <View
                  style={{
                    paddingTop: Dimensions.get('screen').height * 0.03,
                    // borderWidth: 1,
                    flexDirection: 'row',
                  }}>
                  <AsyncImage
                    style={{
                      width: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      height: switchMerchant
                        ? 35
                        : Dimensions.get('screen').height * 0.18,
                      borderRadius: 9999,
                      marginLeft: Dimensions.get('screen').width * 0.01,
                      borderWidth: 1,
                    }}
                    // source={{
                    //   uri: item.avatar
                    // }}
                    // item={item}
                    hideLoading={true}
                  />
                  <View
                    style={{
                      marginLeft: Dimensions.get('screen').width * 0.02,
                      justifyContent: 'space-between',
                      height: Dimensions.get('screen').height * 0.15,
                    }}>
                    <Text>Temporary Guest</Text>
                    <Text>-</Text>
                    <Text>-</Text>
                    <Text>-</Text>
                  </View>
                  <View
                    style={{
                      position: 'absolute',
                      right: 10,
                      top: Dimensions.get('screen').height * 0.03,
                    }}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: Dimensions.get('screen').width * 0.13,
                            height: Dimensions.get('screen').height * 0.08,
                          }
                          : {},
                      ]}
                      onPress={() => { }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Change Guest
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={[
                    {
                      flexDirection: 'row',
                      justifyContent: 'space-evenly',
                      width: '100%',
                      marginTop: Dimensions.get('screen').height * 0.03,
                    },
                    switchMerchant
                      ? {
                        width: Dimensions.get('screen').width * 0.84,
                        alignSelf: 'center',
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Party Size
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            color: Colors.whiteColor,
                            textAlign: 'center',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        2
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={true}
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        3:00PM
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={true}
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Indoor
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        6
                      </Text>
                    </View>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      {
                        width: '15%',
                        height: 70,
                        backgroundColor: Colors.tabMint,
                        borderRadius: 10,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingHorizontal: 30,
                        paddingTop: 16,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                      },
                      switchMerchant
                        ? {
                          width: '22%',
                          height: 60,
                        }
                        : {},
                    ]}
                    onPress={() => { }}>
                    <View
                      style={[
                        {
                          marginTop: -15,
                        },
                        switchMerchant
                          ? {
                            marginTop: 15,
                            height: 60,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 20 } : {},
                        ]}>
                        Turn Time
                      </Text>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'center',
                            color: Colors.whiteColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        1h 30m
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    marginTop: Dimensions.get('screen').height * 0.03,
                    paddingHorizontal: 10,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Tags</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation tags have been added
                      </Text>
                    </View>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Reservation Notes</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <Text
                        style={{
                          paddingVertical:
                            Dimensions.get('screen').height * 0.05,
                          textAlign: 'center',
                        }}>
                        No reservation notes have been added
                      </Text>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Created</Text>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                            // borderWidth: 1,
                            textAlignVertical: 'center',
                          }}>
                          2:24 PM 3 Dec 2021
                        </Text>
                        <Text
                          style={{
                            paddingVertical: 5,
                            paddingHorizontal: 7,
                            borderRadius: 5,
                            borderWidth: 1,
                          }}>
                          In-house
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: Dimensions.get('screen').height * 0.03,
                    }}>
                    <View
                      style={{
                        borderWidth: 1,
                        borderRadius: 10,
                        width: '45%',
                      }}>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          width: '100%',
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.002,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text>Manual Entry</Text>
                        <TouchableOpacity>
                          <Text>Edit</Text>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          paddingHorizontal:
                            Dimensions.get('screen').width * 0.01,
                          paddingVertical:
                            Dimensions.get('screen').height * 0.03,
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}>
                        <Text
                          style={{
                            textAlign: 'center',
                          }}>
                          Bill amount manual entry
                        </Text>
                        <Text style={{}}>RM0.00</Text>
                      </View>
                    </View>
                  </View>
                  <View
                    style={{
                      borderWidth: 1,
                      borderRadius: 10,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        borderBottomWidth: 1,
                        width: '100%',
                        paddingHorizontal:
                          Dimensions.get('screen').width * 0.01,
                        paddingVertical:
                          Dimensions.get('screen').height * 0.002,
                        flexDirection: 'row',
                      }}>
                      <View
                        style={{
                          flex: 2,
                        }}>
                        <Text>Update to this reservation</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Details</Text>
                      </View>
                      <View
                        style={{
                          flex: 1,
                        }}>
                        <Text>Time of change</Text>
                      </View>
                      <View
                        style={{
                          flex: 0.5,
                        }}>
                        <Text>Source</Text>
                      </View>
                    </View>
                    <FlatList
                      style={{
                        overflow: 'visible',
                        zIndex: 1000,
                        marginBottom: 10,
                      }}
                      nestedScrollEnabled={true}
                      data={dummyData}
                      renderItem={renderTemporaryGuestDetails}
                      keyExtractor={(item, index) => String(index)}
                    />
                  </View>
                </View>
              </ScrollView>
            </View>
          </View>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },

  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },

  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default FloorScreen;
