import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  Modal as ModalComponent,
  useWindowDimensions,
  DevSettings,
  Platform,
  TouchableHighlight,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import { TextInput, FlatList, ScrollView } from 'react-native-gesture-handler';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from '../util/User';
import LoginScreen from './LoginScreen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from 'react-native-check-box';
// import { color } from 'react-native-reanimated';
import AIcon from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import Styles from '../constant/Styles';
import moment, { isDate } from 'moment';
// import Barcode from 'react-native-barcode-builder';
import Switch from 'react-native-switch-pro';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet,
  parseImagePickerResponse
} from '../util/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { ORDER_TYPE, RESET_DATA_TYPE, USER_ORDER_PAYMENT_OPTIONS, WEEK, EXPAND_TAB_TYPE, CASH_DRAWER_OPEN_EVENT_TYPE_DROPDOWN_LIST, CASH_DRAWER_OPEN_EVENT_TYPE, USER_IDLE_SIGN_OUT_EVENT_TYPE, USER_IDLE_SIGN_OUT_EVENT_TYPE_DROPDOWN_LIST, KD_PRINT_EVENT_TYPE_DROPDOWN_LIST, KD_PRINT_EVENT_TYPE, KD_PRINT_VARIATION, KD_PRINT_VARIATION_DROPDOWN_LIST, KD_FONT_SIZE, KD_FONT_SIZE_DROPDOWN_LIST, RECEIPT_PRINTING_EVENT_TYPE, RECEIPT_PRINTING_EVENT_TYPE_DROPDOWN_LIST, WEB_ORDER_VARIANT_LAYOUT, REPORT_DATA_SIZE_LIMIT, ORDER_TYPE_DETAILS, ORDER_TYPE_DETAILS_DROPDOWN_LIST, WEB_ORDER_LIST_LAYOUT, REPORT_DISPLAY_TYPE, QR_OPERATION_TIME, WEB_ORDER_UPSELLING_LAYOUT, OUTLET_DISPLAY_PAIRING_TYPE, OUTLET_DISPLAY_PAIRING_DEVICE, OUTLET_DISPLAY_PAIRING_TYPE_DROPDOWN_LIST, OUTLET_DISPLAY_PAIRING_DEVICE_DROPDOWN_LIST, ROLE_TYPE, OUTLET_SHIFT_STATUS } from '../constant/common';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import AsyncImage from '../components/asyncImage';
import { uploadImageToFirebaseStorage } from '../util/common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import {
  // USBPrinter,
  NetPrinter,
  BLEPrinter,
} from '@conodene/react-native-thermal-receipt-printer-image-qr';
import { CODEPAGE, ESCPOS_CMD } from '../constant/printer';
import {
  connectToPrinter,
  convertUtf8ArrayToStr,
  printBarcode,
} from '../util/printer';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import DeviceInfo from 'react-native-device-info';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { TableStore } from '../store/tableStore';
import APILocal from '../util/apiLocalReplacers';
import { EI_USER_FIELDS, EI_USER_FIELDS_DROPDOWN_LIST } from "../constant/einvoice";
const RNFS = require('@dr.pogodin/react-native-fs');
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const SettingScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [selfCollect, setSelfCollect] = useState(true);
  const [openHourPickerVisible, setOpenHourPickerVisible] = useState(false);
  const [closeHourPickerVisible, setCloseHourPickerVisible] = useState(false);
  const [openHour, setOpenHour] = useState('');
  const [closeHour, setCloseHour] = useState('');
  const [isChecked2, setIsChecked2] = useState(false);
  const [isChecked3, setIsChecked3] = useState(false);
  const [isChecked4, setIsChecked4] = useState(false);
  const [isChecked5, setIsChecked5] = useState(false);
  const [isChecked6, setIsChecked6] = useState(false);
  const [isChecked7, setIsChecked7] = useState(false);
  const [isChecked8, setIsChecked8] = useState(false);
  const [isChecked9, setIsChecked9] = useState(false);
  const [isChecked10, setIsChecked10] = useState(false);
  const [isChecked11, setIsChecked11] = useState(false);
  const [isChecked12, setIsChecked12] = useState(false);
  const [isChecked13, setIsChecked13] = useState(false);
  const [value1, setValue1] = useState('');
  const [value2, setValue2] = useState('');
  const [amount, setAmount] = useState('');
  const [hourStart, setHourStart] = useState('');
  const [hourEnd, setHourEnd] = useState('');
  const [days, setDays] = useState(false);
  const [days1, setDays1] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showDistance, setShowDistance] = useState('');
  const [expiryPeriod, setExpiryPeriod] = useState('');
  const [extentionCharges, setExtentionCharges] = useState('');
  const [extentionDuration, setExtentionDuration] = useState('');
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [merchantDisplay, setMerchantDisplay] = useState(true);
  const [shift, setShift] = useState(false);
  const [tax, setTax] = useState(false);
  const [sample, setSample] = useState(false);
  const [redemption, setRedemption] = useState(false);
  const [redemptionList, setRedemptionList] = useState(true);
  const [redemptionAdd, setRedemptionAdd] = useState(false);
  const [order, setOrder] = useState(false);
  const [previousState, setPreviousState] = useState(false);
  const [receipt, setReceipt] = useState([]);
  const [detail, setDetail] = useState([]);
  const [number, setNumber] = useState([]);
  const [merchantInfo, setMerchantInfo] = useState([]);
  const [outlet, setOutlet] = useState([]);
  const [outletInfo, setOutletInfo] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  // const [outletId, setOutletId] = useState([]);
  const [merInfo, setMerInfo] = useState([]);
  // const [merchantId, setMerchantId] = useState([]);
  const [show, setShow] = useState(false);
  const [showModal3, setShowModal3] = useState(false);
  const [showModal4, setShowModal4] = useState(false);
  const [showModal5, setShowModal5] = useState(false);
  const [closingAmount, setClosingAmount] = useState('');
  const [options, setOptions] = useState([]);
  const [shift1, setShift1] = useState([]);
  const [status, setStatus] = useState(false);
  const [statusHalal, setStatusHalal] = useState(false);
  const [isQRPrintReceipt, setIsQRPrintReceipt] = useState(false);
  const [isQRNotPrintLogo, setIsQRNotPrintLogo] = useState(false);

  const [logo, setLogo] = useState('');
  const [logoType, setLogoType] = useState('');
  const [cover, setCover] = useState('');
  const [coverType, setCoverType] = useState('');

  const [name, setName] = useState('');
  const [tname, setTname] = useState('');
  const [rate, setRate] = useState('');
  const [address, setAddress] = useState('');
  const [addressDisplay, setAddressDisplay] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [emailWarningStock, setEmailWarningStock] = useState('');
  const [payment, setPayment] = useState('');
  const [time, setTime] = useState('');
  const [statue, setStatue] = useState('');
  const [status1, setStatus1] = useState(false);
  const [outlets, setOutlets] = useState([]);
  const [outletId, setOutletId] = useState(null);
  const [myTextInput, setMyTextInput] = useState(React.createRef());
  const [start_time, setStart_time] = useState(false);
  const [end_time, setEnd_time] = useState(false);
  const [rev_time, setRev_time] = useState('');
  const [category, setCategory] = useState('');
  const [close, setClose] = useState('Closed');
  const [showNote, setShowNote] = useState(false);
  const [expandView, setExpandView] = useState(false);
  const [value, setValue] = useState('');
  const [extendOption, setExtendOption] = useState([
    { optionId: 1, price: 20, day: 7, days: false },
  ]);
  const [redemptionInfo, setRedemptionInfo] = useState([]);
  const [alloutlet, setAlloutlet] = useState([]);
  const [discount, setDiscount] = useState('');
  const [amount1, setAmount1] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [categoryOutlet, setCategoryOutlet] = useState([]);
  const [extend, setExtend] = useState([]);
  const [outletss, setOutletss] = useState([]);
  const [redemptionDetail, setRedemptionDetail] = useState([]);
  const [outletInfo1, setOutletInfo1] = useState([]);
  const [category1, setCategory1] = useState([]);
  const [merchantName, setMerchantName] = useState('');
  const [addOutletName, setAddOutletName] = useState('');
  const [addOutletWindow, setAddOutletWindow] = useState(false);
  const [taxList, setTaxList] = useState([]);
  const [note1, setNote1] = useState('');
  const [note2, setNote2] = useState('');
  const [note3, setNote3] = useState('');
  const [openings, setOpenings] = useState([]);
  const [editOpeningIndex, setEditOpeningIndex] = useState(0);

  const [editOpeningType, setEditOpeningType] = useState('start');
  const [outletLat, setOutletLat] = useState(0);
  const [outletLng, setOutletLng] = useState(0);

  const inputAcRef = useRef(null);

  const [isLogoChanged, setIsLogoChanged] = useState(false);
  const [isCoverChanged, setIsCoverChanged] = useState(false);

  const [dropDownResetList, setDropDownResetList] = useState([]);
  const [scOrderTypes, setScOrderTypes] = useState([ORDER_TYPE.DINEIN]);
  const [taxOrderTypes, setTaxOrderTypes] = useState([ORDER_TYPE.DINEIN, ORDER_TYPE.DELIVERY, ORDER_TYPE.PICKUP]);

  //////////////////////////////////////////////////////////

  // 2023-01-05 - Add app type and name support for sc

  const [scOrderTypeDetails, setScOrderTypeDetails] = useState([ORDER_TYPE_DETAILS.POS, ORDER_TYPE_DETAILS.QR])
  const [scName, setScName] = useState('');

  //////////////////////////////////////////////////////////

  // halal toggle and pork free toggle useState
  const [isEnabled, setIsEnabled] = useState(false);
  const toggleSwitch = () => setIsEnabled((previousState) => !previousState);

  const [printerName, setPrinterName] = useState('Printer Name');
  const [printerIP, setPrinterIP] = useState('*************');

  const [sstRate, setSstRate] = useState('6');
  const [sstNumber, setSstNumber] = useState('');
  const [scRate, setScRate] = useState('5');
  const [sstActive, setSstActive] = useState(true);
  const [scActive, setScActive] = useState(false);

  const [showModalBreakTime, setShowModalBreakTime] = useState(false);
  const [showTimePickerBreakTimeStart, setShowTimePickerBreakTimeStart] = useState(false);
  const [showTimePickerBreakTimeEnd, setShowTimePickerBreakTimeEnd] = useState(false);
  const [selectedBreakTimeDay, setSelectedBreakTimeDay] = useState(WEEK[0]);
  const [selectedBreakTimeDayIndex, setSelectedBreakTimeDayIndex] = useState(0);

  /////////////////////////////////////////////////////////////////////  

  const [pickupPaymentOptions, setPickupPaymentOptions] = useState(
    USER_ORDER_PAYMENT_OPTIONS.ONLINE,
  );
  const [deliveryPaymentOptions, setDeliveryPaymentOptions] = useState(
    USER_ORDER_PAYMENT_OPTIONS.ONLINE,
  );
  const [dineinPaymentOptions, setDineinPaymentOptions] = useState(
    USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
  );
  const [dineinPaymentOptionsGeneric, setDineinPaymentOptionsGeneric] = useState(
    USER_ORDER_PAYMENT_OPTIONS.ONLINE,
  );
  const [docketPaymentOptions, setDocketPaymentOptions] = useState(
    USER_ORDER_PAYMENT_OPTIONS.ONLINE,
  );
  const [reservationPaymentOptions, setReservationPaymentOptions] = useState(
    USER_ORDER_PAYMENT_OPTIONS.ONLINE,
  );
  const [cashDrawerOpeningOptions, setCashDrawerOpeningOptions] = useState(
    CASH_DRAWER_OPEN_EVENT_TYPE.ALWAYS,
  );
  const [receiptPrintingOptions, setReceiptPrintingOptions] = useState(
    CASH_DRAWER_OPEN_EVENT_TYPE.ALWAYS,
  );
  const [userIdleSignOutOptions, setUserIdleSignOutOptions] = useState(
    USER_IDLE_SIGN_OUT_EVENT_TYPE.NEVER,
  );
  const switchTableLayout = TableStore.useState(s => s.switchTableLayout);
  const [dTableLayout, setDTableLayout] = useState(false);

  const forceCloseShiftBeforeSignOut = TableStore.useState(s => s.forceCloseShiftBeforeSignOut);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [currOutletOpening, setCurrOutletOpening] = useState({});
  const [currOutletOpeningOff, setCurrOutletOpeningOff] = useState({});
  const [currOutletBreakTime, setCurrOutletBreakTime] = useState({
    [WEEK[0]]: [],
    [WEEK[1]]: [],
    [WEEK[2]]: [],
    [WEEK[3]]: [],
    [WEEK[4]]: [],
    [WEEK[5]]: [],
    [WEEK[6]]: [],
  });

  //////////////////////////////////////////////////////////

  // 2022-09-14 - Add support to toggle whether to skip user info

  const [skipUserInfo, setSkipUserInfo] = useState(false);

  //////////////////////////////////////////////////////////

  // 2022-10-17 - To control the deliver/reject/undo deliver/undo reject event for printing kitchen docket

  const [kdPrintEventTypes, setKdPrintEventTypes] = useState([]);

  //////////////////////////////////////////////////////////

  // 2022-10-17 - To control the variation for printing kitchen docket

  const [kdPrintVariation, setKdPrintVariation] = useState(KD_PRINT_VARIATION.SUMMARY);

  ////////////////////////////////////////////////////////// 

  const [kdFontSize, setKdFontSize] = useState(KD_FONT_SIZE.NORMAL);

  //////////////////////////////////////////////////////////

  const [kdHeaderFontSize, setKdHeaderFontSize] = useState(KD_FONT_SIZE.EXTRA_LARGE);

  //////////////////////////////////////////////////////////

  const [isKdPrintSku, setIsKdPrintSku] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-12-08 - To toggle printing variant/addon title

  const [toPrintVariantAddonTitle, setToPrintVariantAddonTitle] = useState(true);

  //////////////////////////////////////////////////////////

  // 2023-03-03 - For toggle offline mode

  const [toggleOfflineMode, setToggleOfflineMode] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-06-12 - For toggle open order

  const [toggleOpenOrder, setToggleOpenOrder] = useState(false);

  //////////////////////////////////////////////////////////


  // 2023-03-06 - For multiple pos terminal mode

  const [multiplePosTerminalMode, setMultiplePosTerminalMode] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-03-22 - For multiple pos terminal mode

  const [toggleDisableAutoPrint, setToggleDisableAutoPrint] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-06-30 - To disable printing alerts

  const [toggleDisablePrintingAlert, setToggleDisablePrintingAlert] = useState(false);

  //////////////////////////////////////////////////////////

  // 2024-10-16 - customer display support

  const [customerDisplaySupport, setCustomerDisplaySupport] = useState(false);
  const [fcsWidth, setFCSWidth] = useState(0);
  const [fcsHeight, setFCSHeight] = useState(0);
  const [fcsText, setFCSText] = useState(0);

  //////////////////////////////////////////////////////////

  // 2022-12-28 - To control web order variant layout

  const [webOrderVariantLayout, setWebOrderVariantLayout] = useState(
    WEB_ORDER_VARIANT_LAYOUT.HORIZONTAL,
  );

  const [webOrderUpsellingLayout, setWebOrderUpsellingLayout] = useState(
    WEB_ORDER_UPSELLING_LAYOUT.NORMAL,
  )

  //////////////////////////////////////////////////////////

  // 2022-12-29 - To control report data size limit

  const [reportDataSizeLimit, setReportDataSizeLimit] = useState(
    REPORT_DATA_SIZE_LIMIT._500,
  );

  //////////////////////////////////////////////////////////

  // 2023-01-03 - Print receipt when user paid online

  const [printReceiptWhenPaidOnline, setPrintReceiptWhenPaidOnline] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-01-06 - Force to enter card info options (for Table > Payment Summary)

  const [forceToEnterCardInfo, setForceToEnterCardInfo] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-02-13 - To control web order list layout

  // const [webOrderListLayout, setWebOrderListLayout] = useState(
  //   WEB_ORDER_LIST_LAYOUT.GRID,
  // );

  //////////////////////////////////////////////////////////

  // 2023-02-21 - For web order v2 layout

  const [webOrderV2Layout, setWebOrderV2Layout] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-04-13 - Ask payment first options

  const [askPaymentFirstOrderTypes, setAskPaymentFirstOrderTypes] = useState([
    ORDER_TYPE.PICKUP,
    ORDER_TYPE.DELIVERY,
  ]);

  //////////////////////////////////////////////////////////

  // 2023-05-10 - Kitchen docket to print user info (name and phone)

  const [isKdPrintUserInfo, setIsKdPrintUserInfo] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-05-10 - Service charge for other d. orders

  const [scRateOtherD, setScRateOtherD] = useState('5');
  const [scActiveOtherD, setScActiveOtherD] = useState(false);
  const [scNameOtherD, setScNameOtherD] = useState(false);

  //////////////////////////////////////////////////////////

  // 2022-12-29 - To control report data size limit

  const [reportDisplayType, setReportDisplayType] = useState(
    REPORT_DISPLAY_TYPE.DAY,
  );

  //////////////////////////////////////////////////////////

  // 2023-05-19 - Auto print pay slip

  const [autoPrintPaySlip, setAutoPrintPaySlip] = useState(true);

  //////////////////////////////////////////////////////////

  // 2023-10-30 - Print KD/OS only, when order paid

  const [printKdOsWhenOrderPaid, setPrintKdOsWhenOrderPaid] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-12-14 - Shorten shift report

  const [shortenShiftReport, setShortenShiftReport] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-12-15 - Shorten receipt

  const [shortenReceipt, setShortenReceipt] = useState(false);

  //////////////////////////////////////////////////////////

  // 2024-01-19 - QR operation time

  const [qrOperationTime, setQrOperationTime] = useState(QR_OPERATION_TIME.FOLLOW_OPERATION_HOURS_AND_SHIFT);

  //////////////////////////////////////////////////////////

  // 2024-11-06 - host & display support

  const [odPairingType, setOdPairingType] = useState(OUTLET_DISPLAY_PAIRING_TYPE.A);
  const [odPairingDevice, setOdPairingDevice] = useState(OUTLET_DISPLAY_PAIRING_DEVICE.HOST);
  const [odActive, setOdActive] = useState(false);

  //////////////////////////////////////////////////////////

  // 2025-04-23 - e-invoice asking fields

  const [eiAskFields, setEiAskFields] = useState([]);

  //////////////////////////////////////////////////////////

  const userName = UserStore.useState((s) => s.name);
  const merchantNameHeader = MerchantStore.useState((s) => s.name);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const merchantLogo = MerchantStore.useState((s) => s.logo);
  const merchantLastUpdated = MerchantStore.useState((s) => s.merchantLastUpdated);
  const merchantShortcode = MerchantStore.useState((s) => s.shortcode);

  const outletsOpeningDict = OutletStore.useState((s) => s.outletsOpeningDict);

  const merchantId = UserStore.useState((s) => s.merchantId);

  const isOfflineReady = CommonStore.useState((s) => s.isOfflineReady);

  const [temp, setTemp] = useState('');

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const supportCodeData = CommonStore.useState(s => s.supportCodeData);

  const [retailMode, setRetailMode] = useState(false);
  const role = UserStore.useState((s) => s.role);
  const currOutletShiftStatus = OutletStore.useState((s) => s.currOutletShiftStatus);

  const setState = () => { };

  useEffect(() => {
    const useEffectCallback = async () => {
      var toggleDisableAutoPrintRaw = await AsyncStorage.getItem('toggleDisableAutoPrint');

      if (toggleDisableAutoPrintRaw === '1') {
        global.outletToggleDisableAutoPrint = true;

        setToggleDisableAutoPrint(true);
      }
      else if (toggleDisableAutoPrintRaw === '0') {
        global.outletToggleDisableAutoPrint = false;

        setToggleDisableAutoPrint(false);
      }
      else {
        global.outletToggleDisableAutoPrint = false;

        setToggleDisableAutoPrint(false);
      }

      //////////////////////////////////////////

      var toggleDisablePrintingAlertRaw = await AsyncStorage.getItem('toggleDisablePrintingAlert');

      if (toggleDisablePrintingAlertRaw === '1') {
        global.outletToggleDisablePrintingAlert = true;

        setToggleDisablePrintingAlert(true);
      }
      else if (toggleDisablePrintingAlertRaw === '0') {
        global.outletToggleDisablePrintingAlert = false;

        setToggleDisablePrintingAlert(false);
      }
      else {
        global.outletToggleDisablePrintingAlert = false;

        setToggleDisablePrintingAlert(false);
      }

      //////////////////////////////////////////

      var customerDisplaySupportRaw = await AsyncStorage.getItem('customerDisplaySupport');

      if (customerDisplaySupportRaw === '1') {
        global.customerDisplaySupport = true;

        setCustomerDisplaySupport(true);
      }
      else if (customerDisplaySupportRaw === '0') {
        global.customerDisplaySupport = false;

        setCustomerDisplaySupport(false);
      }
      else {
        global.customerDisplaySupport = false;

        setCustomerDisplaySupport(false);
      }

      CommonStore.update(s => {
        s.customerDisplaySupport = global.customerDisplaySupport;
      });

      //////////////////////////////////////////

      var odPairingTypeRaw = await AsyncStorage.getItem('odPairingType');

      if (odPairingTypeRaw) {
        global.odPairingType = odPairingTypeRaw;

        setOdPairingType(odPairingTypeRaw);

        OutletStore.update(s => {
          s.odPairingType = odPairingTypeRaw;
        });
      }

      var odPairingDeviceRaw = await AsyncStorage.getItem('odPairingDevice');

      if (odPairingDeviceRaw) {
        global.odPairingDevice = odPairingDeviceRaw;

        setOdPairingDevice(odPairingDeviceRaw);

        OutletStore.update(s => {
          s.odPairingDevice = odPairingDeviceRaw;
        })
      }

      //////////////////////////////////////////
    };

    useEffectCallback();
  }, []);

  useEffect(() => {
    setMerchantName(merchantNameHeader);
    setLogo(merchantLogo);
  }, [merchantNameHeader, merchantLogo, currOutlet]);

  useEffect(() => {
    if (
      currOutlet &&
      currOutlet.uniqueId &&
      currOutletId === currOutlet.uniqueId
    ) {
      setAddress(currOutlet.address);
      setAddressDisplay(currOutlet.address ? currOutlet.address : '');
      setPhone(currOutlet.phone ? currOutlet.phone : '');
      setEmail(currOutlet.email ? currOutlet.email : '');
      setEmailWarningStock(currOutlet.emailWarningStock ? currOutlet.emailWarningStock : '');

      setStatus(currOutlet.isPickupAccepted);
      setStatus1(currOutlet.isDeliveryAccepted);
      setStatusHalal(currOutlet.isHalal ? currOutlet.isHalal : false);
      setIsQRPrintReceipt(currOutlet.isQRPrintReceipt ? currOutlet.isQRPrintReceipt : false);
      setIsQRNotPrintLogo(currOutlet.isQRNotPrintLogo ? currOutlet.isQRNotPrintLogo : false);

      setSkipUserInfo(currOutlet.skipUserInfo ? currOutlet.skipUserInfo : false);

      setCover(currOutlet.cover);

      setOutletLat(currOutlet.lat);
      setOutletLng(currOutlet.lng);

      inputAcRef &&
        inputAcRef.current &&
        inputAcRef.current.setAddressText(currOutlet.address);

      // if (outletsOpeningDict[currOutletId]) {
      //   setCurrOutletOpening(outletsOpeningDict[currOutletId]);
      // }

      if (currOutlet.outletOpeningOff) {
        setCurrOutletOpeningOff(currOutlet.outletOpeningOff);
      }
      else {
        setCurrOutletOpeningOff({});
      }

      if (currOutlet.outletBreakTime) {
        setCurrOutletBreakTime(currOutlet.outletBreakTime);
      }
      else {
        setCurrOutletBreakTime({
          [WEEK[0]]: [],
          [WEEK[1]]: [],
          [WEEK[2]]: [],
          [WEEK[3]]: [],
          [WEEK[4]]: [],
          [WEEK[5]]: [],
          [WEEK[6]]: [],
        });
      }

      setPickupPaymentOptions(currOutlet.pickupPaymentOptions);
      setDeliveryPaymentOptions(currOutlet.deliveryPaymentOptions);
      setDineinPaymentOptions(currOutlet.dineinPaymentOptions);
      setDineinPaymentOptionsGeneric(currOutlet.dineinPaymentOptionsGeneric || USER_ORDER_PAYMENT_OPTIONS.ONLINE);
      setDocketPaymentOptions(currOutlet.docketPaymentOptions || USER_ORDER_PAYMENT_OPTIONS.ONLINE);
      setReservationPaymentOptions(currOutlet.reservationPaymentOptions || USER_ORDER_PAYMENT_OPTIONS.ONLINE);
      setCashDrawerOpeningOptions(currOutlet.cashDrawerOpeningOptions || CASH_DRAWER_OPEN_EVENT_TYPE.ALWAYS);
      setReceiptPrintingOptions(currOutlet.receiptPrintingOptions || RECEIPT_PRINTING_EVENT_TYPE.ALWAYS);
      setUserIdleSignOutOptions(currOutlet.userIdleSignOutOptions || USER_IDLE_SIGN_OUT_EVENT_TYPE.NEVER);

      setAskPaymentFirstOrderTypes(currOutlet.askPaymentFirstOrderTypes || [ORDER_TYPE.PICKUP, ORDER_TYPE.DELIVERY]);

      setSstRate((currOutlet.taxRate * 100).toFixed(0));
      setSstNumber(currOutlet.taxNum ? currOutlet.taxNum : ''); //sstNumber
      setScRate((currOutlet.scRate * 100).toFixed(0));
      setSstActive(currOutlet.taxActive);
      setScActive(currOutlet.scActive);
      setScOrderTypes(currOutlet.scOrderTypes || [ORDER_TYPE.DINEIN]);
      setTaxOrderTypes(currOutlet.taxOrderTypes ? currOutlet.taxOrderTypes : [ORDER_TYPE.DINEIN, ORDER_TYPE.PICKUP, ORDER_TYPE.DELIVERY]);
      setScOrderTypeDetails(currOutlet.scOrderTypeDetails ? currOutlet.scOrderTypeDetails : [ORDER_TYPE_DETAILS.POS, ORDER_TYPE_DETAILS.QR]);
      setScName(currOutlet.scName ? currOutlet.scName : '');

      setScRateOtherD(((currOutlet.scRateOtherD ? currOutlet.scRateOtherD : 0) * 100).toFixed(0));
      setScActiveOtherD(currOutlet.scActiveOtherD ? currOutlet.scActiveOtherD : false);
      setScNameOtherD(currOutlet.scNameOtherD ? currOutlet.scNameOtherD : '');

      setKdPrintEventTypes(currOutlet.kdPrintEventTypes !== undefined ? currOutlet.kdPrintEventTypes : [
        KD_PRINT_EVENT_TYPE.DELIVER,
        KD_PRINT_EVENT_TYPE.REJECT,
        KD_PRINT_EVENT_TYPE.UNDO_DELIVER,
        KD_PRINT_EVENT_TYPE.UNDO_REJECT,
        KD_PRINT_EVENT_TYPE.SWITCH_TABLE,
      ]);

      setKdPrintVariation(currOutlet.kdPrintVariation !== undefined ? currOutlet.kdPrintVariation : KD_PRINT_VARIATION.SUMMARY);

      setKdFontSize(currOutlet.kdFontSize !== undefined ? currOutlet.kdFontSize : KD_FONT_SIZE.NORMAL);

      setKdHeaderFontSize(currOutlet.kdHeaderFontSize !== undefined ? currOutlet.kdHeaderFontSize : KD_FONT_SIZE.EXTRA_LARGE);

      setIsKdPrintSku(currOutlet.isKdPrintSku !== undefined ? currOutlet.isKdPrintSku : false);

      setToPrintVariantAddonTitle(currOutlet.toPrintVariantAddonTitle !== undefined ? currOutlet.toPrintVariantAddonTitle : true);

      setIsKdPrintUserInfo(currOutlet.isKdPrintUserInfo !== undefined ? currOutlet.isKdPrintUserInfo : false);

      setToggleOfflineMode(currOutlet.toggleOfflineMode !== undefined ? currOutlet.toggleOfflineMode : false);

      setToggleOpenOrder(currOutlet.toggleOpenOrder !== undefined ? currOutlet.toggleOpenOrder : false);

      setMultiplePosTerminalMode(currOutlet.multiplePosTerminalMode !== undefined ? currOutlet.multiplePosTerminalMode : false);

      // setToggleDisableAutoPrint(currOutlet.toggleDisableAutoPrint !== undefined ? currOutlet.toggleDisableAutoPrint : false);

      setPrintReceiptWhenPaidOnline(currOutlet.printReceiptWhenPaidOnline !== undefined ? currOutlet.printReceiptWhenPaidOnline : false);

      setAutoPrintPaySlip(currOutlet.autoPrintPaySlip !== undefined ? currOutlet.autoPrintPaySlip : true);

      setShortenShiftReport(currOutlet.shortenShiftReport !== undefined ? currOutlet.shortenShiftReport : false);

      setShortenReceipt(currOutlet.shortenReceipt !== undefined ? currOutlet.shortenReceipt : false);

      setWebOrderVariantLayout(currOutlet.webOrderVariantLayout !== undefined ? currOutlet.webOrderVariantLayout : WEB_ORDER_VARIANT_LAYOUT.HORIZONTAL);

      setWebOrderUpsellingLayout(currOutlet.webOrderUpsellingLayout !== undefined ? currOutlet.webOrderUpsellingLayout : WEB_ORDER_UPSELLING_LAYOUT.NORMAL);

      // setWebOrderListLayout(currOutlet.webOrderListLayout !== undefined ? currOutlet.webOrderListLayout : WEB_ORDER_LIST_LAYOUT.GRID);

      setReportDataSizeLimit(currOutlet.reportDataSizeLimit !== undefined ? currOutlet.reportDataSizeLimit : REPORT_DATA_SIZE_LIMIT._100);

      setReportDisplayType(currOutlet.reportDisplayType !== undefined ? currOutlet.reportDisplayType : REPORT_DISPLAY_TYPE.DAY);

      setForceToEnterCardInfo(currOutlet.forceToEnterCardInfo !== undefined ? currOutlet.forceToEnterCardInfo : false);

      setWebOrderV2Layout(currOutlet.webOrderV2Layout !== undefined ? currOutlet.webOrderV2Layout : false);

      setQrOperationTime(currOutlet.qrOperationTime ? currOutlet.qrOperationTime : QR_OPERATION_TIME.FOLLOW_OPERATION_HOURS_AND_SHIFT);

      setFCSHeight(currOutlet.fcsHeight ? parseFloat(currOutlet.fcsHeight) : 0);
      setFCSWidth(currOutlet.fcsWidth ? parseFloat(currOutlet.fcsWidth) : 0);
      setFCSText(currOutlet.fcsText ? parseFloat(currOutlet.fcsText) : 0);

      setOdActive(currOutlet.odActive !== undefined ? currOutlet.odActive : false);

      setDTableLayout(currOutlet.dTableLayout !== undefined ? currOutlet.dTableLayout : false);

      setEiAskFields(currOutlet.eiAskFields !== undefined ? currOutlet.eiAskFields : [
        EI_USER_FIELDS.EMAIL,
        EI_USER_FIELDS.ID,
        EI_USER_FIELDS.TIN,
        EI_USER_FIELDS.ADDRESS,
      ]);
    }
  }, [
    currOutlet,
    currOutletId,
    // outletsOpeningDict,
    // inputAcRef
  ]);

  useEffect(() => {
    if (outletsOpeningDict[currOutletId]) {
      setCurrOutletOpening(outletsOpeningDict[currOutletId]);
    }
  }, [
    outletsOpeningDict,
  ]);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          General Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  useEffect(() => {
    loadFromAsyncStorage();
  }, []);

  const loadFromAsyncStorage = async () => {
    // const printerIPRaw = await AsyncStorage.getItem('printerIP');

    const printerIPRaw = global.printerIP;

    if (printerIPRaw) {
      setPrinterIP(printerIPRaw);
    }
  };

  const getRedemptionDetail = (param) => {
    // ApiClient.GET(API.getOneSettingRedemption + param).then((result) => {
    //   setState({
    //     redemptionDetail: result,
    //     outletInfo1: result.outlet.name,
    //     category1: result.category.name,
    //     expiryPeriod: result.expiryPeriod,
    //     extentionCharges: result.extentionCharges,
    //     extentionDuration: result.extentionDuration,
    //   });
    // });
  };

  // componentDidMount = () => {

  //   ApiClient.GET(API.getOutletByMerchant + User.getMerchantId()).then((result) => {
  //     setState({ outletInfo: result });
  //     result.map((element) => {
  //       setState({
  //         outletId: element.id,
  //         outletName: element.name,
  //         merchantName: element.merchant.name
  //       });
  //     });
  //   });

  //   ApiClient.GET(API.getAllSettingRedemption + User.getMerchantId()).then((result) => {
  //     setState({ redemptionInfo: result });
  //   });

  //   outletFunc()
  //   ApiClient.GET(API.getOutletCategory + User.getOutletId()).then((result) => {
  //     if (result !== undefined) {
  //       setState({ categoryOutlet: result });
  //     }

  //   });
  // }

  // console.log('device model');
  // console.log(DeviceInfo.getModel());

  let version = DeviceInfo.getVersion();

  const pushArray1 = (param) => {
    const push = outletss;
    push.push({
      outletId: param,
    });
  };

  const removePush1 = (param) => {
    const items = outletss;
    const filterArray = items.filter((item) => item.id !== param);
    setState({ alloutlet: filterArray });
  };

  const check3 = (param) => {
    if (isChecked12 == false) {
      setState({ isChecked12: true });
      removePush1(param);
    } else {
      setState({ isChecked12: false });
      pushArray1(param);
    }
  };

  const filter = (param) => {
    // ApiClient.GET(API.getAllSettingRedemption + User.getMerchantId()).then((result) => {
    //   const result1 = items.filter((item) => item.outletId !== param)
    //   setState({ redemptionInfo: result1 });
    // });
  };

  const check1 = (price, duration) => {
    if (isChecked11 == false) {
      setState({
        isChecked11: true,
        extentionCharges: '0',
        extentionDuration: '0',
      });
    } else {
      setState({
        isChecked11: false,
        extentionCharges: price,
        extentionDuration: duration,
      });
    }
  };

  const outletFunc = () => {
    // ApiClient.GET(API.getOutletByMerchant).then((result) => {
    //   const tmpCategories = {};
    //   for (const category of result) {
    //     const outletName = category.merchant.name
    //     const outletId = category.id
    //     if (!tmpCategories[outletName]) {
    //       tmpCategories[outletName] = {
    //         label: outletName,
    //         value: outletId,
    //       };
    //     }
    //   }
    //   const categories = Object.values(tmpCategories);
    //   setState({ outlets: categories });
    // });
  };

  const onaddoption = () => {
    const extendOption = extendOption;
    extendOption.push({
      optionId: (extendOption.length + 1).toString(),
      price: '',
      day: '',
      days,
    });
    setState({ extendOption });
  };

  const renderOptions = () => {
    const options = [];
    const extendOption = extendOption;
    for (const opt of extendOption) {
      options.push(
        <View>
          <View style={{ flexDirection: 'row' }}>
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <CheckBox
                style={{
                  padding: 10,
                }}
                onClick={() => {
                  setState({
                    isChecked11: !isChecked11,
                  });
                  check1(extentionCharges, extentionDuration);
                }}
                checkBoxColor={Colors.fieldtBgColor}
                uncheckedCheckBoxColor={Colors.tabGrey}
                checkedCheckBoxColor={Colors.primaryColor}
                isChecked={isChecked11}
              />
            </View>
            <View>
              <View>
                <Text style={{ color: Colors.descriptionColor }}>RM:</Text>
                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={styles.textInput8}
                  placeholder="Amount"
                  defaultValue={extentionCharges}
                  onChangeText={(text) => {
                    const extendOption = extendOption;
                    const item = extendOption.find(
                      (obj) => obj.id === extendOption.id,
                    );
                    item.price = text;
                    setState({ extendOption, extentionCharges: text });
                  }}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  value={(value) => {
                    const extendOption = extendOption;
                    const item = extendOption.find(
                      (obj) => obj.id === extendOption.id,
                    );
                    value = item.price;
                  }}
                  ref={myTextInput}
                />
              </View>
            </View>
            <View
              style={{
                width: '6%',
                justifyContent: 'center',
                alignItems: 'center',
                marginLeft: '3%',
              }}>
              <Text style={{ fontSize: 15 }}>For</Text>
            </View>
            <View style={{ marginLeft: '5%' }}>
              <Text
                style={{
                  color: Colors.descriptionColor,
                  fontSize: 15,
                }}>
                Durations:
              </Text>
              <View style={styles.textInput10}>
                <View style={{ flex: 1 }}>
                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{ alignSelf: 'center' }}
                    placeholder="Period"
                    defaultValue={extentionDuration}
                    onChangeText={(text) => {
                      const extendOption = extendOption;
                      const item = extendOption.find(
                        (obj) => obj.id === extendOption.id,
                      );
                      item.day = text;
                      setState({ extendOption, extentionDuration: text });
                    }}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    value={(value) => {
                      const extendOption = extendOption;
                      const item = extendOption.find(
                        (obj) => obj.id === extendOption.id,
                      );
                      value = item.day;
                    }}
                    ref={myTextInput}
                  />
                </View>
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    borderLeftWidth: StyleSheet.hairlineWidth,
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      marginTop: 15,
                      marginLeft: '5%',
                      color: Colors.descriptionColor,
                    }}>
                    {days1 == false ? 'Days' : 'Months'}
                  </Text>
                  <TouchableOpacity
                    onPress={() => {
                      setState({ days1: !days1 });
                    }}>
                    <View
                      style={{
                        marginLeft: '30%',
                        marginTop: 18,
                      }}>
                      <SimpleLineIcons name="arrow-down" size={12} />
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </View>,
      );
    }
    return options;
  };

  const orderFunc = () => {
    var body = {
      merchantId: User.getMerchantId(),
      isAllOutlet: isChecked6 == true ? '1' : null,
      outletId,
      deliveryDistance: showDistance,
      deliveryFee: isChecked6 == true ? amount : amount1,
      deliveryHourStart: hourStart,
      deliveryHourEnd: hourEnd,
      deliveryPrice: isChecked8 == true ? value1 : 0,
      pickUpPrice: isChecked9 == true ? value2 : 0,
      fireorder: status1,
      category,
    };
    // ApiClient.POST(API.createSettingOrder, body, false).then((result) => {
    //   if (result.outletId != null) {
    //     Alert.alert(
    //       'Congratulation!',
    //       'You Have Successfully Created',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => { },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const renderItem = ({ item }) => (
    <View
      style={{
        backgroundColor: '#ffffff',
        borderRadius: 5,
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderTopWidth: StyleSheet.hairlineWidth,
        borderTopColor: '#c4c4c4',
      }}>
      <Text
        style={{
          paddingLeft: 15,
          flex: 2,
          alignSelf: 'center',
          fontFamily: 'NunitoSans-SemiBold',
          fontSize: 18,
        }}>
        {item.outlet.name}
      </Text>
      <View
        style={{
          flex: 2,
          alignSelf: 'center',
          alignItems: 'center',
        }}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: 'NunitoSans-SemiBold',
            marginRight: '20%',
          }}>
          {item.category == null ? 'Unknown' : item.category.name}
        </Text>
      </View>
      <View
        style={{
          flex: 2,
          alignItems: 'center',
          alignSelf: 'center',
        }}>
        <Text
          style={{
            alignSelf: 'center',
            fontSize: 16,
            fontFamily: 'NunitoSans-SemiBold',
          }}>
          {item.expiryPeriod} Days
        </Text>
      </View>
      <View
        style={{
          flex: 4,
        }}>
        <View
          style={{
            height: 50,
            width: '80%',
            backgroundColor: Colors.fontDark,
            justifyContent: 'center',
            borderRadius: 5,
            flexDirection: 'row',
            alignItems: 'center',
            marginLeft: '5%',
          }}>
          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 15,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Bold',
            }}>
            {item.outlet.name}
          </Text>
          <View style={{ alignItems: 'flex-end' }}>
            <AntDesign name="close" size={15} color={Colors.whiteColor} />
          </View>
        </View>
      </View>
      <View style={{ flex: 1, alignItems: 'center' }}>
        <TouchableOpacity
          onPress={() => {
            setState({ redemptionList: !redemptionList }),
              getRedemptionDetail(item.id);
          }}>
          <FontAwesome5 name="edit" size={23} color={Colors.primaryColor} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const redemptionFunc = () => {
    var body = {
      merchantId: User.getMerchantId(),
      isAllOutlet: isChecked13 == true ? '1' : null,
      outletId,
      category,
      expiryPeriod,
      extentionCharges,
      extentionDuration,
      redemptionOutletId: outletId,
    };
    // ApiClient.POST(API.createSettingRedemption, body, false).then((result) => {
    //   if (result.outletId != null) {
    //     Alert.alert(
    //       'Congratulation!',
    //       'Successfully',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => { },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const addOutlet = () => {
    var body = {
      outletName: addOutletName,
      merchantId: User.getMerchantId(),
      merchantShortcode,
    };

    ApiClient.POST(API.createOutlet, body, false).then((result) => {
      if (result && result.status === 'success') {
        Alert.alert(
          'Success!',
          'Outlet has been added',
          [
            {
              text: 'OK',
              onPress: () => { },
            },
          ],
          { cancelable: false },
        );
      }
    });
  };

  const renderTax = ({ item }) => (
    <View
      style={{
        backgroundColor: '#FFFFFF',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#C4C4C4',
      }}>
      <Text style={{ width: '23%', color: Colors.primaryColor }}>
        {item.name}
      </Text>
      <Text style={{ width: '52%' }}>{item.rate}%</Text>
      <TouchableOpacity
        onPress={() => {
          deleteTax(item.id);
        }}
        style={{ width: '40%' }}>
        <Icon name="trash-2" size={20} color="#eb3446" />
      </TouchableOpacity>
    </View>
  );

  // function here
  const onCloseShiftBtn = (key) => {
    var decimal = closingAmount.split('.')[1];
    if (key >= 0 || key == '.') {
      if (closingAmount.includes('.'))
        if (closingAmount.length < 12 && decimal.length < 2)
          setState({ closingAmount: closingAmount + key });
      if (!closingAmount.includes('.')) {
        if (closingAmount.length < 12)
          setState({ closingAmount: closingAmount + key });
      }
    } else if (closingAmount.length > 0)
      setState({ closingAmount: closingAmount.slice(0, key) });
  };

  const getCurrentShift = (outletId) => {
    // ApiClient.GET(API.getCurrentShift + outletId).then((result) => {
    //   setState({ shift1: result.success });
    // });
    // try {
    //   if (result.id != null) {
    //     Alert.alert(
    //       '',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => { },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // } catch (error) { }
  };

  const getTax = (outletId) => {
    ApiClient.GET(API.merchantTax + outletId).then((result) => {
      setState({ taxList: result });
    });
  };

  const checkTaxName = (tname) => {
    for (const tax of taxList) {
      if (tname.toLowerCase() == tax.name.toLowerCase()) {
        return true;
      }
    }
    return false;
  };

  const createTax = () => {
    if (!tname) {
      Alert.alert(
        'Error',
        'Please fill in the information',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );

    } else if (!Number.isInteger(Number(rate))) {
      Alert.alert(
        'Error',
        'Tax is only in numeric',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );

    } else if (checkTaxName(tname)) {
      Alert.alert(
        'Error',
        'Name has been used',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );

    } else {
      var body = {
        name: tname,
        rate,
        desciption: '',
        outletId: User.getOutletId(),
      };
      // ApiClient.POST(API.taxes, body, false).then((result) => {
      //   try {
      //     if (result) {
      //       Alert.alert(
      //         'Congratulation!',
      //         'You Have Successfully Inserted',
      //         [
      //           {
      //             text: 'OK',
      //             onPress: () => { },
      //           },
      //         ],
      //         { cancelable: false },
      //       );
      //       getTax(User.getOutletId())
      //     }
      //   } catch (error) {
      //     Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
      //       cancelable: false,
      //     });
      //   }
      // });
    }
  };

  const deleteTax = (taxId) => {
    // console.log('TAX ID', taxId);
    var body = {
      taxId,
    };
    // ApiClient.POST(API.deleteTax, body, false).then((result) => {
    //   try {
    //     // console.log("RESULT", result)
    //     if (result) {
    //       Alert.alert(
    //         'Successfully!',
    //         'You Have Successfully Deleted',
    //         [
    //           {
    //             text: 'OK',
    //             onPress: () => { },
    //           },
    //         ],
    //         { cancelable: false },
    //       );
    //       getTax(User.getOutletId())
    //     }
    //   } catch (error) {
    //     Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
    //       cancelable: false,
    //     });
    //   }
    // });
  };

  const renderSearch = (item) => {
    return (
      <View style={{ flexDirection: 'column' }}>
        <Text style={{ fontWeight: '700', fontSize: 14, marginBottom: 5 }}>
          {item.structured_formatting.main_text}
        </Text>

        <Text style={{ fontSize: 12, color: 'grey' }}>{item.description}</Text>
      </View>
    );
  };

  const outletById = (param) => {
    //<= outletId is not used in the param
    ApiClient.GET(API.outlet2 + param).then((result) => {
      //<= all you need to do is add parameter here
      // console.log('RESULT OUTLET', result);
      setState({ outlet: result, openings: result.openings });
      // console.log('openings', openings);
      setState({ status: result.status });
      setState({ merInfo: result.merchant[0] });
      setState({ cover: result.cover });
      setState({ logo: result.merchant[0].logo });
      setState({ openings: result.openings });

      myTextInput.current.clear();
    });
  };

  const editOutlet = (param) => {
    var body = {
      outletId: currOutletId,
      cover,

      merchantLogo: logo,

      address,
      addressDisplay,
      name,
      latlng: '',
      phone,
      email,
      taxId: '',
      status: '1',
      isBusy: '1',
      reservationStatus: true,
      openings: [
        {
          week: 'Monday',
          startTime: openings[0] ? openings[0].startTime : null,
          endTime: openings[0] ? openings[0].endTime : null,
        },
        {
          week: 'Tuesday',
          startTime: openings[1] ? openings[1].startTime : null,
          endTime: openings[1] ? openings[1].endTime : null,
        },
        {
          week: 'Wednesday',
          startTime: openings[2] ? openings[2].startTime : null,
          endTime: openings[2] ? openings[2].endTime : null,
        },
        {
          week: 'Thursday',
          startTime: openings[3] ? openings[3].startTime : null,
          endTime: openings[3] ? openings[3].endTime : null,
        },
        {
          week: 'Friday',
          startTime: openings[4] ? openings[4].startTime : null,
          endTime: openings[4] ? openings[4].endTime : null,
        },
        {
          week: 'Saturday',
          startTime: openings[5] ? openings[5].startTime : null,
          endTime: openings[5] ? openings[5].endTime : null,
        },
        {
          week: 'Sunday',
          startTime: openings[6] ? openings[6].startTime : null,
          endTime: openings[6] ? openings[6].endTime : null,
        },
      ],
      payments: [
        {
          name: payment,
        },
      ],
    };

    // console.log('body', body);

    // ApiClient.PATCH(API.editOutlet, body, false).then((result) => {
    //   if (result.id != null) {
    //     Alert.alert(
    //       'Congratulation!',
    //       'You Have Successfully Inserted',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => { },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
    // outletById(param);
    // myTextInput.current.clear();
  };

  const updateOutletDetails = async (param) => {
    setLoading(true);

    var merchantLogoPath = logo;
    if (logo && logoType && isLogoChanged) {
      // outletItemIdLocal = selectedProductEdit.uniqueId;

      merchantLogoPath = await uploadImageToFirebaseStorage(
        {
          uri: logo,
          type: logoType,
        },
        `/merchant/${merchantId}/logo${logoType}`,
      );
    }

    var outletCoverPath = cover;
    if (cover && coverType && isCoverChanged) {
      // outletItemIdLocal = selectedProductEdit.uniqueId;

      outletCoverPath = await uploadImageToFirebaseStorage(
        {
          uri: cover,
          type: coverType,
        },
        `/merchant/${merchantId}/outlet/${currOutletId}/cover${coverType}`,
      );
    }

    var body = {
      outletId: currOutletId,
      cover: outletCoverPath,

      merchantId,
      merchantLogo: merchantLogoPath,

      address: addressDisplay ? addressDisplay : address,
      addressDisplay,
      merchantName,

      lat: outletLat,
      lng: outletLng,

      phone,
      email,

      isPickupAccepted: status,
      isDeliveryAccepted: status1,
      isHalal: statusHalal,
      isQRPrintReceipt,
      isQRNotPrintLogo,

      skipUserInfo,

      outletOpeningDetails: currOutletOpening,
      outletOpeningOff: currOutletOpeningOff,
      outletBreakTime: currOutletBreakTime,

      pickupPaymentOptions,
      deliveryPaymentOptions,
      dineinPaymentOptions,
      dineinPaymentOptionsGeneric,
      docketPaymentOptions,
      reservationPaymentOptions,
      cashDrawerOpeningOptions,
      receiptPrintingOptions,
      userIdleSignOutOptions,

      askPaymentFirstOrderTypes,

      taxRate: !isNaN(sstRate) ? +(sstRate / 100).toFixed(2) : 0.06,
      taxNum: sstNumber ? sstNumber : '',
      scRate: !isNaN(scRate) ? +(scRate / 100).toFixed(2) : 0.05,
      taxActive: sstActive,
      scActive,
      scOrderTypes,
      taxOrderTypes,
      scOrderTypeDetails,
      scName,

      scRateOtherD: !isNaN(scRateOtherD) ? +(scRateOtherD / 100).toFixed(2) : 0.05,
      scActiveOtherD,
      scNameOtherD,

      kdPrintEventTypes,

      kdPrintVariation,

      kdFontSize,

      kdHeaderFontSize,

      isKdPrintSku,

      toPrintVariantAddonTitle,

      isKdPrintUserInfo,

      toggleOfflineMode,

      toggleOpenOrder,

      multiplePosTerminalMode,

      // toggleDisableAutoPrint: toggleDisableAutoPrint,

      printReceiptWhenPaidOnline,

      autoPrintPaySlip,

      shortenShiftReport,

      shortenReceipt,

      webOrderVariantLayout,

      webOrderUpsellingLayout,

      // webOrderListLayout: webOrderListLayout,

      reportDataSizeLimit,

      reportDisplayType,

      switchTableLayout,
      dTableLayout,

      forceCloseShiftBeforeSignOut,

      emailWarningStock,

      forceToEnterCardInfo,

      webOrderV2Layout,

      qrOperationTime,

      retailMode,

      fcsHeight,
      fcsWidth,
      fcsText,

      odActive,

      eiAskFields,

      // taxId: '',
      // status: '1',
      // isBusy: '1',
      // reservationStatus: true,
    };

    // console.log('body', body);

    APILocal.updateOutletDetails({ body }).then((result) => {
      ApiClient.POST(API.updateOutletDetails, body, false).then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Outlet details has been updated',
            [
              {
                text: 'OK',
                onPress: () => {
                  setLoading(false);
                },
              },
            ],
            { cancelable: false },
          );
        }
      });

      Alert.alert(
        'Success',
        'Outlet details has been updated, and will be synced to devices across the outlet.',
        [
          {
            text: 'OK',
            onPress: () => {
              setLoading(false);
            },
          },
        ],
        { cancelable: false },
      );
    });

    // Alert.alert(
    //   'Success',
    //   'Outlet details has been updated, and will be synced to devices across the outlet.',
    //   [
    //     {
    //       text: 'OK',
    //       onPress: () => {
    //         setLoading(false);
    //       },
    //     },
    //   ],
    //   { cancelable: false },
    // );
    // });
  };

  const resetCache = async () => {
    var dataFolder = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : (`${RNFS.ExternalStorageDirectoryPath}/Documents`)}/KooDooData`;

    let exists = await RNFS.exists(dataFolder);
    if (exists) {
      await RNFS.unlink(dataFolder);
    } else {
    }

    // Alert.alert('Info', 'Please close the app and open again for the changes to take effect.');

    // DevSettings.reload();

    console.log('done!');
  };

  const resetOutletData = async () => {
    setLoading(true);

    var body = {
      resetDataTypes: dropDownResetList,

      outletId: currOutletId,
      merchantId,
    };

    // console.log('body', body);

    ApiClient.POST(API.resetOutletData, body, false).then((result) => {
      if (result && result.status === 'success') {
        if (dropDownResetList.includes(RESET_DATA_TYPE.USER_ORDER)) {
          OutletStore.update(s => {
            s.allOutletsUserOrdersDoneCache = [];
            s.allOutletsUserOrdersCache = [];
          });
        }
        else if (dropDownResetList.includes(RESET_DATA_TYPE.CRM_USER)) {
          CommonStore.update(s => {
            s.selectedCustomerEdit = null;
          });
        }

        setLoading(false);

        Alert.alert(
          'Success',
          'Outlet data has been reset.',
          [
            {
              text: 'OK',
              onPress: () => {
                setLoading(false);
              },
            },
          ],
          { cancelable: false },
        );
      }
    });
  };

  const handleChoosePhotoCover = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 1280,
      maxHeight: 628, // 628
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        if (responseParsed.width > 1280 || responseParsed.height > 628) {
          Alert.alert('Cover\'s width and height must be same or less than 1280x628.');
          return;
        }

        // setState({ image: response.uri });
        setCover(responseParsed.uri);
        setCoverType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));

        setIsCoverChanged(true);
      }
    });
  };

  const handleChoosePhotoLogo = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 256,
      maxHeight: 256,
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        if (responseParsed.width > 256 || responseParsed.height > 256) {
          Alert.alert('Logo\'s width and height must be same or less than 256x256.');
          return;
        }

        setLogo(responseParsed.uri);
        setLogoType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));

        setIsLogoChanged(true);
      }
    });
  };

  const handleChoosePhoto1 = () => {
    const options = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };
    launchImageLibrary(options, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        // setState({ cover: response.uri });
        setIsCoverChanged(true);

        setCover(responseParsed.uri);
        setCoverType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));
      }
    });
  };

  const closingShift = () => {
    var body = {
      outletId: User.getOutletId(),
      amount: closingAmount,
    };
    // ApiClient.POST(API.closeShift, body, false).then((result) => {
    //   if (result) {
    //     Alert.alert(
    //       'Successfully',
    //       'Close Success',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => {
    //             _logout();
    //             setState({
    //               showModal3: false,
    //               show: false,
    //               showModal5: false,
    //             });
    //           },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const getCurrentDate = () => {
    var date = new Date().getDate();
    var month = new Date().getMonth() + 1;
    var year = new Date().getFullYear();
    var hours = new Date().getHours();
    var min = new Date().getMinutes();
    var sec = new Date().getSeconds();
    var day = new Date().getDay();

    return (
      // hours + '9.00AM' + min + '' + day +'Thursday'  + month + 'September' + date + '20'+ year + '2020'
      ('9.00AM, ' + 'Thursday ' + 'September ' + '20 ' + '2020')
    );
  };

  const _logout = async () => {
    await AsyncStorage.clear();
    User.setlogin(false);
    User.getRefreshMainScreen();
  };

  const addSection = () => {
    setState({ showNote: true });
  };

  // default = () => {
  //   if (status == 0) {
  //     return false;
  //   } else if (status == 1) {
  //     return true;
  //   }
  // }

  // default1 = () => {
  //   if (status1 == 0) {
  //     return false;
  //   } else if (status1 == 1) {
  //     return true;
  //   }
  // }

  // return = () => {
  //   if (status == false) {
  //     return 0;
  //   } else if (status == true) {
  //     return 1;
  //   }
  // }
  // return1 = () => {
  //   if (status == false) {
  //     return 0;
  //   } else if (status == true) {
  //     return 1;
  //   }
  // }

  // update = () => {
  //   if (status == false) {
  //     return 0;
  //   } else if (status == true) {
  //     return 1;
  //   }
  // }

  const bindPrinter = async () => {
    if (printerIP.length > 0) {
      try {
        await AsyncStorage.setItem('printerIP', printerIP);

        const result = await connectToPrinter();

        if (result) {
          Alert.alert('Success', 'IP has been binded');
        } else {
          Alert.alert('Error', 'Unable to bind the IP');
        }
      } catch (ex) {
        // console.log(ex);
      }
    } else {
      Alert.alert('Error', 'Invalid IP address');
    }
  };

  const unbindPrinter = async () => {
    try {
      await AsyncStorage.removeItem('printerIP');

      Alert.alert('Succes', 'IP has been unbinded');
    } catch (ex) {
      // console.log(ex);

      Alert.alert('Error', 'Failed to unbind the IP');
    }
  };

  const testPrinter = async () => {
    // console.log('test printer');

    await connectToPrinter();

    var testItems = [
      {
        name: 'Coconut Coffee',
        remarks: 'LESS SWEET',
        price: 8,
        discount: 0,
        quantity: 1,
        subtotal: 8,
        tax: 0,
      },
      {
        name: 'Yogurt Coffee',
        remarks: 'LESS ICE',
        price: 8,
        discount: 0,
        quantity: 1,
        subtotal: 8,
        tax: 0,
      },
    ];

    try {
      var result = `${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.UNDERLINE_OFF}${ESCPOS_CMD.BOLD_OFF}`;
      result += `${ESCPOS_CMD.CENTER}${currOutlet.name}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${currOutlet.address}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${currOutlet.phone}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${merchantName}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}SST ID No. 0012612771${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}ORDER #38${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[...Array(48)]
        .map((i) => '-')
        .join('')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}Mark${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}0127148876${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Receipt Date'.padEnd(
        12,
        ' ',
      )} : ${moment().format('ddd, MMM D, YYYY')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Receipt #'.padEnd(12, ' ')} : ${'38'}${ESCPOS_CMD.NEWLINE
        }`;
      result += `${ESCPOS_CMD.LEFT}${'Cashier'.padEnd(
        12,
        ' ',
      )} : ${'Sophie Kim'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'ITEM'.padEnd(20, ' ')}${'PRICE'.padEnd(
        8,
        ' ',
      )}${'DISC'.padEnd(8, ' ')}${'QTY'.padEnd(4, ' ')}${'SUB'.padStart(
        8,
        ' ',
      )}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${[...Array(48)]
        .map((i) => '-')
        .join('')}${ESCPOS_CMD.NEWLINE}`;

      for (var i = 0; i < testItems.length; i++) {
        const testItem = testItems[i];

        result += `${ESCPOS_CMD.LEFT}${testItem.name
          .slice(0, 20)
          .padEnd(20, ' ')}${(
            `RM${(testItem.price || 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padEnd(8, ' ')}${(
            `RM${testItem.discount.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padEnd(8, ' ')}${testItem.quantity.toString().padEnd(4, ' ')}${(
            `RM${testItem.subtotal.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padStart(8, ' ')}${ESCPOS_CMD.NEWLINE}`;

        if (testItem.remarks) {
          result += `${ESCPOS_CMD.LEFT}${testItem.remarks
            .slice(0, 20)
            .padEnd(20, ' ')}${''.padEnd(8, ' ')}${''.padEnd(
              8,
              ' ',
            )}${''.padEnd(4, ' ')}${''.padEnd(8, ' ')}${ESCPOS_CMD.NEWLINE}`;
        }

        if (i !== testItems.length - 1) {
          result += `${ESCPOS_CMD.NEWLINE}`;
        }
      }

      result += `${ESCPOS_CMD.CENTER}${[...Array(48)]
        .map((i) => '-')
        .join('')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Subtotal'.padEnd(24, ' ')}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.subtotal, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}`;
      result += `${ESCPOS_CMD.LEFT}${'Discount'.padEnd(24, ' ')}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.discount, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}`;
      result += `${ESCPOS_CMD.LEFT}${'Tax (6%)'.padEnd(24, ' ')}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.tax, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}${'Total'.padEnd(
        12,
        ' ',
      )}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.subtotal, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(12, ' ')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[...Array(48)]
        .map((i) => '-')
        .join('')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Received'.padEnd(24, ' ')}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.subtotal, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Balance'.padEnd(24, ' ')}${(
        `RM${(0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

      result += `${ESCPOS_CMD.LEFT}${'Notes:'}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'1.'}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'2.'}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'3.'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE
        }${ESCPOS_CMD.NEWLINE}`;

      result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_2H
        }${'Thank you for your order'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.BARCODE_HEIGHT}${ESCPOS_CMD.BARCODE_WIDTH}${ESCPOS_CMD.BARCODE_FONT_A}${ESCPOS_CMD.BARCODE_TXT_OFF}${ESCPOS_CMD.BARCODE_EAN13}978020137962${ESCPOS_CMD.NEWLINE}`;
      // result += `${ESCPOS_CMD.CENTER}${printBarcode({
      //   data: 'TEST12345',
      //   type: 'ean',
      // }, 'cp936')}${ESCPOS_CMD.NEWLINE}`;

      NetPrinter.printText(result);
    } catch (ex) {
      // console.log(ex);
    }
  };

  const logOutButton = async () => {
    return Alert.alert(`Logout`, `Are you sure you want to logout?`, [
      {
        text: 'YES',
        // onPress={async () => {
        //     await AsyncStorage.clear();
        //     User.setlogin(false);
        //     User.getRefreshMainScreen();
        // }}

        onPress: async () => {
          // to support offline-mode
          // AsyncStorage.clear();

          UserStore.update((s) => {
            s.avatar = '';
            s.dob = null;
            s.email = '';
            s.gender = '';
            s.name = '';
            s.number = '';
            s.outletId = '';
            s.race = '';
            s.state = '';
            s.uniqueName = '';
            s.updatedAt = null;
            s.merchantId = '';
            s.role = '';
            s.refreshToken = '';
            s.firebaseUid = '';
            s.privileges = [];
            s.screensToBlock = [];
          });

          MerchantStore.update((s) => {
            s.allOutlets = [];
            s.allOutletsDict = {};
            s.currOutletId = '',
              s.currOutlet = {
                uniqueId: '',
                privileges: [],
              };
          });

          const merchantId = await AsyncStorage.getItem('merchantId');
          // const currOutletId = await AsyncStorage.getItem('currOutletId');

          // clock out employee
          // const bodyClockOut = {
          //   employeeId: firebaseUid,
          //   logoutTime: Date.now(),

          //   merchantId,
          //   outletId: currOutletId,
          // };

          if (role === ROLE_TYPE.ADMIN || role === ROLE_TYPE.STORE_MANAGER) {
            // 2022-06-16 - No need first
            // ApiClient.POST(API.updateUserClockInOut, bodyClockOut).then(
            //   (result) => {
            //     console.log('updateUserClockOut', result);
            //   },
            // );
          }
          else {
            // if waiter, clock out manually outside
          }

          const tokenFcm = await AsyncStorage.getItem('tokenFcm');

          AsyncStorage.multiRemove([
            'accessToken',
            'userData',
            'refreshToken',

            'merchantLogoUrl',

            // 'lastActivity',

            'email',
            'password',

            'printerList',

            'supportCodeData',

            // 'isPrintingKDAndOS',
          ]);

          global.signInAlready = false;

          const body = {
            role,
            merchantId,
            outletId: currOutlet.uniqueId,
            tokenFcm,
          };

          // Token.clear();
          // User.setlogin(false);
          // User.getRefreshMainScreen();

          // logToFile('App.js | logOutButton');

          try {
            ApiClient.POST(API.logoutUser, body).then((result) => {
              User.setlogin(false);
              User.setMerchantId(null);
              User.setUserData(null);
              User.setUserId(null);
              User.setRefreshToken(null);
              User.setOutletId(null);
              User.getRefreshMainScreen();

              global.privileges = [];
              global.privileges_state = [];

              global.isOnLoginPage = true;
              global.isOnPinLoginPage = false;

              if (global.simulateTabletMode) {
                performResize(
                  {
                    windowPhysicalPixels: {
                      height: 2160,
                      width: 1620,
                      scale: 2,
                    },
                  },
                  'iPad 9th Generation',
                  false,
                  false,
                  true,
                  global.windowWidthOriginal,
                  global.windowHeightOriginal,
                  global.fontScaleOriginal,
                );
              }

              //////////////////////////////////

              // 2023-01-29 - clear offline data after logout

              try {
                global.watermelonDBDatabase
                  .write(async () => {
                    await global.watermelonDBDatabase.unsafeResetDatabase();
                  });
              }
              catch (ex) {
                console.error(ex);
              }

              global.funcSwitchShowApp(false);

              //////////////////////////////////

              // CommonStore.replace(initialCommonStore);
              // MerchantStore.replace(initialMerchantStore);
              // OutletStore.replace(initialOutletStore);
              // NotificationStore.replace(initialNotificationStore);
              // UserStore.replace(initialUserStore);
              // PageStore.replace(initialPageStore);
            });
          }
          catch (ex) {
            console.error(ex);
          }

          // if (clearDashboardDataFunc) {
          //   clearDashboardDataFunc();
          // }
        },
      },

      {
        text: 'NO',
        onPress: () => { },
      },
    ]);
  };

  // function end

  const week = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  var resetDataTypeDropdownList = [
    // {
    //   label: 'Order',
    //   value: RESET_DATA_TYPE.USER_ORDER,
    // },
    {
      label: 'Customer',
      value: RESET_DATA_TYPE.CRM_USER,
    },
  ];

  if (supportCodeData) {
    resetDataTypeDropdownList.push({
      label: 'Order',
      value: RESET_DATA_TYPE.USER_ORDER,
    });
  }

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={10}
            expandSettings
          />
        </View> */}

        <ScrollView
          showsVerticalScrollIndicator={false}
          // scrollEnabled={switchMerchant}
          style={{}}
          contentContainerStyle={{
            flex: 1,
            paddingBottom: switchMerchant
              ? windowHeight * 0.001
              : windowHeight * 0.03,
            backgroundColor: Colors.highlightColor,
          }}>
          <ScrollView horizontal>
            <View style={[
              styles.content,
              {
                width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
              }
            ]}>
              {/* <View style={{flexDirection: 'row', marginBottom: 10}}>
            <View style={{flexDirection: 'row', flex: 1}}></View>
            <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput}
              placeholder="search"
              onChangeText={(text) => {
                setState({search: text.trim()});
              }}
              value={email}
            />
          </View> */}
              {/* <View
            style={{
              flexDirection: 'row',
              backgroundColor: Colors.highlightColor,
              padding: 12,
            }}>
            <TouchableOpacity
              onPress={() => {
                setState({
                  merchantDisplay: false,
                  shift: false,
                  tax: false,
                  sample: true,
                  redemption: false,
                  order: false,
                });
              }}>
              <Text>General</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  shift: true,
                  merchantDisplay: false,
                  tax: false,
                  sample: false,
                  redemption: false,
                  order: false,
                });
                getCurrentShift(User.getOutletId());
              }}>
              <Text style={{ marginLeft: 30 }}>Shift</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  tax: true,
                  merchantDisplay: false,
                  shift: false,
                  sample: false,
                  redemption: false,
                  order: false,
                });
                getTax(User.getOutletId())
              }}>
              <Text style={{ marginLeft: 30 }}>Tax</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  sample: true,
                  tax: false,
                  merchantDisplay: false,
                  shift: false,
                  redemption: false,
                  order: false,
                });
                setState({
                  showNote: false,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Sample Receipt</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  merchantDisplay: false,
                  shift: false,
                  tax: false,
                  sample: false,
                  redemption: false,
                  order: true,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Order</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  merchantDisplay: false,
                  shift: false,
                  tax: false,
                  sample: false,
                  redemption: true,
                  order: false,
                  redemptionList: true,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Redemption</Text>
            </TouchableOpacity>
          </View> */}

              {merchantDisplay ? (
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    // height: windowHeight - 120,
                    width: windowWidth * 0.87,
                    alignSelf: 'center',
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    // elevation: 1,
                    elevation: 3,

                    borderRadius: 5,
                    // borderRadius: 8,
                  }}>
                  <KeyboardAwareScrollView
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                    contentContainerStyle={
                      {
                        //top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                      }
                    }>
                    <View
                      style={{
                        flexDirection: 'row',
                        // zIndex: 1,
                        padding: Platform.OS == 'ios' ? 10 : 30,
                      }}>
                      <View style={{ flex: 1.1, paddingLeft: 15 }}>
                        <Text
                          style={{
                            marginRight: 50,
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 20 : 25,
                          }}>
                          Merchant Logo
                        </Text>
                        <View
                          style={{
                            flexDirection: 'row',
                            flex: 1,
                            alignItems: 'flex-start',

                            // marginLeft: -windowWidth * 0.01,
                          }}>
                          {/* left */}
                          <TouchableOpacity
                            style={{
                              alignItems: 'center',
                              marginTop: 20,
                              marginRight: 20,
                              // flex: 1,
                              zIndex: 1000,
                            }}
                            onPress={handleChoosePhotoLogo}>
                            {merchantLogo ? (
                              <AsyncImage
                                source={{ uri: logo }}
                                item={{
                                  updatedAt: merchantLastUpdated,
                                }}
                                style={{
                                  width: 120,
                                  height: 120,
                                  backgroundColor: Colors.secondaryColor,
                                  borderRadius: 10,
                                }}
                                hideLoading
                              />) : (
                              <View
                                style={{
                                  width: 120,
                                  height: 120,
                                  backgroundColor: Colors.secondaryColor,
                                  borderRadius: 10,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}>
                                <FontAwesome5
                                  name="image"
                                  size={50}
                                  color={Colors.blackColor} />
                              </View>)
                            }
                            <View style={{ alignItems: 'center' }}>
                              {/* <TouchableOpacity
                          style={
                            {
                              // height: 30,
                              // width: 50
                            }
                          }
                          onPress={() => {
                            handleChoosePhotoLogo();
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 15,
                              color: Colors.primaryColor,
                              marginTop: 5,
                            }}>
                            Change
                          </Text>
                        </TouchableOpacity> */}
                            </View>
                            <TouchableOpacity
                              style={{
                                position: 'absolute',
                                bottom: 5,
                                right: -30,
                                // backgroundColor: 'red',
                              }} onPress={handleChoosePhotoLogo}>
                              <FontAwesome5
                                name="edit"
                                size={switchMerchant ? 12 : 23}
                                color={Colors.primaryColor}
                              />
                            </TouchableOpacity>
                          </TouchableOpacity>
                          <View
                            style={{
                              flex: 3,
                              marginTop: 20,
                              //marginLeft: '-2%',
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                marginBottom: 10,
                                //justifyContent: 'space-between',
                              }}>
                              <View
                                style={{
                                  justifyContent: 'center',
                                  flex: 1,
                                  paddingLeft: 10,
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  Merchant Name:
                                </Text>
                              </View>

                              <View
                                style={{
                                  //width: 210,
                                  height: 40,
                                  justifyContent: 'center',
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 8,
                                  flex: 1.5,
                                  marginRight: 20,
                                }}>
                                {/* <Text style={{ fontFamily: 'NunitoSans-SemiBold', color: Colors.descriptionColor, paddingHorizontal: 10 }}>{merchantName}</Text> */}
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    // color: Colors.descriptionColor,
                                    color: Colors.blackColor,
                                    paddingHorizontal: 10,
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholder={
                                    merchantName == null
                                      ? ' Not Found '
                                      : merchantName
                                  }
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(merchantName)
                                  //   setMerchantName('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (merchantName == '') {
                                  //     setMerchantName(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    if (text.charAt(0) === ' ') {
                                      text = text.trimStart(); // remove leading space characters
                                    }
                                    // setState({ merchantName: text });
                                    setMerchantName(text);
                                  }}
                                  value={merchantName}
                                  ref={myTextInput}
                                  spellCheck={false}
                                />
                              </View>
                            </View>

                            {/* <View style={{
                              flexDirection: 'row',
                              marginBottom: 10,

                              paddingLeft: 10,
                            }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <TouchableOpacity
                                  style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: '#0F1A3C',
                                    borderRadius: 5,
                                    width: 180,
                                    paddingHorizontal: 10,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -4,
                                  }}
                                  disabled={loading}
                                  onPress={() => {
                                    Alert.alert(
                                      'Info',
                                      'Are your sure want to proceed to reset the cache? (Note: Will auto-restart the app.)',
                                      [
                                        {
                                          text: 'OK',
                                          onPress: () => {
                                            resetCache();
                                          },
                                        },
                                        {
                                          text: 'Cancel',
                                          onPress: () => {
                                          },
                                        },
                                      ],
                                      { cancelable: false },
                                    );
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      //marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    {'RESET CACHE'}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </View> */}
                            {/* <View
                        style={{
                          flexDirection: 'row',
                          //justifyContent: 'space-between',
                        
                        }}>
                        <View style={{ justifyContent: 'center', flex: 1, paddingLeft: 10 }}>
                          <Text style={styles.textSize}>Choose Outlet:</Text>
                        </View>
                        <View style={{ flex: 1.6 }}>
                          <View
                            style={{
                              //width: 210,
                              height: 50,
                              justifyContent: 'center',
                              marginRight: 80
                              //marginLeft: 10,
                            }}>
                            <DropDownPicker
                              // items={outletInfo.map((item) => ({
                              //   label: item.name, //<= after hayden change you need to change it to item.name
                              //   value: item.id,
                              // }))}
                              items={targetOutletDropdownList}
                              // defaultValue={outletId}
                              containerStyle={{ height: 40, width: 220 }}
                              placeholder="Choose outlet"
                              placeholderStyle={{
                                color: Colors.descriptionColor,
                                fontSize: 16,
                                fontFamily: 'NunitoSans-Regular',
                              }}
                              style={{ backgroundColor: '#fafafa' }}
                              labelStyle={{
                                fontSize: 16,
                                fontFamily: 'NunitoSans-Regular',
                              }}
                              itemStyle={{
                                justifyContent: 'flex-start',
                                fontSize: 16,
                                fontFamily: 'NunitoSans-Regular',
                              }}
                              dropDownStyle={{ backgroundColor: '#fafafa' }}
                              onChangeItem={(item) => {
                                // setState({ outletId: item.value });

                                // setOutletId(item.value);
                                // outletById(item.value)

                                MerchantStore.update((s) => {
                                  s.currOutletId = item.value;
                                });
                              }}
                              // defaultValue={currOutletId}
                              defaultValue={selectedTargetOutletId}
                              multiple={false}
                            // onClose={() => {
                            //   outletById(outletId);
                            // }} //you didn't pass in outletid in your function parameter
                            />
                          </View>
                        </View>
                      </View> */}
                          </View>
                        </View>

                        <View
                          style={
                            (styles.merchantDisplayView,
                            {
                              // backgroundColor: 'red'
                              marginTop: 25,
                              marginBottom: 15,
                            })
                          }>
                          <View
                            style={{
                              justifyContent: 'center',
                              alignItems: 'flex-start',
                            }}>
                            <Text
                              style={{
                                marginRight: 50,
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 20 : 25,
                              }}>
                              Merchant Display
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                justifyContent: 'flex-start',
                                width: '80%',
                              }}>
                              <View>
                                <Text
                                  style={{
                                    color: Colors.fieldtTxtColor,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 16,
                                  }}>
                                  Customer will see this
                                </Text>
                              </View>
                              <View style={{ marginLeft: 10 }}>
                                {/* <TouchableOpacity
                            onPress={() => {
                              handleChoosePhoto1();
                            }}>
                            <Text
                              style={{
                                fontSize: 15,
                                color: Colors.primaryColor,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Change
                            </Text>
                          </TouchableOpacity> */}
                              </View>
                            </View>
                          </View>
                        </View>

                        <TouchableOpacity
                          style={
                            (styles.merchantDisplayView,
                            {
                              // backgroundColor: 'red',
                              marginBottom: 30,
                            })
                          }
                          onPress={handleChoosePhotoCover}>
                          {currOutlet.cover ? (
                            <AsyncImage
                              source={{ uri: cover }}
                              item={currOutlet}
                              style={{
                                resizeMode: 'contain',
                                width: '90%',
                                height: 120,
                                backgroundColor: Colors.secondaryColor,
                                borderRadius: 10,
                              }}
                              hideLoading
                            />) : (
                            <View
                              style={{
                                width: '90%',
                                height: 120,
                                backgroundColor: Colors.secondaryColor,
                                borderRadius: 10,
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}>
                              <FontAwesome5
                                name="image"
                                size={50}
                                color={Colors.blackColor} />
                            </View>)}

                          <TouchableOpacity
                            style={{
                              position: 'absolute',
                              bottom: 5,
                              right: 0,
                            }} onPress={handleChoosePhotoCover}>
                            <FontAwesome5
                              name="edit"
                              size={switchMerchant ? 12 : 23}
                              color={Colors.primaryColor}
                            />
                          </TouchableOpacity>
                        </TouchableOpacity>

                        {/* merchantname */}
                        <View style={{}}>
                          <View style={{ width: '75%' }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'flex-start',
                              }}>
                              <View
                                style={{
                                  width: '25%',
                                  justifyContent: 'center',
                                  marginTop: 5,
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {`Address\n(GPS)`}
                                </Text>
                              </View>

                              <View style={{ width: '90%' }}>
                                <GooglePlacesAutocomplete
                                  // placeholder="📍  Outlet address"
                                  placeholder={address}
                                  minLength={2} // minimum length of text to search
                                  autoFocus={false}
                                  textInputProps={{ placeholderTextColor: 'black' }}
                                  returnKeyType={'search'} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
                                  listViewDisplayed="false" // true/false/undefined
                                  fetchDetails
                                  renderDescription={(row) => renderSearch(row)} // custom description render
                                  onPress={(data, details = null) => {
                                    // 'details' is provided when fetchDetails = true
                                    // console.log('data', data);
                                    // console.log('details', details);

                                    setAddress(details.formatted_address);
                                    setOutletLat(details.geometry.location.lat);
                                    setOutletLng(details.geometry.location.lng);

                                    // // console.log("data.description", data.description);
                                    // // console.log("data.structured_formatting.main_text", data.structured_formatting.main_text);
                                    // props.navigation.navigate('AddAddress', { test: 3, address: data.description, name: data.structured_formatting.main_text, location: details.geometry.location })
                                  }}
                                  getDefaultValue={() => address}
                                  query={{
                                    // available options: https://developers.google.com/places/web-service/autocomplete
                                    //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
                                    // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
                                    key: 'AIzaSyAcXc3dCqZwxETpxBaw-4lTBxS482FvhwU',
                                    language: 'en', // language of the results
                                    types: 'address', // default: 'geocode'
                                    components: 'country:my',
                                  }}
                                  styles={{
                                    textInputContainer: {
                                      width: '100%',
                                      borderWidth: 1,
                                      borderRightWidth: 2,
                                      borderRadius: 5,
                                      borderColor: '#E5E5E5',
                                    },
                                    textInput: {
                                      backgroundColor: Colors.fieldtBgColor,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                      height: 80,
                                      marginBottom: 0,
                                      color: 'black',
                                      //backgroundColor: 'red',
                                      textAlignVertical: 'top',
                                    },
                                    description: {
                                      // fontWeight: 'bold',
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: switchMerchant ? 10 : 14,
                                    },
                                    predefinedPlacesDescription: {
                                      color: '#1faadb',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    },
                                    listView: {
                                      //backgroundColor: 'red',
                                      //marginLeft: 85,
                                      //marginTop: 40,
                                      width: '100%',
                                      //alignSelf: 'center',
                                      borderColor: '#E5E5E5',
                                      borderWidth: 1,
                                      borderRadius: 5,
                                      zIndex: 10,
                                      // height: 800,
                                      //position: 'absolute',
                                    },
                                    container: {
                                      //backgroundColor: 'red',
                                    },
                                    zIndex: 10,
                                  }}
                                  enablePoweredByContainer={false}
                                  //currentLocation={false}
                                  currentLocationLabel="Current location"
                                  nearbyPlacesAPI="GooglePlacesSearch"
                                  GoogleReverseGeocodingQuery={{}}
                                  GooglePlacesSearchQuery={{
                                    rankby: 'distance',
                                  }}
                                  filterReverseGeocodingByTypes={[
                                    'locality',
                                    'administrative_area_level_3',
                                  ]}
                                  debounce={200}
                                  keepResultsAfterBlur
                                />
                              </View>
                            </View>

                            {/* address to show */}
                            <View
                              style={{ flexDirection: 'row', marginVertical: 15 }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {`Address\n(Display)`}
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  textAlignVertical={'top'}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  style={{
                                    padding: 10,
                                    height: 80,
                                    width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={
                                    outlet == null
                                      ? 'Fill in the ddress'
                                      : outlet.addressDisplay
                                  }
                                  onChangeText={(text) => {
                                    setAddressDisplay(text);
                                  }}
                                  value={addressDisplay}
                                />
                              </View>
                            </View>

                            <View
                              style={{ flexDirection: 'row', marginVertical: 15 }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  Contact
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  style={{
                                    padding: 10,
                                    height: 40,
                                    width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  keyboardType={'decimal-pad'}
                                  placeholder={
                                    outlet == null
                                      ? ' Fill in contact '
                                      : outlet.phone
                                  }
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(phone)
                                  //   setPhone('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (phone == '' || phone == '6') {
                                  //     setPhone(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    if (text.length === 0) {
                                      return; // Do nothing if the user deletes the entire input
                                    }
                                    if (text.charAt(0) !== "6") {
                                      text = `6${text}`; // Add a "6" at the beginning of the input
                                    }
                                    // setState({ phone: text });
                                    setPhone(text);
                                  }}
                                  value={phone}
                                  ref={myTextInput}
                                />
                              </View>
                            </View>

                            <View
                              style={{ flexDirection: 'row', marginVertical: 15 }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  Email
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={{
                                    padding: 10,
                                    height: 40,
                                    width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholder={
                                    outlet == null
                                      ? ' Fill in email '
                                      : outlet.email
                                  }
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(email)
                                  //   setEmail('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (email == '') {
                                  //     setEmail(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    // setState({ phone: text });
                                    setEmail(text);
                                  }}
                                  value={email}
                                  ref={myTextInput}
                                />
                              </View>
                            </View>

                            <View
                              style={{ flexDirection: 'row', marginVertical: 15 }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {`Email\n(W. Stock)`}
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={{
                                    padding: 10,
                                    height: 40,
                                    width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholder={
                                    outlet == null
                                      ? ' Fill in email '
                                      : outlet.email
                                  }
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(emailWarningStock)
                                  //   setEmailWarningStock('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (emailWarningStock == '') {
                                  //     setEmailWarningStock(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    // setState({ phone: text });
                                    setEmailWarningStock(text);
                                  }}
                                  value={emailWarningStock}
                                  ref={myTextInput}
                                />
                              </View>
                            </View>

                            {/* <View
                            style={{ flexDirection: 'row', marginVertical: 15 }}>
                            <View
                              style={{ width: '25%', justifyContent: 'center' }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Website
                              </Text>
                            </View>
                            <View style={{ width: '61%', flexDirection: 'row' }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholderTextColor={Platform.select({
                                  ios: '#000',
                                })}
                                placeholderTextColor={'#000'}
                                style={[
                                  styles.textInput,
                                  {
                                    padding: 10,
                                    height: 40,
                                    width: 80,
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}
                                placeholder={
                                  // outlet == null
                                  //   ? ' Fill in contact '
                                  //   : outlet.phone
                                  'subdomain'
                                }
                                // onChangeText={(text) => {
                                //   setPhone(text);
                                // }}
                                // value={phone}
                                ref={myTextInput}
                              />
                            </View>
                            <View
                              style={{
                                justifyContent: 'center',
                                marginLeft:
                                  windowWidth * 0.005,
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                .mykoodoo.com
                              </Text>
                            </View>
                          </View> */}

                            <View
                              style={{
                                flexDirection: 'row',
                                marginVertical: 15,
                                position: 'relative',
                                bottom: 7,
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {'SST\nRate (%)'}
                                </Text>
                              </View>
                              <View style={{ width: '35%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={'6'}
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(sstRate)
                                  //   setSstRate('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (sstRate == '') {
                                  //     setSstRate(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setSstRate(text);
                                  }}
                                  value={sstRate}
                                  ref={myTextInput}
                                />
                              </View>

                              <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',
                              }}>
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 20,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                    // bottom: -2,
                                  }}
                                  value={sstActive}
                                  onSyncPress={(statusTemp) =>
                                    // setState({ status: status })
                                    setSstActive(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>

                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.2 : windowWidth * 0.2,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: '2%',
                                  // borderWidth:1
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Dine In',
                                      value: ORDER_TYPE.DINEIN,
                                    },
                                    {
                                      label: 'Takeaway',
                                      value: ORDER_TYPE.PICKUP,
                                    },
                                    {
                                      label: 'Delivery',
                                      value: ORDER_TYPE.DELIVERY,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: switchMerchant ? 35 : 40, width: '90%' }}
                                  placeholder="Order Type"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(items) => {
                                    setTaxOrderTypes(items);
                                  }}
                                  defaultValue={taxOrderTypes}
                                  multiple
                                  multipleText='%d type(s) selected'
                                />
                              </View>

                            </View>

                            <View
                              style={{
                                flexDirection: 'row',
                                marginVertical: 15,
                                position: 'relative',
                                bottom: 7,
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {'SST\nNumber'}
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={'J11-8018-2000001'}
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(sstNumber)
                                  //   setSstNumber('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (sstNumber == '') {
                                  //     setSstNumber(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setSstNumber(text);
                                  }}
                                  value={sstNumber}
                                  ref={myTextInput}
                                />
                              </View>

                              <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',

                                opacity: 0,
                              }}>
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 20,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                    // bottom: -2,
                                  }}
                                  value={sstActive}
                                  onSyncPress={(statusTemp) =>
                                    // setState({ status: status })
                                    setSstActive(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>
                            </View>

                            {/* ///////////////////////////////////////////////// */}

                            {/* service charge for dinein/takeaway/delivery */}

                            <View
                              style={{
                                flexDirection: 'row',
                                marginVertical: 15,
                                position: 'relative',
                                bottom: 7,

                                alignItems: 'center',
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {'Service\nCharge (%)'}
                                </Text>
                              </View>

                              <View style={{ width: '35%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={' 5 '}
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(scRate)
                                  //   setScRate('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (scRate == '') {
                                  //     setScRate(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setScRate(text);
                                  }}
                                  value={scRate}
                                  ref={myTextInput}
                                />
                              </View>

                              <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',
                              }}>
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 20,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                    // bottom: -2,
                                  }}
                                  value={scActive}
                                  onSyncPress={(statusTemp) =>
                                    // setState({ status: status })
                                    setScActive(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>

                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.2 : windowWidth * 0.2,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: '2%',
                                  // borderWidth:1
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Dine In',
                                      value: ORDER_TYPE.DINEIN,
                                    },
                                    {
                                      label: 'Takeaway',
                                      value: ORDER_TYPE.PICKUP,
                                    },
                                    {
                                      label: 'Delivery',
                                      value: ORDER_TYPE.DELIVERY,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: switchMerchant ? 35 : 40, width: '90%' }}
                                  placeholder="Order Type"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(items) => {
                                    setScOrderTypes(items);
                                  }}
                                  defaultValue={scOrderTypes}
                                  multiple
                                  multipleText='%d type(s) selected'
                                />
                              </View>
                            </View>

                            {/* 2nd row for service charge */}

                            <View
                              style={{
                                flexDirection: 'row',
                                // marginVertical: 15,
                                marginTop: 0,
                                marginBottom: 10,
                                position: 'relative',
                                bottom: 7,
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {''}
                                </Text>
                              </View>

                              <View style={{ width: '35%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={'Name to shown'}
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(scName)
                                  //   setScName('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (scName == '') {
                                  //     setScName(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setScName(text);
                                  }}
                                  value={scName}
                                  ref={myTextInput}
                                />
                              </View>

                              {/* <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',
                              }}>
                              </View> */}

                              {/* 2023-02-10 - Hide medium type selection first */}
                              {/* <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.2 : windowWidth * 0.2,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: '2%',
                                  // borderWidth:1
                                }}>
                                <DropDownPicker
                                  items={ORDER_TYPE_DETAILS_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: switchMerchant ? 35 : 40, width: '90%' }}
                                  placeholder="Order Type"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(items) => {
                                    setScOrderTypeDetails(items);
                                  }}
                                  defaultValue={scOrderTypeDetails}
                                  multiple={true}
                                  multipleText='%d medium type(s) selected'
                                />
                              </View> */}
                            </View>

                            {/* ///////////////////////////////////////////////// */}

                            {/* service charge for open orders */}

                            <View
                              style={{
                                flexDirection: 'row',
                                marginVertical: 15,
                                position: 'relative',
                                bottom: 7,

                                alignItems: 'center',
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {'Service\nCharge (%)\n(Other D.)'}
                                </Text>
                              </View>

                              <View style={{ width: '35%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={' 5 '}
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(scRateOtherD)
                                  //   setScRateOtherD('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (scRateOtherD == '') {
                                  //     setScRateOtherD(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setScRateOtherD(text);
                                  }}
                                  value={scRateOtherD}
                                  ref={myTextInput}
                                />
                              </View>

                              <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',
                              }}>
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 20,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                    // bottom: -2,
                                  }}
                                  value={scActiveOtherD}
                                  onSyncPress={(statusTemp) =>
                                    // setState({ status: status })
                                    setScActiveOtherD(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>

                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.2 : windowWidth * 0.2,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: '2%',
                                  // borderWidth:1
                                }}>
                                {/* <DropDownPicker
                                  items={[
                                    {
                                      label: 'Dine In',
                                      value: ORDER_TYPE.DINEIN,
                                    },
                                    {
                                      label: 'Takeaway',
                                      value: ORDER_TYPE.PICKUP,
                                    },
                                    {
                                      label: 'Delivery',
                                      value: ORDER_TYPE.DELIVERY,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: switchMerchant ? 35 : 40, width: '90%' }}
                                  placeholder="Order Type"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(items) => {
                                    setScOrderTypes(items);
                                  }}
                                  defaultValue={scOrderTypes}
                                  multiple
                                  multipleText='%d type(s) selected'
                                /> */}
                              </View>
                            </View>

                            {/* 2nd row for service charge */}

                            <View
                              style={{
                                flexDirection: 'row',
                                // marginVertical: 15,
                                marginTop: 0,
                                marginBottom: 10,
                                position: 'relative',
                                bottom: 7,
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {''}
                                </Text>
                              </View>

                              <View style={{ width: '35%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={'Name to shown'}
                                  //iOS
                                  // clearTextOnFocus
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(scNameOtherD)
                                  //   setScNameOtherD('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (scNameOtherD == '') {
                                  //     setScNameOtherD(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setScNameOtherD(text);
                                  }}
                                  value={scNameOtherD}
                                  ref={myTextInput}
                                />
                              </View>
                            </View>

                            {/* ///////////////////////////////////////////////// */}
                          </View>

                          {/* <View style={styles.viewContainer}>
                      <View style={{ flex: 1 }}>
                        <Text style={styles.textSize}>Payment Method:</Text>
                      </View>
                      <View style={{}}>
                        <DropDownPicker
                          items={[
                            {
                              label: 'Cash',
                              value: 'Cash',
                            },
                            {
                              label: 'Credit Card',
                              value: 'CreditCard',
                            },
                          ]}
                          containerStyle={{ height: 40 }}
                          placeholder={outlet == null ? ' Select ' : outlet.payment}
                          placeholderStyle={{ color: Colors.descriptionColor, fontSize: 19 }}
                          labelStyle={{ fontSize: 19 }}
                          style={styles.textInput}
                          itemStyle={{
                            justifyContent: 'flex-start',
                          }}
                          dropDownStyle={{ backgroundColor: '#fafafa' }}
                          onChangeItem={(item) =>
                            // setState({
                            //   payment: item.value,
                            // })
                            setPayment(item.value)
                          }
                        />
                      </View>
                    </View> */}

                          {/* <View style={styles.viewContainer}>
                      <View style={{ flex: 1 }}>
                        <Text style={styles.textSize}>
                          Start reservation time:
                          </Text>
                      </View>
                      <View>
                        <DateTimePickerModal
                          minimumDate={new Date()}
                          minuteInterval={30}
                          isVisible={showDateTimePicker}
                          mode={pickerMode}
                          display={pickerMode == "time" ? "spinner" : "default"} //for iOS to use minuteInterval
                          onConfirm={(dt) => {
                            const date = moment(dt);
                            if (pickerMode == 'time') {
                              // setState({
                              //   rev_time: date.format('HH:mm'),
                              // });
                              setRev_time(date.format('HH:mm'));
                            }
                            // setState({ showDateTimePicker: false });
                            setShowDateTimePicker(false);
                          }}
                          onCancel={() => {
                            // setState({ showDateTimePicker: false });
                            setShowDateTimePicker(false);
                          }}
                        />
                        <View style={styles.textInput}>
                          <View style={{ flexDirection: 'row', flex: 1 }}>
                            <View style={{ flex: 4 }}>
                              <Text style={{ paddingVertical: 16 }}>
                                {rev_time}
                              </Text>
                            </View>
                            <View
                              style={{
                                flex: 1,
                                marginTop: 15,
                                marginLeft: 170
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  // setState({
                                  //   pickerMode: 'time',
                                  //   showDateTimePicker: true,
                                  // });
                                  setPickerMode('time');
                                  setShowDateTimePicker(true);
                                }}>
                                <Icon name="clock" size={18} color="green" />
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View> */}

                          {/* Halal toggle selection and pork free */}
                          {/* <View style={{
                      flexDirection:'row',

                    }}>
                    <Text style={[
                            styles.textSize,
                            {
                              width: windowWidth * 0.15,
                            },
                          ]}>
                      { isEnabled? "Pork Free": "Halal" }
                    </Text>
                    <Switch
                      value={isEnabled}
                      style={{
                        width: '10%',
                        marginBottom: '3%',
                      }}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive='#dddddd'
                      onSyncPress ={() => {
                      }}
                    />
                    </View> */}

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.15,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                QR On Receipt
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                width={42}
                                style={{
                                  //flexDirection: 'row',
                                  //width: '15%',
                                  marginRight: 20,
                                  // marginLeft: 20,
                                  //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                  bottom: -2,
                                }}
                                value={isQRPrintReceipt}
                                onSyncPress={(statusTemp) =>
                                  // setState({ status: status })
                                  setIsQRPrintReceipt(statusTemp)
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                            </View>
                          </View>

                          {/* <View style={styles.viewContainer}>
                          <View>
                            <Text
                              style={[
                                styles.textSize,
                                {
                                  width: windowWidth * 0.15,
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                },
                              ]}>
                              Logo(s) On Receipt
                            </Text>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 1,
                              alignItems: 'center',
                            }}>
                            <Switch
                              width={42}
                              style={{
                                //flexDirection: 'row',
                                //width: '15%',
                                marginRight: 20,
                                // marginLeft: 20,
                                //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                bottom: -2,
                              }}
                              value={!isQRNotPrintLogo}
                              onSyncPress={(statusTemp) =>
                                // setState({ status: status })
                                setIsQRNotPrintLogo(!statusTemp)
                              }
                              circleColorActive={Colors.primaryColor}
                              circleColorInactive={Colors.fieldtTxtColor}
                              backgroundActive="#dddddd"
                            />
                          </View>
                        </View> */}

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.15,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Halal
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                width={42}
                                style={{
                                  //flexDirection: 'row',
                                  //width: '15%',
                                  marginRight: 20,
                                  // marginLeft: 20,
                                  //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                  bottom: -2,
                                }}
                                value={statusHalal}
                                onSyncPress={(statusTemp) =>
                                  // setState({ status: status })
                                  setStatusHalal(statusTemp)
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.15,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Accepting Takeaway
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                width={42}
                                style={{
                                  //flexDirection: 'row',
                                  //width: '15%',
                                  marginRight: 20,
                                  // marginLeft: 20,
                                  //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                  bottom: -2,
                                }}
                                value={status}
                                onSyncPress={(statusTemp) =>
                                  // setState({ status: status })
                                  setStatus(statusTemp)
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>
                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.15,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Accepting Delivery
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                width={42}
                                style={{
                                  //flexDirection: 'row',
                                  //width: '15%',
                                  marginRight: 20,
                                  // marginLeft: 35,
                                  //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                  bottom: -2,
                                }}
                                value={status1}
                                onSyncPress={(status1Temp) =>
                                  // setState({ status1: status1 })
                                  setStatus1(status1Temp)
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status1
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status1 ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.15,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {`Allow To Skip\nUser Info`}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                width={42}
                                style={{
                                  //flexDirection: 'row',
                                  //width: '15%',
                                  marginRight: 20,
                                  // marginLeft: 35,
                                  //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                  bottom: -2,
                                }}
                                value={skipUserInfo}
                                onSyncPress={(value) =>
                                  // setState({ status1: status1 })
                                  setSkipUserInfo(value)
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status1
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status1 ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.15,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {`Switch Table Layout`}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                width={42}
                                style={{
                                  //flexDirection: 'row',
                                  //width: '15%',
                                  marginRight: 20,
                                  // marginLeft: 35,
                                  //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                  bottom: -2,
                                }}
                                value={switchTableLayout}
                                onSyncPress={(value) =>
                                  TableStore.update(s => {
                                    s.switchTableLayout = value;
                                  })
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  color: switchTableLayout === true
                                    ? Colors.primaryColor
                                    : Colors.fieldtTxtColor,
                                  alignSelf: 'center',
                                }}>
                                {switchTableLayout === true ? 'Fast' : 'Normal'}
                              </Text>
                            </View>
                          </View>

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.15,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {`Draggable\nTable Layout`}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                width={42}
                                style={{
                                  //flexDirection: 'row',
                                  //width: '15%',
                                  marginRight: 20,
                                  // marginLeft: 35,
                                  //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                  bottom: -2,
                                }}
                                value={dTableLayout}
                                onSyncPress={(value) => {
                                  setDTableLayout(value);
                                }}
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                            </View>
                          </View>

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.15,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {`Force Close Shift\nBefore Sign Out`}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                width={42}
                                style={{
                                  marginRight: 20,
                                  bottom: -2,
                                }}
                                value={forceCloseShiftBeforeSignOut}
                                onSyncPress={(value) =>
                                  TableStore.update(s => {
                                    s.forceCloseShiftBeforeSignOut = value;
                                  })
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -4 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Report Display\nType'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Day',
                                      value: REPORT_DISPLAY_TYPE.DAY,
                                    },
                                    {
                                      label: 'Shift',
                                      value: REPORT_DISPLAY_TYPE.SHIFT,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Size limit"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setReportDisplayType(item.value);
                                  }}
                                  defaultValue={reportDisplayType}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -5 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Report Data\nSize Limit'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: '50',
                                      value: REPORT_DATA_SIZE_LIMIT._50,
                                    },
                                    {
                                      label: '100',
                                      value: REPORT_DATA_SIZE_LIMIT._100,
                                    },
                                    {
                                      label: '500',
                                      value: REPORT_DATA_SIZE_LIMIT._500,
                                    },
                                    {
                                      label: '1000',
                                      value: REPORT_DATA_SIZE_LIMIT._1000,
                                    },
                                    {
                                      label: '1500',
                                      value: REPORT_DATA_SIZE_LIMIT._1500,
                                    },
                                    {
                                      label: '2000',
                                      value: REPORT_DATA_SIZE_LIMIT._2000,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Size limit"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setReportDataSizeLimit(item.value);
                                  }}
                                  defaultValue={reportDataSizeLimit}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          {/* <View style={[styles.viewContainer, { zIndex: -49 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Web Order\nV2 Layout'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={webOrderV2Layout}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setWebOrderV2Layout(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View> */}

                          <View style={[styles.viewContainer, { zIndex: -9 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Web Order\nVariant Layout'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Current (Horizontal)',
                                      value: WEB_ORDER_VARIANT_LAYOUT.HORIZONTAL,
                                    },
                                    {
                                      label: 'New (Vertical)',
                                      value: WEB_ORDER_VARIANT_LAYOUT.VERTICAL,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Layout options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setWebOrderVariantLayout(item.value);
                                  }}
                                  defaultValue={webOrderVariantLayout}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -10 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Web Order\nUpselling Layout'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Normal',
                                      value: WEB_ORDER_UPSELLING_LAYOUT.NORMAL,
                                    },
                                    {
                                      label: 'Large',
                                      value: WEB_ORDER_UPSELLING_LAYOUT.LARGE,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Layout options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setWebOrderUpsellingLayout(item.value);
                                  }}
                                  defaultValue={webOrderUpsellingLayout}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          {/* <View style={[styles.viewContainer, { zIndex: -10 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Web Order\nList (Mobile)'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Current (Grid)',
                                      value: WEB_ORDER_LIST_LAYOUT.GRID,
                                    },
                                    {
                                      label: 'New (List)',
                                      value: WEB_ORDER_LIST_LAYOUT.LIST,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Layout options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setWebOrderListLayout(item.value);
                                  }}
                                  defaultValue={webOrderListLayout}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View> */}

                          <View style={[styles.viewContainer, , { zIndex: -11 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.15,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {`Force To Enter\nCard Info`}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                width={42}
                                style={{
                                  //flexDirection: 'row',
                                  //width: '15%',
                                  marginRight: 20,
                                  // marginLeft: 35,
                                  //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                  bottom: -2,
                                }}
                                value={forceToEnterCardInfo}
                                onSyncPress={(value) =>
                                  // setState({ status1: status1 })
                                  setForceToEnterCardInfo(value)
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status1
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status1 ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -11 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                QR Operation Time
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Follow Shift',
                                      value: QR_OPERATION_TIME.FOLLOW_SHIFT,
                                    },
                                    {
                                      label: 'Follow Operation Hours',
                                      value: QR_OPERATION_TIME.FOLLOW_OPERATION_HOURS,
                                    },
                                    {
                                      label: 'Follow Operation Hours + Shift',
                                      value: QR_OPERATION_TIME.FOLLOW_OPERATION_HOURS_AND_SHIFT,
                                    },

                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Operation Time"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setQrOperationTime(item.value);
                                  }}
                                  defaultValue={qrOperationTime}
                                // multiple
                                // multipleText='%d type(s) selected'
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -12 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Ask Payment Options First
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Dine In',
                                      value: ORDER_TYPE.DINEIN,
                                    },
                                    {
                                      label: 'Takeaway',
                                      value: ORDER_TYPE.PICKUP,
                                    },
                                    {
                                      label: 'Delivery',
                                      value: ORDER_TYPE.DELIVERY,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(items) => {
                                    setAskPaymentFirstOrderTypes(items);
                                  }}
                                  defaultValue={askPaymentFirstOrderTypes}
                                  multiple
                                  multipleText='%d type(s) selected'
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -13 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Delivery Payment Options
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Pay Now',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
                                    },
                                    {
                                      label: 'Pay Later',
                                      value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
                                    },
                                    {
                                      label: 'All',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ALL,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setDeliveryPaymentOptions(item.value);
                                  }}
                                  defaultValue={deliveryPaymentOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -15 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Takeaway Payment Options
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Pay Now',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
                                    },
                                    {
                                      label: 'Pay Later',
                                      value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
                                    },
                                    {
                                      label: 'All',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ALL,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setPickupPaymentOptions(item.value);
                                  }}
                                  defaultValue={pickupPaymentOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          {/* <View style={[styles.viewContainer, { zIndex: -20 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Dine-in Payment Options\n(Dynamic QR)'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Pay Now',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
                                    },
                                    {
                                      label: 'Pay Later',
                                      value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
                                    },
                                    {
                                      label: 'All',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ALL,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setDineinPaymentOptions(item.value);
                                  }}
                                  defaultValue={dineinPaymentOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View> */}

                          {/* <View style={[styles.viewContainer, { zIndex: -30 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Dine-in Payment Options\n(Static QR)'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Pay Now',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
                                    },
                                    {
                                      label: 'Pay Later',
                                      value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
                                    },
                                    {
                                      label: 'All',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ALL,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setDineinPaymentOptionsGeneric(item.value);
                                  }}
                                  defaultValue={dineinPaymentOptionsGeneric}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View> */}

                          <View style={[styles.viewContainer, { zIndex: -40 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Docket Payment Options'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Pay Now',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
                                    },
                                    {
                                      label: 'Pay Later',
                                      value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
                                    },
                                    {
                                      label: 'All',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ALL,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setDocketPaymentOptions(item.value);
                                  }}
                                  defaultValue={docketPaymentOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          {/* Reservation Payment Option */}
                          <View style={[styles.viewContainer, { zIndex: -47 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Reservation Payment Options'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Pay Now',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
                                    },
                                    {
                                      label: 'Pay Later',
                                      value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
                                    },
                                    {
                                      label: 'All',
                                      value: USER_ORDER_PAYMENT_OPTIONS.ALL,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Pay options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setReservationPaymentOptions(item.value);
                                  }}
                                  defaultValue={reservationPaymentOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -49 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Print Receipt\nWhen Paid Online'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={printReceiptWhenPaidOnline}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setPrintReceiptWhenPaidOnline(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -49 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Auto Print\nPay Slip'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={autoPrintPaySlip}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setAutoPrintPaySlip(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          {/* <View style={[styles.viewContainer, { zIndex: -50 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Print KD/OS Only,\nWhen Order Paid'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={printKdOsWhenOrderPaid}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setPrintKdOsWhenOrderPaid(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View> */}

                          <View style={[styles.viewContainer, { zIndex: -50 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Shorten\nShift Report'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={shortenShiftReport}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setShortenShiftReport(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -50 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Shorten\nReceipt'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={shortenReceipt}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setShortenReceipt(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -55 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Receipt\nPrinting Options'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={RECEIPT_PRINTING_EVENT_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Receipt options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setReceiptPrintingOptions(item.value);
                                  }}
                                  defaultValue={receiptPrintingOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -60 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Cash Drawer\nOpening Options'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={CASH_DRAWER_OPEN_EVENT_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setCashDrawerOpeningOptions(item.value);
                                  }}
                                  defaultValue={cashDrawerOpeningOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          {/*
                          <View style={[styles.viewContainer, { zIndex: -70 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}
                              >
                                {'Kitchen Docket\nPrinting Options'}
                              </Text>
                            </View>

                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}
                            >
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}
                              >
                                <DropDownPicker
                                  items={KD_PRINT_EVENT_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="KD Printing Options"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(items) => {
                                    setKdPrintEventTypes(items);
                                  }}
                                  defaultValue={kdPrintEventTypes}
                                  multiple
                                  multipleText="%d event(s) selected"
                                />
                                <DropDownPicker
                                  items={KD_PRINT_EVENT_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setCashDrawerOpeningOptions(item.value);
                                  }}
                                  defaultValue={cashDrawerOpeningOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>
                          */}

                          {/*
                          <View style={[styles.viewContainer, { zIndex: -80 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}
                              >
                                {'Kitchen Docket\nPrinting Variation'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}
                            >
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}
                              >
                                <DropDownPicker
                                  items={KD_PRINT_VARIATION_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="KD Printing Variation"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(item) => {
                                    setKdPrintVariation(item.value);
                                  }}
                                  defaultValue={kdPrintVariation}
                                  // multiple={true}
                                  // multipleText="%d event(s) selected"
                                />
                                <DropDownPicker
                                  items={KD_PRINT_EVENT_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setCashDrawerOpeningOptions(item.value);
                                  }}
                                  defaultValue={cashDrawerOpeningOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>
                          */}

                          {/* <View style={[styles.viewContainer, { zIndex: -90 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nFont Size'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={KD_FONT_SIZE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="KD Font Size"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(item) => {
                                    setKdFontSize(item.value);
                                  }}
                                  defaultValue={kdFontSize}
                                // multiple={true}
                                // multipleText='%d event(s) selected'
                                />
                                <DropDownPicker
                                  items={KD_PRINT_EVENT_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setCashDrawerOpeningOptions(item.value);
                                  }}
                                  defaultValue={cashDrawerOpeningOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View> */}

                          {/* <View style={[styles.viewContainer, { zIndex: -91 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nFont Size\n(Table, Order ID/Type)'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={KD_FONT_SIZE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="KD Font Size"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(item) => {
                                    setKdHeaderFontSize(item.value);
                                  }}
                                  defaultValue={kdHeaderFontSize}
                                // multiple={true}
                                // multipleText='%d event(s) selected'
                                />
                                <DropDownPicker
                                  items={KD_PRINT_EVENT_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setCashDrawerOpeningOptions(item.value);
                                  }}
                                  defaultValue={cashDrawerOpeningOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View> */}

                          {/* <View style={[styles.viewContainer, { zIndex: -100 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nPrinting SKU'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={isKdPrintSku}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setIsKdPrintSku(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View> */}

                          <View style={[styles.viewContainer, { zIndex: -100 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'To Print\nVariant/Add-on\nGroup'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={toPrintVariantAddonTitle}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setToPrintVariantAddonTitle(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          {/* <View style={[styles.viewContainer, { zIndex: -100 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nPrinting\nUser Info'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={isKdPrintUserInfo}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setIsKdPrintUserInfo(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View> */}

                          <View style={[styles.viewContainer, { zIndex: -100 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Open Mode'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={toggleOpenOrder}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setToggleOpenOrder(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          {
                            currOutlet && currOutlet.showOfflineMode
                              ?
                              <View style={[styles.viewContainer, { zIndex: -100 }]}>
                                <View>
                                  <Text
                                    style={[
                                      styles.textSize,
                                      {
                                        width: windowWidth * 0.2,
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: switchMerchant ? 10 : 14,
                                      },
                                    ]}>
                                    {'Offline Mode'}
                                  </Text>
                                </View>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <View
                                    style={{
                                      width: 200,
                                      height: 50,
                                      justifyContent: 'center',
                                      marginLeft: 5,
                                    }}>
                                    <View
                                      style={{
                                        flexDirection: 'row',
                                        flex: 1,
                                        alignItems: 'center',
                                      }}>
                                      <Switch
                                        width={42}
                                        style={{
                                          //flexDirection: 'row',
                                          //width: '15%',
                                          marginRight: 20,
                                          // marginLeft: 20,
                                          //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                          bottom: -2,
                                        }}
                                        value={toggleOfflineMode}
                                        onSyncPress={(statusTemp) =>
                                          // setState({ status: status })
                                          setToggleOfflineMode(statusTemp)
                                        }
                                        circleColorActive={Colors.primaryColor}
                                        circleColorInactive={Colors.fieldtTxtColor}
                                        backgroundActive="#dddddd"
                                      />
                                    </View>
                                  </View>
                                </View>
                              </View>
                              :
                              <></>
                          }

                          <View style={[styles.viewContainer, { zIndex: -100 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Multiple POS\nTerminal Mode'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={multiplePosTerminalMode}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setMultiplePosTerminalMode(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -101 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Disable\nAuto Printing'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={toggleDisableAutoPrint}
                                    onSyncPress={async (statusTemp) => {
                                      // setState({ status: status })
                                      setToggleDisableAutoPrint(statusTemp);

                                      await AsyncStorage.setItem('toggleDisableAutoPrint', statusTemp ? '1' : '0');

                                      global.outletToggleDisableAutoPrint = statusTemp ? true : false;
                                    }}
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -102 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Disable\nPrinting Alerts'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={toggleDisablePrintingAlert}
                                    onSyncPress={async (statusTemp) => {
                                      // setState({ status: status })
                                      setToggleDisablePrintingAlert(statusTemp);

                                      await AsyncStorage.setItem('toggleDisablePrintingAlert', statusTemp ? '1' : '0');

                                      global.outletToggleDisablePrintingAlert = statusTemp ? true : false;
                                    }}
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          {/* /////////////////////////////////// */}

                          {/* 2024-10-16 - enable external display support */}

                          <View style={[styles.viewContainer, { zIndex: -102 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Customer\nDisplay'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={customerDisplaySupport}
                                    onSyncPress={async (statusTemp) => {
                                      // setState({ status: status })
                                      setCustomerDisplaySupport(statusTemp);

                                      await AsyncStorage.setItem('customerDisplaySupport', statusTemp ? '1' : '0');

                                      global.customerDisplaySupport = statusTemp ? true : false;

                                      CommonStore.update(s => {
                                        s.customerDisplaySupport = global.customerDisplaySupport;
                                      });
                                    }}
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -103, width: '65%' }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Customer\nDisplay Height'}
                              </Text>
                            </View>

                            <View style={{ width: '35%', justifyContent: 'center' }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                placeholderStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                //placeholderTextColor={'#000'}
                                keyboardType="decimal-pad"
                                style={{
                                  padding: 5,
                                  height: 40,
                                  paddingLeft: 10,
                                  // width: '120%',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                  backgroundColor: Colors.fieldtBgColor,
                                }}
                                placeholder={'0'}
                                //iOS
                                // clearTextOnFocus
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(fcsHeight)
                                //   setFCSHeight('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (fcsHeight == '') {
                                //     setFCSHeight(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={(text) => {
                                  const isValidNumber = /^(\d+(\.\d*)?|\.\d+)?$/.test(text);
                                  if (isValidNumber || text === '') { // Allow empty string for clearing
                                    setFCSHeight(text);
                                  }
                                }}
                                value={fcsHeight && fcsWidth !== '' ? fcsHeight.toString() : 0}
                                ref={myTextInput}
                              />
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -104, width: '65%' }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Customer\nDisplay Width'}
                              </Text>
                            </View>

                            <View style={{ width: '35%' }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                placeholderStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                keyboardType="decimal-pad"
                                //placeholderTextColor={'#000'}
                                style={{
                                  padding: 5,
                                  height: 40,
                                  paddingLeft: 10,
                                  // width: '120%',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                  backgroundColor: Colors.fieldtBgColor,
                                }}
                                placeholder={'0'}
                                //iOS
                                // clearTextOnFocus
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(fcsWidth)
                                //   setFCSWidth('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (fcsWidth == '') {
                                //     setFCSWidth(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={(text) => {
                                  const isValidNumber = /^(\d+(\.\d*)?|\.\d+)?$/.test(text);
                                  if (isValidNumber || text === '') { // Allow empty string for clearing
                                    setFCSWidth(text);
                                  }
                                }}
                                value={fcsWidth && fcsWidth !== '' ? fcsWidth.toString() : 0}
                                ref={myTextInput}
                              />
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -105, width: '65%' }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Customer\nDisplay Text Size'}
                              </Text>
                            </View>

                            <View style={{ width: '35%' }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                placeholderStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                keyboardType="decimal-pad"
                                //placeholderTextColor={'#000'}
                                style={{
                                  padding: 5,
                                  height: 40,
                                  paddingLeft: 10,
                                  // width: '120%',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                  backgroundColor: Colors.fieldtBgColor,
                                }}
                                placeholder={'0'}
                                //iOS
                                // clearTextOnFocus
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(fcsText)
                                //   setFCSText('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (fcsText == '') {
                                //     setFCSText(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={(text) => {
                                  const isValidNumber = /^(\d+(\.\d*)?|\.\d+)?$/.test(text);
                                  if (isValidNumber || text === '') { // Allow empty string for clearing
                                    setFCSText(text);
                                  }
                                }}
                                value={fcsText && fcsText !== '' ? fcsText.toString() : 0}
                                ref={myTextInput}
                              />
                            </View>
                          </View>

                          {/* /////////////////////////////////// */}
                          {/* 2024-11-26 - host & display support */}

                          <View style={[styles.viewContainer, { zIndex: -106 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'App Display'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={odActive}
                                    onSyncPress={async (statusTemp) => {
                                      // setState({ status: status })
                                      setOdActive(statusTemp);
                                    }}
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -107 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Pairing Type'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={OUTLET_DISPLAY_PAIRING_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Pairing type"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={async (item) => {
                                    setOdPairingType(item.value);

                                    await AsyncStorage.setItem('odPairingType', item.value);

                                    global.odPairingType = item.value;

                                    OutletStore.update(s => {
                                      s.odPairingType = item.value;
                                    });
                                  }}
                                  defaultValue={odPairingType}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -109 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Pairing Device'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={OUTLET_DISPLAY_PAIRING_DEVICE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Pairing device"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={async (item) => {
                                    setOdPairingDevice(item.value);

                                    await AsyncStorage.setItem('odPairingDevice', item.value);

                                    global.odPairingDevice = item.value;

                                    OutletStore.update(s => {
                                      s.odPairingDevice = item.value;
                                    });
                                  }}
                                  defaultValue={odPairingDevice}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          {/* /////////////////////////////////// */}

                          <View style={[styles.viewContainer, { zIndex: -110 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Idle Auto Logout\nOptions'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={USER_IDLE_SIGN_OUT_EVENT_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setUserIdleSignOutOptions(item.value);
                                  }}
                                  defaultValue={userIdleSignOutOptions}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View>

                          {/* /////////////////////////////////// */}

                          {/* 2025-04-23 - configurable fields for e-invoice */}

                          <View style={[styles.viewContainer, { zIndex: -115 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'e-Invoice\nAsking Fields'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={EI_USER_FIELDS_DROPDOWN_LIST}
                                  containerStyle={{ height: 40 }}
                                  placeholder="e-Invoice Asking Fields"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(items) => {
                                    setEiAskFields(items);
                                  }}
                                  defaultValue={eiAskFields}
                                  multiple={true}
                                  multipleText='%d field(s) selected'
                                />
                                {/* <DropDownPicker
                                  items={KD_PRINT_EVENT_TYPE_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Payment options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setCashDrawerOpeningOptions(item.value);
                                  }}
                                  defaultValue={cashDrawerOpeningOptions}
                                  multiple={false}
                                /> */}
                              </View>
                            </View>
                          </View>

                          {/* /////////////////////////////////// */}

                          <View style={[styles.viewContainer, { zIndex: -120 }]}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.2 : windowWidth * 0.2,
                                height: 50,
                                justifyContent: 'center',
                                // borderWidth:1
                              }}>
                              <DropDownPicker
                                items={resetDataTypeDropdownList}
                                // defaultValue={outletId}
                                containerStyle={{ height: switchMerchant ? 35 : 40, width: '90%' }}
                                placeholder="Reset Options"
                                arrowStyle={{
                                  height: '180%',
                                  bottom: switchMerchant ? 1 : 0,
                                }}
                                placeholderStyle={{
                                  color: Colors.descriptionColor,
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Regular',
                                }}
                                style={{ backgroundColor: '#fafafa' }}
                                labelStyle={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Regular',
                                }}
                                itemStyle={{
                                  justifyContent: 'flex-start',
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Regular',
                                  marginLeft: 5,
                                }}
                                dropDownStyle={{
                                  backgroundColor: '#fafafa',
                                  // maxHeight: 45,
                                  maxHeight: 200,
                                }}
                                onChangeItem={(items) => {
                                  setDropDownResetList(items);
                                }}
                                defaultValue={dropDownResetList}
                                multiple
                                multipleText='%d items have been selected'
                              />
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <TouchableOpacity
                                  style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: '#0F1A3C',
                                    borderRadius: 5,
                                    width: 120,
                                    paddingHorizontal: 10,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -4,
                                  }}
                                  disabled={loading}
                                  onPress={() => {
                                    if (dropDownResetList.length > 0) {
                                      Alert.alert(
                                        'Warning',
                                        'Are your sure want to proceed with resetting data?\n\nNote: This will clear your existing data',
                                        [
                                          {
                                            text: 'OK',
                                            onPress: () => {
                                              resetOutletData();
                                            },
                                          },
                                          {
                                            text: 'Cancel',
                                            onPress: () => {
                                            },
                                          },
                                        ],
                                        { cancelable: false },
                                      );
                                    } else {
                                      Alert.alert(
                                        'Info',
                                        'Please select at least one option to proceed',
                                        [
                                          {
                                            text: 'OK',
                                            onPress: () => { },
                                          },
                                        ],
                                        { cancelable: false },
                                      );
                                    }
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      //marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    {'RESET'}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </View>
                          </View>

                          <View
                            style={{
                              width: 200,
                              height: 50,
                              justifyContent: 'center',
                              zIndex: -130,
                            }}>
                            <TouchableOpacity
                              style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                backgroundColor: '#0F1A3C',
                                borderRadius: 5,
                                width: 120,
                                paddingHorizontal: 10,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -4,
                              }}
                              disabled={loading}
                              onPress={async () => {
                                if (currOutlet.forceCloseShiftBeforeSignOut === true && currOutletShiftStatus === OUTLET_SHIFT_STATUS.OPENED) {
                                  Alert.alert('Info', 'Please close the shift first before sign out or log out.')
                                }
                                else {
                                  await logOutButton();
                                }
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                {'LOGOUT'}
                              </Text>
                            </TouchableOpacity>
                          </View>

                          {/* <View style={styles.viewContainer}>
                      <View>
                        <Text style={styles.textSize}>Ordering Method:</Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1
                        }}>
                        <TouchableOpacity
                          style={{
                            marginVertical: 10,
                            width: '100%',
                            flexDirection: 'row',
                            flex: 1,
                            marginLeft: 35
                          }}
                          onPress={() => {
                            // setState({
                            //   selfCollect: !selfCollect,
                            // });
                            setSelfCollect(!selfCollect);
                          }}
                        >
                          <View style={{
                            marginHorizontal: 15,
                            backgroundColor:
                              selfCollect
                                ? Colors.primaryColor
                                : "#e8e9eb",
                            width: 25,
                            height: 25,
                            justifyContent: "center",
                            alignItems: "center",
                            borderRadius: 6,
                          }}>
                            <Icon
                              name="check"
                              size={17}
                              color={
                                selfCollect
                                  ? Colors.whiteColor
                                  : Colors.descriptionColor
                              }
                            />
                          </View>
                          <Text style={{
                            fontSize: 15,
                            color: selfCollect
                              ? Colors.primaryColor
                              : Colors.descriptionColor
                          }}>Self Collection</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={{
                            marginVertical: 10,
                            width: '100%',
                            flexDirection: 'row',
                            flex: 1,
                            marginLeft: 20
                          }}
                          onPress={() => {
                            // setState({
                            //   selfCollect: !selfCollect,
                            // });
                            setSelfCollect(!selfCollect);
                          }}
                        >
                          <View style={{
                            marginHorizontal: 15,
                            backgroundColor:
                              !selfCollect
                                ? Colors.primaryColor
                                : "#e8e9eb",
                            width: 25,
                            height: 25,
                            justifyContent: "center",
                            alignItems: "center",
                            borderRadius: 6,
                          }}>
                            <Icon
                              name="check"
                              size={17}
                              color={
                                !selfCollect
                                  ? Colors.whiteColor
                                  : Colors.descriptionColor
                              }
                            />
                          </View>
                          <Text style={{
                            fontSize: 15,
                            color: !selfCollect
                              ? Colors.primaryColor
                              : Colors.descriptionColor
                          }}>Table Order</Text>
                        </TouchableOpacity>
                      </View>
                    </View> */}

                          {/* <View style={styles.viewContainer}>
                      <View style={{ flex: 1 }}>
                        <Text style={styles.textSize}>
                          Payment Options:
                            </Text>
                      </View>
                      <TouchableOpacity
                        onPress={() => {
                          // setState({
                          //   expandView: !expandView,
                          // });
                          setExpandView(!expandView);
                        }}>
                        <View style={styles.textInput}>
                          <Text
                            style={{
                              fontSize: 16,
                              alignSelf: 'center',
                              marginLeft: '5%',
                              color: Colors.descriptionColor,
                            }}>
                            Payment Options
                              </Text>
                          <View style={{ marginLeft: '40%', alignSelf: 'center' }}>
                            <SimpleLineIcons name="arrow-down" size={12} />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View> */}

                          <View style={styles.viewContainer}>
                            {expandView == true ? (
                              <View
                                style={{
                                  width: 300,
                                  height: 220,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 7,
                                  marginLeft: '36%',
                                  flex: 1,
                                }}>
                                <ScrollView
                                  showsVerticalScrollIndicator={false}
                                  style={{ width: '100%', height: '100%' }}>
                                  <TouchableOpacity
                                    style={{
                                      marginVertical: 10,
                                      width: '100%',
                                      flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                      // setState({
                                      //   isChecked2: !isChecked2,
                                      // });
                                      setIsChecked2(!isChecked2);
                                    }}>
                                    <View
                                      style={{
                                        marginHorizontal: 15,
                                        backgroundColor: isChecked2
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                        width: 25,
                                        height: 25,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        borderRadius: 6,
                                      }}>
                                      <Icon
                                        name="check"
                                        size={17}
                                        color={
                                          isChecked2
                                            ? Colors.whiteColor
                                            : Colors.descriptionColor
                                        }
                                      />
                                    </View>
                                    <Image
                                      source={{
                                        uri: 'https://i.ibb.co/jLSJNG2/creditc.png',
                                      }}
                                      style={{
                                        resizeMode: 'contain',
                                        width: 100,
                                        height: 30,
                                      }}
                                    />
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    style={{
                                      marginVertical: 10,
                                      width: '100%',
                                      flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                      // setState({
                                      //   isChecked3: !isChecked3,
                                      // });
                                      setIsChecked3(!isChecked3);
                                    }}>
                                    <View
                                      style={{
                                        marginHorizontal: 15,
                                        backgroundColor: isChecked3
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                        width: 25,
                                        height: 25,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        borderRadius: 6,
                                      }}>
                                      <Icon
                                        name="check"
                                        size={17}
                                        color={
                                          isChecked3
                                            ? Colors.whiteColor
                                            : Colors.descriptionColor
                                        }
                                      />
                                    </View>
                                    <Image
                                      source={{
                                        uri: 'https://newsroom.mastercard.com/wp-content/uploads/2016/09/paypal-logo.png',
                                      }}
                                      style={{
                                        resizeMode: 'contain',
                                        width: 100,
                                        height: 30,
                                      }}
                                    />
                                  </TouchableOpacity>

                                  <TouchableOpacity
                                    style={{
                                      marginVertical: 10,
                                      width: '100%',
                                      flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                      // setState({
                                      //   isChecked4: !isChecked4,
                                      // });
                                      setIsChecked4(!isChecked4);
                                    }}>
                                    <View
                                      style={{
                                        marginHorizontal: 15,
                                        backgroundColor: isChecked4
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                        width: 25,
                                        height: 25,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        borderRadius: 6,
                                      }}>
                                      <Icon
                                        name="check"
                                        size={17}
                                        color={
                                          isChecked4
                                            ? Colors.whiteColor
                                            : Colors.descriptionColor
                                        }
                                      />
                                    </View>
                                    <Text
                                      style={{
                                        fontSize: 18,
                                        color: isChecked4
                                          ? Colors.primaryColor
                                          : Colors.descriptionColor,
                                      }}>
                                      E-wallet
                                    </Text>
                                  </TouchableOpacity>

                                  <TouchableOpacity
                                    style={{
                                      marginVertical: 10,
                                      width: '100%',
                                      flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                      // setState({
                                      //   isChecked5: !isChecked5,
                                      // });
                                      setIsChecked5(!isChecked5);
                                    }}>
                                    <View
                                      style={{
                                        marginHorizontal: 15,
                                        backgroundColor: isChecked5
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                        width: 25,
                                        height: 25,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        borderRadius: 6,
                                      }}>
                                      <Icon
                                        name="check"
                                        size={17}
                                        color={
                                          isChecked5
                                            ? Colors.whiteColor
                                            : Colors.descriptionColor
                                        }
                                      />
                                    </View>
                                    <Text
                                      style={{
                                        fontSize: 18,
                                        color: isChecked5
                                          ? Colors.primaryColor
                                          : Colors.descriptionColor,
                                      }}>
                                      Online Transfer
                                    </Text>
                                  </TouchableOpacity>
                                </ScrollView>
                              </View>
                            ) : null}
                          </View>
                        </View>
                      </View>
                      {/* right */}
                      <View style={{ flex: 1, marginTop: 20 }}>
                        {/* <TouchableOpacity
                    style={[
                      styles.addNewView,
                      {
                        // alignSelf: 'center',
                      },
                    ]}
                    onPress={() => {
                      // setState({ addOutletWindow: true })
                      setAddOutletWindow(true);
                    }}>
                    <View style={styles.addButtonView}>
                      <View style={{ marginLeft: '15%' }}>
                        <AntDesign
                          name="pluscircle"
                          size={20}
                          color={Colors.primaryColor}
                        />
                      </View>
                      <Text
                        style={{ marginLeft: 10, color: Colors.primaryColor }}>
                        Add New Outlet
                      </Text>
                    </View>
                  </TouchableOpacity> */}
                        <ModalView
                          supportedOrientations={['landscape', 'portrait']}
                          animationType="fade"
                          visible={addOutletWindow}
                          transparent>
                          <View
                            style={{
                              backgroundColor: Colors.modalBgColor,
                              flex: 1,
                              width: '100%',
                              height: '100%',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <View
                              style={{
                                // width: '40%',
                                // height: '30%',
                                width: windowWidth * 0.4,
                                height: windowHeight * 0.3,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 12,
                                justifyContent: 'center',
                                alignItems: 'center',

                                ...getTransformForModalInsideNavigation(),
                              }}>
                              <TouchableOpacity
                                style={{
                                  position: 'absolute',
                                  top: '8%',
                                  right: '5%',
                                  // width: 40,
                                  // height: 40,
                                  // backgroundColor: 'red'
                                }}
                                onPress={() => setAddOutletWindow(false)}>
                                {/* <Close size={26} color={Colors.blackColor} /> */}
                                <AIcon
                                  name="closecircle"
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                />
                              </TouchableOpacity>

                              <View style={{ padding: 13 }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: 18,
                                  }}>
                                  Add New Outlet Name
                                </Text>
                              </View>

                              <View
                                style={{
                                  width: '50%',
                                  height: '20%',
                                  backgroundColor: 'white',
                                  borderRadius: 10,
                                  marginBottom: 15,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 15,
                                    color: Colors.descriptionColor,
                                    width: '100%',
                                    textAlign: 'center',
                                  }}
                                  placeholder=" Outlet Name"
                                  onChangeText={(text) => {
                                    // setState({ addOutletName: text });
                                    setAddOutletName(text);
                                  }}
                                  value={addOutletName}
                                />
                              </View>

                              <View
                                style={{
                                  width: '30%',
                                  height: '20%',
                                  backgroundColor: Colors.primaryColor,
                                  borderRadius: 10,
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  padding: 13,
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    addOutlet();
                                    // setState({ addOutletWindow: false });
                                    setAddOutletWindow(false);
                                  }}>
                                  <Text
                                    style={{
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: 20,
                                      color: 'white',
                                    }}>
                                    Add
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </View>
                          </View>
                        </ModalView>

                        <ModalView
                          supportedOrientations={['landscape', 'portrait']}
                          animationType="fade"
                          visible={showModalBreakTime}
                          transparent>
                          <View
                            style={{
                              backgroundColor: Colors.modalBgColor,
                              flex: 1,
                              width: '100%',
                              height: '100%',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <View
                              style={{
                                // width: '40%',
                                // height: '60%',
                                width: windowWidth * 0.4,
                                height: windowHeight * 0.6,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 12,
                                justifyContent: 'center',
                                alignItems: 'center',

                                ...getTransformForModalInsideNavigation(),
                              }}>
                              <TouchableOpacity
                                style={{
                                  position: 'absolute',
                                  top: '8%',
                                  right: '5%',
                                  // width: 40,
                                  // height: 40,
                                  // backgroundColor: 'red'
                                }}
                                onPress={() => setShowModalBreakTime(false)}>
                                {/* <Close size={26} color={Colors.blackColor} /> */}
                                <AIcon
                                  name="closecircle"
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                />
                              </TouchableOpacity>

                              <View style={{ padding: 13 }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: 18,
                                  }}>
                                  Break Time
                                </Text>
                              </View>

                              {/* <View
                              style={{
                                width: '50%',
                                height: '20%',
                                backgroundColor: 'white',
                                borderRadius: 10,
                                marginBottom: 15,
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 15,
                                  color: Colors.descriptionColor,
                                  width: '100%',
                                  textAlign: 'center',
                                }}
                                placeholder=" Outlet Name"
                                onChangeText={(text) => {
                                  // setState({ addOutletName: text });
                                  setAddOutletName(text);
                                }}
                                value={addOutletName}
                              />
                            </View> */}

                              <View
                                style={{
                                  //flexDirection: 'row',
                                  //width: '100%',
                                  marginLeft: '6%',

                                  marginTop: '5%',

                                  // backgroundColor: 'green',

                                  width: '100%',
                                  height: '50%',
                                }}>
                                <View style={{ flexDirection: 'row' }}>
                                  <View style={{ flex: 0.6 }}>
                                    <Text style={styles.textSize} />
                                  </View>
                                  <View style={{ flex: 1.3, marginBottom: 20 }}>
                                    <Text
                                      style={{
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}>
                                      Start Time:
                                    </Text>
                                  </View>
                                  <View style={{ flex: 1.3, marginBottom: 20 }}>
                                    <Text
                                      style={{
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}>
                                      End Time:
                                    </Text>
                                  </View>
                                  <View style={{ flex: 0.8, marginBottom: 20 }}>
                                    {/* <Text
                                    style={{
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}>
                                    Action:
                                  </Text> */}

                                    <TouchableOpacity
                                      style={{
                                        // backgroundColor: 'red'
                                      }}
                                      onPress={() => {
                                        // setCurrOutletOpeningOff({
                                        //   ...currOutletOpeningOff,
                                        //   [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                        // });

                                        // setShowModalBreakTime(true);

                                        if (currOutletBreakTime[selectedBreakTimeDay].length >= 5) {
                                          Alert.alert('Info', 'Max time slots for break time is 5 for now.')
                                        }
                                        else {
                                          setCurrOutletBreakTime({
                                            ...currOutletBreakTime,
                                            [selectedBreakTimeDay]: [
                                              ...currOutletBreakTime[selectedBreakTimeDay],
                                              `${moment().format('HHmm')}-${moment().format('HHmm')}`,
                                            ],
                                          });
                                        }
                                      }}>
                                      {/* <FontAwesome5
                                      name='edit'
                                      size={23}
                                      style={{
                                        bottom: 6,
                                      }}
                                      color={Colors.primaryColor}
                                    /> */}
                                      <Icon
                                        name="plus-circle"
                                        size={switchMerchant ? 17 : 20}
                                        color={Colors.primaryColor}
                                      />
                                    </TouchableOpacity>
                                  </View>
                                </View>
                                <View style={{}}>
                                  {currOutletBreakTime &&
                                    currOutletBreakTime[selectedBreakTimeDay] &&
                                    currOutletBreakTime[selectedBreakTimeDay].map((breakTimeGroup, index) => {
                                      // var outletDay = null
                                      // //// console.log("openings", openings)
                                      // for (const d of openings) {
                                      //   if (d.week.toLowerCase() == day.toLowerCase()) {
                                      //     outletDay = d
                                      //   }
                                      // }

                                      var outletOpeningToday = null;

                                      var startTime = '';
                                      var endTime = '';
                                      var startTimeStr = null;
                                      var endTimeStr = null;

                                      ////////////////////////////////////////////////////////

                                      // var outletOpeningWeek = [];

                                      // const weekOrders = [
                                      //   'monday',
                                      //   'tuesday',
                                      //   'wednesday',
                                      //   'thursday',
                                      //   'friday',
                                      //   'saturday',
                                      //   'sunday',
                                      // ];

                                      // const dayRaw = weekOrders[index];

                                      try {
                                        // const outletOpening = currOutletOpening;

                                        // if (outletOpening) {
                                        //   outletOpeningToday =
                                        //     outletOpening[WEEK[index]];
                                        // }

                                        // if (outletOpeningToday) {
                                        //   startTimeStr =
                                        //     outletOpeningToday.split('-')[0];
                                        //   endTimeStr = outletOpeningToday.split('-')[1];

                                        //   startTime = moment(startTimeStr, 'HHmm');
                                        //   endTime = moment(endTimeStr, 'HHmm');

                                        //   // isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                        // }

                                        // // console.log(outletOpeningToday);

                                        startTimeStr =
                                          breakTimeGroup.split('-')[0];
                                        endTimeStr = breakTimeGroup.split('-')[1];

                                        startTime = moment(startTimeStr, 'HHmm');
                                        endTime = moment(endTimeStr, 'HHmm');

                                        ////////////////////////////////////////////////////////
                                        // do for all days

                                        // for (var i = 0; i < weekOrders.length; i++) {
                                        //   const tempOpeningDay = outletOpening[weekOrders[i]];

                                        //   var startTimeStrDay = '';
                                        //   var endTimeStrDay = '';
                                        //   var isOpeningNowDay = false;

                                        //   if (tempOpeningDay && tempOpeningDay.length > 0) {
                                        //     startTimeStrDay = tempOpeningDay.split('-')[0];
                                        //     endTimeStrDay = tempOpeningDay.split('-')[1];

                                        //     const startTime = moment(startTimeStrDay, 'HHmm');
                                        //     const endTime = moment(endTimeStrDay, 'HHmm');

                                        //     isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                        //   }

                                        //   outletOpeningWeek.push({
                                        //     startTimeStr: startTimeStrDay,
                                        //     endTimeStr: endTimeStrDay,
                                        //     isOpeningNow: isOpeningNowDay,
                                        //     day: i,
                                        //   });
                                        // }
                                      } catch (ex) {
                                        console.error(ex);
                                      }

                                      return (
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            marginBottom: 10,
                                          }}>
                                          <View style={{ flex: 0.6 }}>
                                            <Text
                                              style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: switchMerchant ? 10 : 14,
                                              }}>
                                              {''}
                                            </Text>
                                          </View>
                                          <View style={{ flex: 1.3, paddingRight: '0%' }}>
                                            <TouchableOpacity
                                              // disabled={currOutletOpeningOff[WEEK[index]]}
                                              style={{}}
                                              onPress={() => {
                                                // setOpenHourPickerVisible(false);
                                                // setCloseHourPickerVisible(true);
                                                // setEditOpeningIndex(index);

                                                setSelectedBreakTimeDayIndex(index);
                                                setShowTimePickerBreakTimeStart(true);
                                              }}>
                                              <View style={{ flexDirection: 'row' }}>
                                                <Text
                                                  style={{
                                                    // color: Colors.fieldtTxtColor,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    color: Colors.blackColor,
                                                    fontSize: switchMerchant ? 10 : 14,
                                                  }}>
                                                  {
                                                    // (currOutletBreakTime[dayRaw] && currOutletBreakTime[dayRaw].length > 0)
                                                    //   ?
                                                    //   currOutletBreakTime[dayRaw].map(breakTimeGroup => breakTimeGroup).join('\n')
                                                    //   :
                                                    //   '-'
                                                    (startTimeStr)
                                                      ?
                                                      startTimeStr
                                                      :
                                                      '-'
                                                  }
                                                </Text>
                                                {/* <MaterialIcons
                                          name="keyboard-arrow-down"
                                          size={switchMerchant ? 15 : 20}
                                          style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                        /> */}
                                              </View>
                                            </TouchableOpacity>
                                          </View>

                                          <View style={{ flex: 1.3, paddingRight: '0%' }}>
                                            <TouchableOpacity
                                              // disabled={currOutletOpeningOff[WEEK[index]]}
                                              style={{}}
                                              onPress={() => {
                                                // setOpenHourPickerVisible(false);
                                                // setCloseHourPickerVisible(true);
                                                // setEditOpeningIndex(index);

                                                setSelectedBreakTimeDayIndex(index);
                                                setShowTimePickerBreakTimeEnd(true);
                                              }}>
                                              <View style={{ flexDirection: 'row' }}>
                                                <Text
                                                  style={{
                                                    // color: Colors.fieldtTxtColor,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    color: Colors.blackColor,
                                                    fontSize: switchMerchant ? 10 : 14,
                                                  }}>
                                                  {
                                                    // (currOutletBreakTime[dayRaw] && currOutletBreakTime[dayRaw].length > 0)
                                                    //   ?
                                                    //   currOutletBreakTime[dayRaw].map(breakTimeGroup => breakTimeGroup).join('\n')
                                                    //   :
                                                    //   '-'
                                                    (endTimeStr)
                                                      ?
                                                      endTimeStr
                                                      :
                                                      '-'
                                                  }
                                                </Text>
                                                {/* <MaterialIcons
                                          name="keyboard-arrow-down"
                                          size={switchMerchant ? 15 : 20}
                                          style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                        /> */}
                                              </View>
                                            </TouchableOpacity>
                                          </View>

                                          {/* <View style={{ flex: 1.3 }}>
                                          <TouchableOpacity
                                            // disabled={currOutletOpeningOff[WEEK[index]]}
                                            style={{}}
                                            onPress={() => {
                                              setOpenHourPickerVisible(false);
                                              setCloseHourPickerVisible(true);
                                              setEditOpeningIndex(index);
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                              <Text
                                                style={{
                                                  // color: Colors.fieldtTxtColor,
                                                  fontFamily: 'NunitoSans-Regular',
                                                  color: Colors.blackColor,
                                                  fontSize: switchMerchant ? 10 : 14,
                                                }}>
                                                {!currOutletOpeningOff[WEEK[index]] ? endTimeStr : '-'}
                                              </Text>
                                              <MaterialIcons
                                                name="keyboard-arrow-down"
                                                size={switchMerchant ? 15 : 20}
                                                style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                              />
                                            </View>
                                          </TouchableOpacity>
                                        </View> */}

                                          <View style={{
                                            flex: 0.8,
                                            justifyContent: 'center',
                                            // alignItems: 'center'                                    
                                          }}>
                                            {/* <Switch
                                      value={currOutletOpeningOff[WEEK[index]]}
                                      onSyncPress={() => {
                                        setCurrOutletOpeningOff({
                                          ...currOutletOpeningOff,
                                          [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                        });
                                      }}
                                      width={switchMerchant ? 34 : 36}
                                      circleColorActive={Colors.primaryColor}
                                      circleColorInactive={Colors.fieldtTxtColor}
                                      backgroundActive="#dddddd"
                                    /> */}
                                            <TouchableOpacity
                                              style={{
                                                // backgroundColor: 'red'
                                              }}
                                              onPress={() => {
                                                // setCurrOutletOpeningOff({
                                                //   ...currOutletOpeningOff,
                                                //   [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                                // });

                                                // setShowModalBreakTime(true);

                                                setCurrOutletBreakTime({
                                                  ...currOutletBreakTime,
                                                  [selectedBreakTimeDay]: currOutletBreakTime[selectedBreakTimeDay].filter((breakTimeStr, index) => {
                                                    return index !== selectedBreakTimeDayIndex;
                                                  }),
                                                });
                                              }}>
                                              {/* <FontAwesome5
                                              name='edit'
                                              size={23}
                                              style={{
                                                bottom: 6,
                                              }}
                                              color={Colors.primaryColor}
                                            /> */}
                                              <Icon
                                                name="minus-circle"
                                                size={switchMerchant ? 17 : 20}
                                                color="#eb3446"
                                              />
                                            </TouchableOpacity>
                                          </View>
                                        </View>
                                      );
                                    })}
                                </View>
                              </View>

                              {/* <View
                              style={{
                                width: '30%',
                                height: '20%',
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 10,
                                justifyContent: 'center',
                                alignItems: 'center',
                                padding: 13,
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  addOutlet();
                                  // setState({ addOutletWindow: false });
                                  setAddOutletWindow(false);
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: 20,
                                    color: 'white',
                                  }}>
                                  Add
                                </Text>
                              </TouchableOpacity>
                            </View> */}
                            </View>
                          </View>
                        </ModalView>

                        {/* <View style={styles.merchantDisplayView}>
                    <View style={{ justifyContent: 'center', alignItems: 'flex-start' }}>
                      <Text style={{ marginRight: 50, fontSize: 19 }}>Merchant Display</Text>
                      <View style={{ flexDirection: 'row', flex: 1, justifyContent: 'space-between', width: '80%' }}>
                        <View>
                          <Text style={{ color: Colors.fieldtTxtColor, fontSize: 15 }}>Your customer will see this picture</Text>
                        </View>
                        <View style={{ marginLeft: 30 }}>
                          <TouchableOpacity
                            onPress={() => {
                              handleChoosePhoto1();
                            }}>
                            <Text
                              style={{
                                fontSize: 15,
                                color: Colors.primaryColor,
                              }}>
                              change
                              </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>
                  <View style={styles.merchantDisplayView}>
                    <Image
                      source={{ uri: cover }}
                      style={{
                        resizeMode: 'contain',
                        width: 300,
                        height: 120,
                        backgroundColor: Colors.secondaryColor
                      }}></Image>
                  </View> */}

                        {/* opening hour */}
                        <View
                          style={{
                            //flexDirection: 'row',
                            //width: '100%',
                            marginLeft: '6%',
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flex: 1.2 }}>
                              <Text style={styles.textSize} />
                            </View>
                            <View style={{ flex: 1.3, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Opening Hours:
                              </Text>
                            </View>
                            <View style={{ flex: 1.3, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Closing Hours:
                              </Text>
                            </View>
                            <View style={{ flex: 0.8, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Off Day:
                              </Text>
                            </View>
                          </View>
                          <View style={{}}>
                            <DateTimePickerModal
                              locale="en_GB"
                              isVisible={openHourPickerVisible}
                              mode={'time'}
                              date={
                                (currOutletOpening[WEEK[editOpeningIndex]] &&
                                  currOutletOpening[WEEK[editOpeningIndex]].split('-')[0])
                                  ?
                                  moment().set({
                                    hour: parseInt((currOutletOpening[WEEK[editOpeningIndex]].split('-')[0]).slice(0, 2)),
                                    minute: parseInt((currOutletOpening[WEEK[editOpeningIndex]].split('-')[0]).slice(2, 4)),
                                  }).toDate()
                                  :
                                  new Date()
                              }
                              onConfirm={(dt) => {
                                // const date = moment(dt);
                                // var newOpenings = openings
                                // newOpenings[editOpeningIndex].startTime = date.format('hh:mmA')

                                const openingTime =
                                  currOutletOpening[WEEK[editOpeningIndex]];
                                const endTimeStr = openingTime.split('-')[1];

                                // setState({
                                //   openHourPickerVisible: !openHourPickerVisible,
                                //   closeHourPickerVisible: !closeHourPickerVisible,
                                //   openings: newOpenings
                                // });
                                setOpenHourPickerVisible(false);
                                setCloseHourPickerVisible(false);
                                setCurrOutletOpening({
                                  ...currOutletOpening,
                                  [WEEK[editOpeningIndex]]: `${moment(dt).format(
                                    'HHmm',
                                  )}-${endTimeStr}`,
                                });
                              }}
                              onCancel={() => {
                                setOpenHourPickerVisible(false);
                              }}
                            />

                            <DateTimePickerModal
                              locale="en_GB"
                              isVisible={closeHourPickerVisible}
                              mode={'time'}
                              date={
                                (currOutletOpening[WEEK[editOpeningIndex]] &&
                                  currOutletOpening[WEEK[editOpeningIndex]].split('-')[1])
                                  ?
                                  moment().set({
                                    hour: parseInt((currOutletOpening[WEEK[editOpeningIndex]].split('-')[1]).slice(0, 2)),
                                    minute: parseInt((currOutletOpening[WEEK[editOpeningIndex]].split('-')[1]).slice(2, 4)),
                                  }).toDate()
                                  :
                                  new Date()
                              }
                              onConfirm={(dt) => {
                                // const date = moment(dt);
                                // var newOpenings = openings
                                // newOpenings[editOpeningIndex].endTime = date.format('hh:mmA')

                                const openingTime =
                                  currOutletOpening[WEEK[editOpeningIndex]];
                                const startTimeStr = openingTime.split('-')[0];

                                // setState({
                                //   closeHourPickerVisible: !closeHourPickerVisible,
                                //   openings: newOpenings
                                // });
                                setOpenHourPickerVisible(false);
                                setCloseHourPickerVisible(false);
                                setCurrOutletOpening({
                                  ...currOutletOpening,
                                  [WEEK[
                                    editOpeningIndex
                                  ]]: `${startTimeStr}-${moment(dt).format(
                                    'HHmm',
                                  )}`,
                                });
                              }}
                              onCancel={() => {
                                // setState({
                                //   closeHourPickerVisible: !closeHourPickerVisible
                                // });
                                setCloseHourPickerVisible(false);
                              }}
                            />
                            {currOutletOpening &&
                              currOutletOpening.uniqueId &&
                              week.map((day, index) => {
                                // var outletDay = null
                                // //// console.log("openings", openings)
                                // for (const d of openings) {
                                //   if (d.week.toLowerCase() == day.toLowerCase()) {
                                //     outletDay = d
                                //   }
                                // }

                                var outletOpeningToday = null;

                                var startTime = '';
                                var endTime = '';
                                var startTimeStr = null;
                                var endTimeStr = null;

                                ////////////////////////////////////////////////////////

                                var outletOpeningWeek = [];

                                const weekOrders = [
                                  'monday',
                                  'tuesday',
                                  'wednesday',
                                  'thursday',
                                  'friday',
                                  'saturday',
                                  'sunday',
                                ];

                                try {
                                  const outletOpening = currOutletOpening;

                                  if (outletOpening) {
                                    outletOpeningToday =
                                      outletOpening[WEEK[index]];
                                  }

                                  if (outletOpeningToday) {
                                    startTimeStr =
                                      outletOpeningToday.split('-')[0];
                                    endTimeStr = outletOpeningToday.split('-')[1];

                                    startTime = moment(startTimeStr, 'HHmm');
                                    endTime = moment(endTimeStr, 'HHmm');

                                    // isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  }

                                  // console.log(outletOpeningToday);

                                  ////////////////////////////////////////////////////////
                                  // do for all days

                                  // for (var i = 0; i < weekOrders.length; i++) {
                                  //   const tempOpeningDay = outletOpening[weekOrders[i]];

                                  //   var startTimeStrDay = '';
                                  //   var endTimeStrDay = '';
                                  //   var isOpeningNowDay = false;

                                  //   if (tempOpeningDay && tempOpeningDay.length > 0) {
                                  //     startTimeStrDay = tempOpeningDay.split('-')[0];
                                  //     endTimeStrDay = tempOpeningDay.split('-')[1];

                                  //     const startTime = moment(startTimeStrDay, 'HHmm');
                                  //     const endTime = moment(endTimeStrDay, 'HHmm');

                                  //     isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  //   }

                                  //   outletOpeningWeek.push({
                                  //     startTimeStr: startTimeStrDay,
                                  //     endTimeStr: endTimeStrDay,
                                  //     isOpeningNow: isOpeningNowDay,
                                  //     day: i,
                                  //   });
                                  // }
                                } catch (ex) {
                                  console.error(ex);
                                }

                                return (
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      marginBottom: 10,
                                    }}>
                                    <View style={{ flex: 1.2 }}>
                                      <Text
                                        style={{
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}>
                                        {day}
                                      </Text>
                                    </View>
                                    <View style={{ flex: 1.3 }}>
                                      <TouchableOpacity
                                        disabled={currOutletOpeningOff[WEEK[index]]}
                                        onPress={() => {
                                          setOpenHourPickerVisible(true);
                                          setCloseHourPickerVisible(false);
                                          setEditOpeningIndex(index);
                                        }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          <Text
                                            style={{
                                              // color: Colors.fieldtTxtColor,
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.blackColor,
                                              fontSize: switchMerchant ? 10 : 14,
                                            }}>
                                            {!currOutletOpeningOff[WEEK[index]] ? startTimeStr : '-'}
                                          </Text>
                                          <MaterialIcons
                                            name="keyboard-arrow-down"
                                            size={switchMerchant ? 15 : 20}
                                            style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                          />
                                        </View>
                                      </TouchableOpacity>
                                    </View>

                                    <View style={{ flex: 1.3 }}>
                                      <TouchableOpacity
                                        disabled={currOutletOpeningOff[WEEK[index]]}
                                        style={{}}
                                        onPress={() => {
                                          setOpenHourPickerVisible(false);
                                          setCloseHourPickerVisible(true);
                                          setEditOpeningIndex(index);
                                        }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          <Text
                                            style={{
                                              // color: Colors.fieldtTxtColor,
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.blackColor,
                                              fontSize: switchMerchant ? 10 : 14,
                                            }}>
                                            {!currOutletOpeningOff[WEEK[index]] ? endTimeStr : '-'}
                                          </Text>
                                          <MaterialIcons
                                            name="keyboard-arrow-down"
                                            size={switchMerchant ? 15 : 20}
                                            style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                          />
                                        </View>
                                      </TouchableOpacity>
                                    </View>

                                    <View style={{
                                      flex: 0.8,
                                      justifyContent: 'center',
                                      // alignItems: 'center'                                    
                                    }}>
                                      <Switch
                                        value={currOutletOpeningOff[WEEK[index]]}
                                        onSyncPress={() => {
                                          setCurrOutletOpeningOff({
                                            ...currOutletOpeningOff,
                                            [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                          });
                                        }}
                                        width={switchMerchant ? 34 : 36}
                                        circleColorActive={Colors.primaryColor}
                                        circleColorInactive={Colors.fieldtTxtColor}
                                        backgroundActive="#dddddd"
                                      />
                                      {/* <TouchableOpacity
                                      style={{
                                        // backgroundColor: 'red'
                                      }}
                                      onPress={() => {
                                        setCurrOutletOpeningOff({
                                          ...currOutletOpeningOff,
                                          [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                        });
                                      }}>
                                      <MaterialCommunityIcons
                                        name='beach'
                                        size={30}
                                        style={{
                                          bottom: 6,
                                        }}
                                        color={currOutletOpeningOff[WEEK[index]] ? Colors.primaryColor : Colors.fieldtBgColor2}
                                      />
                                    </TouchableOpacity> */}
                                    </View>
                                  </View>
                                );
                              })}
                          </View>
                        </View>

                        {/* Grab opening hour */}
                        {/* <View
                          style={{
                            //flexDirection: 'row',
                            //width: '100%',
                            marginLeft: '6%',
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flex: 1.2 }}>
                              <Text style={styles.textSize} />
                            </View>
                            <View style={{ flex: 1.3, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Opening Hours:
                              </Text>
                            </View>
                            <View style={{ flex: 1.3, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Closing Hours:
                              </Text>
                            </View>
                            <View style={{ flex: 0.8, marginBottom: 20 }}>
                              {/* <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Off Day:
                              </Text> *
                            </View>
                          </View>
                          <View style={{}}>
                            <DateTimePickerModal
                              locale="en_GB"
                              isVisible={openHourPickerVisible}
                              mode={'time'}
                              date={
                                (currOutletOpening[WEEK[editOpeningIndex]] &&
                                  currOutletOpening[WEEK[editOpeningIndex]].split('-')[0])
                                  ?
                                  moment().set({
                                    hour: parseInt((currOutletOpening[WEEK[editOpeningIndex]].split('-')[0]).slice(0, 2)),
                                    minute: parseInt((currOutletOpening[WEEK[editOpeningIndex]].split('-')[0]).slice(2, 4)),
                                  }).toDate()
                                  :
                                  new Date()
                              }
                              onConfirm={(dt) => {
                                // const date = moment(dt);
                                // var newOpenings = openings
                                // newOpenings[editOpeningIndex].startTime = date.format('hh:mmA')

                                const openingTime =
                                  currOutletOpening[WEEK[editOpeningIndex]];
                                const endTimeStr = openingTime.split('-')[1];

                                // setState({
                                //   openHourPickerVisible: !openHourPickerVisible,
                                //   closeHourPickerVisible: !closeHourPickerVisible,
                                //   openings: newOpenings
                                // });
                                setOpenHourPickerVisible(false);
                                setCloseHourPickerVisible(false);
                                setCurrOutletOpening({
                                  ...currOutletOpening,
                                  [WEEK[editOpeningIndex]]: `${moment(dt).format(
                                    'HHmm',
                                  )}-${endTimeStr}`,
                                });
                              }}
                              onCancel={() => {
                                setOpenHourPickerVisible(false);
                              }}
                            />

                            <DateTimePickerModal
                              locale="en_GB"
                              isVisible={closeHourPickerVisible}
                              mode={'time'}
                              date={
                                (currOutletOpening[WEEK[editOpeningIndex]] &&
                                  currOutletOpening[WEEK[editOpeningIndex]].split('-')[1])
                                  ?
                                  moment().set({
                                    hour: parseInt((currOutletOpening[WEEK[editOpeningIndex]].split('-')[1]).slice(0, 2)),
                                    minute: parseInt((currOutletOpening[WEEK[editOpeningIndex]].split('-')[1]).slice(2, 4)),
                                  }).toDate()
                                  :
                                  new Date()
                              }
                              onConfirm={(dt) => {
                                // const date = moment(dt);
                                // var newOpenings = openings
                                // newOpenings[editOpeningIndex].endTime = date.format('hh:mmA')

                                const openingTime =
                                  currOutletOpening[WEEK[editOpeningIndex]];
                                const startTimeStr = openingTime.split('-')[0];

                                // setState({
                                //   closeHourPickerVisible: !closeHourPickerVisible,
                                //   openings: newOpenings
                                // });
                                setOpenHourPickerVisible(false);
                                setCloseHourPickerVisible(false);
                                setCurrOutletOpening({
                                  ...currOutletOpening,
                                  [WEEK[
                                    editOpeningIndex
                                  ]]: `${startTimeStr}-${moment(dt).format(
                                    'HHmm',
                                  )}`,
                                });
                              }}
                              onCancel={() => {
                                // setState({
                                //   closeHourPickerVisible: !closeHourPickerVisible
                                // });
                                setCloseHourPickerVisible(false);
                              }}
                            />
                            {currOutletOpening &&
                              currOutletOpening.uniqueId &&
                              week.map((day, index) => {
                                // var outletDay = null
                                // //// console.log("openings", openings)
                                // for (const d of openings) {
                                //   if (d.week.toLowerCase() == day.toLowerCase()) {
                                //     outletDay = d
                                //   }
                                // }

                                var outletOpeningToday = null;

                                var startTime = '';
                                var endTime = '';
                                var startTimeStr = null;
                                var endTimeStr = null;

                                ////////////////////////////////////////////////////////

                                var outletOpeningWeek = [];

                                const weekOrders = [
                                  'monday',
                                  'tuesday',
                                  'wednesday',
                                  'thursday',
                                  'friday',
                                  'saturday',
                                  'sunday',
                                ];

                                try {
                                  const outletOpening = currOutletOpening;

                                  if (outletOpening) {
                                    outletOpeningToday =
                                      outletOpening[WEEK[index]];
                                  }

                                  if (outletOpeningToday) {
                                    startTimeStr =
                                      outletOpeningToday.split('-')[0];
                                    endTimeStr = outletOpeningToday.split('-')[1];

                                    startTime = moment(startTimeStr, 'HHmm');
                                    endTime = moment(endTimeStr, 'HHmm');

                                    // isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  }

                                  // console.log(outletOpeningToday);

                                  ////////////////////////////////////////////////////////
                                  // do for all days

                                  // for (var i = 0; i < weekOrders.length; i++) {
                                  //   const tempOpeningDay = outletOpening[weekOrders[i]];

                                  //   var startTimeStrDay = '';
                                  //   var endTimeStrDay = '';
                                  //   var isOpeningNowDay = false;

                                  //   if (tempOpeningDay && tempOpeningDay.length > 0) {
                                  //     startTimeStrDay = tempOpeningDay.split('-')[0];
                                  //     endTimeStrDay = tempOpeningDay.split('-')[1];

                                  //     const startTime = moment(startTimeStrDay, 'HHmm');
                                  //     const endTime = moment(endTimeStrDay, 'HHmm');

                                  //     isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  //   }

                                  //   outletOpeningWeek.push({
                                  //     startTimeStr: startTimeStrDay,
                                  //     endTimeStr: endTimeStrDay,
                                  //     isOpeningNow: isOpeningNowDay,
                                  //     day: i,
                                  //   });
                                  // }
                                } catch (ex) {
                                  console.error(ex);
                                }

                                return (
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      marginBottom: 10,
                                    }}>
                                    <View style={{ flex: 1.2 }}>
                                      <Text
                                        style={{
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}>
                                        {day}
                                      </Text>
                                    </View>
                                    <View style={{ flex: 1.3 }}>
                                      <TouchableOpacity
                                        disabled={currOutletOpeningOff[WEEK[index]]}
                                        onPress={() => {
                                          setOpenHourPickerVisible(true);
                                          setCloseHourPickerVisible(false);
                                          setEditOpeningIndex(index);
                                        }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          <Text
                                            style={{
                                              // color: Colors.fieldtTxtColor,
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.blackColor,
                                              fontSize: switchMerchant ? 10 : 14,
                                            }}>
                                            {!currOutletOpeningOff[WEEK[index]] ? startTimeStr : '-'}
                                          </Text>
                                          <MaterialIcons
                                            name="keyboard-arrow-down"
                                            size={switchMerchant ? 15 : 20}
                                            style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                          />
                                        </View>
                                      </TouchableOpacity>
                                    </View>

                                    <View style={{ flex: 1.3 }}>
                                      <TouchableOpacity
                                        disabled={currOutletOpeningOff[WEEK[index]]}
                                        style={{}}
                                        onPress={() => {
                                          setOpenHourPickerVisible(false);
                                          setCloseHourPickerVisible(true);
                                          setEditOpeningIndex(index);
                                        }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          <Text
                                            style={{
                                              // color: Colors.fieldtTxtColor,
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.blackColor,
                                              fontSize: switchMerchant ? 10 : 14,
                                            }}>
                                            {!currOutletOpeningOff[WEEK[index]] ? endTimeStr : '-'}
                                          </Text>
                                          <MaterialIcons
                                            name="keyboard-arrow-down"
                                            size={switchMerchant ? 15 : 20}
                                            style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                          />
                                        </View>
                                      </TouchableOpacity>
                                    </View>

                                    <View style={{
                                      flex: 0.8,
                                      justifyContent: 'center',
                                      // alignItems: 'center'                                    
                                    }}>
                                      <Switch
                                        value={currOutletOpeningOff[WEEK[index]]}
                                        onSyncPress={() => {
                                          setCurrOutletOpeningOff({
                                            ...currOutletOpeningOff,
                                            [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                          });
                                        }}
                                        width={switchMerchant ? 34 : 36}
                                        circleColorActive={Colors.primaryColor}
                                        circleColorInactive={Colors.fieldtTxtColor}
                                        backgroundActive="#dddddd"
                                      />
                                      {/* <TouchableOpacity
                                      style={{
                                        // backgroundColor: 'red'
                                      }}
                                      onPress={() => {
                                        setCurrOutletOpeningOff({
                                          ...currOutletOpeningOff,
                                          [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                        });
                                      }}>
                                      <MaterialCommunityIcons
                                        name='beach'
                                        size={30}
                                        style={{
                                          bottom: 6,
                                        }}
                                        color={currOutletOpeningOff[WEEK[index]] ? Colors.primaryColor : Colors.fieldtBgColor2}
                                      />
                                    </TouchableOpacity> *
                                    </View>
                                  </View>
                                );
                              })}
                          </View>
                        </View> */}
                        {/* break time */}

                        <View
                          style={{
                            //flexDirection: 'row',
                            //width: '100%',
                            marginLeft: '6%',

                            marginTop: '5%',
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flex: 1.2 }}>
                              <Text style={styles.textSize} />
                            </View>
                            <View style={{ flex: 2.6, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Break Time:
                              </Text>
                            </View>
                            <View style={{ flex: 0.8, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Action:
                              </Text>
                            </View>
                          </View>
                          <View style={{}}>
                            <DateTimePickerModal
                              locale="en_GB"
                              isVisible={showTimePickerBreakTimeStart}
                              mode={'time'}
                              date={
                                (currOutletBreakTime[selectedBreakTimeDay] && currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex] &&
                                  currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex].split('-')[0])
                                  ?
                                  moment().set({
                                    hour: parseInt((currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex].split('-')[0]).slice(0, 2)),
                                    minute: parseInt((currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex].split('-')[0]).slice(2, 4)),
                                  }).toDate()
                                  :
                                  new Date()
                              }
                              onConfirm={(dt) => {
                                // const date = moment(dt);
                                // var newOpenings = openings
                                // newOpenings[editOpeningIndex].startTime = date.format('hh:mmA')

                                const openingTime =
                                  currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex];
                                const endTimeStr = openingTime.split('-')[1];

                                // setState({
                                //   openHourPickerVisible: !openHourPickerVisible,
                                //   closeHourPickerVisible: !closeHourPickerVisible,
                                //   openings: newOpenings
                                // });

                                setShowTimePickerBreakTimeStart(false);
                                setShowTimePickerBreakTimeEnd(false);
                                setCurrOutletBreakTime({
                                  ...currOutletBreakTime,
                                  [selectedBreakTimeDay]: currOutletBreakTime[selectedBreakTimeDay].map((breakTimeStr, index) => {
                                    return index === selectedBreakTimeDayIndex
                                      ?
                                      `${moment(dt).format(
                                        'HHmm',
                                      )}-${endTimeStr}`
                                      :
                                      breakTimeStr;
                                  }),
                                });

                                // setOpenHourPickerVisible(false);
                                // setCloseHourPickerVisible(false);
                                // setCurrOutletOpening({
                                //   ...currOutletOpening,
                                //   [WEEK[editOpeningIndex]]: `${moment(dt).format(
                                //     'HHmm',
                                //   )}-${endTimeStr}`,
                                // });
                              }}
                              onCancel={() => {
                                setShowTimePickerBreakTimeStart(false);
                                // setOpenHourPickerVisible(false);
                              }}
                            />

                            <DateTimePickerModal
                              locale="en_GB"
                              isVisible={showTimePickerBreakTimeEnd}
                              mode={'time'}
                              date={
                                (currOutletBreakTime[selectedBreakTimeDay] && currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex] &&
                                  currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex].split('-')[1])
                                  ?
                                  moment().set({
                                    hour: parseInt((currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex].split('-')[1]).slice(0, 2)),
                                    minute: parseInt((currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex].split('-')[1]).slice(2, 4)),
                                  }).toDate()
                                  :
                                  new Date()
                              }
                              onConfirm={(dt) => {
                                // const date = moment(dt);
                                // var newOpenings = openings
                                // newOpenings[editOpeningIndex].endTime = date.format('hh:mmA')

                                const openingTime =
                                  currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex];
                                const startTimeStr = openingTime.split('-')[0];

                                // setState({
                                //   closeHourPickerVisible: !closeHourPickerVisible,
                                //   openings: newOpenings
                                // });
                                // setOpenHourPickerVisible(false);
                                // setCloseHourPickerVisible(false);
                                // setCurrOutletOpening({
                                //   ...currOutletOpening,
                                //   [WEEK[
                                //     editOpeningIndex
                                //   ]]: `${startTimeStr}-${moment(dt).format(
                                //     'HHmm',
                                //   )}`,
                                // });

                                setShowTimePickerBreakTimeStart(false);
                                setShowTimePickerBreakTimeEnd(false);
                                setCurrOutletBreakTime({
                                  ...currOutletBreakTime,
                                  [selectedBreakTimeDay]: currOutletBreakTime[selectedBreakTimeDay].map((breakTimeStr, index) => {
                                    return index === selectedBreakTimeDayIndex
                                      ?
                                      `${startTimeStr}-${moment(dt).format(
                                        'HHmm',
                                      )}`
                                      :
                                      breakTimeStr;
                                  }),
                                });
                              }}
                              onCancel={() => {
                                // setState({
                                //   closeHourPickerVisible: !closeHourPickerVisible
                                // });
                                // setCloseHourPickerVisible(false);
                                setShowTimePickerBreakTimeEnd(false);
                              }}
                            />

                            {currOutletBreakTime &&
                              week.map((day, index) => {
                                // var outletDay = null
                                // //// console.log("openings", openings)
                                // for (const d of openings) {
                                //   if (d.week.toLowerCase() == day.toLowerCase()) {
                                //     outletDay = d
                                //   }
                                // }

                                var outletOpeningToday = null;

                                var startTime = '';
                                var endTime = '';
                                var startTimeStr = null;
                                var endTimeStr = null;

                                ////////////////////////////////////////////////////////

                                var outletOpeningWeek = [];

                                const weekOrders = [
                                  'monday',
                                  'tuesday',
                                  'wednesday',
                                  'thursday',
                                  'friday',
                                  'saturday',
                                  'sunday',
                                ];

                                const dayRaw = weekOrders[index];

                                try {
                                  const outletOpening = currOutletOpening;

                                  if (outletOpening) {
                                    outletOpeningToday =
                                      outletOpening[WEEK[index]];
                                  }

                                  if (outletOpeningToday) {
                                    startTimeStr =
                                      outletOpeningToday.split('-')[0];
                                    endTimeStr = outletOpeningToday.split('-')[1];

                                    startTime = moment(startTimeStr, 'HHmm');
                                    endTime = moment(endTimeStr, 'HHmm');

                                    // isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  }

                                  // console.log(outletOpeningToday);

                                  ////////////////////////////////////////////////////////
                                  // do for all days

                                  // for (var i = 0; i < weekOrders.length; i++) {
                                  //   const tempOpeningDay = outletOpening[weekOrders[i]];

                                  //   var startTimeStrDay = '';
                                  //   var endTimeStrDay = '';
                                  //   var isOpeningNowDay = false;

                                  //   if (tempOpeningDay && tempOpeningDay.length > 0) {
                                  //     startTimeStrDay = tempOpeningDay.split('-')[0];
                                  //     endTimeStrDay = tempOpeningDay.split('-')[1];

                                  //     const startTime = moment(startTimeStrDay, 'HHmm');
                                  //     const endTime = moment(endTimeStrDay, 'HHmm');

                                  //     isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  //   }

                                  //   outletOpeningWeek.push({
                                  //     startTimeStr: startTimeStrDay,
                                  //     endTimeStr: endTimeStrDay,
                                  //     isOpeningNow: isOpeningNowDay,
                                  //     day: i,
                                  //   });
                                  // }
                                } catch (ex) {
                                  console.error(ex);
                                }

                                return (
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      marginBottom: 10,
                                    }}>
                                    <View style={{ flex: 1.2 }}>
                                      <Text
                                        style={{
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}>
                                        {day}
                                      </Text>
                                    </View>
                                    <View style={{ flex: 2.6, paddingRight: '0%' }}>
                                      <View style={{ flexDirection: 'row', paddingRight: '10%' }}>
                                        <Text
                                          style={{
                                            // color: Colors.fieldtTxtColor,
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.blackColor,
                                            fontSize: switchMerchant ? 10 : 14,
                                          }}>
                                          {
                                            currOutletOpeningOff[dayRaw]
                                              ?
                                              '-'
                                              :
                                              (
                                                (currOutletBreakTime[dayRaw] && currOutletBreakTime[dayRaw].length > 0)
                                                  ?
                                                  currOutletBreakTime[dayRaw].map(breakTimeGroup => breakTimeGroup).join('\n')
                                                  :
                                                  '-'
                                              )
                                          }
                                        </Text>
                                        {/* <MaterialIcons
                                          name="keyboard-arrow-down"
                                          size={switchMerchant ? 15 : 20}
                                          style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                        /> */}
                                      </View>
                                    </View>

                                    {/* <View style={{ flex: 1.3 }}>
                                    <TouchableOpacity
                                      disabled={currOutletOpeningOff[WEEK[index]]}
                                      style={{}}
                                      onPress={() => {
                                        setOpenHourPickerVisible(false);
                                        setCloseHourPickerVisible(true);
                                        setEditOpeningIndex(index);
                                      }}>
                                      <View style={{ flexDirection: 'row' }}>
                                        <Text
                                          style={{
                                            // color: Colors.fieldtTxtColor,
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.blackColor,
                                            fontSize: switchMerchant ? 10 : 14,
                                          }}>
                                          {!currOutletOpeningOff[WEEK[index]] ? endTimeStr : '-'}
                                        </Text>
                                        <MaterialIcons
                                          name="keyboard-arrow-down"
                                          size={switchMerchant ? 15 : 20}
                                          style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                        />
                                      </View>
                                    </TouchableOpacity>
                                  </View> */}

                                    <View style={{
                                      flex: 0.8,
                                      justifyContent: 'center',
                                      // alignItems: 'center'                                    
                                    }}>
                                      {/* <Switch
                                      value={currOutletOpeningOff[WEEK[index]]}
                                      onSyncPress={() => {
                                        setCurrOutletOpeningOff({
                                          ...currOutletOpeningOff,
                                          [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                        });
                                      }}
                                      width={switchMerchant ? 34 : 36}
                                      circleColorActive={Colors.primaryColor}
                                      circleColorInactive={Colors.fieldtTxtColor}
                                      backgroundActive="#dddddd"
                                    /> */}
                                      <TouchableOpacity
                                        disabled={currOutletOpeningOff[dayRaw]}
                                        style={{
                                          // backgroundColor: 'red'
                                        }}
                                        onPress={() => {
                                          // setCurrOutletOpeningOff({
                                          //   ...currOutletOpeningOff,
                                          //   [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                          // });

                                          setSelectedBreakTimeDayIndex(0);
                                          setSelectedBreakTimeDay(dayRaw);

                                          setShowModalBreakTime(true);
                                        }}>
                                        <FontAwesome5
                                          name='edit'
                                          size={23}
                                          style={{
                                            bottom: 6,
                                          }}
                                          color={currOutletOpeningOff[dayRaw] ? Colors.fieldtTxtColor : Colors.primaryColor}
                                        />
                                      </TouchableOpacity>
                                    </View>
                                  </View>
                                );
                              })}
                          </View>
                        </View>

                        <View style={[styles.container, {
                          zIndex: -102,
                          paddingLeft: '6%',
                          marginVertical: '11%',
                        }]}>
                          <View>
                            <Text
                              style={[
                                styles.textSize,
                                {
                                  width: windowWidth * 0.2,
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                },
                              ]}>
                              {'Retail Mode'}
                            </Text>
                          </View>
                          <Switch
                            width={42}
                            style={{
                              //flexDirection: 'row',
                              //width: '15%',
                              marginRight: 20,
                              // marginLeft: 20,
                              //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                              bottom: -2,
                            }}
                            value={retailMode}
                            onSyncPress={async (statusTemp) => {
                              // setState({ status: status })
                              setRetailMode(statusTemp);

                              TableStore.update(s => {
                                s.retailMode = statusTemp;
                              })

                              // console.log(statusTemp);

                              // await AsyncStorage.setItem('toggleDisablePrintingAlert', statusTemp ? '1' : '0');

                              // global.outletToggleDisablePrintingAlert = statusTemp ? true : false;
                            }}
                            circleColorActive={Colors.primaryColor}
                            circleColorInactive={Colors.fieldtTxtColor}
                            backgroundActive="#dddddd"
                          />
                        </View>

                        <View
                          style={{
                            marginTop: 20,
                            width: '100%',
                            // backgroundColor: 'red',

                            // flexDirection: 'row',
                            // alignItems: 'center',
                            // justifyContent: 'center',
                          }}>
                          {/* <View
                      style={{
                        // backgroundColor: 'red',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        margin: 15,
                      }}> */}

                          {/* <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 15,
                          color: Colors.fieldtTxtColor,
                          // width: '100%',
                          backgroundColor: Colors.fieldtBgColor,
                          width: 140,
                          textAlign: 'center',
                          height: 40,
                          borderRadius: 8,
                          marginBottom: 10
                        }}
                        placeholder="Printer Name"
                        onChangeText={(text) => {
                          // setState({ addOutletName: text });
                          // setAddOutletName(text);
                          setPrinterName(text);
                        }}
                        defaultValue={printerName}
                        // keyboardType={'decimal-pad'}
                      />       */}

                          {/* <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 15,
                          color: Colors.fieldtTxtColor,
                          // width: '100%',
                          backgroundColor: Colors.fieldtBgColor,
                          width: 140,
                          textAlign: 'center',
                          height: 40,
                          borderRadius: 8,
                        }}
                        placeholder="Printer IP"
                        onChangeText={(text) => {
                          // setState({ addOutletName: text });
                          // setAddOutletName(text);
                          setPrinterIP(text);
                        }}
                        defaultValue={printerIP}
                        keyboardType={'decimal-pad'}
                      /> */}

                          {/* <View
                      style={{
                        // backgroundColor: 'red',

                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: 15,

                      }}> */}

                          {/* <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                              width: 0,
                              height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginBottom: 10,
                        }}
                        onPress={() => {
                          // setState({ addOutletWindow: true })
                          bindPrinter();
                        }}>
                        <View>
                          <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                          }}>
                            BIND PRINTER IP
                          </Text>
                        </View>
                      </TouchableOpacity> */}

                          {/* <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 180,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                              width: 0,
                              height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginBottom: 10,
                        }}
                        onPress={() => {
                          unbindPrinter();
                        }}>
                        <View>
                          <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                          }}>
                            UNBIND PRINTER IP
                          </Text>
                        </View>
                      </TouchableOpacity> */}

                          {/* <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                              width: 0,
                              height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          testPrinter();
                        }}>
                        <View>
                          <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                          }}>
                            TEST PRINTER
                          </Text>
                        </View>
                      </TouchableOpacity> */}
                          {/* </View> */}
                          {/* </View> */}
                        </View>
                      </View>
                    </View>
                    <View
                      style={{
                        width: '100%',
                        alignItems: 'center',
                        marginBottom: 30,
                        zIndex: -1000,
                        // borderWidth: 1,
                      }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -4,
                        }}
                        disabled={loading}
                        onPress={() => {
                          // update();
                          // editOutlet();
                          if (merchantName !== '') {
                            updateOutletDetails();
                          }
                          else {
                            Alert.alert('Info', 'The merchant name cannot be empty', [
                              {
                                text: 'OK',
                                onPress: () => {
                                  setLoading(false);
                                },
                              },
                            ],
                              { cancelable: false },
                            );
                          }
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          {loading ? 'LOADING...' : 'SAVE'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View
                      style={{
                        height: 120,
                      }} />
                    <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginHorizontal: '2%', marginBottom: '2%' }}>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', }}>Offline Ready: {isOfflineReady ? 'Yes | ' : 'No | '}</Text>

                      <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', }}>Version: </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Regular' }}>{version}</Text>
                    </View>
                  </KeyboardAwareScrollView>
                </View>
              ) : null}
            </View>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: '100%',
    height: '100%',
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,

    // paddingLeft: '5%',
    paddingRight: '10%',
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 75,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 110,
    height: Platform.OS == 'ios' ? 30 : 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput10: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput1: {
    fontFamily: 'NunitoSans-Regular',
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: 'NunitoSans-Regular',
    width: '85%',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 10,
  },
  textInput4: {
    width: '85%',
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: 'NunitoSans-Regular',
    width: '80 %',
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: 'center',
  },
  textInput7: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button1: {
    width: '15%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: '60%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: '2%',
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: '30%',
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 30,
  },
  textSize: {
    fontSize: 16,
    fontFamily: 'NunitoSans-SemiBold',
  },
  viewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0,
    width: '100%',
    marginBottom: 15,
  },
  openHourContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    //justifyContent: 'space-between',
    //flex: 1,
    marginBottom: 15,
    //width: '100%',
    //paddingRight: 30,
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
  },
  addButtonView1: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 65,
    marginTop: 0,
    width: '83%',
    alignSelf: 'flex-end',
  },

  addNewView1: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    alignItems: 'center',
  },
  addNewView2: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 65,
    marginTop: 7,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftView: {
    flexDirection: 'row',
    backgroundColor: Colors.fieldtBgColor2,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,

    fontFamily: 'NunitoSans-Bold',
    fontSize: 25,
  },
  closeView: {
    flexDirection: 'row',
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 250,
    height: 40,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
    marginBottom: '20%',
  },
  taxView: {
    flexDirection: 'row',
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'flex-end',
  },
  sectionView: {
    flexDirection: 'row',
    borderRadius: 5,
    padding: 16,
    alignItems: 'center',
  },
  receiptView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  circleIcon2: {
    width: 120,
    height: 120,
    resizeMode: 'contain',
    flex: 1,
  },
  circleIcon: {
    width: 150,
    height: 150,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default SettingScreen;
