diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/a1968877989d8648ac1d88df5c7ec927/results.bin b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/a1968877989d8648ac1d88df5c7ec927/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/a1968877989d8648ac1d88df5c7ec927/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/a1968877989d8648ac1d88df5c7ec927/transformed/classes/classes_dex/classes.dex b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/a1968877989d8648ac1d88df5c7ec927/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..6d2ac64
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/a1968877989d8648ac1d88df5c7ec927/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/c5afb052fd68ff61c060cabff09cc36a/results.bin b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/c5afb052fd68ff61c060cabff09cc36a/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/c5afb052fd68ff61c060cabff09cc36a/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/c5afb052fd68ff61c060cabff09cc36a/transformed/classes/classes_dex/classes.dex b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/c5afb052fd68ff61c060cabff09cc36a/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..a2691c1
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/.transforms/c5afb052fd68ff61c060cabff09cc36a/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/generated/source/buildConfig/debug/com/pinmi/react/printer/BuildConfig.java b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/generated/source/buildConfig/debug/com/pinmi/react/printer/BuildConfig.java
new file mode 100644
index 0000000..e037878
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/generated/source/buildConfig/debug/com/pinmi/react/printer/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.pinmi.react.printer;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.pinmi.react.printer";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..14230dd
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,15 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.pinmi.react.printer" >
+
+    <uses-sdk android:minSdkVersion="26" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.BLUETOOTH" />
+    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+    <uses-permission android:name="android.hardware.usb.UsbAccessory" />
+    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
+    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
+    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..30215ef
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.pinmi.react.printer",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..988c5ea
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..f08e397
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..a9551e5
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/compile_symbol_list/debug/R.txt
@@ -0,0 +1 @@
+int string app_name 0x0
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..466cf3a
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Thu Jan 02 10:04:14 MYT 2025
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
new file mode 100644
index 0000000..88fa3fc
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="app_name">RNPrinter</string>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..36b4fe7
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\res"><file path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">RNPrinter</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..221ffef
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\debug\jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..8cbba1d
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\debug\shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..20d89b2
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\build\intermediates\shader_assets\debug\out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/BuildConfig.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/BuildConfig.class
new file mode 100644
index 0000000..a355c37
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/BuildConfig.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNBLEPrinterModule.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNBLEPrinterModule.class
new file mode 100644
index 0000000..f146c55
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNBLEPrinterModule.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNNetPrinterModule.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNNetPrinterModule.class
new file mode 100644
index 0000000..360286c
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNNetPrinterModule.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNPrinterModule.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNPrinterModule.class
new file mode 100644
index 0000000..8c53a97
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNPrinterModule.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNPrinterPackage.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNPrinterPackage.class
new file mode 100644
index 0000000..5eb6d2a
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNPrinterPackage.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNUSBPrinterModule.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNUSBPrinterModule.class
new file mode 100644
index 0000000..3c83a0c
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/RNUSBPrinterModule.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterAdapter$1.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterAdapter$1.class
new file mode 100644
index 0000000..055eb28
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterAdapter$1.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterAdapter.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterAdapter.class
new file mode 100644
index 0000000..476a2bd
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterAdapter.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterDevice.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterDevice.class
new file mode 100644
index 0000000..45d5266
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterDevice.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.class
new file mode 100644
index 0000000..3e11793
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/BLEPrinterDeviceId.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.class
new file mode 100644
index 0000000..4a822e9
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$1.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$2.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$2.class
new file mode 100644
index 0000000..8656786
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$2.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$3.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$3.class
new file mode 100644
index 0000000..11e81be
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter$3.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter.class
new file mode 100644
index 0000000..b603e5f
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterAdapter.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterDevice.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterDevice.class
new file mode 100644
index 0000000..3add4c1
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterDevice.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterDeviceId.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterDeviceId.class
new file mode 100644
index 0000000..d7bec49
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/NetPrinterDeviceId.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/PrinterAdapter.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/PrinterAdapter.class
new file mode 100644
index 0000000..98266a2
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/PrinterAdapter.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/PrinterDevice.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/PrinterDevice.class
new file mode 100644
index 0000000..dc5e0c1
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/PrinterDevice.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/PrinterDeviceId.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/PrinterDeviceId.class
new file mode 100644
index 0000000..045b7b8
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/PrinterDeviceId.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.class
new file mode 100644
index 0000000..aad7d17
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter$1.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter$2.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter$2.class
new file mode 100644
index 0000000..d07884a
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter$2.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter.class
new file mode 100644
index 0000000..467b604
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterAdapter.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterDevice.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterDevice.class
new file mode 100644
index 0000000..f65c44b
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterDevice.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterDeviceId.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterDeviceId.class
new file mode 100644
index 0000000..fcf8894
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/USBPrinterDeviceId.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/UtilsImage.class b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/UtilsImage.class
new file mode 100644
index 0000000..759571c
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/javac/debug/classes/com/pinmi/react/printer/adapter/UtilsImage.class differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..34ca0ec
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,3 @@
+R_DEF: Internal format may change without notice
+local
+string app_name
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..ebb40d6
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,29 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.pinmi.react.printer" >
+4
+5    <uses-sdk android:minSdkVersion="26" />
+6
+7    <uses-permission android:name="android.permission.INTERNET" />
+7-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:4:5-67
+7-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:4:22-64
+8    <uses-permission android:name="android.permission.BLUETOOTH" />
+8-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:5:5-68
+8-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:5:22-65
+9    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+9-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:6:5-74
+9-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:6:22-71
+10    <uses-permission android:name="android.hardware.usb.UsbAccessory" />
+10-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:7:5-73
+10-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:7:22-70
+11    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
+11-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:8:5-76
+11-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:8:22-73
+12    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
+12-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:10:5-76
+12-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:10:22-73
+13    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
+13-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:11:5-73
+13-->C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:11:22-70
+14
+15</manifest>
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..14230dd
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,15 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.pinmi.react.printer" >
+
+    <uses-sdk android:minSdkVersion="26" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.BLUETOOTH" />
+    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
+    <uses-permission android:name="android.hardware.usb.UsbAccessory" />
+    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
+    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
+    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/packaged_res/debug/values/values.xml b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/packaged_res/debug/values/values.xml
new file mode 100644
index 0000000..88fa3fc
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/packaged_res/debug/values/values.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="app_name">RNPrinter</string>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..10719cd
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..b08a4fe
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1,2 @@
+com.pinmi.react.printer
+string app_name
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..82420d1
--- /dev/null
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,45 @@
+-- Merging decision tree log ---
+manifest
+ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:1:1-13:12
+INJECTED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:1:1-13:12
+	package
+		ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:2:5-38
+		INJECTED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml
+	xmlns:android
+		ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:1:11-69
+uses-permission#android.permission.INTERNET
+ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:4:5-67
+	android:name
+		ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:4:22-64
+uses-permission#android.permission.BLUETOOTH
+ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:5:5-68
+	android:name
+		ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:5:22-65
+uses-permission#android.permission.BLUETOOTH_ADMIN
+ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:6:5-74
+	android:name
+		ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:6:22-71
+uses-permission#android.hardware.usb.UsbAccessory
+ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:7:5-73
+	android:name
+		ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:7:22-70
+uses-permission#android.permission.ACCESS_WIFI_STATE
+ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:8:5-76
+	android:name
+		ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:8:22-73
+uses-permission#android.permission.BLUETOOTH_CONNECT
+ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:10:5-76
+	android:name
+		ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:10:22-73
+uses-permission#android.permission.BLUETOOTH_SCAN
+ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:11:5-73
+	android:name
+		ADDED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml:11:22-70
+uses-sdk
+INJECTED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml
+INJECTED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from C:\Users\<USER>\Desktop\KooDoo\KooDooApp\koodoo-merchant-v2\node_modules\@conodene\react-native-thermal-receipt-printer-image-qr\android\src\main\AndroidManifest.xml
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BLEPrinterAdapter$1.class.uniqueId1 b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BLEPrinterAdapter$1.class.uniqueId1
new file mode 100644
index 0000000..055eb28
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BLEPrinterAdapter$1.class.uniqueId1 differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BLEPrinterAdapter.class.uniqueId0 b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BLEPrinterAdapter.class.uniqueId0
new file mode 100644
index 0000000..77ea1aa
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/BLEPrinterAdapter.class.uniqueId0 differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/RNBLEPrinterModule.class.uniqueId2 b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/RNBLEPrinterModule.class.uniqueId2
new file mode 100644
index 0000000..4167225
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/RNBLEPrinterModule.class.uniqueId2 differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/RNPrinterPackage.class.uniqueId3 b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/RNPrinterPackage.class.uniqueId3
new file mode 100644
index 0000000..5eb6d2a
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/compileTransaction/stash-dir/RNPrinterPackage.class.uniqueId3 differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..9cc14ca
Binary files /dev/null and b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/src/main/java/com/pinmi/react/printer/RNBLEPrinterModule.java b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/src/main/java/com/pinmi/react/printer/RNBLEPrinterModule.java
index 6bec1f5..8533516 100644
--- a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/src/main/java/com/pinmi/react/printer/RNBLEPrinterModule.java
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/src/main/java/com/pinmi/react/printer/RNBLEPrinterModule.java
@@ -67,10 +67,10 @@ public class RNBLEPrinterModule extends ReactContextBaseJavaModule implements RN
         }
 
         // Check permission
-        if (reactContext.checkSelfPermission(android.Manifest.permission.BLUETOOTH) != PackageManager.PERMISSION_GRANTED) {
-            errorCallback.invoke("Bluetooth permission is not granted");
-            return;
-        }
+        // if (reactContext.checkSelfPermission(android.Manifest.permission.BLUETOOTH) != PackageManager.PERMISSION_GRANTED) {
+        //     errorCallback.invoke("Bluetooth permission is not granted");
+        //     return;
+        // }
 
         if(printerDevices.size() > 0) {
             for (PrinterDevice printerDevice : printerDevices) {
diff --git a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/src/main/java/com/pinmi/react/printer/adapter/BLEPrinterAdapter.java b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/src/main/java/com/pinmi/react/printer/adapter/BLEPrinterAdapter.java
index 8e66ed0..8fb4d38 100644
--- a/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/src/main/java/com/pinmi/react/printer/adapter/BLEPrinterAdapter.java
+++ b/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/android/src/main/java/com/pinmi/react/printer/adapter/BLEPrinterAdapter.java
@@ -97,9 +97,9 @@ public class BLEPrinterAdapter implements PrinterAdapter{
         }
         
         // Check for Bluetooth permission
-        if (mContext.checkSelfPermission(android.Manifest.permission.BLUETOOTH) != PackageManager.PERMISSION_GRANTED) {
-            return printerDevices;
-        }
+        // if (mContext.checkSelfPermission(android.Manifest.permission.BLUETOOTH) != PackageManager.PERMISSION_GRANTED) {
+        //     return printerDevices;
+        // }
 
         Set<BluetoothDevice> pairedDevices = getBTAdapter().getBondedDevices();
         for (BluetoothDevice device : pairedDevices) {
@@ -121,10 +121,10 @@ public class BLEPrinterAdapter implements PrinterAdapter{
         }
 
         // Check for Bluetooth permission
-        if (mContext.checkSelfPermission(android.Manifest.permission.BLUETOOTH) != PackageManager.PERMISSION_GRANTED) {
-            errorCallback.invoke("Bluetooth permission is not granted");
-            return;
-        }
+        // if (mContext.checkSelfPermission(android.Manifest.permission.BLUETOOTH) != PackageManager.PERMISSION_GRANTED) {
+        //     errorCallback.invoke("Bluetooth permission is not granted");
+        //     return;
+        // }
 
         BLEPrinterDeviceId blePrinterDeviceId = (BLEPrinterDeviceId)printerDeviceId;
         if(this.mBluetoothDevice != null){
