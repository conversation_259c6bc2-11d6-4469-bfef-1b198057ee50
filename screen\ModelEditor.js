import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
    StyleSheet, Image, // View,
    Alert, TouchableOpacity, Dimensions, TextInput, Modal as ModalComponent,
    PermissionsAndroid, Platform, ActivityIndicator, useWindowDimensions
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Feather from 'react-native-vector-icons/Feather';
import Colors from '../constant/Colors';
import Close from 'react-native-vector-icons/AntDesign';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import ImagePicker from 'react-native-image-picker';
import API from '../constant/API';
import { isEnabled } from 'react-native/Libraries/Performance/Systrace';
import DropDownPicker from 'react-native-dropdown-picker';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ListItem, { Separator } from './ListItem';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import VirtualizedView from './VirtualizedView';
import Switch from 'react-native-switch-pro';
import RNFetchBlob from 'rn-fetch-blob';
import DocumentPicker from 'react-native-document-picker';
import {
    getTransformForModalInsideNavigation,
    getTransformForScreenInsideNavigation,
    isTablet
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import {
    autofitColumns,
    convertArrayToCSV,
    generateEmailReport,
    getImageFromFirebaseStorage,
    getPathForFirebaseStorageFromBlob,
    uploadFileToFirebaseStorage,
} from '../util/common';
import moment from 'moment';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import XLSX from 'xlsx';
import { zip, unzip, unzipAssets, subscribe } from 'react-native-zip-archive';
const RNFS = require('@dr.pogodin/react-native-fs');
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import {
    EMAIL_REPORT_TYPE,
    PRODUCT_SORT,
    PRODUCT_SORT_VALUE,
    PRODUCT_SORT_COMPARE_OPERATOR,
    PRODUCT_SORT_FIELD_TYPE_COMPARE,
    EXPAND_TAB_TYPE,
    ROLE_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import Entypo from 'react-native-vector-icons/Entypo';
import APILocal from '../util/apiLocalReplacers';
import {
    ORDER_TYPE,
    ORDER_TYPE_PARSED,
    PURCHASE_ORDER_STATUS,
} from '../constant/common';

import { NestableScrollContainer, NestableDraggableFlatList } from 'react-native-draggable-flatlist';
import Tooltip from 'react-native-walkthrough-tooltip';

import CheckBox from '@react-native-community/checkbox';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { FlashList } from "@shopify/flash-list";
import { useFocusEffect } from "@react-navigation/native";
import { extname } from 'path';

const View = require(
    'react-native/Libraries/Components/View/ViewNativeComponent'
).default;
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const ModelEditor = React.memo((props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    //////////////////////////////////////////////////////////////

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const allOutlets = MerchantStore.useState((s) => s.allOutlets);

    const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );

    //////////////////////////////////////////////////////////////

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity       
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={{
                        width: 124,
                        height: 26,
                    }}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        ...global.getHeaderTitleStyle(),
                    },
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Model Editor
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >

                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    const [keyboardHeight] = useKeyboard();
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);


    // const [templateImage, setTemplateImage] = useState('');

    /////////////////////////////////////////////////////////////

    const [outletItems, setOutletItems] = useState([]);

    const [importModal, setImportModal] = useState(false);
    const [exportModal, setExportModal] = useState(false);

    const [exportModalVisibility, setExportModalVisibility] = useState(false);
    const [exportEmail, setExportEmail] = useState('');

    const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);
    const outletCategories = OutletStore.useState((s) => s.outletCategories);
    const outletCategoriesDict = OutletStore.useState(
        (s) => s.outletCategoriesDict,
    );

    const selectedOutletCategoryEdit = CommonStore.useState(
        (s) => s.selectedOutletCategoryEdit,
    );

    const isLoading = CommonStore.useState((s) => s.isLoading);
    const isLoadingExportCsv = CommonStore.useState((s) => s.isLoadingExportCsv);
    const isLoadingExportExcel = CommonStore.useState(
        (s) => s.isLoadingExportExcel,
    );

    const [exportExcelLoading, setExportExcelLoading] = useState(false);
    const [exportCSVLoading, setExportCSVLoading] = useState(false);

    const firebaseUid = UserStore.useState((s) => s.firebaseUid);
    const userName = UserStore.useState((s) => s.name);
    const merchantName = MerchantStore.useState((s) => s.name);
    const merchantLogo = MerchantStore.useState((s) => s.logo);
    const merchantId = UserStore.useState((s) => s.merchantId);
    const [productSort, setProductSort] = useState('');

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const [supported3DFormats] = useState(['.glb', '.gltf', '.obj', '.fbx', '.stl']);

    const handleFileUpload = async () => {
    const res = await DocumentPicker.getDocumentAsync({ type: '*/*' });
        if (res.type === 'success') {
            const reader = await fetch(res.uri).then(r => r.arrayBuffer());
            const wb = XLSX.read(reader, { type: 'buffer' });
            const wsname = wb.SheetNames[0];
            const data = XLSX.utils.sheet_to_json(wb.Sheets[wsname]);
            useModelStore.getState().importModels(data);
        }
    };
    

    /////////////////////////////////////////////////////////////

    useEffect(() => {
        requestStoragePermission();
    }, []);

    const setState = () => { };

    const requestStoragePermission = async () => {
        try {
            const granted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
                {
                    title: 'Navro Storage Permission',
                    message: 'Navro App needs access to your storage ',
                    buttonNegative: 'Cancel',
                    buttonPositive: 'OK',
                },
            );
            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                // console.log('Storage permission granted');
            } else {
                // console.log('Storage permission denied');
            }
        } catch (err) {
            console.warn(err);
        }
    };

    const [models, setModels] = useState([]);
    const [uploading, setUploading] = useState(false);
    const [selectedFile, setSelectedFile] = useState(null);

    const handleFilePick = async () => {
        try {
            const res = await DocumentPicker.pick({
                type: [
                    DocumentPicker.types.xlsx, 
                    DocumentPicker.types.csv,
                    DocumentPicker.types.allFiles // For 3D models
                ],
                allowMultiSelection: false,
            });
            
            const fileExt = extname(res.name).toLowerCase();
            const is3DFile = supported3DFormats.includes(fileExt);
            
            setSelectedFile({
                ...res,
                is3DFile,
                fileExt
            });
            
        } catch (err) {
            if (!DocumentPicker.isCancel(err)) {
                Alert.alert('Error', 'Failed to pick file');
            }
        }
    };

    const parseFileContents = async (file) => {
    try {
        if (file.is3DFile) {
            // Placeholder for 3D files - basic metadata extraction
            return {
                id: uuidv4(),
                name: file.name.replace(file.fileExt, ''),
                type: '3d_model',
                fileType: file.fileExt,
                fileSize: file.size,
                uploadedAt: new Date().toISOString(),
                previewUrl: null, // Placeholder for future thumbnail
                metadata: {
                    vertices: 0,    // Placeholder - to be populated by renderer
                    polygons: 0,    // Placeholder - to be populated by renderer
                    materials: [],  // Placeholder - to be populated by renderer
                    version: '1.0'  // Placeholder
                }
            };
        } else {
            // Existing spreadsheet parsing with enhancements
            const fileContent = await RNFS.readFile(file.uri, 'base64');
            const workbook = XLSX.read(fileContent, { type: 'base64' });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);
            
            return jsonData.map(item => ({
                id: uuidv4(),
                name: item.name || 'Unnamed Model',
                type: 'data_model',
                data: item,
                uploadedAt: new Date().toISOString(),
                fileType: file.type,
                fileSize: file.size,
                metadata: {
                    columns: Object.keys(item),
                    rowCount: jsonData.length,
                    source: file.name
                }
            }));
        }
    } catch (error) {
        console.error('Parsing error:', error);
        throw error;
    }
    };
    const handleUpload = async () => {
    if (!selectedFile) return;

    setUploading(true);
    
    try {
        const processedModels = await parseFileContents(selectedFile);
        
        // If single 3D model, wrap in array for consistent handling
        const modelsToAdd = Array.isArray(processedModels) ? 
            processedModels : [processedModels];
        
        setModels(prev => [...prev, ...modelsToAdd]);
        
        // Save to storage
        await AsyncStorage.setItem('models', JSON.stringify([
            ...models,
            ...modelsToAdd
        ]));
        
        Alert.alert(
            'Success', 
            selectedFile.is3DFile ? 
                '3D model ready for rendering' : 
                `${modelsToAdd.length} models processed`
        );
        
    } catch (error) {
        Alert.alert('Error', 'Failed to process file');
    } finally {
        setUploading(false);
    }
    };

    

    
    // function end

    return (
        
        (
            
        <View
            style={[
                styles.container,
                !isTablet()
                    ? {
                        transform: [{ scaleX: 1 }, { scaleY: 1 }],
                    }
                    : {},
                {
                    ...getTransformForScreenInsideNavigation(),
                }
            ]}>
            <View
                style={[
                    switchMerchant ? styles.content_PhoneAdjustment : styles.content,
                    {
                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,

                        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
                    },
                    Platform.OS == 'ios' ?
                        {
                            paddingTop: -16,
                            marginTop: 16,
                        } : {},
                ]}>
                <ScrollView
                    scrollEnabled={true}
                    nestedScrollEnabled>
                    <View style={{ width: '100%', alignItems: 'center', }}>
                        <View
                            style={{
                                justifyContent: 'center',
                                alignSelf: 'center',
                                backgroundColor: 'white',
                                width: windowWidth * 0.87,
                                height: windowHeight * 0.8,
                            }}>

                        </View>
                    </View>
                </ScrollView>
            </View>
            <View style={{ padding: 16 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Upload Model File</Text>
  
            <Button title="Pick File" onPress={handleFilePick} />
                {selectedFile && (
                    <Text style={{ marginVertical: 4 }}>
                        Selected: {selectedFile.name}
                    </Text>
                )}

                <Button
                    title={uploading ? "Uploading..." : "Upload"}
                    onPress={handleUpload}
                    disabled={!selectedFile || uploading}
                />

                <Text style={{ marginTop: 16, fontSize: 16 }}>Uploaded Models:</Text>
                <FlatList
                    data={models}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item }) => (
                    <View style={styles.modelItem}>
                    <View style={styles.modelHeader}>
                    <Text style={styles.modelName}>{item.name}</Text>
                    <View style={[
                        styles.modelTypeBadge,
                        item.type === '3d_model' ? styles.model3D : styles.modelData
                    ]}>
                    <Text style={styles.modelTypeText}>
                        {item.type === '3d_model' ? '3D' : 'DATA'}
                    </Text>
                    </View>
                    </View>
                    <Text style={styles.modelDetails}>
                        {new Date(item.uploadedAt).toLocaleDateString()} • 
                        {item.fileType} • 
                        {(item.fileSize / 1024).toFixed(2)} KB
                    </Text>
            
                    {item.type === '3d_model' && (
                        <Text style={styles.placeholderText}>
                            [3D renderer integration pending]
                        </Text>
                    )}
                </View>
    )}
            />
            </View>
            <View style={{ padding: 16 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 16 }}>
                Model File Management
            </Text>

            <View style={styles.uploadSection}>
                <Button 
                    title="Select File" 
                    onPress={handleFilePick} 
                    disabled={uploading}
                />
                
                {selectedFile && (
                    <View style={styles.fileInfo}>
                        <Text>Selected: {selectedFile.name}</Text>
                        <Text>Type: {selectedFile.type}</Text>
                        <Text>Size: {(selectedFile.size / 1024).toFixed(2)} KB</Text>
                    </View>
                )}

                {uploading && (
                    <View style={styles.progressContainer}>
                        <Text>Uploading: {progress}%</Text>
                        <View style={styles.progressBar}>
                            <View style={[styles.progressFill, { width: `${progress}%` }]} />
                        </View>
                    </View>
                )}

                <Button
                    title={uploading ? "Uploading..." : "Upload & Process"}
                    onPress={handleUpload}
                    disabled={!selectedFile || uploading}
                />
            </View>

            <Text style={styles.sectionTitle}>Uploaded Models:</Text>
            
            <FlatList
                data={models}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                    <View style={styles.modelItem}>
                        <Text style={styles.modelName}>{item.name}</Text>
                        <Text style={styles.modelDetails}>
                            {new Date(item.uploadedAt).toLocaleDateString()} • 
                            {item.fileType} • 
                            {(item.fileSize / 1024).toFixed(2)} KB
                        </Text>
                    </View>
                )}
                ListEmptyComponent={
                    <Text style={styles.emptyText}>No models uploaded yet</Text>
                }
            />
            </View>
        </View>)
    );
});

const styles = StyleSheet.create({
    confirmBox: {
        width: '30%',
        height: '30%',
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    confirmBox1: {
        width: '30%',
        height: '60%',
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
    },
    container: {
        flex: 1,
        flexDirection: 'row',
        backgroundColor: Colors.highlightColor,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    list1: {
        backgroundColor: Colors.whiteColor,
        height: Dimensions.get('window').height * 0.62,
        width: Dimensions.get('window').width * 0.87,
        marginTop: 10,
        marginHorizontal: 30,
        marginBottom: 30,
        alignSelf: 'center',
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    list1_PhoneAdjustment: {
        backgroundColor: Colors.whiteColor,
        width: Dimensions.get('window').width * 0.79,
        minHeight: Dimensions.get('window').height * 0.01,
        marginTop: 0,
        marginHorizontal: 30,
        marginBottom: 30,
        alignSelf: 'center',
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    listItem: {
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        //width: windowWidth * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 12,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
        // width: windowWidth * (1 - Styles.sideBarWidth),
    },
    content_PhoneAdjustment: {
        padding: 16,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
        // width: windowWidth * (1 - Styles.sideBarWidth),
    },
    content1: {
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
    },
    content2: {
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
    },
    content3: {
        backgroundColor: Colors.highlightColor,
        width: 40,
        height: 40,
        borderRadius: 20,
        alignSelf: 'flex-end',
        position: 'absolute',
        right: -15,
        marginVertical: -10,
    },
    content4: {
        backgroundColor: Colors.highlightColor,
        width: 120,
        height: 20,
        borderRadius: 20,
        alignSelf: 'center',
        alignItems: 'center',
        marginTop: 10,
    },
    content5: {
        backgroundColor: Colors.whiteColor,
        width: 140,
        height: 140,
        borderRadius: 5,
        marginLeft: 40,
        borderStyle: 'dashed',
        borderWidth: 2,
        alignItems: 'center',
        borderColor: Colors.primaryColor,
    },

    content6: {
        backgroundColor: '#717378',
        width: 40,
        height: 40,
        marginLeft: 20,
        marginVertical: 10,
        borderRadius: 5,
    },

    searchBar: {
        marginHorizontal: 16,
        backgroundColor: Colors.fieldtBgColor,
        flexDirection: 'row',
        padding: 12,
        borderRadius: 10,
        alignContent: 'center',
    },
    submitText: {
        height: 40,
        paddingHorizontal: 20,
        flexDirection: 'row',
        color: '#4cd964',
        textAlign: 'center',
        borderRadius: 10,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    detailContainer: {
        flexDirection: 'row',
        borderBottomWidth: StyleSheet.hairlineWidth,
        marginTop: 20,
        borderBottomColor: Colors.fieldtTxtColor,
        justifyContent: 'space-between',
    },
    tableIcon: {
        // backgroundColor: '#717378',
        width: (Dimensions.get('window').width - 550) / 4,
        height: (Dimensions.get('window').width - 550) / 4,
        borderRadius: 10,
        marginLeft: 30,
        marginBottom: 20,
        justifyContent: 'center',
        borderStyle: 'dashed',
        borderWidth: 1,
        borderColor: Colors.fieldtTxtColor,
    },
    category: {
        width: 150,
        justifyContent: 'center',
        alignItems: 'center',
    },
    infoTab: {
        backgroundColor: Colors.highlightColor,
        width: Dimensions.get('window').width * 0.25,
        height: -100,
    },
    textFieldInput: {
        backgroundColor: Colors.fieldtBgColor,
        width: 250,
        height: 60,
        borderRadius: 5,
        padding: 5,
        marginVertical: 5,
        borderWidth: 1,
        borderColor: '#E5E5E5',
        paddingLeft: 10,
    },
    textFieldInput1: {
        height: 50,
        width: 200,
        paddingHorizontal: 40,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
    },
    textInventory: {
        height: 50,
        width: 100,
        paddingHorizontal: 40,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
    },
    textFieldInput2: {
        height: 50,
        width: 270,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 0,
    },
    textInput: {
        width: 100,
        height: 40,
        // padding:5,
        backgroundColor: Colors.whiteColor,
        borderRadius: 0,
        marginRight: '32%',
        flexDirection: 'row',
        alignContent: 'center',
        alignItems: 'center',

        shadowOffset:
            Platform.OS == 'ios'
                ? {
                    width: 0,
                    height: 0,
                }
                : {
                    width: 0,
                    height: 7,
                },
        shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
        shadowRadius: Platform.OS == 'ios' ? 0 : 0.51,
        elevation: 15,
    },
    textPrice: {
        width: 100,
        padding: 10,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        paddingTop: 10,
    },
    textTax: {
        width: 100,
        padding: 10,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        paddingTop: 10,
    },
    textInput1: {
        width: 100,
        padding: 6,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
    },
    button: {
        width: '50%',
        height: 50,
        backgroundColor: Colors.primaryColor,
        borderRadius: 10,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
    },
    textInput4: {
        width: 200,
        height: 50,
        borderRadius: 10,
        marginRight: '41%',
    },
    itemText: {
        margin: 10,
        color: 'white',
        fontSize: 24,
        backgroundColor: 'blue',
        width: '100%',
        height: 50,
    },
    confirmMenu: {
        width: '70%',
        height: '70%',
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    searchIcon: {
        backgroundColor: Colors.whiteColor,
        height: 40,
        padding: 10,
        shadowOffset:
            Platform.OS == 'ios'
                ? {
                    width: 0,
                    height: 0,
                }
                : {
                    width: 0,
                    height: 7,
                },
        shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
        shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

        elevation: 15,
    },
    searchView: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: 10,
        flexDirection: 'row',
    },
    //DUplicate header logo
    // headerLogo: {
    //     width: 100,
    //     height: 25,
    // },
    circleIcon: {
        width: 30,
        height: 30,
        // resizeMode: 'contain',
        marginRight: 10,
        alignSelf: 'center',
    },

    footer: {
        flexDirection: 'row',
        justifyContent: 'center',
        // alignContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white',
        padding: 20,
        paddingTop: 15,
        marginTop: 0,
        width: '115%',

        marginLeft: '-5%',
        bottom: '-15%',

        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 7,
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: Dimensions.get('window').width * 0.2,
        width: Dimensions.get('window').width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('window').width * 0.03,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('window').width * 0.04,
        top: Dimensions.get('window').width * 0.04,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: 'center',
        top: '20%',
        position: 'absolute',
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalTitleText: {
        fontFamily: 'NunitoSans-Bold',
        textAlign: 'center',
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 18,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        flex: 1,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
        width: '20%',
    },
    modalSaveButton: {
        width: Dimensions.get('window').width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
        uploadSection: {
        marginBottom: 20,
        padding: 16,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 8,
    },
    fileInfo: {
        marginVertical: 12,
        padding: 12,
        backgroundColor: 'white',
        borderRadius: 6,
    },
    progressContainer: {
        marginVertical: 12,
    },
    progressBar: {
        height: 6,
        backgroundColor: '#e0e0e0',
        borderRadius: 3,
        marginTop: 4,
        overflow: 'hidden',
    },
    progressFill: {
        height: '100%',
        backgroundColor: Colors.primaryColor,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginTop: 20,
        marginBottom: 12,
    },
    modelItem: {
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    modelName: {
        fontWeight: '600',
        fontSize: 16,
    },
    modelDetails: {
        color: 'gray',
        fontSize: 12,
        marginTop: 4,
    },
    emptyText: {
        textAlign: 'center',
        marginTop: 20,
        color: 'gray',
    },
    modelHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    modelTypeBadge: {
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 4,
    },
    model3D: {
        backgroundColor: '#4CAF50',
    },
    modelData: {
        backgroundColor: '#2196F3',
    },
    modelTypeText: {
        color: 'white',
        fontSize: 12,
        fontWeight: 'bold',
    },
    placeholderText: {
        fontStyle: 'italic',
        color: '#888',
        marginTop: 4,
    },

});
export default ModelEditor;
