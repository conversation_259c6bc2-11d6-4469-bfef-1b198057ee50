import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Dimensions,
    useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { TextInput, } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Styles from '../constant/Styles';
import Switch from 'react-native-switch-pro';
import {
    getTransformForScreenInsideNavigation,
    isTablet
} from '../util/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import { EXPAND_TAB_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { parseValidPriceText } from '../util/common';
import APILocal from '../util/apiLocalReplacers';

const LiveSelling = props => {
    const {
        navigation,
    } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const [keyboardHeight] = useKeyboard();

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [loading, setLoading] = useState(false);
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

    //////////////////////////////////////////////////////////////////////////

    const [temp, setTemp] = useState('');

    const [paymentLinkExpirationMinute, setPaymentLinkExpirationMinute] = useState('30');
    const [paymentLinkExpirationActive, setPaymentLinkExpirationActive] = useState(false);

    const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
    const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const currOutlet = MerchantStore.useState((s) => s.currOutlet);

    const allOutlets = MerchantStore.useState(s => s.allOutlets);
    const merchantId = UserStore.useState(s => s.merchantId);

    const userName = UserStore.useState(s => s.name);
    const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    useEffect(() => {
        if (currOutlet && currOutlet.uniqueId) {
            setPaymentLinkExpirationMinute(currOutlet.paymentLinkExpirationMinute ? parseFloat(currOutlet.paymentLinkExpirationMinute).toFixed(0) : '30');
            setPaymentLinkExpirationActive(currOutlet.paymentLinkExpirationActive ? currOutlet.paymentLinkExpirationActive : false);
        }
    }, [currOutlet]);

    useEffect(() => {
        setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

        if (selectedTargetOutletId === '' && allOutlets.length > 0) {
            setSelectedTargetOutletId(allOutlets[0].uniqueId);
        }
    }, [allOutlets]);

    useEffect(() => {
        if (currOutlet && currOutlet.uniqueId) {
            setPaymentLinkExpirationActive(currOutlet.lsPle !== undefined ? currOutlet.lsPle : true);
        }
    }, [
        currOutlet,
    ]);

    //////////////////////////////////////////////////////////////////////////

    const setState = () => { };

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        // width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //     ? { right: windowWidth * 0.1 }
                    //     : {},
                    // windowWidth <= 768
                    //     ? { right: 20 }
                    //     : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Live Selling Setting
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    const orderFunc = () => {
        var body = {
            merchantId,
            // outletId: selectedTargetOutletId,
            outletId: currOutletId,
            // paymentLinkExpirationMinute: +(parseFloat(paymentLinkExpirationMinute).toFixed(0)),
            lsPle: paymentLinkExpirationActive,
        };

        // console.log(body);

        // ApiClient.POST(API.updateOutletOrderDetails, body, false)
        APILocal.updateOutletLiveSelingDetails({ body })
            .then((result) => {
                setLoading(true)
                if (result && result.status === 'success') {
                    Alert.alert(
                        'Success',
                        'Settings has been updated.',
                        [
                            {
                                text: 'OK',
                                onPress: () => {
                                    setLoading(false)
                                },
                            },
                        ],
                        { cancelable: false },
                    );
                }
            });
    }

    return (
        <UserIdleWrapper disabled={!isMounted}>
            <View style={[styles.container, !isTablet() ? {
                transform: [
                    { scaleX: 1 },
                    { scaleY: 1 },
                ],
            } : {}, {
                ...getTransformForScreenInsideNavigation(),
            }]}>
                {/* <View style={[styles.sidebar, !isTablet() ? {
                    width: windowWidth * 0.08,
                } : {}, switchMerchant ? {
                    // width: '10%'
                } : {}, {
                    width: windowWidth * 0.08,
                }]}>
                    <SideBar navigation={props.navigation} selectedTab={10} expandSettings />
                </View> */}

                <ScrollView
                    showsVerticalScrollIndicator={false}
                    // scrollEnabled={switchMerchant}
                    style={{ backgroundColor: Colors.highlightColor }}
                    contentContainerStyle={{
                        paddingBottom: windowHeight * 0.1,
                        backgroundColor: Colors.highlightColor,
                    }}>
                    <ScrollView horizontal>

                        <View style={[styles.content, {
                            width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
                        }]}>

                            <View style={{
                                backgroundColor: Colors.whiteColor,
                                // height: windowHeight - 120,
                                height: '100%',
                                width: windowWidth * 0.87,
                                alignSelf: 'center',

                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                // elevation: 1,
                                elevation: 3,
                                borderRadius: 5,

                                // borderRadius: 8,
                            }}>
                                <KeyboardAwareScrollView
                                    contentContainerStyle={{
                                        // top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                                    }}>
                                    <View style={{}}>
                                        <View style={{ flexDirection: 'row', padding: 30, zIndex: -3 }}>
                                            <View style={{ flexDirection: 'row', marginTop: 30, zIndex: -3 }}>
                                                {/* left */}
                                                <View style={{
                                                    justifyContent: 'center',
                                                    // flex: 1,
                                                    // width: '33.33%',
                                                }}>
                                                    <View>
                                                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                                            Payment Link Expiration (30 min){' '}
                                                        </Text>
                                                    </View>
                                                </View>

                                                <View style={{
                                                    flexDirection: 'row',
                                                    // backgroundColor: 'red',
                                                    // width: '40.33%',
                                                }}>
                                                    {/* <View style={{
                                                        width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                                        height: 50,
                                                        justifyContent: 'center',
                                                    }}>
                                                        <TextInput
                                                            underlineColorAndroid={Colors.fieldtBgColor}
                                                            style={[styles.textInputPaymentLink, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                                            placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                                            itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                                            keyboardType={'decimal-pad'}
                                                            placeholder="60"
                                                            containerStyle={{ height: 40 }}
                                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                            defaultValue={paymentLinkExpirationMinute}
                                                            //iOS
                                                            clearTextOnFocus={true}
                                                            //////////////////////////////////////////////
                                                            //Android
                                                            onFocus={() => {
                                                                setTemp(paymentLinkExpirationMinute)
                                                                setPaymentLinkExpirationMinute('');
                                                            }}
                                                            ///////////////////////////////////////////////
                                                            //When textinput is not selected
                                                            onEndEditing={() => {
                                                                if (paymentLinkExpirationMinute == '') {
                                                                    setPaymentLinkExpirationMinute(temp);
                                                                }
                                                            }}
                                                            //////////////////////////////////////////////
                                                            onChangeText={text => {
                                                                setPaymentLinkExpirationMinute(text);
                                                            }}
                                                        />
                                                    </View> */}

                                                    <View style={{
                                                        marginLeft: switchMerchant ? 5 : 10,
                                                        justifyContent: 'center',
                                                    }}>
                                                        <Switch
                                                            width={42}
                                                            style={{}}
                                                            value={paymentLinkExpirationActive}
                                                            onSyncPress={(statusTemp) =>
                                                                setPaymentLinkExpirationActive(statusTemp)
                                                            }
                                                            circleColorActive={Colors.primaryColor}
                                                            circleColorInactive={Colors.fieldtTxtColor}
                                                            backgroundActive="#dddddd"
                                                        />
                                                    </View>
                                                </View>
                                            </View>
                                        </View>
                                    </View>

                                    <View style={{ alignItems: 'center', marginBottom: 20, }}>
                                        <TouchableOpacity
                                            style={{
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#0F1A3C',
                                                borderRadius: 5,
                                                width: 120,
                                                paddingHorizontal: 10,
                                                height: 35,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                            }}
                                            disabled={loading}
                                            onPress={() => { orderFunc() }}>
                                            <Text style={{
                                                color: Colors.whiteColor,
                                                //marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                                {loading ? 'LOADING...' : 'SAVE'}
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                </KeyboardAwareScrollView>
                            </View>
                        </View>
                    </ScrollView>
                </ScrollView>
            </View>
        </UserIdleWrapper>
    );

}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        fontFamily: 'NunitoSans-Regular',
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
    },
    content: {
        padding: 20,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
        // backgroundColor: 'lightgrey',
        backgroundColor: Colors.highlightColor,
    },
    textInputPaymentLink: {
        backgroundColor: Colors.fieldtBgColor,
        width: 200,
        height: 40,
        borderRadius: 5,
        padding: 5,
        marginVertical: 5,
        borderWidth: 1,
        borderColor: '#E5E5E5',
        paddingLeft: 10,
        //marginLeft: 190,
    },
    textInput: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 0,
        flex: 1,
        flexDirection: 'row',
    },
    headerLeftStyle: {
        width: Dimensions.get('screen').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
export default LiveSelling;