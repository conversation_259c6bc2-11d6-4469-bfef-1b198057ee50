import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
  ActivityIndicator,
  Linking,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { TextInput, FlatList } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
import Switch from 'react-native-switch-pro';
import GCalendar from '../assets/svg/GCalendar';
import {
  isTablet, listenToCurrOutletIdReservationChanges, updateEPDetailsToUserOrders
} from '../util/common';
import { UserStore } from '../store/userStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import { parseValidPriceText } from '../util/common';
import CheckBox from 'react-native-check-box';
import { color } from 'react-native-reanimated';
import Close from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import moment, { isDate } from 'moment';
import Barcode from 'react-native-barcode-builder';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { EXPAND_TAB_TYPE, ORDER_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import APILocal from '../util/apiLocalReplacers';
import { qrUrl } from '../constant/env';
import Entypo from 'react-native-vector-icons/Entypo';
import firestore from '@react-native-firebase/firestore';
import { Collections } from "../constant/firebase";
import { RadioButton } from "react-native-paper";
import AsyncImage from "../components/asyncImage";


const FulfillmentScreen = (props) => {
  const { navigation } = props;

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  // const [merchantId, setMerchantId] = useState([]);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const epOrderList = CommonStore.useState((s) => s.epOrderList);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const merchantId = UserStore.useState((s) => s.merchantId);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const historyStartDate = CommonStore.useState(s => s.historyStartDate);
  const historyEndDate = CommonStore.useState(s => s.historyEndDate);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const [isMounted, setIsMounted] = useState(true);

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const deliveryCreditBalance = OutletStore.useState((s) => s.deliveryCreditBalance);
  const deliveryCreditWalletId = OutletStore.useState((s) => s.deliveryCreditWalletId);

  //////////
  //Added 2024-04-17
  const [integrationId, setIntegrationId] = useState("");
  const [selectedProvince, setSelectedProvince] = useState("");

  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');

  ///////////////////////////////////////////////////////////////////

  // 2024-04-17 - easyparcel details

  const [epStateFrom, setEpStateFrom] = useState('sgr');
  const [epNameFrom, setEpNameFrom] = useState('');
  const [epAddr1From, setEpAddr1From] = useState('');
  const [epCityFrom, setEpCityFrom] = useState('');
  const [epCodeFrom, setEpCodeFrom] = useState('');
  const [epUserCourierName, setEpUserCourierName] = useState('');
  const [epUserPrice, setEpUserPrice] = useState('');
  const [epPickupDate, setEpPickupDate] = useState('');

  const [epStateToTemp, setEpStateToTemp] = useState('sgr');
  const [epNameToTemp, setEpNameToTemp] = useState('');
  const [epPhoneToTemp, setEpPhoneToTemp] = useState('');
  const [epAddr1ToTemp, setEpAddr1ToTemp] = useState('');
  const [epCityToTemp, setEpCityToTemp] = useState('');
  const [epCodeToTemp, setEpCodeToTemp] = useState(''); // postcode

  const [epScheduleDt, setEpScheduleDt] = useState(Date.now());

  const [epRates, setEpRates] = useState([]);

  const [epGetRatesByOrderIdDict, setEpGetRatesByOrderIdDict] = useState({});

  const [shipLoading, setShipLoading] = useState(false);

  const outletItems = OutletStore.useState((s) => s.outletItems);

  ///////////////////////////////////////////////////////////////////

  ////////////////////
  ///////////////////
  ///////////////////

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('FulfillmentScreen');

            CommonStore.update((s) => {
              s.currPage = 'FulfillmentScreen';
              s.currPageStack = [...currPageStack, 'FulfillmentScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
            });
          }
        }}
        style={{
          width: windowWidth * 0.17,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            marginRight: Platform.OS === 'ios' ? "27%" : 0,
            bottom: switchMerchant ? '2%' : 0,
            width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
          },
          windowWidth >= 768 && switchMerchant
            ? { right: windowWidth * 0.1 }
            : {},
          windowWidth <= 768
            ? { right: 20 }
            : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Fulfillment
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  ////////////////////////////////////////////////////////////////////////////////////////////////////////////

  // 2024-04-18 - easyparcel changes

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      setEpStateFrom(currOutlet.epStateFrom ? currOutlet.epStateFrom : 'sgr');
      setEpNameFrom(currOutlet.epNameFrom ? currOutlet.epNameFrom : '');
      setEpAddr1From(currOutlet.epAddr1From ? currOutlet.epAddr1From : '');
      setEpCityFrom(currOutlet.epCityFrom ? currOutlet.epCityFrom : '');
      setEpCodeFrom(currOutlet.epCodeFrom ? currOutlet.epCodeFrom : '');
    }
  }, [currOutlet]);

  useEffect(() => {
    if (epOrderList && epOrderList.length > 0 &&
      epOrderList[0] && epOrderList[0].uniqueId) {
      if (epOrderList[0].epStateTo) {
        setEpStateToTemp(epOrderList[0].epStateTo);
      }

      if (epOrderList[0].epNameTo) {
        setEpNameToTemp(epOrderList[0].epNameTo);
      }

      if (epOrderList[0].epPhoneTo) {
        setEpPhoneToTemp(epOrderList[0].epPhoneTo);
      }

      if (epOrderList[0].epAddr1To) {
        setEpAddr1ToTemp(epOrderList[0].epAddr1To);
      }

      if (epOrderList[0].epCityTo) {
        setEpCityToTemp(epOrderList[0].epCityTo);
      }

      if (epOrderList[0].epCodeTo) {
        setEpCodeToTemp(epOrderList[0].epCodeTo);
      }

      if (epOrderList[0].epScheduleDt) {
        setEpScheduleDt(epOrderList[0].epScheduleDt);
      }
      if (epOrderList[0].epUserCourierName) {
        setEpUserCourierName(epOrderList[0].epUserCourierName);
      }
      if (epOrderList[0].epUserPrice) {
        setEpUserPrice(epOrderList[0].epUserPrice);
      }
      if (epOrderList[0].epPickupDate) {
        setEpPickupDate(epOrderList[0].epPickupDate);
      }
    }
  }, [
    epOrderList,
  ]);

  ////////////////////////////////////////////////////////////////////////////////////////////////////////////

  // 2024-04-18 - easyparcel changes

  const renderOrderItems = ({ item, index }) => {
    const foundItem = outletItems.find(findItem => findItem.uniqueId === item.itemId)
    return (


      <View style={{ flexDirection: "row", alignItems: 'center', marginBottom: 15 }}>
        {
          item.image
            ?
            <AsyncImage
              source={{ uri: item.image }}
              style={{ width: 45, height: 45, borderRadius: 5, }}
            />
            :
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                width: 45,
                height: 45,
                borderWidth: 1,
                borderColor: '#E5E5E5',
                borderRadius: 5,

              }}>
              <Ionicons
                name="cart-outline"
                size={switchMerchant ? 15 : 25}
              />
            </View>
        }
        <Text style={{ fontSize: 16, width: '40%', marginLeft: 5 }}>
          {item.itemName}
        </Text>
        <Text style={{ fontSize: 16, width: '30%' }}>
          {foundItem.skuMerchant}
        </Text>

        <Text style={{ fontSize: 16, width: '15%' }}>
          {foundItem.weight ? item.weight : 0.1}
        </Text>

        <Text style={{ fontSize: 16, width: '10%' }}>
          {item.quantity}
        </Text>
      </View>
    );

  };

  // const foundItem = outletItems.find(findItem => findItem.uniqueId === cartItem.itemId); foundItem.skuMerchant ? foundItem.skuMerchant : "N/A"

  const renderRates = ({ item, index }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          setEpGetRatesByOrderIdDict({
            [epOrderList[0].uniqueId]: item,
          });
        }}
        style={{ flexDirection: "row", alignItems: 'center', marginTop: 10, }}>
        <RadioButton
          status={
            (epGetRatesByOrderIdDict[epOrderList[0].uniqueId]
              &&
              epGetRatesByOrderIdDict[epOrderList[0].uniqueId].service_id === item.service_id)
              ?
              'checked'
              :
              'unchecked'
          }
        />
        <View style={{ width: '10%', marginLeft: 10, }}>
          {item.courier_logo ?
            <AsyncImage style={[
              {
                width: 80,
                height: 70,
                top: 3,
              },
              switchMerchant
                ? {
                  width:
                    windowWidth * 0.05,
                  height:
                    windowHeight * 0.05,
                  alignSelf: 'center',
                }
                : {},
            ]}
              resizeMode="contain"
              // source={require('../assets/image/pgeon_logo.png')}
              source={{
                uri: item.courier_logo,
              }}
            />
            :
            <View style={[
              {
                width: 80,
                height: 70,
                top: 3,
                backgroundColor: Colors.secondaryColor,
              },
            ]} />
          }
        </View>

        <View style={{ width: '50%', }}>
          <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Regular', width: '95%', }} numberOfLines={1}>
            {item.service_name ? item.service_name : 'N/A'}
          </Text>
          <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Bold' }}>
            {`RM ${item.price ? item.price : 'N/A'}`}
          </Text>

        </View>
        <View style={{ width: '25%', }}>
          <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Regular' }}>
            Scheduled pickup for parcel
          </Text>
          <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Bold' }}>
            {item.scheduled_start_date ? item.scheduled_start_date : moment().format('YYYY MMM DD')}
          </Text>
        </View>
        <View style={{ width: '15%', }}>
          <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Regular' }}>
            Pickup Date
          </Text>
          <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Bold' }}>
            {item.pickup_date ? item.pickup_date : moment().format('YYYY MMM DD')}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const epRateChecking = (orderList, outlet, cb) => {
    let body = {
      objList: orderList.map(order => {
        return {
          epCodeFrom,
          epStateFrom,
          epCountryFrom: 'MY',
          epCodeTo: epCodeToTemp,
          epStateTo: epStateToTemp,
          epCountryTo: 'MY',

          weight: Math.ceil(order.cartItems.reduce((accum, item) => {
            return accum + (item.weight ? item.weight : 0.1);
          }, 0)),

          epScheduleDt: epScheduleDt ? moment(epScheduleDt).format('YYYY-MM-DD') : moment().add(1, 'day').format('YYYY-MM-DD'),
        };
      }),
    };


    ApiClient.POST(API.epRateChecking, body)
      .then((result) => {
        console.log('result');
        console.log(result);

        typeof cb === 'function' && cb(result);

        // if (result && result.status === 'success') {
        // } else {
        //   Alert.alert(
        //     'Error',
        //     'Please try again later.',
        //   );
        // }
      })
      .catch((err) => {
        console.log(err);

        // Alert.alert(
        //   'Error',
        //   'Please try again later.',
        // );
      });
  };

  const epMakingOrder = (orderList, outlet, rates, cb) => {
    let body = {
      objList: orderList.map((order, index) => {
        let serviceId = '';
        if (epGetRatesByOrderIdDict[order.uniqueId] && epGetRatesByOrderIdDict[order.uniqueId].service_id) {
          serviceId = epGetRatesByOrderIdDict[order.uniqueId].service_id;
        }

        if (serviceId === '' && rates[index]) {
          serviceId = rates[index].service_id;
        }

        let sendEmail = outlet.email;

        if (order.userEmail && !order.userEmail.startsWith('user-') &&
          order.userEmail.length < 28) {
          // valid email

          sendEmail = order.userEmail;
        }

        return {
          epCodeFrom,
          epStateFrom,
          epCountryFrom: 'MY',
          epCodeTo: epCodeToTemp,
          epStateTo: epStateToTemp,
          epCountryTo: 'MY',

          weight: Math.ceil(order.cartItems.reduce((accum, item) => {
            return accum + (item.weight ? item.weight : 0.1);
          }, 0)),

          epContent: order.cartItems.map(item => item.itemName).join(', '),
          epQuantity: order.cartItems.reduce((accum, item) => accum + item.quantity, 0),

          epServiceId: serviceId,

          epNameFrom,
          epPhoneFrom: outlet.phone ? outlet.phone : '',
          epAddr1From,
          epCityFrom,

          epNameTo: epNameToTemp,
          epPhoneTo: epPhoneToTemp,
          epAddr1To: epAddr1ToTemp,
          epCityTo: epCityToTemp,

          epSms: true,
          epEmail: sendEmail,

          epScheduleDt: epScheduleDt ? moment(epScheduleDt).format('YYYY-MM-DD') : moment().add(1, 'day').format('YYYY-MM-DD'),
        };
      }),
    };

    ApiClient.POST(API.epMakingOrder, body)
      .then((result) => {
        console.log('result');
        console.log(result);

        typeof cb === 'function' && cb(result);

        // if (result && result.status === 'success') {
        // } else {
        //   Alert.alert(
        //     'Error',
        //     'Please try again later.',
        //   );
        // }
      })
      .catch((err) => {
        console.log(err);

        // Alert.alert(
        //   'Error',
        //   'Please try again later.',
        // );
      });
  };

  const epMakingOrderPayment = (orderList, outlet, epMakingOrderResultList, totalDeliveryCharges, cb) => {
    let body = {
      objList: orderList.map((order, index) => {
        return {
          epOrderNo: epMakingOrderResultList[index].order_number,
        };
      }),

      totalDeliveryCharges,
      deliveryCreditWalletId,
      merchantId,
    };

    ApiClient.POST(API.epMakingOrderPayment, body)
      .then((result) => {
        console.log('result');
        console.log(result);

        typeof cb === 'function' && cb(result);

        // if (result && result.status === 'success') {
        // } else {
        //   Alert.alert(
        //     'Error',
        //     'Please try again later.',
        //   );
        // }
      })
      .catch((err) => {
        console.log(err);

        // Alert.alert(
        //   'Error',
        //   'Please try again later.',
        // );
      });
  };

  ///////////////////////////////////////////////////////////

  // function end

  return (

    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={10}
            expandSettings
          />
        </View> */}

        <ScrollView
          showsVerticalScrollIndicator={false}
          // scrollEnabled={switchMerchant}
          style={{ backgroundColor: Colors.highlightColor, }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.05,
            backgroundColor: Colors.highlightColor,
          }}>
          <View style={{
            width: windowWidth * 0.865,
            alignSelf: 'center',
          }}>
            <TouchableOpacity
              style={{
                height: windowHeight * 0.055,
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
                marginTop: 10,
              }}
              onPress={() => {
                props.navigation.navigate('History');
                CommonStore.update((s) => {
                  s.currPage = 'History';
                  s.currPageStack = [...currPageStack, 'History'];
                });

                if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                  });
                }
              }}>
              <Icon
                name="chevron-left"
                size={switchMerchant ? 20 : 30}
                color={Colors.primaryColor}
              />
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 14 : 17,
                  color: Colors.primaryColor,
                  marginBottom: Platform.OS === 'ios' ? 0 : 1,
                }}>
                Back
              </Text>
            </TouchableOpacity>
          </View>
          <View style={{
            padding: 20,
            backgroundColor: 'white',
            width: windowWidth * 0.85,
            borderRadius: 5,
            alignSelf: 'center',
            marginTop: 20,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 5,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            // elevation: 1,
            elevation: 3,
          }}>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 5,
                marginTop: 15,
                flex: 1
              }}>
              <View style={{ marginRight: 25, width: '15%', height: 80, }}>
                <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                  Order Number
                </Text>
                <Text style={{ fontSize: 16, marginTop: 5 }}>
                  {
                    epOrderList.length > 0
                      ?
                      `#${epOrderList[0].orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${epOrderList[0].orderId}`
                      :
                      'N/A'
                  }
                </Text>
              </View>

              <View style={{ marginRight: 25, width: '15%', height: 80, }}>
                <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                  Receiver
                </Text>
                <Text style={{ fontSize: 16, marginTop: 5 }}>
                  {epNameToTemp ? epNameToTemp : 'N/A'}
                </Text>
              </View>

              <View style={{ marginRight: 25, width: '20%', height: 80, }}>
                <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                  Shipping From
                </Text>
                <Text style={{ fontSize: 16, marginTop: 5 }}>
                  {epAddr1From ? epAddr1From : 'N/A'}
                </Text>
              </View>
              <View style={{ marginRight: 25, width: '20%', height: 80, }}>
                <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                  Shipping To
                </Text>
                <Text style={{ fontSize: 16, marginTop: 5 }} numberOfLines={4}>
                  {epAddr1ToTemp ? epAddr1ToTemp : 'N/A'}
                </Text>
              </View>
              <View style={{ marginRight: 25, width: '20%', height: 80 }}>
                <Text style={{ fontWeight: 'bold', fontSize: 18, color: Colors.primaryColor }}>
                  User Selected Courier
                </Text>
                <Text style={{ fontSize: 16, marginTop: 5, color: Colors.primaryColor }} numberOfLines={4}>
                  {`${epUserCourierName ? epUserCourierName : 'N/A'} ${epUserPrice ? `(RM ${epUserPrice})` : 'RM: N/A'} ${epPickupDate ? `(Schedule Date: ${epPickupDate})` : 'Schedule Date: N/A'}`}
                </Text>
              </View>

            </View>
            <View style={{ marginTop: 40, flexDirection: "row", alignItems: 'center' }}>
              <View style={{ width: 45 }} />
              <Text style={{ fontSize: 16, width: '40%', marginLeft: 5 }}>
                Item
              </Text>
              <Text style={{ fontSize: 16, width: '30%' }}>
                SKU
              </Text>
              <Text style={{ fontSize: 16, width: '15%' }}>
                Weight (kg)
              </Text>
              <Text style={{ fontSize: 16, width: '10%' }}>
                Quantity
              </Text>
            </View>
            <View
              style={{
                borderBottomWidth: 0.7,
                borderColor: 'grey', // Change the color as needed
                marginVertical: 17, // Adjust margin as needed
              }} />

            {
              epOrderList.length > 0
                ?
                <FlatList
                  showsVerticalScrollIndicator={false}
                  // data={historyOrders.slice(0).sort((a, b) => {
                  //   return b.orderDate - a.orderDate;
                  // })}
                  data={epOrderList[0].cartItems}
                  renderItem={renderOrderItems}
                  keyExtractor={(item, index) => String(index)}
                  contentContainerStyle={{
                    paddingBottom: 40,
                  }}
                />
                :
                <></>
            }

            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 60 }}>
              <View style={{ width: '47%' }}>
                <Text style={{ fontSize: 16, marginTop: 5, }}>
                  Name
                </Text>
                <TextInput
                  onChangeText={(text) => {
                    setEpNameToTemp(text);
                  }}
                  value={epNameToTemp}
                  style={styles.textInput}
                />
              </View>
              <View style={{ width: '47%' }}>
                <Text style={{ fontSize: 16, marginTop: 5 }}>
                  Phone Number
                </Text>
                <TextInput
                  keyboardType="phone-pad"
                  onChangeText={(text) => {
                    setEpPhoneToTemp(text);
                  }}
                  value={epPhoneToTemp}
                  style={styles.textInput}
                />
              </View>
            </View>
            <View style={{}}>

              <Text style={{ fontSize: 16, marginTop: 5 }}>
                Address
              </Text>
              <TextInput
                onChangeText={(text) => {
                  setEpAddr1ToTemp(text);
                }}
                value={epAddr1ToTemp}
                style={styles.textInput}
              />

              {/* <Text style={{ fontSize: 16, marginTop: 5 }}>
                Address 2
              </Text>
              <TextInput
                style={styles.textInput}
              /> */}

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 5 }}>
                <View style={{ width: '47%' }}>
                  <Text style={{ fontSize: 16, marginTop: 5, }}>
                    City
                  </Text>
                  <TextInput
                    onChangeText={(text) => {
                      setEpCityToTemp(text);
                    }}
                    value={epCityToTemp}
                    style={styles.textInput}
                  />
                </View>

                <View style={{ width: '47%' }}>
                  <Text style={{ fontSize: 16, marginTop: 5 }}>
                    Zip Code
                  </Text>
                  <TextInput
                    onChangeText={(text) => {
                      setEpCodeToTemp(text);
                    }}
                    value={epCodeToTemp}
                    style={styles.textInput}
                  />
                </View>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 5 }}>
                <View style={{ width: '47%' }}>
                  <Text style={{ fontSize: 16, marginTop: 5, }}>
                    Country
                  </Text>
                  <TextInput
                    editable={false}
                    /* onChangeText={(text) => {
                        
                    }}
                    value={}*/
                    defaultValue="Malaysia"
                    style={styles.textInput}
                  />
                </View>

                <View style={{ width: '47%' }}>
                  <Text style={{ fontSize: 16, marginTop: 5 }}>
                    Province
                  </Text>
                  <RNPickerSelect
                    placeholder={{
                      label: 'Select a province',
                      value: 'empty',
                    }}
                    items={[
                      {
                        label: 'Johor',
                        value: 'jhr',
                      },
                      {
                        label: 'Kedah',
                        value: 'kdh',
                      },
                      {
                        label: 'Kelantan',
                        value: 'ktn',
                      },
                      {
                        label: 'Melaka',
                        value: 'mlk',
                      },
                      {
                        label: 'Negeri Sembilan',
                        value: 'nsn',
                      },
                      {
                        label: 'Pahang',
                        value: 'phg',
                      },
                      {
                        label: 'Perak',
                        value: 'prk',
                      },
                      {
                        label: 'Perlis',
                        value: 'pls',
                      },
                      {
                        label: 'Pulau Pinang',
                        value: 'png',
                      },
                      {
                        label: 'Selangor',
                        value: 'sgr',
                      },
                      {
                        label: 'Terengganu',
                        value: 'trg',
                      },
                      {
                        label: 'Kuala Lumpur',
                        value: 'kul',
                      },
                      {
                        label: 'Putra Jaya',
                        value: 'pjy',
                      },
                      {
                        label: 'Sarawak',
                        value: 'srw',
                      },
                      {
                        label: 'Sabah',
                        value: 'sbh',
                      },
                      {
                        label: 'Labuan',
                        value: 'lbn',
                      },
                    ]}
                    style={{
                      inputIOS: styles.textInput,
                      inputAndroid: styles.textInput,
                      pickerContainer: {
                        height: 120,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        textAlign: 'left',
                      },
                      viewContainer: {
                        zIndex: 1000,
                      },
                    }}
                    onValueChange={(item) =>
                      setEpStateToTemp(item.value)
                    }
                    value={epStateToTemp}
                  />
                </View>
              </View>
            </View>
            <View style={{ marginTop: 60, flexDirection: "row", alignItems: 'center' }}>
              <Text style={{ fontSize: 16 }}>
                Choose your pick up date
              </Text>
              {/* <Text style={{ fontSize: 16, left: 150 }}>
                Choose a delivery option
              </Text> */}
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View
                style={[
                  {
                    marginRight: 5,
                    paddingHorizontal: 5,
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderRadius: 10,
                    paddingVertical: 10,
                    justifyContent: 'center',
                    backgroundColor: Colors.whiteColor,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 10,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: 1,
                    height: 45,
                    width: 160,
                  },
                ]}>
                <TouchableOpacity
                  onPress={() => {
                    setShowDateTimePicker(true);
                  }}
                  style={{
                    marginHorizontal: 4,
                  }}>
                  <Text
                    style={[
                      { fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {moment(epScheduleDt).format('DD MMM yyyy')}
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={
                  switchMerchant
                    ? {
                      paddingHorizontal: 25,
                      flex: 1,
                      paddingTop: 30,
                      paddingBottom: windowHeight * 0.05,
                    }
                    : { flex: 1, paddingHorizontal: 25, paddingVertical: 30 }
                }>
                <DateTimePickerModal
                  //supportedOrientations={['landscape', 'portrait']}
                  isVisible={showDateTimePicker}
                  mode={'date'}
                  onConfirm={(text) => {
                    // setRev_date(moment(text).startOf('day'));
                    // CommonStore.update(s => {
                    //   s.historyStartDate = moment(text).startOf('day');
                    // });
                    setEpScheduleDt(moment(text).valueOf());
                    setShowDateTimePicker(false);
                  }}
                  onCancel={() => {
                    setShowDateTimePicker(false);
                  }}
                  style={{ zIndex: 1000 }}
                  minimumDate={moment().toDate()}
                  date={moment(epScheduleDt).toDate()}
                />
              </View >

              {/* <RNPickerSelect
                placeholder={{
                  label: 'Select Delivery Option',
                  value: 'empty',
                }}
                items={[
                  {
                    label: 'Pickup',
                    value: 'pckup',
                  },
                  {
                    label: 'Dropoff',
                    value: 'doff',
                  },
                ]}
                style={{
                  inputIOS: styles.textInput,
                  inputAndroid: styles.textInput,
                  pickerContainer: {
                    height: 45,
                    backgroundColor: Colors.whiteColor,

                    textAlign: 'left',
                  },
                  viewContainer: {
                    backgroundColor: Colors.whiteColor,
                    paddingHorizontal: 10,
                    width: 200,
                    left: -550,
                  },
                }
                }
                onValueChange={(item) =>
                  setSelectedProvince(item.value)
                }
                value={selectedProvince}
              /> */}
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
              <TouchableOpacity
                style={{
                  marginTop: 20,
                  justifyContent: 'center',
                  flexDirection: 'row',
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  backgroundColor: '#4E9F7D',
                  borderRadius: 5,
                  //width: 160,
                  paddingHorizontal: 15,
                  height: switchMerchant ? 30 : 45,
                  // width: switchMerchant ? 100 : 200,
                  alignItems: 'center',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  zIndex: -1,
                }}
                onPress={() => {
                  setShipLoading(true);
                  epRateChecking(epOrderList, currOutlet, (result) => {
                    console.log('result');
                    console.log(result);

                    if (result && result.status === 'success' &&
                      result.data && result.data.api_status === 'Success') {
                      if (result.data.result.length > 0) {
                        if (result.data.result[0] &&
                          result.data.result[0].rates &&
                          result.data.result[0].rates.length > 0) {
                          let epRatesTemp = result.data.result[0].rates.filter(rate => !rate.service_name.includes('Pgeon'));

                          setEpRates(epRatesTemp);

                          // setEpGetRatesByOrderIdDict({
                          //   ...epGetRatesByOrderIdDict,
                          //   [epOrderList[0].uniqueId]: epRatesTemp[0],
                          // });
                          setShipLoading(false);
                        }
                      }
                    }
                    else {
                      // error handling
                    }
                  });
                }}>
                <View
                  style={{ flexDirection: 'row', alignItems: 'center' }}>
                  {/* <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} /> */}
                  <Text
                    style={{
                      color: Colors.whiteColor,
                      //marginLeft: 5,
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                    QUOTE SHIPPING RATE
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
          <View style={{
            padding: 20,
            backgroundColor: 'white',
            width: windowWidth * 0.85,
            borderRadius: 5,
            alignSelf: 'center',
            marginTop: 20,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 5,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            // elevation: 1,
            elevation: 3,
          }}>
            <View style={{ flexDirection: "row", alignItems: 'center' }}>
              <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-SemiBold' }}>
                Choose a service to deliver your parcel
              </Text>
            </View>
            {!shipLoading ?
              <>
                {epOrderList.length > 0 && epRates.length > 0
                  ?
                  <>
                    <FlatList
                      showsVerticalScrollIndicator={false}
                      // data={historyOrders.slice(0).sort((a, b) => {
                      //   return b.orderDate - a.orderDate;
                      // })}
                      data={epRates}
                      renderItem={renderRates}
                      keyExtractor={(item, index) => String(index)}
                      contentContainerStyle={{
                        paddingBottom: 40,
                      }}
                    />

                    <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                      <TouchableOpacity
                        style={{
                          marginTop: 20,
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 30 : 45,
                          width: switchMerchant ? 100 : 200,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          let isRateChosen = false;
                          let totalDeliveryCharges = 0;
                          let validOrderNum = 0;

                          for (let i = 0; i < epOrderList.length; i++) {
                            const order = epOrderList[i];

                            if (epGetRatesByOrderIdDict[order.uniqueId] && epGetRatesByOrderIdDict[order.uniqueId].service_id) {
                              totalDeliveryCharges += parseFloat(epGetRatesByOrderIdDict[order.uniqueId].price);
                              validOrderNum++;
                            }
                          }

                          if (validOrderNum === epOrderList.length) {
                            // 2024-04-25 - need check delivery balance first

                            if (deliveryCreditBalance > 0 &&
                              deliveryCreditBalance >= totalDeliveryCharges) {
                              // valid

                              epMakingOrder(epOrderList, currOutlet, [], (result) => {
                                console.log(`epMakingOrder - ${moment().format('YYYY-MM-DD, HH:mm:ss')}`);
                                console.log(result);

                                if (result && result.status === 'success' &&
                                  result.data && result.data.api_status === 'Success') {
                                  if (result.data.result.length > 0) {
                                    const epMakingOrderResultList = result.data.result;

                                    console.log(`epMakingOrderResultList - ${moment().format('YYYY-MM-DD, HH:mm:ss')}`);
                                    console.log(epMakingOrderResultList);

                                    epMakingOrderPayment(epOrderList, currOutlet, epMakingOrderResultList, totalDeliveryCharges, async (result) => {
                                      console.log(`epMakingOrderPayment - ${moment().format('YYYY-MM-DD, HH:mm:ss')}`);
                                      console.log(result);

                                      if (result && result.status === 'success' &&
                                        result.data && result.data.api_status === 'Success') {
                                        if (result.data.result.length > 0) {
                                          let epMakingOrderPaymentResultList = result.data.result;

                                          console.log(`epMakingOrderPaymentResultList - ${moment().format('YYYY-MM-DD, HH:mm:ss')}`);
                                          console.log(epMakingOrderPaymentResultList);

                                          //////////////////

                                          // test account don't have credit, we insert fake data here

                                          if (epMakingOrderPaymentResultList.length > 0 &&
                                            epMakingOrderPaymentResultList[0].messagenow === 'Insufficient Credit') {
                                            epMakingOrderPaymentResultList[0].parcel = [
                                              {
                                                parcelno: 'EP-PQKTE',
                                                awb: '************',
                                                awb_id_link: 'https://koodooprod.s3.ap-southeast-1.amazonaws.com/pdf/airway-example.pdf',
                                                tracking_url: 'https://easyparcel.com/my/en/track/details/?courier=Skynet&amp;awb=************'
                                              },
                                            ];
                                          }

                                          //////////////////

                                          // update the easyparcel result into the UserOrder

                                          await updateEPDetailsToUserOrders(epOrderList, currOutlet, epMakingOrderResultList, epMakingOrderPaymentResultList, () => {
                                            Alert.alert('Info', 'Order(s) had been fulfilled.');

                                            navigation.navigate('History', {
                                              params: {
                                              },
                                            });
                                          });
                                        }
                                      }
                                    });
                                  }
                                }
                              });
                            }
                            else {
                              Alert.alert('Info', `Delivery credit not sufficient, please contact your account manager to top up the credit.\n\nRemaining delivery credit: RM ${deliveryCreditBalance.toFixed(2)}\nRequired delivery credit: RM ${totalDeliveryCharges.toFixed(2)}`);
                            }
                          }
                          else {
                            Alert.alert('Info', 'Please select the courier service before proceed.')
                          }
                        }}>
                        <View
                          style={{ flexDirection: 'row', alignItems: 'center' }}>
                          {/* <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} /> */}
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            CONFIRM
                          </Text>
                        </View>
                      </TouchableOpacity>
                    </View>
                  </>
                  :
                  <>
                    <View style={{ justifyContent: "center", alignItems: 'center', marginTop: 20, height: 80, }}>
                      <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Regular' }}>
                        No shipping rates found
                      </Text>
                    </View>
                  </>
                }
              </>
              :
              <View style={{ height: 50, marginVertical: 15, }}>
                <ActivityIndicator color={Colors.primaryColor} size={"large"} />
              </View>
            }
          </View>
        </ScrollView>
      </View >

    </UserIdleWrapper >

  );

};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  container1: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },

  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: '100%',
    height: '100%',
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInputTakeawayCharges: {
    backgroundColor: Colors.fieldtBgColor,
    width: 200,
    height: 40,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
    //marginLeft: 190,
  },

  textInput: {
    //fontFamily: 'NunitoSans-Regular',
    //width: 300,
    marginTop: 10,
    color: 'black',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
    paddingLeft: 10,
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 60,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 5,
    borderColor: '#E5E5E5',
    borderWidth: 1,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 110,
    height: Platform.OS == 'ios' ? 30 : 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput10: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput1: {
    fontFamily: 'NunitoSans-Regular',
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: 'NunitoSans-Regular',
    width: '85%',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 10,
  },
  textInput4: {
    width: '85%',
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: 'NunitoSans-Regular',
    width: '80 %',
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: 'center',
  },
  textInput7: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button1: {
    width: '15%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: '60%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: '2%',
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: '30%',
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 30,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold',
  },
  viewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0,
    marginBottom: 10,
  },
  openHourContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
    marginBottom: 15,
    width: '100%',
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
  },
  addButtonView1: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 65,
    marginTop: 7,
    width: '83%',
    alignSelf: 'flex-end',
  },
  addNewView1: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    alignItems: 'center',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftView: {
    flexDirection: 'row',
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  closeView: {
    flexDirection: 'row',
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 200,
    height: 40,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  taxView: {
    flexDirection: 'row',
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'flex-end',
  },
  sectionView: {
    flexDirection: 'row',
    borderRadius: 5,
    padding: 16,
    alignItems: 'center',
  },
  receiptView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
});
export default FulfillmentScreen;
