import { Text } from "react-native-fast-text";
import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useRef,
  createRef,
  useCallback,
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  Dimensions,
  TouchableOpacity,
  Switch,
  Modal as ModalComponent,
  KeyboardAvoidingView,
  Platform,
  useWindowDimensions,
  InteractionManager,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Feather from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import DropDownPicker from 'react-native-dropdown-picker';
import Styles from '../constant/Styles';
import moment from 'moment';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import { NativeViewGestureHandler, TextInput, ScrollView, FlatList } from 'react-native-gesture-handler';
import {
  getAddOnChoicePrice,
  getAddOnChoiceQuantity,
  getCartItemPriceWithoutAddOn,
  getOrderDiscountInfo,
  getOrderDiscountInfoInclOrderBased,
  getTransformForModalFullScreen,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet,
  logToFile
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import {
  ORDER_TYPE,
  PAYMENT_CHANNEL_NAME_PARSED,
  DINE_IN_SORT_FIELD_TYPE,
  DINE_IN_SORT_FIELD_TYPE_VALUE,
  REPORT_SORT_COMPARE_OPERATOR,
  REPORT_SORT_FIELD_TYPE_COMPARE,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  KD_PRINT_VARIATION,
  KD_ITEM_STATUS,
  PRIVILEGES_NAME,
  ROLE_TYPE,
  KD_PRINT_EVENT_TYPE,
  ORDER_TYPE_SUB,
} from '../constant/common';
import { PRINTER_USAGE_TYPE } from '../constant/printer';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import Entypo from 'react-native-vector-icons/Entypo';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { useNetInfo } from "@react-native-community/netinfo";
import { FlashList } from "@shopify/flash-list";
import { calcPrintTotalForKdIndividual, openCashDrawer, printDocket, printDocketCancelled, printDocketForKD, printDocketForKDCancelled, printKDSummaryCategoryWrapper, printUserOrder } from '../util/printer';
// import { storageMMKV } from '../util/storageMMKV';
import firestore from '@react-native-firebase/firestore';

import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { Collections } from "../constant/firebase";
import CheckBox from '@react-native-community/checkbox';

const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const OrderScreen = React.memo((props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const netInfo = useNetInfo();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [sort, setSort] = useState(null);
  const [sort1, setSort1] = useState(null);
  const [outletId, setOutletId] = useState(User.getOutletId());
  const [table, setTable] = useState([]);
  const [prepareTime, setPrepareTime] = useState([]);
  const [order, setOrder] = useState([]);
  const [expandOrder, setExpandOrder] = useState(false);
  const [filter, setFilter] = useState(null);
  const [lastSort, setLastSort] = useState(null);
  const [lastFilter, setLastFilter] = useState(null);
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [currToPrioritizeOrder, setCurrToPrioritizeOrder] = useState({});
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [modalCancelVisibility, setModalCancelVisibility] = useState(false);
  const [currToCancelOrder, setCurrToCancelOrder] = useState({});
  const [currOrderIndex, setCurrOrderIndex] = useState(0);

  const [modalAuthorizeVisibility, setModalAuthorizeVisibility] =
    useState(false);
  const [currToAuthorizeOrder, setCurrToAuthorizeOrder] = useState({});

  var BUTTON_APPEAR = {
    AUTHORIZED: 'BUTTON_APPEAR.AUTHORIZED',
    UNAUTHORIZED: 'BUTTON_APPEAR.UNAUTHORIZED',
  };

  const [buttonAppear, setButtonAppear] = useState(BUTTON_APPEAR.UNAUTHORIZED);

  const [controller, setController] = useState({});
  const [controller1, setController1] = useState({});
  const [refArray, setRefArray] = useState([]);

  const [refreshRate, setRefreshRate] = useState(new Date());

  const [expandViewDict, setExpandViewDict] = useState({});

  const ingreStat = 1;

  const [dineInOrders, setDineInOrders] = useState([]);

  ///////////Sorting////////
  const [sortOrderID, setSortOrderID] = useState();
  const [sortDateTime, setSortDateTime] = useState();
  const [sortCustomerName, setSortCustomerName] = useState();
  const [sortWaitingTime, setSortWaitingTime] = useState();
  const [sortAuthorization, setSortAuthorization] = useState();
  // const [sortPaymentMethod, setSortPaymentMethod] = useState();
  const [sortTotalPrice, setSortTotalPrice] = useState();
  /////////////////////////////

  const [search, setSearch] = useState('');
  const [filterType, setFilterType] = useState(0)

  const userOrders = OutletStore.useState((s) => s.userOrders);
  // const userOrdersTableDict = OutletStore.useState((s) => s.userOrdersTableDict);

  const userName = UserStore.useState((s) => s.name);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const merchantName = MerchantStore.useState((s) => s.name);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  /////////////////////////////////////////////////////////////////////////

  // 2025-06-26 - Individual User Managed Category
  const userManagedCategory = UserStore.useState((s) => s.userManagedCategory);

  /////////////////////////////////////////////////////////////////////////

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const privileges_state = UserStore.useState((s) => s.privileges);

  const [privileges, setPrivileges] = useState([]);
  const role = UserStore.useState((s) => s.role);
  const pinNo = UserStore.useState(s => s.pinNo);
  const [remark, setRemark] = useState('');
  const [selectedCartItemDict, setSelectedCartItemDict] = useState({});
  const [expandedOrderId, setExpandedOrderId] = useState(null);

  useEffect(() => {
    const useEffectCallback = async () => {
      // admin full access

      // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
      // const enteredPinNo = storageMMKV.getString('enteredPinNo');
      const enteredPinNo = await global.watermelonDBDatabase.localStorage.get('enteredPinNo');

      if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
        setPrivileges([
          "EMPLOYEES",
          "OPERATION",
          "PRODUCT",
          "INVENTORY",
          "INVENTORY_COMPOSITE",
          "DOCKET",
          "VOUCHER",
          "PROMOTION",
          "CRM",
          "LOYALTY",
          "TRANSACTIONS",
          "REPORT",
          "RESERVATIONS",

          // for action
          'REFUND_ORDER',

          'SETTINGS',

          'QUEUE',

          'OPEN_CASH_DRAWER',

          'KDS',

          'UPSELLING',

          // for Kitchen

          'REJECT_ITEM',
          'CANCEL_ORDER',
          //'REFUND_tORDER',

          'MANUAL_DISCOUNT',
        ]);
      } else {
        setPrivileges(privileges_state || []);
      }
    };

    useEffectCallback();
  }, [role, privileges_state, pinNo]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      setTargetOutletDropdownList(
        allOutlets.map((outlet) => ({
          label: outlet.name,
          value: outlet.uniqueId,
        })),
      );

      if (allOutlets.length > 0) {
        setSelectedTargetOutletId(currOutletId);
      }
    });
  }, [allOutlets, currOutletId]);

  // useEffect(() => {
  //   if (isMounted) {
  //     InteractionManager.runAfterInteractions(() => {
  //       setDineInOrders(
  //         userOrders.filter((order) => {
  //           console.log('===========');
  //           console.log(order.orderId);
  //           console.log(order.orderType);
  //           console.log(order.uniqueId);

  //           return order.orderType === ORDER_TYPE.DINEIN &&
  //             (
  //               order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED
  //               ||
  //               order.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED
  //               ||
  //               order.orderStatus === USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT
  //               ||
  //               order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING
  //               ||
  //               order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED
  //               ||
  //               order.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED
  //             )
  //         }
  //         ),
  //       );

  //       // // console.log('dineInOrders');
  //       // // console.log(userOrders.filter(order => order.orderType === ORDER_TYPE.DINEIN));
  //     });
  //   }
  // }, [userOrders, isMounted]);

  useEffect(() => {
    setRefArray((ref) =>
      Array(dineInOrders.length)
        .fill()
        .map((_, i) => ref[i] || createRef()),
    );
  }, [dineInOrders.length]);


  const [filteredDineInOrders, setFilteredDineInOrders] = useState([]);

  useEffect(() => {
    const filtered = sortDineIn(dineInOrders, sort1)
      .slice(0)
      .sort((a, b) => b.orderDate - a.orderDate)
      .sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder)
      .filter((item) => {
        // Apply search filter
        if (search.trim() !== '') {
          const searchLowerCase = search.toLowerCase();
          const itemMatch = item.cartItems.some(cartItem =>
            cartItem.name.toLowerCase().includes(searchLowerCase) ||
            cartItem.addOns.some(addOn =>
              addOn.name.toLowerCase().includes(searchLowerCase) ||
              addOn.choiceNames.some(choiceName =>
                choiceName.toLowerCase().includes(searchLowerCase)
              )
            )
          );

          const otherFieldsMatch =
            (item.tableCode || '').toLowerCase().includes(searchLowerCase) ||
            item.orderId.toLowerCase().includes(searchLowerCase) ||
            item.waiterName.toLowerCase().includes(searchLowerCase);

          if (!(itemMatch || otherFieldsMatch)) return false;
        }

        // Apply filter type
        if (filterType === 1) {
          return item.paymentDetails === null;
        }
        else if (filterType === 2) {
          return item.paymentDetails !== null;
        }

        return true; // All Orders or no filter
      });

    setFilteredDineInOrders(filtered);
    console.log('userOrder', filtered[0])
  }, [dineInOrders, sort1, search, filterType]);

  // useEffect(() => {
  //   setTimeout(() => {
  //     setRefreshRate(new Date());

  //     checkOvertimeOrders();
  //   }, 30000);

  //   checkOvertimeOrders();
  // }, [refreshRate]);

  const sortDineIn = (dataList, dineInSortFieldType) => {
    var dataListTemp = [...dataList];
    // console.log('dineindataList');
    // console.log(dataList);

    // console.log('dineInSortFieldType');
    // console.log(dineInSortFieldType);

    const dineInSortFieldTypeValue =
      DINE_IN_SORT_FIELD_TYPE_VALUE[dineInSortFieldType];

    const dineInSortFieldTypeCompare =
      REPORT_SORT_FIELD_TYPE_COMPARE[dineInSortFieldType];

    //TABLE_CODE
    // if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
    //   if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.TABLE_CODE_ASC) {
    //     dataListTemp.sort1((a, b) =>
    //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
    //         .localeCompare(
    //          b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
    //     );
    //   } else {
    //     dataListTemp.sort1((a, b) =>
    //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
    //         .localeCompare(
    //         b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : ''),
    //     );
    //   }
    // }
    // else if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
    //   if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.TABLE_CODE_DESC) {
    //     dataListTemp.sort1((a, b) =>
    //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
    //         .localeCompare(
    //          a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
    //     );
    //   } else {
    //     dataListTemp.sort1((a, b) =>
    //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
    //         .localeCompare(
    //         a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : ''),
    //     );
    //   }
    // }

    //ORDER_ID
    // if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
    //   if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.ORDER_ID_ASC) {
    //     dataListTemp.sort1((a, b) =>
    //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
    //         -
    //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
    //     );
    //   } else {
    //     dataListTemp.sort1((a, b) =>
    //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
    //         -
    //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : ''),
    //     );
    //   }
    // }
    // else if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
    //   if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.ORDER_ID_DESC) {
    //     dataListTemp.sort1((a, b) =>
    //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
    //         -
    //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '')
    //     );
    //   } else {
    //     dataListTemp.sort1((a, b) =>
    //         (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '')
    //         -
    //         (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : ''),
    //     );
    //   }
    // }

    //ORDER_DATE
    if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.ORDER_DATE_ASC) {
        dataListTemp.sort1(
          (a, b) =>
            (moment(a[dineInSortFieldTypeValue]).valueOf()
              ? moment(a[dineInSortFieldTypeValue]).valueOf()
              : null) -
            (moment(b[dineInSortFieldTypeValue]).valueOf()
              ? moment(b[dineInSortFieldTypeValue]).valueOf()
              : null),
        );
      } else {
        dataListTemp.sort1(
          (a, b) =>
            (moment(a[dineInSortFieldTypeValue]).valueOf()
              ? moment(a[dineInSortFieldTypeValue]).valueOf()
              : null) -
            (moment(b[dineInSortFieldTypeValue]).valueOf()
              ? moment(b[dineInSortFieldTypeValue]).valueOf()
              : null),
        );
      }
    } else if (
      dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.ORDER_DATE_DESC) {
        dataListTemp.sort1(
          (a, b) =>
            (moment(b[dineInSortFieldTypeValue]).valueOf()
              ? moment(b[dineInSortFieldTypeValue]).valueOf()
              : null) -
            (moment(a[dineInSortFieldTypeValue]).valueOf()
              ? moment(a[dineInSortFieldTypeValue]).valueOf()
              : null),
        );
      } else {
        dataListTemp.sort1(
          (a, b) =>
            (moment(b[dineInSortFieldTypeValue]).valueOf()
              ? moment(b[dineInSortFieldTypeValue]).valueOf()
              : null) -
            (moment(a[dineInSortFieldTypeValue]).valueOf()
              ? moment(a[dineInSortFieldTypeValue]).valueOf()
              : null),
        );
      }
    }

    //WAITER_NAME
    if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.WAITER_NAME_ASC) {
        dataListTemp.sort1((a, b) =>
          (a[dineInSortFieldTypeValue]
            ? a[dineInSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort1((a, b) =>
          (a[dineInSortFieldTypeValue]
            ? a[dineInSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '',
          ),
        );
      }
    } else if (
      dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.WAITER_NAME_DESC) {
        dataListTemp.sort1((a, b) =>
          (b[dineInSortFieldTypeValue]
            ? b[dineInSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort1((a, b) =>
          (b[dineInSortFieldTypeValue]
            ? b[dineInSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '',
          ),
        );
      }
    }

    //WAITING_TIME
    if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.WAITING_TIME_ASC) {
        dataListTemp.sort1(
          (a, b) =>
            (moment(a[dineInSortFieldTypeValue]).valueOf()
              ? moment(a[dineInSortFieldTypeValue]).valueOf()
              : '') -
            (moment(b[dineInSortFieldTypeValue]).valueOf()
              ? moment(b[dineInSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort1(
          (a, b) =>
            (moment(a[dineInSortFieldTypeValue]).valueOf()
              ? moment(a[dineInSortFieldTypeValue]).valueOf()
              : '') -
            (moment(b[dineInSortFieldTypeValue]).valueOf()
              ? moment(b[dineInSortFieldTypeValue]).valueOf()
              : ''),
        );
      }
    } else if (
      dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.WAITING_TIME_DESC) {
        dataListTemp.sort1(
          (a, b) =>
            (moment(b[dineInSortFieldTypeValue]).valueOf()
              ? moment(b[dineInSortFieldTypeValue]).valueOf()
              : '') -
            (moment(a[dineInSortFieldTypeValue]).valueOf()
              ? moment(a[dineInSortFieldTypeValue]).valueOf()
              : ''),
        );
      } else {
        dataListTemp.sort1(
          (a, b) =>
            (moment(b[dineInSortFieldTypeValue]).valueOf()
              ? moment(b[dineInSortFieldTypeValue]).valueOf()
              : '') -
            (moment(a[dineInSortFieldTypeValue]).valueOf()
              ? moment(a[dineInSortFieldTypeValue]).valueOf()
              : ''),
        );
      }
    }

    //PAYMENT_DETAILS

    //FINAL_PRICE
    if (dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.FINAL_PRICE_ASC) {
        dataListTemp.sort1(
          (a, b) =>
            (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '') -
            (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort1(
          (a, b) =>
            (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : '') -
            (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : ''),
        );
      }
    } else if (
      dineInSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (dineInSortFieldType === DINE_IN_SORT_FIELD_TYPE.FINAL_PRICE_DESC) {
        dataListTemp.sort1(
          (a, b) =>
            (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '') -
            (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : ''),
        );
      } else {
        dataListTemp.sort1(
          (a, b) =>
            (b[dineInSortFieldTypeValue] ? b[dineInSortFieldTypeValue] : '') -
            (a[dineInSortFieldTypeValue] ? a[dineInSortFieldTypeValue] : ''),
        );
      }
    }

    return dataListTemp;
  };

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.05 }
          //   : {},
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Dine-In Order
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   setInterval(() => {
  //     getOrderList();
  //   }, 5000);
  //   getOrderList();

  // }

  const checkOvertimeOrders = async () => {
    for (var i = 0; i < dineInOrders.length; i++) {
      const waitingTime =
        // (moment().valueOf() - dineInOrders[i].estimatedPreparedDate) /
        (moment().valueOf() - dineInOrders[i].createdAt) /
        (1000 * 60);

      if (waitingTime >= 300) {
        await cancelOrder(dineInOrders[i], false);
      }
    }
  };

  const getOrderList = () => {
    ApiClient.GET(API.getCurrentOutletOrder + User.getOutletId()).then(
      (result) => {
        var dineInList = [];
        for (const order of result) {
          if (order.customTable != 'TAKE AWAY') {
            dineInList.push(order);
          }
        }

        if (unfilteredOrder && unfilteredOrder.length > 0) {
          var diff = false;

          if (dineInList.length !== unfilteredOrder.length) {
            diff = true;
          } else {
            for (var i = 0; i < dineInList.length; i++) {
              if (dineInList[i].id !== unfilteredOrder[i].id) {
                diff = true;
                break;
              }
            }
          }

          diff &&
            setState({
              order: [...dineInList],
              unfilteredOrder: [...dineInList],
            });
        } else {
          setState({ order: [...dineInList], unfilteredOrder: [...dineInList] });
        }
      },
    );
  };

  const sortingOrders = (param) => {
    if (param.value == 1) {
      //orderid
      setDineInOrders(
        dineInOrders
          .slice(0)
          .sort((a, b) => b.orderId.localeCompare(a.orderId)),
      );
    }
    if (param.value == 2) {
      //date time
      setDineInOrders(
        dineInOrders.slice(0).sort((a, b) => b.orderDate - a.orderDate),
      );
    }
    if (param.value == 3) {
      //Name
      setDineInOrders(
        dineInOrders
          .slice(0)
          .sort((a, b) => b.userName.localeCompare(a.userName)),
      );
    }
    if (param.value == 4) {
      //Waiting Time
      setDineInOrders(
        dineInOrders
          .slice(0)
          // .sort(
          //   (a, b) =>
          //     moment().valueOf() -
          //     b.estimatedPreparedDate -
          //     (moment().valueOf() - a.estimatedPreparedDate),
          // ),
          .sort(
            (a, b) =>
              moment().valueOf() -
              b.createdAt -
              (moment().valueOf() - a.createdAt),
          ),
      );
    }
    if (param.value == 5) {
      //Payment Method
      setDineInOrders(
        dineInOrders
          .slice(0)
          .sort((a, b) => b.orderStatus.localeCompare(a.orderStatus)),
      );
    }
    if (param.value == 6) {
      //Total
      setDineInOrders(
        dineInOrders.slice(0).sort((a, b) => b.finalPrice - a.finalPrice),
      );
    }
  };

  const filterOrders = (param) => {
    if (param.value == 0) {
      // All orders
      setDineInOrders(
        userOrders.filter((order) => order.orderType === ORDER_TYPE.DINEIN),
      );
    }

    if (param.value == 1) {
      //Awaiting Authorizaion
      setDineInOrders(
        userOrders.filter(
          (order) =>
            order.orderType === ORDER_TYPE.DINEIN &&
            order.paymentDetails === null,
        ),
      );
    }

    if (param.value == 2) {
      //Paid
      setDineInOrders(
        userOrders.filter(
          (order) =>
            order.orderType === ORDER_TYPE.DINEIN &&
            order.paymentDetails !== null,
        ),
      );
    }
  };

  const expandOrderFunc = (param) => {
    // if (expandOrder == false) {
    //   // return setState({ expandOrder: true }), param.expandOrder = true;
    //   // setExpandOrder(true);
    //   setExpandViewDict({
    //     ...expandViewDict,
    //     [param.uniqueId]: true,
    //   });
    // } else {
    //   // return setState({ expandOrder: false }), param.expandOrder = false;
    //   // setExpandOrder(false);
    //   setExpandViewDict({
    //     ...expandViewDict,
    //     [param.uniqueId]: undefined,
    //   });
    // }

    // if (!expandViewDict[param.uniqueId]) {
    //   // Clear selectedCartItemDict when expanding a different order
    //   setSelectedCartItemDict({});

    //   // return setState({ expandOrder: true }), param.expandOrder = true;
    //   // setExpandOrder(true);
    //   setExpandViewDict({
    //     ...expandViewDict,
    //     [param.uniqueId]: true,
    //   });
    // } else {
    //   // return setState({ expandOrder: false }), param.expandOrder = false;
    //   // setExpandOrder(false);
    //   setExpandViewDict({
    //     ...expandViewDict,
    //     [param.uniqueId]: false,
    //   });
    // }
    if (expandedOrderId !== param.uniqueId) {
      setSelectedCartItemDict({});
      setExpandedOrderId(param.uniqueId);
      console.log('order id', param.uniqueId)
    } else {
      setExpandedOrderId(null);
    }
  };

  const prioritizeOrder = (param) => {
    var body = {
      orderId: param,
    };

    // Alert.alert(
    //   'Success',
    //   'The order had been prioritized',
    //   [{ text: 'OK', onPress: () => { } }],
    //   { cancelable: false },
    // );
    // setVisible(false);

    // ApiClient.POST(API.prioritizeOrder, body, false)
    APILocal.prioritizeOrder({ body })
      .then((result) => {
        if (result !== null) {
          Alert.alert(
            'Success',
            'Order has been prioritized',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          // setState({ visible: false, visible1: false });
          setVisible(false);

          for (const ref of refArray) {
            if (refArray.indexOf(ref) === currOrderIndex && ref && ref.current) {
              ref.current.close();
            }
          }
        } else {
          Alert.alert(
            'Failed',
            'Your request has failed',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          // setState({ visible: false, visible1: false });
          setVisible(false);
        }
      });
  };

  const cancelOrder = async (param, showAlert = true) => {
    var body = {
      orderId: param.uniqueId,
      tableId: param.tableId,

      cancelRemarks: remark || '',

      orderIdHuman: `#${(param.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + param.orderId}`,

      cartItems: param.cartItems,
      cartItemsCancelled: param.cartItemsCancelled ? param.cartItemsCancelled : [],
    };

    // ApiClient.POST(API.cancelUserOrderByMerchant, body, false)
    APILocal.cancelUserOrderByMerchant({ body, uid: firebaseUid })
      .then(
        async (result) => {
          if (result && result.status === 'success') {
            const order = {
              ...param,
              ...result.data,
            };

            await printUserOrder(
              {
                // orderId: orderIdList[i],
                orderData: order,
                // receiptNote: currOutlet.receiptNote || '',
              },
              false,
              [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
              false,
              false,
              false,
              netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
              true,
              [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
              // cancelUser,
            );

            printKDSummaryCategoryWrapper(
              {
                // orderId: orderIdList[i],
                orderData: order,
              },
              [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
              // cancelUser,
            );

            if (order && order.cartItemsCancelled && order.cartItemsCancelled.length > 0) {
              for (let bdIndex = 0; bdIndex < order.cartItemsCancelled.length; bdIndex++) {
                if (!order.cartItemsCancelled[bdIndex].isDocket) {
                  await printDocketForKDCancelled(
                    {
                      userOrder: order,
                      cartItem: order.cartItemsCancelled[bdIndex],
                    },
                    // true,
                    [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                    // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                    [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                    // cancelUser,
                  );
                }
              }

              for (let index = 0; index < order.cartItemsCancelled.length; index++) {
                if (order.cartItemsCancelled[index].isDocket) {
                  await printDocketCancelled(
                    {
                      userOrder: order,
                      cartItem: order.cartItemsCancelled[index],
                    },
                    // true,
                    [PRINTER_USAGE_TYPE.RECEIPT],
                    [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                    // cancelUser,
                  );
                }
              }
            }

            if (showAlert) {
              Alert.alert(
                'Success',
                'Order has been cancelled',
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
              );
              setModalCancelVisibility(false);

              for (const ref of refArray) {
                if (
                  refArray.indexOf(ref) === currOrderIndex &&
                  ref &&
                  ref.current
                ) {
                  ref.current.close();
                }
              }
            }

            if (global.currOutlet.cancelRejectPrintOs) {
              printUserOrder(
                {
                  orderData: order,
                },
                false,
                [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                false,
                false,
                false,
                { isInternetReachable: true, isConnected: true },
                true, // for isPrioritized
              );

              if (order.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
                printUserOrder(
                  {
                    // orderId: refundOrderId,
                    orderData: order,
                    receiptNote: currOutlet.receiptNote || '',
                  },
                  true,
                  [PRINTER_USAGE_TYPE.RECEIPT],
                  false,
                  false,
                  false,
                  netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                  true,
                  [],
                  [],
                  true,
                );
              }
            }

            if (global.outletKdEventTypes.includes(KD_PRINT_EVENT_TYPE.CANCEL)) {
              await printUserOrder(
                {
                  // orderId: orderIdList[i],
                  orderData: order,
                  // receiptNote: currOutlet.receiptNote || '',
                },
                false,
                [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                false,
                false,
                false,
                netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                true,
                [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                // cancelUser,
              );

              printKDSummaryCategoryWrapper(
                {
                  // orderId: orderIdList[i],
                  orderData: order,
                },
                [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                // cancelUser,
              );

              if (order && order.cartItemsCancelled && order.cartItemsCancelled.length > 0) {
                for (let bdIndex = 0; bdIndex < order.cartItemsCancelled.length; bdIndex++) {
                  if (!order.cartItemsCancelled[bdIndex].isDocket) {
                    await printDocketForKDCancelled(
                      {
                        userOrder: order,
                        cartItem: order.cartItemsCancelled[bdIndex],
                      },
                      // true,
                      [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                      // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                      [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                      // cancelUser,
                    );
                  }
                }

                for (let index = 0; index < order.cartItemsCancelled.length; index++) {
                  if (order.cartItemsCancelled[index].isDocket) {
                    await printDocketCancelled(
                      {
                        userOrder: order,
                        cartItem: order.cartItemsCancelled[index],
                      },
                      // true,
                      [PRINTER_USAGE_TYPE.RECEIPT],
                      [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                      // cancelUser,
                    );
                  }
                }
              }
            }
          } else if (showAlert) {
            Alert.alert(
              'Failed',
              'Your request has failed',
              [{ text: 'OK', onPress: () => { } }],
              { cancelable: false },
            );
            // setState({ visible: false });
            // setVisible(false);
            setModalCancelVisibility(false);
          }
        },
      );
  };

  // const rightAction = (item, index) => {
  //   return (
  //     <View
  //       style={{
  //         justifyContent: 'center',
  //         alignItems: 'center',
  //         flexDirection: 'row',
  //         // width: '20%',
  //       }}>
  //       <TouchableOpacity
  //         onPress={() => {
  //           // setState({
  //           //   currToPrioritizeOrder: item,
  //           //   visible: true,
  //           // });
  //           setCurrOrderIndex(index);
  //           setCurrToPrioritizeOrder(item);
  //           setVisible(true);
  //         }}
  //         style={{
  //           height: '100%',
  //           justifyContent: 'center',
  //           alignItems: 'center',
  //           alignContent: 'center',
  //           alignSelf: 'center',
  //           backgroundColor: Colors.primaryColor,
  //           underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //           paddingBottom: 6,
  //           width: 75,
  //         }}>
  //         {switchMerchant ? (
  //           <MaterialCommunityIcons
  //             name="message-alert-outline"
  //             size={10}
  //             color={Colors.whiteColor}
  //             style={{ marginTop: 10 }}
  //           />
  //         ) : (
  //           <MaterialCommunityIcons
  //             name="message-alert-outline"
  //             size={40}
  //             color={Colors.whiteColor}
  //             style={{ marginTop: 10 }}
  //           />
  //         )}

  //         <Text
  //           style={[
  //             {
  //               color: Colors.whiteColor,
  //               fontSize: 12,
  //               fontFamily: 'NunitoSans-Regular',
  //               textAlign: 'center',
  //               width: windowWidth <= 1133 ? '85%' : '80%',
  //             },
  //             switchMerchant
  //               ? {
  //                 fontSize: 10,
  //               }
  //               : {},
  //           ]}>
  //           {`Prioritize\nOrder`}
  //         </Text>
  //       </TouchableOpacity>

  //       {(
  //         (item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
  //           item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
  //           item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
  //           item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED)
  //         &&
  //         (item.tableId === '')
  //         // &&
  //         // item.paymentDetails === null
  //       )
  //         ? (
  //           <TouchableOpacity
  //             onPress={() => {
  //               // setState({
  //               //   currToPrioritizeOrder: item,
  //               //   visible: true,
  //               // });

  //               // setCurrOrderIndex(index);
  //               // setCurrToPrioritizeOrder(item);
  //               // setVisible(true);

  //               CommonStore.update(s => {
  //                 s.isCheckingOutTakeaway = true;

  //                 s.checkingOutTakeawayOrder = item;

  //                 s.timestamp = Date.now();
  //               }, () => {
  //                 navigation.navigate('Table');
  //               });
  //             }}
  //             style={{
  //               height: '100%',
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               alignSelf: 'center',
  //               backgroundColor: Colors.tabCyan,
  //               underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //               paddingBottom: 6,
  //               width: 75,
  //             }}>
  //             {switchMerchant ? (
  //               <MaterialIcons
  //                 name="payment"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             ) : (
  //               <MaterialIcons
  //                 name="payment"
  //                 size={40}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             )}
  //             <Text
  //               style={[
  //                 {
  //                   color: Colors.whiteColor,
  //                   fontSize: 12,
  //                   fontFamily: 'NunitoSans-Regular',
  //                   textAlign: 'center',
  //                   width: '80%',
  //                 },
  //                 switchMerchant
  //                   ? {
  //                     fontSize: 10,
  //                   }
  //                   : {},
  //               ]}>
  //               Checkout Order
  //             </Text>
  //           </TouchableOpacity>
  //         ) : null}
  //       {
  //         (currOutlet && currOutlet.privileges &&
  //           currOutlet.privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER))
  //           && privileges && privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER) ?
  //           <TouchableOpacity
  //             onPress={() => {
  //               // setState({
  //               //   currToPrioritizeOrder: item,
  //               //   visible: true,
  //               // });

  //               // setCurrOrderIndex(index);
  //               // setCurrToCancelOrder(item);
  //               // setModalCancelVisibility(true);

  //               if (item.paymentDetails && (
  //                 item.paymentDetails.txn_ID !== undefined ||
  //                 item.paymentDetails.txnId !== undefined
  //               )) {
  //                 Alert.alert('Info', 'Online paid order is unable to cancel.');
  //               }
  //               else {
  //                 setCurrOrderIndex(index);
  //                 setCurrToCancelOrder(item);
  //                 setModalCancelVisibility(true);
  //               }
  //             }}
  //             style={{
  //               height: '100%',
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               alignSelf: 'center',
  //               // backgroundColor: Colors.primaryColor,
  //               backgroundColor: '#d90000',
  //               underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //               paddingBottom: 6,
  //               width: 75,
  //             }}>
  //             {switchMerchant ? (
  //               <MaterialCommunityIcons
  //                 name="close"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             ) : (
  //               <MaterialCommunityIcons
  //                 name="close"
  //                 size={40}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             )}

  //             <Text
  //               style={[
  //                 {
  //                   color: Colors.whiteColor,
  //                   fontSize: 12,
  //                   fontFamily: 'NunitoSans-Regular',
  //                   textAlign: 'center',
  //                   width: windowWidth <= 1133 ? '85%' : '80%',
  //                 },
  //                 switchMerchant
  //                   ? {
  //                     fontSize: 10,
  //                   }
  //                   : {},
  //               ]}>
  //               {`Cancel\nOrder`}
  //             </Text>
  //           </TouchableOpacity>
  //           : <></>
  //       }

  //       {/* 2023-01-30 - For reprint kd */}

  //       <TouchableOpacity
  //         onPress={async () => {
  //           ///////////////////////////////////////////////////////////////////////////

  //           Alert.alert('Info', 'Kitchen docket has been added to print queue');

  //           var printTimes = 1;

  //           if (global.outletCategoriesDict) {
  //             if (item.cartItems && item.cartItems.length > 0) {
  //               for (var i = 0; i < item.cartItems.length; i++) {
  //                 if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
  //                   global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
  //                   printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
  //                 }
  //               }
  //             }

  //             if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
  //               for (var i = 0; i < item.cartItemsCancelled.length; i++) {
  //                 if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
  //                   global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
  //                   printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
  //                 }
  //               }
  //             }
  //           }

  //           for (var i = 0; i < printTimes; i++) {
  //             await printUserOrder(
  //               {
  //                 orderData: item,
  //               },
  //               false,
  //               [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //               false,
  //               false,
  //               false,
  //               { isInternetReachable: true, isConnected: true },
  //               false,
  //               [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
  //             );

  //             printKDSummaryCategoryWrapper(
  //               {
  //                 orderData: item,
  //               },
  //               [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
  //             );

  //             if (item && item.cartItems && item.cartItems.length > 0) {
  //               for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
  //                 if (!item.cartItems[bdIndex].isDocket) {
  //                   await printDocketForKD(
  //                     {
  //                       userOrder: item,
  //                       cartItem: item.cartItems[bdIndex],
  //                     },
  //                     // true,
  //                     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //                     // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
  //                     [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
  //                     // deliveredUser,
  //                   );
  //                 }
  //               }

  //               for (let index = 0; index < item.cartItems.length; index++) {
  //                 if (item.cartItems[index].isDocket) {
  //                   await printDocket(
  //                     {
  //                       userOrder: item,
  //                       cartItem: item.cartItems[index],
  //                     },
  //                     // true,
  //                     [PRINTER_USAGE_TYPE.RECEIPT],
  //                     [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
  //                     // deliveredUser,
  //                   );
  //                 }
  //               }
  //             }
  //           }

  //           // for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
  //           //   await printDocketForKD(
  //           //     {
  //           //       userOrder: item,
  //           //       cartItem: item.cartItems[bdIndex],
  //           //     },
  //           //     // true,
  //           //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           //     // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
  //           //   );
  //           // }

  //           ///////////////////////////////////////////////////////////////////////////

  //           // disconnectPrinter(printer); // no need anymore

  //           // await printUserOrder(
  //           //   {
  //           //     orderId: item.uniqueId,
  //           //     receiptNote: currOutlet.receiptNote || '',
  //           //   },
  //           //   false,
  //           //   [PRINTER_USAGE_TYPE.RECEIPT],
  //           //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           //   false,
  //           //   false,
  //           //   false,
  //           //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
  //           //   // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
  //           //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           // );

  //           // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
  //           //   await printUserOrder(
  //           //     {
  //           //       orderData: item,
  //           //     },
  //           //     false,
  //           //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           //     false,
  //           //     false,
  //           //     false,
  //           //     { isInternetReachable: true, isConnected: true },
  //           //   );
  //           // }
  //           // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
  //           //   printKDSummaryCategoryWrapper(
  //           //     {
  //           //       orderData: item,
  //           //     },
  //           //   );
  //           // }
  //           // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
  //           //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
  //           //     await printDocketForKD(
  //           //       {
  //           //         userOrder: item,
  //           //         cartItem: item.cartItems[bdIndex],
  //           //       },
  //           //       // true,
  //           //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
  //           //     );
  //           //   }
  //           // }
  //         }}
  //         style={{
  //           height: '100%',
  //           justifyContent: 'center',
  //           alignItems: 'center',
  //           alignContent: 'center',
  //           alignSelf: 'center',
  //           backgroundColor: Colors.secondaryColor,
  //           underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //           paddingBottom: 6,
  //           width: 75,
  //         }}>
  //         {switchMerchant ? (
  //           <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
  //         ) : (
  //           <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
  //         )}

  //         <Text
  //           style={[
  //             {
  //               color: Colors.whiteColor,
  //               fontSize: 12,
  //               fontFamily: 'NunitoSans-Regular',
  //               textAlign: 'center',
  //               width: windowWidth <= 1133 ? '85%' : '80%',
  //             },
  //             switchMerchant
  //               ? {
  //                 fontSize: 10,
  //               }
  //               : {},
  //           ]}>
  //           {`Reprint\nKD`}
  //         </Text>
  //       </TouchableOpacity>
  //     </View>
  //   );
  // };

  const renderOrder = ({ item, index }) => {
    const waitingTime = (moment().valueOf() - item.createdAt) / (1000 * 60);
    var hrs = Math.floor(moment().diff(item.updatedAt, 'minutes') / 60);
    var mins = Math.floor(moment().diff(item.updatedAt, 'minutes') % 60);

    ///////////////////////////

    // console.log('order id');
    // console.log(item.orderId);

    // calculate longest

    var longestStr = 5;

    // for (var i = 0; i < item.cartItems.length; i++) {
    //   const cartItemPriceWIthoutAddOn =
    //     item.cartItems[i].price -
    //     item.cartItems[i].addOns.reduce(
    //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
    //       0,
    //     );

    //   if (cartItemPriceWIthoutAddOn.toFixed(0).length > longestStr) {
    //     longestStr = cartItemPriceWIthoutAddOn.toFixed(0).length;
    //   }

    //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
    //     if (
    //       item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length > longestStr
    //     ) {
    //       longestStr = item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length;
    //     }
    //   }
    // }

    // if (item.totalPrice.toFixed(0).length > longestStr) {
    //   longestStr = item.totalPrice.toFixed(0).length;
    // }

    // if (item.discount.toFixed(0).length > longestStr) {
    //   longestStr = item.discount.toFixed(0).length;
    // }

    // if (item.tax.toFixed(0).length > longestStr) {
    //   longestStr = item.tax.toFixed(0).length;
    // }

    // if (item.finalPrice.toFixed(0).length > longestStr) {
    //   longestStr = item.finalPrice.toFixed(0).length;
    // }

    // // console.log(longestStr);

    ///////////////////////////

    // calculate spacing

    var cartItemPriceWIthoutAddOnSpacingList = [];
    var addOnsSpacingList = [];

    // for (var i = 0; i < item.cartItems.length; i++) {
    //   const cartItemPriceWIthoutAddOn =
    //     item.cartItems[i].price -
    //     item.cartItems[i].addOns.reduce(
    //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
    //       0,
    //     );

    //   cartItemPriceWIthoutAddOnSpacingList.push(
    //     Math.max(longestStr - cartItemPriceWIthoutAddOn.toFixed(0).length, 0) +
    //     1,
    //   );

    //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
    //     addOnsSpacingList.push(
    //       Math.max(
    //         longestStr -
    //         item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length,
    //         0,
    //       ) + 1,
    //     );
    //   }
    // }

    // var totalPriceSpacing =
    //   Math.max(longestStr - item.totalPrice.toFixed(0).length, 0) + 1;
    // var discountSpacing =
    //   Math.max(longestStr - item.discount.toFixed(0).length, 0) + 1;
    // var taxSpacing = Math.max(longestStr - item.tax.toFixed(0).length, 0) + 1;
    // var finalPriceSpacing =
    //   Math.max(longestStr - item.finalPrice.toFixed(0).length, 0) + 1;

    ///////////////////////////

    return (
      <View
        style={{
          marginVertical: 5,

          paddingVertical: 5,

          shadowOpacity: 0,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          // elevation: 1,
          elevation: 3,

          ...(item.isReservationOrder && {
            elevation: 2,
            borderRadius: 5,
            backgroundColor: 'white',
            borderWidth: 2,
            borderColor: Colors.tabCyan,
          }),
        }}>
        {/* <Swipeable
          renderRightActions={() => rightAction(item, index)}
          // ref={ref => {
          //   if (ref && !refArray.includes(ref)) {
          //     refArray[index] = ref;
          //   }
          // }}
          ref={refArray[index]}
          onSwipeableWillOpen={() => {
            // for (const ref of refArray) {
            //   if (refArray.indexOf(ref) != index && ref && ref.current) {
            //     ref.current.close()
            //   }
            // }
          }}> */}
        <TouchableOpacity
          onPress={() => {
            // console.log(windowWidth);
            //// console.log('hello123')
            expandOrderFunc(item);
          }}>
          <View
            style={[
              {
                elevation: 1,
                borderRadius: 5,
                backgroundColor: 'white'
              },
              (
                // currOutlet.dineInRequiredAuthorization 
                // &&
                (item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED))
                ?
                {
                  borderWidth: 2,
                  borderColor: Colors.whiteColor,
                }
                :
                {
                  borderWidth: 2,
                  borderColor: Colors.primaryColor,
                },
            ]}>
            <View
              style={{
                opacity: item.isPrioritizedOrder ? 100 : 0,
                width: Platform.OS == 'ios' ? '7%' : '7%',
                alignItems: 'center',
                position: 'absolute',
                zIndex: 1,
                //paddingLeft: '2%',
              }}>
              <View
                style={[
                  {
                    width: 63,
                    backgroundColor: '#e08300',
                    height: 18,
                    top: windowHeight <= 1024 ? 1 : 4,
                    // right: Platform.OS == 'ios' ? 0 : 5,
                    // left: Platform.OS == 'ios' ? 16 : 0,
                    // right: Platform.OS == 'android' ? 0 : 5,
                    // left: Platform.OS == 'android' ? 16 : 0,
                    borderRadius: 5,
                    marginTop: 4,
                  },
                  switchMerchant
                    ? {
                      height: windowHeight * 0.03,
                      width: windowWidth * 0.059,
                      marginTop: '0.5%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      top: 0.5,
                      left: windowWidth * 0.005,
                    }
                    : {},
                ]}>
                <View
                  style={[
                    {
                      flexDirection: 'row',
                      justifyContent: 'center',
                      width: '80%',
                    },
                    switchMerchant
                      ? {
                        // left: windowWidth * -0.003,
                        marginLeft: windowWidth * -0.006,
                      }
                      : {},
                  ]}>
                  {switchMerchant ? (
                    <MaterialCommunityIcons
                      name="message-alert-outline"
                      size={10}
                      color={Colors.whiteColor}
                      style={{ marginLeft: 15, marginRight: 3 }}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name="message-alert-outline"
                      size={17}
                      color={Colors.whiteColor}
                      style={{ marginLeft: 15, marginRight: 3 }}
                    />
                  )}

                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        fontSize: 8,
                        alignSelf: 'center',
                        bottom: 1,
                      },
                      switchMerchant
                        ? {
                          fontSize: 7,
                          marginLeft: '-1%',
                          width: '100%',
                          // borderWidth: 1,
                          textAlign: 'left',
                        }
                        : {},
                    ]}>
                    Prioritize
                  </Text>
                </View>
              </View>
            </View>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                height:
                  item.appType === 'WEB_ORDER' && windowWidth <= 1133 ? windowHeight * 0.19
                    : windowWidth <= 1133 ? windowHeight * 0.14
                      : item.appType === 'WEB_ORDER' ? windowHeight * 0.15
                        : windowHeight * 0.1,
                alignItems: 'center',
                borderBottomColor: Colors.fieldtTxtColor,
                borderBottomWidth:
                  expandViewDict[item.uniqueId] == true
                    ? StyleSheet.hairlineWidth
                    : null,
              }}>
              {/* <View style={{ flex: 0.6, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center' }}>
                <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId}</Text>
              </View>
              <View style={{ flex: 1.3, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', flexDirection: 'row' }}>
                <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>{moment(item.orderDate).format('DD MMM YYYY')}</Text>
                <Text style={{ color: Colors.fontDark, fontSize: 13, fontFamily: 'NunitoSans-Bold', marginTop: 2 }}>{'  '}{moment(item.orderDate).format('LT')}</Text>
              </View>
              <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center' }}>
                <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>{item.userName ? item.userName : 'N/A'}</Text>
              </View>
              <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center' }}>
                <View style={{ alignItems: 'center', paddingVertical: 2, borderRadius: 3, backgroundColor: waitingTime < 16 ? '#9e9e9e' : waitingTime < 21 ? Colors.secondaryColor : '#d90000', paddingHorizontal: 18 }}>
                  <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{waitingTime > 60 ? "OverTime" : ((waitingTime <= 0 ? 0 : waitingTime.toFixed(0)) + ' mins')}</Text>
                </View>
              </View>
              <View style={{ flex: 2, paddingHorizontal: 10, alignItems: 'center' }}>
                <View style={{ backgroundColor: item.status == 1 ? Colors.tabGrey : Colors.primaryColor, paddingVertical: 2, width: item.paymentDate === null ? 200 : 130, alignItems: 'center', borderRadius: 3 }}>
                  <Text style={{ color: Colors.whiteColor, fontSize: 16, fontFamily: 'NunitoSans-Regular' }}>{item.paymentDetails === null ? "Awaiting Authorization" : "Paid"}</Text>
                </View>
              </View>
              <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center' }}>
                <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>RM {(item.finalPrice).toFixed(2)}</Text>
              </View> */}

              <View
                style={{
                  marginHorizontal: 1,
                  width: Platform.OS == 'ios' ? '7%' : '7%',
                  alignItems: 'center',
                  zIndex: 0,
                }}>
                <View
                  style={{
                    width: 60,
                    height: 60,
                    // borderWidth: 1,
                    // borderRadius: 5,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Image
                    style={[
                      {
                        width: 30,
                        height: 30,
                        marginLeft: 0,
                      },
                      switchMerchant
                        ? {
                          width: windowWidth * 0.03,
                          height: windowHeight * 0.05,
                        }
                        : {},
                    ]}
                    resizeMode="contain"
                    source={require('../assets/image/dineinGrey.png')}
                  />
                  {item.appType === 'WEB_ORDER' ?
                    <View
                      style={{
                        width: 40,
                        height: 40,
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingTop: 10
                      }}>
                      <Ionicons name={'qr-code'} size={switchMerchant ? 20 : 25} />
                    </View>
                    : <></>}
                </View>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: '8%',
                  alignItems: 'flex-start',
                }}>
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {item.tableCode ? item.tableCode.slice(0, 6) : ''}
                </Text>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: Platform.OS == 'ios' ? '8%' : '8%',
                  // paddingRight: Platform.OS == 'ios' ? '0.5%' : '2.7%',
                  //width: Platform.OS == 'android' ? '8.5%' : '10%',
                  alignItems: 'flex-start',
                }}>
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',

                      // fontSize: 10,
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  #{item.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}
                  {item.orderId}
                  {/* {item.uniqueId} */}
                </Text>
              </View>

              <View
                style={[
                  {
                    marginHorizontal: 1,
                    width: '16%',
                    textAlign: 'left',
                    paddingLeft: 3,
                    justifyContent: 'flex-start',
                    flexDirection: 'column',
                    // left: Platform.OS == 'ios' ? 15 : 0,
                    // paddingLeft: Platform.OS == 'ios' ? 0 : '2.7%',
                    //paddingLeft: Platform.OS == 'android' ? '2.7%' : 0,
                  },
                  switchMerchant
                    ? {
                      left: windowWidth * 0.002,
                    }
                    : {},
                  windowWidth === 1280 && windowHeight === 800 ? {
                    left: 4,
                  } : {}
                ]}>
                <View style={{
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  justifyContent: 'center',
                }}>
                  <Text
                    style={[
                      {
                        color: Colors.fontDark,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                        //alignItems: 'flex-start',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {moment(item.orderDate).format('DD MMM YYYY')}
                  </Text>
                  <Text
                    style={[
                      {
                        color: Colors.fontDark,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                        //marginTop: 2,
                        //alignItems: 'center',
                        // right: Platform.OS === 'ios' ? '2%' : 0,
                        // right: Platform.OS === 'android' ? '2%' : 0,
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {moment(item.orderDate).format('hh:mm A')}
                  </Text>
                </View>

                {/* 2024-03-11 - to show reservation time part */}

                {
                  (item.isReservationOrder && item.reservationTime)
                    ?
                    <View style={{
                      flexDirection: 'column',
                      alignItems: 'flex-start',
                      justifyContent: 'center',
                    }}>
                      <Text
                        style={[
                          {
                            // color: Colors.fontDark,
                            color: Colors.primaryColor,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                            //alignItems: 'flex-start',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {`On ${moment(item.reservationTime).format('DD MMM YYYY')}`}
                      </Text>
                      <Text
                        style={[
                          {
                            // color: Colors.fontDark,
                            color: Colors.primaryColor,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                            //marginTop: 2,
                            //alignItems: 'center',
                            // right: Platform.OS === 'ios' ? '2%' : 0,
                            // right: Platform.OS === 'android' ? '2%' : 0,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {moment(item.reservationTime).format('hh:mm A')}
                      </Text>
                    </View>
                    :
                    <></>
                }
              </View>

              <View
                style={{
                  marginHorizontal: 1,
                  width: '14%',
                  //width: Platform.OS == 'android' ? '11%' : '14%',
                  alignItems: 'flex-start',
                  // left: Platform.OS == 'ios' ? 5 : 0,
                  // left: Platform.OS == 'android' ? 5 : 0,
                }}>
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}
                  numberOfLines={1}>
                  {item.waiterName ? item.waiterName : 'Waiter'}
                </Text>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: '16%',
                  alignItems: 'flex-start',
                  // right: Platform.OS == 'ios' ? 0 : 30,
                  // right: Platform.OS == 'android' ? 0 : 30,
                  //paddingRight: '2.5%',
                }}>
                <View
                  style={{
                    alignItems: 'center',
                    paddingVertical: 2,
                    borderRadius: 3,
                    backgroundColor:
                      waitingTime < 16
                        ? '#9e9e9e'
                        : waitingTime < 21
                          ? Colors.secondaryColor
                          : '#d90000',
                    paddingHorizontal: 18,
                  }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        fontSize: Platform.OS == 'ios' ? 16 : 16,
                        //fontSize: Platform.OS == 'android' ? 14 : 16,
                        fontFamily: 'NunitoSans-Regular',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {waitingTime > 60
                      ? 'OverTime'
                      : `${waitingTime <= 0 ? 0 : waitingTime.toFixed(0)
                      } mins`}
                    {/* {(waitingTime <= 0 ? 0 : waitingTime.toFixed(0)) +
                        ' mins'} */}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: Platform.OS == 'ios' ? '15%' : '15%',
                  //width: Platform.OS == 'android' ? '17%' : '15%',
                  alignItems: 'flex-start',
                  // paddingLeft: Platform.OS === 'ios' ? '0.5%' : 0,
                  // paddingLeft: Platform.OS === 'android' ? '0.5%' : 0,
                }}>
                {item.paymentDetails === null ? (
                  <View
                    style={{
                      width: '100%',
                    }}>
                    <View
                      style={{
                        backgroundColor: Colors.secondaryColor,
                        paddingVertical: 2,
                        // width: Platform.OS === 'ios' ? 135 : 100,
                        // width: 100,
                        width: '80%',
                        alignItems: 'center',
                        borderRadius: 3,
                      }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Pending
                      </Text>
                    </View>
                  </View>
                ) : (
                  <View
                    style={{
                      width: '100%',
                    }}>
                    <View
                      style={{
                        backgroundColor: Colors.primaryColor,
                        paddingVertical: 2,
                        // width: 135,
                        width: '80%',
                        alignItems: 'center',
                        borderRadius: 3,
                      }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {(item.paymentDetails && item.paymentDetails.channel)
                          ? PAYMENT_CHANNEL_NAME_PARSED[
                            item.paymentDetails.channel
                          ]
                            ? PAYMENT_CHANNEL_NAME_PARSED[
                            item.paymentDetails.channel
                            ]
                            : item.paymentDetails.channel
                              ? item.paymentDetails.channel
                              : 'N/A'
                          : 'N/A'}
                      </Text>
                    </View>
                  </View>
                )}
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: switchMerchant ? '10%' : '14%',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                  flexDirection: 'row',
                  // borderWidth: 1
                }}>
                <Text
                  style={[
                    { fontSize: switchMerchant ? 10 : 16 },
                    switchMerchant ? {} : {},
                  ]}>
                  RM
                </Text>
                <Text
                  style={[
                    Platform.OS === 'android' ? { position: 'relative' } : {},
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : { paddingRight: 25, fontSize: 16 },
                  ]}>
                  {(Math.round(item.finalPrice * 20) / 20)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                </Text>
                {/* <Text
                    style={[
                      {
                        color: Colors.fontDark,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                        //fontVariant: ['tabular-nums'],
                      },
                      Platform.OS === 'android' ? {position: 'relative'} : {},
                      switchMerchant
                        ? {
                            fontSize: 8,
                          }
                        : {},
                    ]}>
                    <Text>RM</Text>
                    <Text
                      style={[
                        {
                          opacity: 0,
                          // borderWidth: 1,
                          ...(Platform.OS === 'android' && {
                            color: 'transparent',
                          }),
                        },
                        switchMerchant
                          ? {
                              fontSize: 8,
                            }
                          : {},
                      ]}>
                      {'0'.repeat(finalPriceSpacing)}
                    </Text>
                    <Text
                      style={[
                        Platform.OS === 'android' ? {position: 'relative'} : {},
                        switchMerchant
                          ? {
                              fontSize: 8,
                            }
                          : {},
                      ]}>
                      {(Math.ceil(item.finalPrice * 20 - 0.05) / 20)
                        .toFixed(2)
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                    </Text>
                  </Text> */}
              </View>
            </View>

            {/* <View
              style={[
                {
                  position: 'absolute',
                  top: Platform.OS == 'ios' ? 3 : 10,
                  right: Platform.OS == 'ios' ? 1 : 5,
                  alignItems: 'center',
                },
                switchMerchant
                  ? {
                    position: 'absolute',
                    top: 5,
                    right: 1,
                  }
                  : {},
              ]}>
              <Icon
                name="chevron-left"
                size={switchMerchant ? 10 : 30}
                color={Colors.fieldtTxtColor}
              /> */}

            {/* <View style={{
                opacity: item.isPrioritizedOrder ? 100 : 0,
              }}>
                <FontAwesome name={'star'} size={26} color={Colors.primaryColor} />
              </View> */}
            {/* </View> */}

            {/* <TouchableOpacity style={{ position: 'absolute', top: Platform.OS == 'ios' ? 3 : 10, right: Platform.OS == 'ios' ? 3 : 10 }} onPress={() => { expandOrderFunc(item) }}>
              <Icon name={expandViewDict[item.uniqueId] == true ? "chevron-up" : "chevron-down"} size={30} color={Colors.tabGrey} />
            </TouchableOpacity> */}

            {expandedOrderId === item.uniqueId ? (
              <View
                style={{
                  minheight: windowHeight * 0.35,
                  marginTop: 30,
                  paddingBottom: 20,
                }}>
                {item.cartItems.map((cartItem, index) => {
                  const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItem);

                  return (
                    (<View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <View
                        style={{
                          width: '100%',
                          alignItems: 'flex-start',
                          flexDirection: 'row',
                          marginBottom: Platform.OS == 'ios' ? 10 : 10,
                          minHeight: 80,
                          //backgroundColor: 'yellow',
                        }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '100%',
                            //backgroundColor: 'blue',
                          }}>
                          {index == 0 ? (
                            <View
                              style={{
                                marginHorizontal: 1,
                                width: Platform.OS == 'ios' ? '6%' : '6%',
                                //justifyContent: 'center',
                                alignItems: 'center',
                                //backgroundColor: 'blue',
                              }}>
                              <TouchableOpacity
                                style={{
                                  alignItems: 'center',
                                  marginTop: '-10%',
                                }}
                                onPress={() => {
                                  var crmUser = null;

                                  if (item.crmUserId !== undefined) {
                                    for (
                                      var i = 0;
                                      i < crmUsers.length;
                                      i++
                                    ) {
                                      if (
                                        item.crmUserId ===
                                        crmUsers[i].uniqueId
                                      ) {
                                        crmUser = crmUsers[i];
                                        break;
                                      }
                                    }
                                  }

                                  if (!crmUser) {
                                    for (
                                      var i = 0;
                                      i < crmUsers.length;
                                      i++
                                    ) {
                                      if (
                                        item.userId ===
                                        crmUsers[i].firebaseUid
                                      ) {
                                        crmUser = crmUsers[i];
                                        break;
                                      }
                                    }
                                  }

                                  if (crmUser) {
                                    CommonStore.update(
                                      (s) => {
                                        s.selectedCustomerEdit = crmUser;
                                        // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                        s.routeParams = {
                                          pageFrom: 'Reservation',
                                        };
                                      },
                                      () => {
                                        navigation.navigate('NewCustomer');
                                      },
                                    );
                                  }
                                }}>
                                <Image
                                  style={{
                                    width: switchMerchant ? 30 : 60,
                                    height: switchMerchant ? 30 : 60,
                                  }}
                                  resizeMode="contain"
                                  source={require('../assets/image/profile-pic.jpg')}
                                />

                                <View
                                  style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                  }}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-Bold',
                                        marginTop: 0,
                                        fontSize: 16,
                                        textAlign: 'center',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}
                                    numberOfLines={1}>
                                    {item.userName ? item.userName : 'Guest'}
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          ) : (
                            <View
                              style={{
                                marginHorizontal: 1,
                                width: Platform.OS == 'ios' ? '6%' : '6%',
                                justifyContent: 'center',
                                alignItems: 'center',
                              }}
                            />
                          )}

                          <View
                            style={{
                              // flex: 0.3,
                              width: '4%',
                              //justifyContent: 'center',
                              alignItems: 'center',
                              //backgroundColor: 'red',
                            }}>
                            <Text
                              style={[
                                {
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 16,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              {index + 1}.
                            </Text>
                          </View>

                          <View
                            style={{
                              width: '3%',
                              // height: '100%',
                              //marginRight: 5,
                              paddingLeft: 8,
                              bottom: 4,
                            }}>
                            <CheckBox
                              style={{
                                ...(Platform.OS === 'ios' && {
                                  width: !isTablet() ? 10 : 16,
                                  height: !isTablet() ? 10 : 16,
                                }),
                              }}
                              value={
                                selectedCartItemDict[
                                cartItem.itemId +
                                cartItem.cartItemDate.toString()
                                ] !== false &&
                                selectedCartItemDict[
                                cartItem.itemId +
                                cartItem.cartItemDate.toString()
                                ] !== undefined
                              }
                              onValueChange={(value) => {
                                if (
                                  selectedCartItemDict[
                                  cartItem.itemId +
                                  cartItem.cartItemDate.toString()
                                  ]
                                ) {
                                  // Remove the item from the dictionary when deselected
                                  const newSelectedCartItemDict = { ...selectedCartItemDict };
                                  delete newSelectedCartItemDict[cartItem.itemId + cartItem.cartItemDate.toString()];
                                  setSelectedCartItemDict(newSelectedCartItemDict);
                                } else {
                                  setSelectedCartItemDict({
                                    ...selectedCartItemDict,
                                    [cartItem.itemId + cartItem.cartItemDate.toString()]: {
                                      itemId: cartItem.itemId,
                                      cartItemDate: cartItem.cartItemDate,

                                      actionQuantity: cartItem.quantity,
                                    },
                                  });
                                }
                              }}
                            />
                          </View>

                          <View
                            style={{
                              width: '10%',
                              //backgroundColor: 'green',
                              alignItems: 'center',
                            }}>
                            {cartItem.image ? (
                              <AsyncImage
                                source={{ uri: cartItem.image }}
                                // item={cartItem}
                                style={{
                                  width: switchMerchant ? 30 : 60,
                                  height: switchMerchant ? 30 : 60,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  borderRadius: 5,
                                }}
                              />
                            ) : (
                              <View
                                style={{
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  width: switchMerchant ? 30 : 60,
                                  height: switchMerchant ? 30 : 60,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  borderRadius: 5,
                                }}>
                                <Ionicons
                                  name="cart-outline"
                                  size={switchMerchant ? 15 : 35}
                                />
                              </View>
                            )}
                          </View>
                          <View style={{ width: '75%' }}>
                            <View
                              style={{
                                marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                marginBottom: 10,
                                //backgroundColor: 'blue',
                                width: '100%',
                                flexDirection: 'row',
                              }}>
                              <View style={{ width: '68.2%' }}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 16,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}>
                                  {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ''}
                                </Text>
                              </View>

                              <View
                                style={{
                                  width: '13%',
                                  // borderWidth: 1
                                }}>
                                <View
                                  style={{
                                    alignItems: 'center',
                                    //backgroundColor: 'yellow',
                                  }}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 16,
                                      },
                                      // Platform.OS === 'android'
                                      //   ? {
                                      //       width: '200%',
                                      //     }
                                      //   : {},
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    x{cartItem.quantity}
                                  </Text>
                                </View>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                  width: switchMerchant ? '13.2%' : '18.65%',
                                  // borderWidth: 1
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        // width: '20%'
                                      }
                                      : { fontSize: 16 }
                                  }>
                                  RM
                                </Text>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {
                                        fontSize: 16,
                                        paddingRight: 25,
                                      }
                                  }>
                                  {cartItemPriceWIthoutAddOn
                                    .toFixed(2)
                                    .replace(
                                      /(\d)(?=(\d{3})+(?!\d))/g,
                                      '$1,',
                                    )}
                                </Text>
                              </View>
                            </View>

                            {cartItem.remarks &&
                              cartItem.remarks.length > 0 ? (
                              <View
                                style={{
                                  alignItems: 'center',
                                  flexDirection: 'row',
                                  marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                }}>
                                <View style={{ justifyContent: 'center' }}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-SemiBold',
                                        fontSize: 16,
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {cartItem.remarks}
                                  </Text>
                                </View>
                              </View>
                            ) : (
                              <></>
                            )}

                            {cartItem.addOns.map((addOnChoice, i) => {
                              const addOnChoices = addOnChoice.choiceNames.join(", ");
                              return (
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    // marginLeft: -5,
                                    width: '100%',
                                  }}>
                                  <View
                                    style={{
                                      width: '68.2%',
                                      flexDirection: 'row',
                                      marginLeft:
                                        Platform.OS == 'ios' ? 14 : 14,
                                      // borderWidth: 1
                                    }}>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: 16,
                                          color: Colors.descriptionColor,
                                          width: '25%',
                                          // marginLeft: 5,
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : {},
                                      ]}>
                                      {`${addOnChoice.name}:`}
                                    </Text>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: 16,
                                          color: Colors.descriptionColor,
                                          width: '75%',
                                          // borderWidth: 1
                                          // marginLeft: 5,
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : {},
                                      ]}>
                                      {`${addOnChoices}`}
                                    </Text>
                                  </View>

                                  <View
                                    style={[
                                      {
                                        width: '13%',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        //backgroundColor: 'blue',
                                      },
                                      switchMerchant
                                        ? {
                                          // borderWidth: 1
                                        }
                                        : {},
                                    ]}>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: 16,
                                          color: Colors.descriptionColor,
                                          width: '28%',
                                          // right: 38,
                                          //backgroundColor: 'green',
                                          textAlign: 'center',
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                            // borderWidth: 1,
                                            // paddingLeft: '7%',
                                            textAlign: 'center',
                                          }
                                          : {},
                                        !switchMerchant &&
                                          Platform.OS === 'android'
                                          ? {}
                                          : {},
                                      ]}>
                                      {`${addOnChoice.quantities
                                        ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                        : ''
                                        }`}
                                    </Text>
                                  </View>

                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      justifyContent: 'space-between',
                                      width: switchMerchant
                                        ? '13.2%'
                                        : '18.65%',
                                      alignItems: 'center',
                                      // borderWidth: 1
                                    }}>
                                    <Text
                                      style={[
                                        switchMerchant
                                          ? { fontSize: 10 }
                                          : {
                                            color: Colors.descriptionColor,
                                            fontSize: 16,
                                          },
                                      ]}>
                                      RM
                                    </Text>
                                    <Text
                                      style={
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : {
                                            color: Colors.descriptionColor,
                                            paddingRight: 25,
                                            fontSize: 16,
                                          }
                                      }>
                                      {(getAddOnChoicePrice(addOnChoice, cartItem))
                                        .toFixed(2)
                                        .replace(
                                          /(\d)(?=(\d{3})+(?!\d))/g,
                                          '$1,',
                                        )}
                                    </Text>
                                  </View>
                                </View>
                              );
                            })}
                          </View>
                        </View>

                        <View
                          style={[
                            {
                              width: '14%',
                              //height: '30%',
                              //justifyContent: 'space-between',
                              flexDirection: 'column',
                              //backgroundColor: 'green',
                            },
                            Platform.OS === 'android' ? {} : {},
                            switchMerchant
                              ? {
                                fontSize: 8,
                              }
                              : {},
                          ]}>
                          <View style={{ marginTop: 3 }}>
                            {cartItem.addOns.map((addOnChoice, i) => {
                              const addOnChoices = addOnChoice.choiceNames.join(", ");

                              return (
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    //width: '138.5%',
                                    //height: 22.5,
                                  }}>
                                  <View
                                    style={[
                                      {
                                        width: '100%',
                                        flexDirection: 'row',
                                        //backgroundColor: 'blue',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          // borderWidth: 1,
                                          paddingLeft:
                                            windowWidth *
                                            0.017,
                                        }
                                        : {},
                                    ]}>
                                    {/* <Text
                                        style={[
                                          switchMerchant ? {fontSize: 8} : {},
                                        ]}>
                                        RM
                                      </Text>
                                      <Text
                                        style={
                                          switchMerchant
                                            ? {fontSize: 8}
                                            : {paddingRight: 25}
                                        }>
                                        {addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0)
                                          .toFixed(2)
                                          .replace(
                                            /(\d)(?=(\d{3})+(?!\d))/g,
                                            '$1,',
                                          )}
                                      </Text> */}
                                  </View>
                                </View>
                              );
                            })}
                          </View>
                        </View>
                      </View>
                      {((index === item.cartItems.length - 1) && item.cartItemsCancelled) ?
                        <>
                          {item.cartItemsCancelled.map((cartItemCancelled, index) => {
                            const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItemCancelled);

                            return (
                              (<View
                                style={{
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}>
                                <View
                                  style={{
                                    width: '100%',
                                    alignItems: 'flex-start',
                                    flexDirection: 'row',
                                    marginBottom: Platform.OS == 'ios' ? 10 : 10,
                                    minHeight: 80,
                                    //backgroundColor: 'yellow',
                                  }}>
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      width: '100%',
                                      // backgroundColor: 'blue',
                                    }}>

                                    {index == 0 ? (
                                      <View
                                        style={{
                                          marginHorizontal: 1,
                                          width: Platform.OS == 'ios' ? '6%' : '6%',
                                          //justifyContent: 'center',
                                          alignItems: 'center',
                                          // backgroundColor: 'blue',
                                        }}>
                                        {/* <TouchableOpacity
                                          style={{
                                            alignItems: 'center',
                                            marginTop: '-10%',
                                          }}
                                          onPress={() => {
                                            var crmUser = null;

                                            if (item.crmUserId !== undefined) {
                                              for (
                                                var i = 0;
                                                i < crmUsers.length;
                                                i++
                                              ) {
                                                if (
                                                  item.crmUserId ===
                                                  crmUsers[i].uniqueId
                                                ) {
                                                  crmUser = crmUsers[i];
                                                  break;
                                                }
                                              }
                                            }

                                            if (!crmUser) {
                                              for (
                                                var i = 0;
                                                i < crmUsers.length;
                                                i++
                                              ) {
                                                if (
                                                  item.userId ===
                                                  crmUsers[i].firebaseUid
                                                ) {
                                                  crmUser = crmUsers[i];
                                                  break;
                                                }
                                              }
                                            }

                                            if (crmUser) {
                                              CommonStore.update(
                                                (s) => {
                                                  s.selectedCustomerEdit = crmUser;
                                                  // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                                  s.routeParams = {
                                                    pageFrom: 'Reservation',
                                                  };
                                                },
                                                () => {
                                                  navigation.navigate('NewCustomer');
                                                },
                                              );
                                            }
                                          }}>
                                          <Image
                                            style={{
                                              width: switchMerchant ? 30 : 60,
                                              height: switchMerchant ? 30 : 60,
                                            }}
                                            resizeMode="contain"
                                            source={require('../assets/image/profile-pic.jpg')}
                                          />

                                          <View
                                            style={{
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                            }}>
                                            <Text
                                              style={[
                                                {
                                                  fontFamily: 'NunitoSans-Bold',
                                                  marginTop: 0,
                                                  fontSize: 16,
                                                  textAlign: 'center',
                                                },
                                                switchMerchant
                                                  ? {
                                                    fontSize: 10,
                                                  }
                                                  : {},
                                              ]}
                                              numberOfLines={1}>
                                              {item.userName ? item.userName : 'Guest'}
                                            </Text>
                                          </View>
                                        </TouchableOpacity> */}
                                      </View>
                                    ) : (
                                      <View
                                        style={{
                                          marginHorizontal: 1,
                                          width: Platform.OS == 'ios' ? '6%' : '6%',
                                          justifyContent: 'center',
                                          alignItems: 'center',
                                        }}
                                      />
                                    )}

                                    <View
                                      style={{
                                        // flex: 0.3,
                                        width: '11%',
                                        //justifyContent: 'center',
                                        alignItems: 'center',
                                        // backgroundColor: 'red',
                                      }}>
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 16,
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        {/* {index + 1}. */}
                                      </Text>
                                    </View>

                                    <View
                                      style={{
                                        width: '3%',
                                        // height: '100%',
                                        //marginRight: 5,
                                        paddingLeft: 8,
                                        bottom: 4,
                                      }}>
                                      {/* <CheckBox
                                          style={{
                                            ...(Platform.OS === 'ios' && {
                                              width: !isTablet() ? 10 : 16,
                                              height: !isTablet() ? 10 : 16,
                                            }),
                                          }}
                                          value={
                                            selectedCartItemDict[
                                            cartItem.itemId +
                                            cartItem.cartItemDate.toString()
                                            ] !== false &&
                                            selectedCartItemDict[
                                            cartItem.itemId +
                                            cartItem.cartItemDate.toString()
                                            ] !== undefined
                                          }
                                          onValueChange={(value) => {
                                            if (
                                              selectedCartItemDict[
                                              cartItem.itemId +
                                              cartItem.cartItemDate.toString()
                                              ]
                                            ) {
                                              // Remove the item from the dictionary when deselected
                                              const newSelectedCartItemDict = { ...selectedCartItemDict };
                                              delete newSelectedCartItemDict[cartItem.itemId + cartItem.cartItemDate.toString()];
                                              setSelectedCartItemDict(newSelectedCartItemDict);
                                            } else {
                                              setSelectedCartItemDict({
                                                ...selectedCartItemDict,
                                                [cartItem.itemId + cartItem.cartItemDate.toString()]: {
                                                  itemId: cartItem.itemId,
                                                  cartItemDate: cartItem.cartItemDate,

                                                  actionQuantity: cartItem.quantity,
                                                },
                                              });
                                            }
                                          }}
                                        /> */}
                                    </View>

                                    <View
                                      style={{
                                        width: '10%',
                                        //backgroundColor: 'green',
                                        alignItems: 'center',
                                      }}>
                                      {cartItemCancelled.image ? (
                                        <AsyncImage
                                          source={{ uri: cartItemCancelled.image }}
                                          // item={cartItem}
                                          style={{
                                            width: switchMerchant ? 30 : 60,
                                            height: switchMerchant ? 30 : 60,
                                            borderWidth: 1,
                                            borderColor: '#E5E5E5',
                                            borderRadius: 5,
                                          }}
                                        />
                                      ) : (
                                        <View
                                          style={{
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            width: switchMerchant ? 30 : 60,
                                            height: switchMerchant ? 30 : 60,
                                            borderWidth: 1,
                                            borderColor: '#E5E5E5',
                                            borderRadius: 5,
                                          }}>
                                          <Ionicons
                                            name="cart-outline"
                                            size={switchMerchant ? 15 : 35}
                                          />
                                        </View>
                                      )}
                                    </View>
                                    <View style={{ width: '75%' }}>
                                      <View
                                        style={{
                                          marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                          marginBottom: 10,
                                          //backgroundColor: 'blue',
                                          width: '100%',
                                          flexDirection: 'row',
                                        }}>
                                        <View style={{ width: '68.2%' }}>
                                          <Text
                                            style={[
                                              {
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: 16,
                                                color: 'red',
                                                textDecorationLine: 'line-through',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {cartItemCancelled.name}{cartItemCancelled.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItemCancelled.unitType]})` : ''}
                                          </Text>
                                        </View>

                                        <View
                                          style={{
                                            width: '13%',
                                            // borderWidth: 1
                                          }}>
                                          <View
                                            style={{
                                              alignItems: 'center',
                                              //backgroundColor: 'yellow',
                                            }}>
                                            <Text
                                              style={[
                                                {
                                                  fontFamily: 'NunitoSans-Bold',
                                                  fontSize: 16,
                                                  color: 'red',
                                                  textDecorationLine: 'line-through',
                                                },
                                                // Platform.OS === 'android'
                                                //   ? {
                                                //       width: '200%',
                                                //     }
                                                //   : {},
                                                switchMerchant
                                                  ? {
                                                    fontSize: 10,
                                                  }
                                                  : {},
                                              ]}>
                                              x{cartItemCancelled.quantity}
                                            </Text>
                                          </View>
                                        </View>
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            width: switchMerchant ? '13.2%' : '18.65%',
                                            // borderWidth: 1
                                          }}>
                                          {/* <Text
                                              style={
                                                switchMerchant
                                                  ? {
                                                    fontSize: 10,
                                                    // width: '20%'
                                                  }
                                                  : { fontSize: 16 }
                                              }>
                                              RM
                                            </Text>
                                            <Text
                                              style={
                                                switchMerchant
                                                  ? {
                                                    fontSize: 10,
                                                  }
                                                  : {
                                                    fontSize: 16,
                                                    paddingRight: 25,
                                                  }
                                              }>
                                              {cartItemPriceWIthoutAddOn
                                                .toFixed(2)
                                                .replace(
                                                  /(\d)(?=(\d{3})+(?!\d))/g,
                                                  '$1,',
                                                )}
                                            </Text> */}
                                        </View>
                                      </View>

                                      {cartItemCancelled.remarks &&
                                        cartItemCancelled.remarks.length > 0 ? (
                                        <View
                                          style={{
                                            alignItems: 'center',
                                            flexDirection: 'row',
                                            marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                          }}>
                                          <View style={{ justifyContent: 'center' }}>
                                            <Text
                                              style={[
                                                {
                                                  fontFamily: 'NunitoSans-SemiBold',
                                                  fontSize: 16,
                                                  color: 'red',
                                                  textDecorationLine: 'line-through',
                                                },
                                                switchMerchant
                                                  ? {
                                                    fontSize: 10,
                                                  }
                                                  : {},
                                              ]}>
                                              {cartItemCancelled.remarks}
                                            </Text>
                                          </View>
                                        </View>
                                      ) : (
                                        <></>
                                      )}

                                      {cartItemCancelled.rejectRemarks &&
                                        cartItemCancelled.rejectRemarks.length > 0 ? (
                                        <View
                                          style={{
                                            alignItems: 'center',
                                            flexDirection: 'row',
                                            marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                          }}>
                                          <View style={{ justifyContent: 'center' }}>
                                            <Text
                                              style={[
                                                {
                                                  fontFamily: 'NunitoSans-SemiBold',
                                                  fontSize: 16,
                                                  color: 'red',
                                                  textDecorationLine: 'line-through',
                                                },
                                                switchMerchant
                                                  ? {
                                                    fontSize: 10,
                                                  }
                                                  : {},
                                              ]}>
                                              {`(${cartItemCancelled.rejectRemarks})`}
                                            </Text>
                                          </View>
                                        </View>
                                      ) : (
                                        <></>
                                      )}

                                      {cartItemCancelled.addOns.map((addOnChoice, i) => {
                                        const addOnChoices = addOnChoice.choiceNames.join(", ");
                                        return (
                                          <View
                                            style={{
                                              flexDirection: 'row',
                                              // marginLeft: -5,
                                              width: '100%',
                                            }}>
                                            <View
                                              style={{
                                                width: '68.2%',
                                                flexDirection: 'row',
                                                marginLeft:
                                                  Platform.OS == 'ios' ? 14 : 14,
                                                // borderWidth: 1
                                              }}>
                                              <Text
                                                style={[
                                                  {
                                                    fontFamily: 'NunitoSans-Bold',
                                                    fontSize: 16,
                                                    color: Colors.descriptionColor,
                                                    width: '25%',
                                                    // marginLeft: 5,
                                                    color: 'red',
                                                    textDecorationLine: 'line-through',
                                                  },
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10,
                                                    }
                                                    : {},
                                                ]}>
                                                {`${addOnChoice.name}:`}
                                              </Text>
                                              <Text
                                                style={[
                                                  {
                                                    fontFamily: 'NunitoSans-Bold',
                                                    fontSize: 16,
                                                    color: Colors.descriptionColor,
                                                    width: '75%',
                                                    // borderWidth: 1
                                                    // marginLeft: 5,
                                                    color: 'red',
                                                    textDecorationLine: 'line-through',
                                                  },
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10,
                                                    }
                                                    : {},
                                                ]}>
                                                {`${addOnChoices}`}
                                              </Text>
                                            </View>

                                            <View
                                              style={[
                                                {
                                                  width: '13%',
                                                  flexDirection: 'row',
                                                  justifyContent: 'center',
                                                  //backgroundColor: 'blue',
                                                },
                                                switchMerchant
                                                  ? {
                                                    // borderWidth: 1
                                                  }
                                                  : {},
                                              ]}>
                                              <Text
                                                style={[
                                                  {
                                                    fontFamily: 'NunitoSans-Bold',
                                                    fontSize: 16,
                                                    color: Colors.descriptionColor,
                                                    width: '28%',
                                                    // right: 38,
                                                    //backgroundColor: 'green',
                                                    textAlign: 'center',
                                                    color: 'red',
                                                    textDecorationLine: 'line-through',
                                                  },
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10,
                                                      // borderWidth: 1,
                                                      // paddingLeft: '7%',
                                                      textAlign: 'center',
                                                    }
                                                    : {},
                                                  !switchMerchant &&
                                                    Platform.OS === 'android'
                                                    ? {}
                                                    : {},
                                                ]}>
                                                {`${addOnChoice.quantities
                                                  ? `x${getAddOnChoiceQuantity(addOnChoice, cartItemCancelled)}`
                                                  : ''
                                                  }`}
                                              </Text>
                                            </View>

                                            <View
                                              style={{
                                                flexDirection: 'row',
                                                justifyContent: 'space-between',
                                                width: switchMerchant
                                                  ? '13.2%'
                                                  : '18.65%',
                                                alignItems: 'center',
                                                // borderWidth: 1
                                              }}>
                                              {/* <Text
                                                  style={[
                                                    switchMerchant
                                                      ? { fontSize: 10 }
                                                      : {
                                                        color: Colors.descriptionColor,
                                                        fontSize: 16,
                                                      },
                                                  ]}>
                                                  RM
                                                </Text>
                                                <Text
                                                  style={
                                                    switchMerchant
                                                      ? {
                                                        fontSize: 10,
                                                      }
                                                      : {
                                                        color: Colors.descriptionColor,
                                                        paddingRight: 25,
                                                        fontSize: 16,
                                                      }
                                                  }>
                                                  {(getAddOnChoicePrice(addOnChoice, cartItem))
                                                    .toFixed(2)
                                                    .replace(
                                                      /(\d)(?=(\d{3})+(?!\d))/g,
                                                      '$1,',
                                                    )}
                                                </Text> */}
                                            </View>
                                          </View>
                                        );
                                      })}
                                    </View>
                                  </View>

                                  <View
                                    style={[
                                      {
                                        width: '14%',
                                        //height: '30%',
                                        //justifyContent: 'space-between',
                                        flexDirection: 'column',
                                        //backgroundColor: 'green',
                                      },
                                      Platform.OS === 'android' ? {} : {},
                                      switchMerchant
                                        ? {
                                          fontSize: 8,
                                        }
                                        : {},
                                    ]}>
                                    <View style={{ marginTop: 3 }}>
                                      {cartItemCancelled.addOns.map((addOnChoice, i) => {
                                        const addOnChoices = addOnChoice.choiceNames.join(", ");

                                        return (
                                          <View
                                            style={{
                                              flexDirection: 'row',
                                              //width: '138.5%',
                                              //height: 22.5,
                                            }}>
                                            <View
                                              style={[
                                                {
                                                  width: '100%',
                                                  flexDirection: 'row',
                                                  //backgroundColor: 'blue',
                                                  alignItems: 'center',
                                                  justifyContent: 'space-between',
                                                },
                                                switchMerchant
                                                  ? {
                                                    fontSize: 10,
                                                    // borderWidth: 1,
                                                    paddingLeft:
                                                      windowWidth *
                                                      0.017,
                                                  }
                                                  : {},
                                              ]}>
                                              {/* <Text
                                        style={[
                                          switchMerchant ? {fontSize: 8} : {},
                                        ]}>
                                        RM
                                      </Text>
                                      <Text
                                        style={
                                          switchMerchant
                                            ? {fontSize: 8}
                                            : {paddingRight: 25}
                                        }>
                                        {addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0)
                                          .toFixed(2)
                                          .replace(
                                            /(\d)(?=(\d{3})+(?!\d))/g,
                                            '$1,',
                                          )}
                                      </Text> */}
                                            </View>
                                          </View>
                                        );
                                      })}
                                    </View>
                                  </View>
                                </View>
                              </View>)
                            );
                          })}
                        </>
                        :
                        <></>}
                      <View style={{ flexDirection: 'row', width: '100%' }}>
                        <View style={{ width: '70%' }} >
                          {/* Moved Buttons */}
                          {index === item.cartItems.length - 1 ? (
                            <>
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  alignItems: 'center',
                                  flexDirection: 'row',
                                  height: 100,
                                  marginHorizontal: 10,
                                  marginTop: 20,
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    // setState({
                                    //   currToPrioritizeOrder: item,
                                    //   visible: true,
                                    // });
                                    setCurrOrderIndex(index);
                                    setCurrToPrioritizeOrder(item);
                                    setVisible(true);
                                  }}
                                  style={[
                                    {
                                      height: '100%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      backgroundColor: Colors.primaryColor,
                                      underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                      width: 75,
                                      marginLeft: 25,
                                      borderRadius: 10,
                                    },
                                    switchMerchant
                                      ? {
                                        width: windowWidth * 0.08,
                                      }
                                      : {},
                                  ]}>
                                  {switchMerchant ? (
                                    <MaterialCommunityIcons
                                      name="message-alert-outline"
                                      size={10}
                                      color={Colors.whiteColor}
                                      style={{ marginTop: 10 }}
                                    />
                                  ) : (
                                    <MaterialCommunityIcons
                                      name="message-alert-outline"
                                      size={40}
                                      color={Colors.whiteColor}
                                      style={{ marginTop: 10 }}
                                    />
                                  )}

                                  <Text
                                    style={[
                                      {
                                        color: Colors.whiteColor,
                                        fontSize: 12,
                                        fontFamily: 'NunitoSans-Regular',
                                        textAlign: 'center',
                                        width: windowWidth <= 1133 ? '85%' : '80%',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {`Prioritize\nOrder`}
                                  </Text>
                                </TouchableOpacity>

                                {/* 2025-07-08 - Herks said Hide it */}
                                {/* {(
                                  ((item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
                                    item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                                    item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                                    item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED || item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) && (item.tableId === ''))
                                  // &&
                                  // item.paymentDetails === null
                                )
                                  ? (
                                    <TouchableOpacity
                                      onPress={() => {
                                        CommonStore.update(s => {
                                          s.isCheckingOutTakeaway = true;

                                          s.checkingOutTakeawayOrder = item;

                                          s.timestamp = Date.now();
                                        }, () => {
                                          navigation.navigate('Table');
                                        });
                                      }}
                                      style={[
                                        {
                                          height: '100%',
                                          justifyContent: 'center',
                                          alignItems: 'center',
                                          backgroundColor: Colors.tabCyan,
                                          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                          width: 75,
                                          marginLeft: 25,
                                          borderRadius: 10,
                                        },
                                        switchMerchant
                                          ? {
                                            width: windowWidth * 0.08,
                                          }
                                          : {},
                                      ]}>
                                      {switchMerchant ? (
                                        <MaterialIcons
                                          name="payment"
                                          size={10}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      ) : (
                                        <MaterialIcons
                                          name="payment"
                                          size={40}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      )}
                                      <Text
                                        style={[
                                          {
                                            color: Colors.whiteColor,
                                            fontSize: 12,
                                            fontFamily: 'NunitoSans-Regular',
                                            textAlign: 'center',
                                            width: '80%',
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        Checkout Order
                                      </Text>
                                    </TouchableOpacity>
                                  ) : null
                                } */}

                                {
                                  true ?
                                    <TouchableOpacity
                                      onPress={() => {
                                        // setState({
                                        //   currToPrioritizeOrder: item,
                                        //   visible: true,
                                        // });

                                        // setCurrOrderIndex(index);
                                        // setCurrToCancelOrder(item);
                                        // setModalCancelVisibility(true);

                                        if ((currOutlet && currOutlet.privileges &&
                                          currOutlet.privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER))
                                          && privileges && privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER)) {
                                          if ((item.paymentDetails && (
                                            item.paymentDetails.channel
                                            // item.paymentDetails.txn_ID !== undefined ||
                                            // item.paymentDetails.txnId !== undefined
                                          ))
                                            || (item.combinedOrderList && item.combinedOrderList.length > 0)
                                          ) {
                                            Alert.alert('Info', 'Paid order is unable to cancel.');
                                          }
                                          else {
                                            setCurrOrderIndex(index);
                                            setCurrToCancelOrder(item);
                                            setModalCancelVisibility(true);
                                          }
                                        }
                                        else {
                                          global.pinUnlockCallback = async () => {
                                            if ((item.paymentDetails && (
                                              item.paymentDetails.channel
                                              // item.paymentDetails.txn_ID !== undefined ||
                                              // item.paymentDetails.txnId !== undefined
                                            ))
                                              || (item.combinedOrderList && item.combinedOrderList.length > 0)
                                            ) {
                                              Alert.alert('Info', 'Paid order is unable to cancel.');
                                            }
                                            else {
                                              setCurrOrderIndex(index);
                                              setCurrToCancelOrder(item);
                                              setModalCancelVisibility(true);
                                            }
                                          };

                                          CommonStore.update(s => {
                                            s.pinUnlockType = PRIVILEGES_NAME.CANCEL_ORDER;
                                            s.showPinUnlockModal = true;
                                          });
                                        }
                                      }}
                                      style={[
                                        {
                                          height: '100%',
                                          justifyContent: 'center',
                                          alignItems: 'center',
                                          // backgroundColor: Colors.primaryColor,
                                          backgroundColor: '#d90000',
                                          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                          width: 75,
                                          marginLeft: 25,
                                          borderRadius: 10,
                                        },
                                        switchMerchant
                                          ? {
                                            width: windowWidth * 0.08,
                                          }
                                          : {},
                                      ]}>
                                      {switchMerchant ? (
                                        <MaterialCommunityIcons
                                          name="close"
                                          size={10}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      ) : (
                                        <MaterialCommunityIcons
                                          name="close"
                                          size={40}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      )}

                                      <Text
                                        style={[
                                          {
                                            color: Colors.whiteColor,
                                            fontSize: 12,
                                            fontFamily: 'NunitoSans-Regular',
                                            textAlign: 'center',
                                            width: windowWidth <= 1133 ? '85%' : '80%',
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        {`Cancel\nOrder`}
                                      </Text>
                                    </TouchableOpacity>
                                    : <></>
                                }

                                {/* 2023-01-30 - For reprint kd */}

                                <TouchableOpacity
                                  onPress={async () => {
                                    ///////////////////////////////////////////////////////////////////////////

                                    Alert.alert('Info', 'Kitchen docket has been added to print queue');

                                    var printTimes = 1;

                                    if (global.outletCategoriesDict) {
                                      if (item.cartItems && item.cartItems.length > 0) {
                                        for (var i = 0; i < item.cartItems.length; i++) {
                                          if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
                                            global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
                                            printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                          }
                                        }
                                      }

                                      if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
                                        for (var i = 0; i < item.cartItemsCancelled.length; i++) {
                                          if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
                                            global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
                                            printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                          }
                                        }
                                      }
                                    }

                                    for (var i = 0; i < printTimes; i++) {
                                      logToFile('dine in - printUserOrder - KITCHEN_DOCKET');

                                      // Check if there are selected items in selectedCartItemDict
                                      const selectedCartItemKeys = Object.keys(selectedCartItemDict || {});
                                      const hasSelectedItems = selectedCartItemKeys.length > 0;
                                      let printOrderData = item;
                                      if (hasSelectedItems) {
                                        // Only include selected cart items
                                        const selectedCartItems = item.cartItems.filter(cartItem =>
                                          selectedCartItemKeys.includes(cartItem.itemId + cartItem.cartItemDate.toString())
                                        );
                                        printOrderData = {
                                          ...item,
                                          cartItems: selectedCartItems,
                                        };
                                      }
                                      await printUserOrder(
                                        {
                                          orderData: printOrderData,
                                        },
                                        false,
                                        [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                        false,
                                        false,
                                        false,
                                        { isInternetReachable: true, isConnected: true },
                                        false,
                                        [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                      );

                                      printKDSummaryCategoryWrapper(
                                        {
                                          orderData: printOrderData,
                                        },
                                        [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                      );

                                      console.log('a======');

                                      if (printOrderData && printOrderData.cartItems && printOrderData.cartItems.length > 0) {
                                        console.log('b======');

                                        const printerIpCountDict = await calcPrintTotalForKdIndividual({
                                          userOrder: printOrderData,
                                        });
                                        const printerTaskId = uuidv4();
                                        global.printingTaskIdDict[printerTaskId] = {};

                                        for (let bdIndex = 0; bdIndex < printOrderData.cartItems.length; bdIndex++) {
                                          console.log('c======');
                                          if (!printOrderData.cartItems[bdIndex].isDocket) {
                                            console.log('d======');

                                            await printDocketForKD(
                                              {
                                                userOrder: printOrderData,
                                                cartItem: printOrderData.cartItems[bdIndex],
                                                printerIpCountDict: printerIpCountDict,
                                                printerTaskId: printerTaskId,
                                              },
                                              // true,
                                              [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                              // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                              [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                              // deliveredUser,
                                            );
                                          }
                                        }

                                        for (let index = 0; index < printOrderData.cartItems.length; index++) {
                                          console.log('e======');
                                          if (printOrderData.cartItems[index].isDocket) {
                                            console.log('f======');
                                            await printDocket(
                                              {
                                                userOrder: printOrderData,
                                                cartItem: printOrderData.cartItems[index],
                                              },
                                              // true,
                                              [PRINTER_USAGE_TYPE.RECEIPT],
                                              [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                              // deliveredUser,
                                            );
                                          }
                                        }
                                      }
                                    }

                                    // Clear selectedCartItemDict after printing
                                    setSelectedCartItemDict({});

                                    // for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                    //   await printDocketForKD(
                                    //     {
                                    //       userOrder: item,
                                    //       cartItem: item.cartItems[bdIndex],
                                    //     },
                                    //     // true,
                                    //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    //     // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                    //   );
                                    // }

                                    ///////////////////////////////////////////////////////////////////////////

                                    // disconnectPrinter(printer); // no need anymore

                                    // await printUserOrder(
                                    //   {
                                    //     orderId: item.uniqueId,
                                    //     receiptNote: currOutlet.receiptNote || '',
                                    //   },
                                    //   false,
                                    //   [PRINTER_USAGE_TYPE.RECEIPT],
                                    //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    //   false,
                                    //   false,
                                    //   false,
                                    //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                    //   // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
                                    //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    // );

                                    // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
                                    //   await printUserOrder(
                                    //     {
                                    //       orderData: item,
                                    //     },
                                    //     false,
                                    //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    //     false,
                                    //     false,
                                    //     false,
                                    //     { isInternetReachable: true, isConnected: true },
                                    //   );
                                    // }
                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
                                    //   printKDSummaryCategoryWrapper(
                                    //     {
                                    //       orderData: item,
                                    //     },
                                    //   );
                                    // }
                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
                                    //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                    //     await printDocketForKD(
                                    //       {
                                    //         userOrder: item,
                                    //         cartItem: item.cartItems[bdIndex],
                                    //       },
                                    //       // true,
                                    //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                    //     );
                                    //   }
                                    // }
                                  }}
                                  style={[
                                    {
                                      height: '100%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      backgroundColor: Colors.secondaryColor,
                                      underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                      width: 75,
                                      marginLeft: 25,
                                      borderRadius: 10,
                                    },
                                    switchMerchant
                                      ? {
                                        width: windowWidth * 0.08,
                                      }
                                      : {},
                                  ]}>
                                  {switchMerchant ? (
                                    <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                  ) : (
                                    <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                  )}

                                  <Text
                                    style={[
                                      {
                                        color: Colors.whiteColor,
                                        fontSize: 12,
                                        fontFamily: 'NunitoSans-Regular',
                                        textAlign: 'center',
                                        width: windowWidth <= 1133 ? '85%' : '80%',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {`Reprint\nKD`}
                                  </Text>
                                </TouchableOpacity>

                                {/* 2023-05-08 - For reprint os */}

                                <TouchableOpacity
                                  onPress={async () => {
                                    ///////////////////////////////////////////////////////////////////////////

                                    Alert.alert('Info', 'Order summary has been added to print queue');

                                    var printTimes = 1;

                                    // if (global.outletCategoriesDict) {
                                    //   if (item.cartItems && item.cartItems.length > 0) {
                                    //     for (var i = 0; i < item.cartItems.length; i++) {
                                    //       if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
                                    //         global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
                                    //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                    //       }
                                    //     }
                                    //   }

                                    //   if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
                                    //     for (var i = 0; i < item.cartItemsCancelled.length; i++) {
                                    //       if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
                                    //         global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
                                    //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                    //       }
                                    //     }
                                    //   }
                                    // }

                                    for (var i = 0; i < printTimes; i++) {
                                      logToFile('dine in - printUserOrder - ORDER_SUMMARY');

                                      await printUserOrder(
                                        {
                                          orderData: item,
                                        },
                                        false,
                                        [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                                        false,
                                        false,
                                        false,
                                        { isInternetReachable: true, isConnected: true },
                                        true, // for isPrioritized
                                        [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                      );

                                      // printKDSummaryCategoryWrapper(
                                      //   {
                                      //     orderData: item,
                                      //   },
                                      //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                      // );

                                      // if (item && item.cartItems && item.cartItems.length > 0) {
                                      //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                      //     if (!item.cartItems[bdIndex].isDocket) {
                                      //       await printDocketForKD(
                                      //         {
                                      //           userOrder: item,
                                      //           cartItem: item.cartItems[bdIndex],
                                      //         },
                                      //         // true,
                                      //         [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                      //         // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                      //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                      //         // deliveredUser,
                                      //       );
                                      //     }
                                      //   }

                                      //   for (let index = 0; index < item.cartItems.length; index++) {
                                      //     if (item.cartItems[index].isDocket) {
                                      //       await printDocket(
                                      //         {
                                      //           userOrder: item,
                                      //           cartItem: item.cartItems[index],
                                      //         },
                                      //         // true,
                                      //         [PRINTER_USAGE_TYPE.RECEIPT],
                                      //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                      //         // deliveredUser,
                                      //       );
                                      //     }
                                      //   }
                                      // }
                                    }
                                  }}
                                  style={[
                                    {
                                      height: '100%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      backgroundColor: Colors.secondaryColor,
                                      underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                      width: 75,
                                      marginLeft: 25,
                                      borderRadius: 10,
                                    },
                                    switchMerchant
                                      ? {
                                        width: windowWidth * 0.08,
                                      }
                                      : {},
                                  ]}>
                                  {switchMerchant ? (
                                    <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                  ) : (
                                    <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                  )}

                                  <Text
                                    style={[
                                      {
                                        color: Colors.whiteColor,
                                        fontSize: 12,
                                        fontFamily: 'NunitoSans-Regular',
                                        textAlign: 'center',
                                        width: windowWidth <= 1133 ? '85%' : '80%',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {`Reprint\nOS`}
                                  </Text>
                                </TouchableOpacity>

                                {/* 2024-08-16 - authorize order */}

                                {
                                  currOutlet.dineInRequiredAuthorization &&
                                    item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED
                                    ?
                                    (
                                      <TouchableOpacity
                                        onPress={() => {
                                          //   currToPrioritizeOrder: item,
                                          //   visible: true,
                                          // });
                                          setCurrOrderIndex(index);
                                          setCurrToAuthorizeOrder(item);
                                          setModalAuthorizeVisibility(true);
                                        }}
                                        style={[
                                          {
                                            height: '100%',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            backgroundColor: '#8fbc8f',
                                            //backgroundColor: Colors.tabCyan,
                                            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                            width: 75,
                                            marginLeft: 25,
                                            borderRadius: 10,
                                          },
                                          switchMerchant
                                            ? {
                                              width: windowWidth * 0.08,
                                            }
                                            : {},
                                        ]}>
                                        {switchMerchant ? (
                                          <Feather
                                            name="check"
                                            size={10}
                                            color={Colors.whiteColor}
                                            style={{ marginTop: 10 }}
                                          />
                                        ) : (
                                          <Feather
                                            name="check"
                                            size={40}
                                            color={Colors.whiteColor}
                                            style={{ marginTop: 10 }}
                                          />
                                        )}

                                        <Text
                                          style={[
                                            {
                                              color: Colors.whiteColor,
                                              fontSize: 12,
                                              fontFamily: 'NunitoSans-Regular',
                                              textAlign: 'center',
                                              width: '80%',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}>
                                          {`Authorize\nOrder`}
                                        </Text>
                                      </TouchableOpacity>
                                    ) : null}
                              </View>

                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  alignItems: 'center',
                                  flexDirection: 'row',
                                  height: 100,
                                  marginHorizontal: 10,
                                  marginTop: 20,
                                }}>
                                <TouchableOpacity
                                  onPress={async () => {
                                    try {
                                      const orderSnapshot = await firestore()
                                        .collection(Collections.UserOrder)
                                        .where('uniqueId', '==', item.uniqueId)
                                        .limit(1)
                                        .get();

                                      if (orderSnapshot && !orderSnapshot.empty) {
                                        const orderDoc = orderSnapshot.docs[0];
                                        const queryOrder = orderDoc.data();

                                        if (queryOrder.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED) {
                                          const undeliveredCartItems = (queryOrder.cartItems || []).filter(
                                            cartItem => !cartItem.deliveredAt && (
                                              userManagedCategory && userManagedCategory.includes(cartItem.categoryId)
                                            )
                                          );

                                          if (undeliveredCartItems.length > 0) {
                                            const body = {
                                              orderItemList: undeliveredCartItems.map(cartItem => ({
                                                ...cartItem,
                                                userOrderId: queryOrder.uniqueId,
                                                userOrder: queryOrder,
                                              })),
                                              orderIdList: [queryOrder.uniqueId],
                                              outlet: currOutlet,
                                            };

                                            const result = await APILocal.orderDeliverMultipleSummary({ body, uid: firebaseUid });

                                            if (result && result.status === 'success') {
                                              await firestore()
                                                .collection(Collections.UserOrder)
                                                .doc(item.uniqueId)
                                                .update({
                                                  notifyAt: Date.now(),
                                                  categoryNamePrepared: undeliveredCartItems
                                                    .map(cartItem => {
                                                      const category = outletCategoriesDict[cartItem.categoryId];
                                                      return category ? category.name : cartItem.categoryName;
                                                    })
                                                    .join(', '),
                                                })

                                              Alert.alert('Success', `Order #${item.orderId} has been delivered and notified`,
                                                [{ text: 'OK' }],
                                                { cancelable: false }
                                              );
                                            }
                                          }
                                          else {
                                            await firestore()
                                              .collection(Collections.UserOrder)
                                              .doc(item.uniqueId)
                                              .update({
                                                notifyAt: Date.now(),
                                              });

                                            Alert.alert('Success', `Order #${item.orderId} has been notified.`,
                                              [{ text: 'OK' }],
                                              { cancelable: false }
                                            );
                                          }
                                        }
                                        else {
                                          await firestore()
                                            .collection(Collections.UserOrder)
                                            .doc(item.uniqueId)
                                            .update({
                                              notifyAt: Date.now(),
                                            });

                                          Alert.alert('Success', `Order #${item.orderId} has been notified.`,
                                            [{ text: 'OK' }],
                                            { cancelable: false }
                                          );
                                        }
                                      }
                                      else {
                                        Alert.alert('Error', 'Failed to send notification');
                                        console.log('No matching orders found');
                                      }
                                    }
                                    catch (error) {
                                      console.error('Failed to update timestamp:', error);
                                      Alert.alert(
                                        'Error',
                                        'Failed to send notification',
                                        [{ text: 'OK' }],
                                        { cancelable: false }
                                      );
                                    }
                                  }}
                                  style={[
                                    {
                                      height: '100%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      backgroundColor: Colors.primaryColor,
                                      underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                      width: 75,
                                      marginLeft: 25,
                                      borderRadius: 10,
                                    },
                                    switchMerchant
                                      ? {
                                        width: windowWidth * 0.08,
                                      }
                                      : {},
                                  ]}>
                                  {switchMerchant ? (
                                    <Ionicons
                                      name="notifications"
                                      size={10}
                                      color={Colors.whiteColor}
                                      style={{ marginTop: 10 }}
                                    />
                                  ) : (
                                    <Ionicons
                                      name="notifications"
                                      size={40}
                                      color={Colors.whiteColor}
                                      style={{ marginTop: 10 }}
                                    />
                                  )}

                                  <Text
                                    style={[
                                      {
                                        color: Colors.whiteColor,
                                        fontSize: 12,
                                        fontFamily: 'NunitoSans-Regular',
                                        textAlign: 'center',
                                        width: windowWidth <= 1133 ? '85%' : '80%',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {`Notify\nOrder`}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </>
                          ) : (
                            <></>
                          )}
                        </View>
                        <View style={{ width: 15 }} />
                        {index === item.cartItems.length - 1 ? (
                          <View
                            style={{
                              flexDirection: 'row',
                              //backgroundColor: 'yellow',
                              width: '28%',
                            }}>
                            <View
                              style={{
                                justifyContent: 'center',
                                width: '100%',
                              }}>
                              <View
                                style={[
                                  {
                                    flexDirection: 'row',
                                  },
                                  switchMerchant
                                    ? {
                                      // width: '50%'
                                    }
                                    : {},
                                ]}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Subtotal:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: switchMerchant ? '35.4%' : '50%',
                                    // borderWidth: 1
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? { fontSize: 10 }
                                        : { fontSize: 16 }
                                    }>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          width: '55%',
                                          textAlign: 'right',
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {((item.isRefundOrder && item.finalPrice <= 0) ? 0 : (
                                      item.totalPrice +
                                      getOrderDiscountInfo(item)
                                    ))
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>
                              {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                                <View
                                  style={{
                                    flexDirection: 'row',
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          width: '50%',
                                        }
                                        : {
                                          fontSize: 16,
                                          width: '50%',
                                          fontFamily: 'Nunitosans-Bold',
                                        }
                                    }>
                                    Delivery Fee:
                                  </Text>
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      justifyContent: 'space-between',
                                      width: switchMerchant ? '35.4%' : '50%',
                                      // borderWidth: 1
                                    }}>
                                    <Text
                                      style={
                                        switchMerchant
                                          ? { fontSize: 10 }
                                          : { fontSize: 16 }
                                      }>
                                      RM
                                    </Text>
                                    <Text
                                      style={
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                            width: '55%',
                                            textAlign: 'right',
                                          }
                                          : { fontSize: 16, paddingRight: 25 }
                                      }>
                                      {item.deliveryFee
                                        .toFixed(2)
                                        .replace(
                                          /(\d)(?=(\d{3})+(?!\d))/g,
                                          '$1,',
                                        )}
                                    </Text>
                                  </View>
                                </View>
                              ) : (
                                <></>
                              )}

                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Discount:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: switchMerchant ? '35.4%' : '50%',
                                    // borderWidth: 1
                                  }}>
                                  <Text
                                    style={[
                                      {
                                        fontSize: switchMerchant ? 10 : 16,
                                      },
                                      switchMerchant ? {} : {},
                                    ]}>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {' '}
                                    {((item.isRefundOrder && item.finalPrice <= 0) ? 0 : (
                                      // item.discount +
                                      (getOrderDiscountInfoInclOrderBased(item))
                                    ))
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>

                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Tax:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: switchMerchant ? '35.4%' : '50%',
                                    // borderWidth: 1
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          // width: '30%'
                                        }
                                        : { fontSize: 16 }
                                    }>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          width: '55%',
                                          textAlign: 'right',
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {item.tax
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Service Charge:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: switchMerchant ? '35.4%' : '50%',
                                    // borderWidth: 1
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          // width: '30%'
                                        }
                                        : { fontSize: 16 }
                                    }>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          width: '55%',
                                          textAlign: 'right',
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {(item.sc || 0)
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Rounding:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: switchMerchant ? '35.4%' : '50%',
                                    // borderWidth: 1
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          // width: '30%'
                                        }
                                        : { fontSize: 16 }
                                    }>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          width: '55%',
                                          textAlign: 'right',
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {(item.finalPrice ? (item.finalPrice - item.finalPriceBefore) : 0)
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>

                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Total:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: switchMerchant ? '35.4%' : '50%',
                                    // borderWidth: 1
                                  }}>
                                  <Text
                                    style={[
                                      {
                                        fontSize: switchMerchant ? 10 : 16,
                                      },
                                      switchMerchant ? {} : {},
                                    ]}>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {item.finalPrice
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>
                            </View>
                          </View>
                        ) : (
                          <></>
                        )}
                      </View>
                      {/*modal above here*/}
                      {/* <View style={{alignItems:'flex-end'}}>
                          <View style={{ flexDirection: 'row' }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>Subtotal: {(Math.ceil(cartItem.price * 20-0.05) /20).toFixed(2)}</Text>
                          </View>
                        </View> */}
                      {/* {(cartItem.remarks && cartItem.remarks.length > 0) ?
                        <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                          
                          <View style={{ flex: 1, justifyContent: 'center', }}>
                            <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 15 }}>{cartItem.remarks}</Text>
                          </View>
                          
                        </View>
                        : <></>
                      } */}
                    </View>)
                  );
                })}
                {(item.cartItems && item.cartItems.length === 0 && item.cartItemsCancelled) ?
                  <>
                    {
                      item.cartItemsCancelled.map((cartItemCancelled, index) => {
                        return (
                          (<View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}>
                            <View
                              style={{
                                width: '100%',
                                alignItems: 'flex-start',
                                flexDirection: 'row',
                                marginBottom: Platform.OS == 'ios' ? 10 : 10,
                                minHeight: 80,
                                //backgroundColor: 'yellow',
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  width: '100%',
                                  //backgroundColor: 'blue',
                                }}>
                                {index == 0 ? (
                                  <View
                                    style={{
                                      marginHorizontal: 1,
                                      width: Platform.OS == 'ios' ? '6%' : '6%',
                                      //justifyContent: 'center',
                                      alignItems: 'center',
                                      //backgroundColor: 'blue',
                                    }}>
                                    {/* <TouchableOpacity
                                      style={{
                                        alignItems: 'center',
                                        marginTop: '-10%',
                                      }}
                                      onPress={() => {
                                        var crmUser = null;

                                        if (item.crmUserId !== undefined) {
                                          for (
                                            var i = 0;
                                            i < crmUsers.length;
                                            i++
                                          ) {
                                            if (
                                              item.crmUserId ===
                                              crmUsers[i].uniqueId
                                            ) {
                                              crmUser = crmUsers[i];
                                              break;
                                            }
                                          }
                                        }

                                        if (!crmUser) {
                                          for (
                                            var i = 0;
                                            i < crmUsers.length;
                                            i++
                                          ) {
                                            if (
                                              item.userId ===
                                              crmUsers[i].firebaseUid
                                            ) {
                                              crmUser = crmUsers[i];
                                              break;
                                            }
                                          }
                                        }

                                        if (crmUser) {
                                          CommonStore.update(
                                            (s) => {
                                              s.selectedCustomerEdit = crmUser;
                                              // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                              s.routeParams = {
                                                pageFrom: 'Reservation',
                                              };
                                            },
                                            () => {
                                              navigation.navigate('NewCustomer');
                                            },
                                          );
                                        }
                                      }}>
                                      <Image
                                        style={{
                                          width: switchMerchant ? 30 : 60,
                                          height: switchMerchant ? 30 : 60,
                                        }}
                                        resizeMode="contain"
                                        source={require('../assets/image/profile-pic.jpg')}
                                      />

                                      <View
                                        style={{
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                        }}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-Bold',
                                              marginTop: 0,
                                              fontSize: 16,
                                              textAlign: 'center',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}
                                          numberOfLines={1}>
                                          {item.userName ? item.userName : 'Guest'}
                                        </Text>
                                      </View>
                                    </TouchableOpacity> */}
                                  </View>
                                ) : (
                                  <View
                                    style={{
                                      marginHorizontal: 1,
                                      width: Platform.OS == 'ios' ? '6%' : '6%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}
                                  />
                                )}

                                <View
                                  style={{
                                    // flex: 0.3,
                                    width: '4%',
                                    //justifyContent: 'center',
                                    alignItems: 'center',
                                    //backgroundColor: 'red',
                                  }}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 16,
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {/* {index + 1}. */}
                                  </Text>
                                </View>

                                <View
                                  style={{
                                    width: '3%',
                                    // height: '100%',
                                    //marginRight: 5,
                                    paddingLeft: 8,
                                    bottom: 4,
                                  }}>
                                  {/* <CheckBox
                                    style={{
                                      ...(Platform.OS === 'ios' && {
                                        width: !isTablet() ? 10 : 16,
                                        height: !isTablet() ? 10 : 16,
                                      }),
                                    }}
                                    value={
                                      selectedCartItemDict[
                                      cartItem.itemId +
                                      cartItem.cartItemDate.toString()
                                      ] !== false &&
                                      selectedCartItemDict[
                                      cartItem.itemId +
                                      cartItem.cartItemDate.toString()
                                      ] !== undefined
                                    }
                                    onValueChange={(value) => {
                                      if (
                                        selectedCartItemDict[
                                        cartItem.itemId +
                                        cartItem.cartItemDate.toString()
                                        ]
                                      ) {
                                        // Remove the item from the dictionary when deselected
                                        const newSelectedCartItemDict = { ...selectedCartItemDict };
                                        delete newSelectedCartItemDict[cartItem.itemId + cartItem.cartItemDate.toString()];
                                        setSelectedCartItemDict(newSelectedCartItemDict);
                                      } else {
                                        setSelectedCartItemDict({
                                          ...selectedCartItemDict,
                                          [cartItem.itemId + cartItem.cartItemDate.toString()]: {
                                            itemId: cartItem.itemId,
                                            cartItemDate: cartItem.cartItemDate,

                                            actionQuantity: cartItem.quantity,
                                          },
                                        });
                                      }
                                    }}
                                  /> */}
                                </View>

                                <View
                                  style={{
                                    width: '10%',
                                    //backgroundColor: 'green',
                                    alignItems: 'center',
                                  }}>
                                  {cartItemCancelled.image ? (
                                    <AsyncImage
                                      source={{ uri: cartItemCancelled.image }}
                                      // item={cartItem}
                                      style={{
                                        width: switchMerchant ? 30 : 60,
                                        height: switchMerchant ? 30 : 60,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        borderRadius: 5,
                                      }}
                                    />
                                  ) : (
                                    <View
                                      style={{
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        width: switchMerchant ? 30 : 60,
                                        height: switchMerchant ? 30 : 60,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        borderRadius: 5,
                                      }}>
                                      <Ionicons
                                        name="cart-outline"
                                        size={switchMerchant ? 15 : 35}
                                      />
                                    </View>
                                  )}
                                </View>
                                <View style={{ width: '75%' }}>
                                  <View
                                    style={{
                                      marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                      marginBottom: 10,
                                      //backgroundColor: 'blue',
                                      width: '100%',
                                      flexDirection: 'row',
                                    }}>
                                    <View style={{ width: '68.2%' }}>
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 16,
                                            color: 'red',
                                            textDecorationLine: 'line-through',
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        {cartItemCancelled.name}{cartItemCancelled.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItemCancelled.unitType]})` : ''}
                                      </Text>
                                    </View>

                                    <View
                                      style={{
                                        width: '13%',
                                        // borderWidth: 1
                                      }}>
                                      <View
                                        style={{
                                          alignItems: 'center',
                                          //backgroundColor: 'yellow',
                                        }}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-Bold',
                                              fontSize: 16,
                                              color: 'red',
                                              textDecorationLine: 'line-through',
                                            },
                                            // Platform.OS === 'android'
                                            //   ? {
                                            //       width: '200%',
                                            //     }
                                            //   : {},
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}>
                                          x{cartItemCancelled.quantity}
                                        </Text>
                                      </View>
                                    </View>
                                    <View
                                      style={{
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        width: switchMerchant ? '13.2%' : '18.65%',
                                        // borderWidth: 1
                                      }}>
                                      {/* <Text
                                        style={
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                              // width: '20%'
                                            }
                                            : { fontSize: 16 }
                                        }>
                                        RM
                                      </Text>
                                      <Text
                                        style={
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {
                                              fontSize: 16,
                                              paddingRight: 25,
                                            }
                                        }>
                                        {cartItemPriceWIthoutAddOn
                                          .toFixed(2)
                                          .replace(
                                            /(\d)(?=(\d{3})+(?!\d))/g,
                                            '$1,',
                                          )}
                                      </Text> */}
                                    </View>
                                  </View>

                                  {cartItemCancelled.remarks &&
                                    cartItemCancelled.remarks.length > 0 ? (
                                    <View
                                      style={{
                                        alignItems: 'center',
                                        flexDirection: 'row',
                                        marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                      }}>
                                      <View style={{ justifyContent: 'center' }}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-SemiBold',
                                              fontSize: 16,
                                              color: 'red',
                                              textDecorationLine: 'line-through',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}>
                                          {cartItemCancelled.remarks}
                                        </Text>
                                      </View>
                                    </View>
                                  ) : (
                                    <></>
                                  )}

                                  {cartItemCancelled.addOns.map((addOnChoice, i) => {
                                    const addOnChoices = addOnChoice.choiceNames.join(", ");
                                    return (
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                          // marginLeft: -5,
                                          width: '100%',
                                        }}>
                                        <View
                                          style={{
                                            width: '68.2%',
                                            flexDirection: 'row',
                                            marginLeft:
                                              Platform.OS == 'ios' ? 14 : 14,
                                            // borderWidth: 1
                                          }}>
                                          <Text
                                            style={[
                                              {
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: 16,
                                                color: Colors.descriptionColor,
                                                width: '25%',
                                                // marginLeft: 5,
                                                color: 'red',
                                                textDecorationLine: 'line-through',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`${addOnChoice.name}:`}
                                          </Text>
                                          <Text
                                            style={[
                                              {
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: 16,
                                                color: Colors.descriptionColor,
                                                width: '75%',
                                                // borderWidth: 1
                                                // marginLeft: 5,
                                                color: 'red',
                                                textDecorationLine: 'line-through',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`${addOnChoices}`}
                                          </Text>
                                        </View>

                                        <View
                                          style={[
                                            {
                                              width: '13%',
                                              flexDirection: 'row',
                                              justifyContent: 'center',
                                              //backgroundColor: 'blue',
                                            },
                                            switchMerchant
                                              ? {
                                                // borderWidth: 1
                                              }
                                              : {},
                                          ]}>
                                          <Text
                                            style={[
                                              {
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: 16,
                                                color: Colors.descriptionColor,
                                                width: '28%',
                                                // right: 38,
                                                //backgroundColor: 'green',
                                                textAlign: 'center',
                                                color: 'red',
                                                textDecorationLine: 'line-through',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  // borderWidth: 1,
                                                  // paddingLeft: '7%',
                                                  textAlign: 'center',
                                                }
                                                : {},
                                              !switchMerchant &&
                                                Platform.OS === 'android'
                                                ? {}
                                                : {},
                                            ]}>
                                            {`${addOnChoice.quantities
                                              ? `x${getAddOnChoiceQuantity(addOnChoice, cartItemCancelled)}`
                                              : ''
                                              }`}
                                          </Text>
                                        </View>

                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            width: switchMerchant
                                              ? '13.2%'
                                              : '18.65%',
                                            alignItems: 'center',
                                            // borderWidth: 1
                                          }}>
                                          {/* <Text
                                            style={[
                                              switchMerchant
                                                ? { fontSize: 10 }
                                                : {
                                                  color: Colors.descriptionColor,
                                                  fontSize: 16,
                                                },
                                            ]}>
                                            RM
                                          </Text>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {
                                                  color: Colors.descriptionColor,
                                                  paddingRight: 25,
                                                  fontSize: 16,
                                                }
                                            }>
                                            {(getAddOnChoicePrice(addOnChoice, cartItemCancelled))
                                              .toFixed(2)
                                              .replace(
                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                '$1,',
                                              )}
                                          </Text> */}
                                        </View>
                                      </View>
                                    );
                                  })}
                                </View>
                              </View>

                              <View
                                style={[
                                  {
                                    width: '14%',
                                    //height: '30%',
                                    //justifyContent: 'space-between',
                                    flexDirection: 'column',
                                    //backgroundColor: 'green',
                                  },
                                  Platform.OS === 'android' ? {} : {},
                                  switchMerchant
                                    ? {
                                      fontSize: 8,
                                    }
                                    : {},
                                ]}>
                                <View style={{ marginTop: 3 }}>
                                  {cartItemCancelled.addOns.map((addOnChoice, i) => {
                                    const addOnChoices = addOnChoice.choiceNames.join(", ");

                                    return (
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                          //width: '138.5%',
                                          //height: 22.5,
                                        }}>
                                        <View
                                          style={[
                                            {
                                              width: '100%',
                                              flexDirection: 'row',
                                              //backgroundColor: 'blue',
                                              alignItems: 'center',
                                              justifyContent: 'space-between',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                                // borderWidth: 1,
                                                paddingLeft:
                                                  windowWidth *
                                                  0.017,
                                              }
                                              : {},
                                          ]}>
                                          {/* <Text
                                        style={[
                                          switchMerchant ? {fontSize: 8} : {},
                                        ]}>
                                        RM
                                      </Text>
                                      <Text
                                        style={
                                          switchMerchant
                                            ? {fontSize: 8}
                                            : {paddingRight: 25}
                                        }>
                                        {addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0)
                                          .toFixed(2)
                                          .replace(
                                            /(\d)(?=(\d{3})+(?!\d))/g,
                                            '$1,',
                                          )}
                                      </Text> */}
                                        </View>
                                      </View>
                                    );
                                  })}
                                </View>
                              </View>
                            </View>
                            <View style={{ flexDirection: 'row', width: '100%' }}>
                              <View style={{ width: '70%' }} >
                                {/* Moved Buttons */}
                                {false
                                  // index === item.cartItems.length - 1 
                                  ? (
                                    <>
                                      <View
                                        style={{
                                          justifyContent: 'flex-start',
                                          alignItems: 'center',
                                          flexDirection: 'row',
                                          height: 100,
                                          marginHorizontal: 10,
                                          marginTop: 20,
                                        }}>
                                        <TouchableOpacity
                                          onPress={() => {
                                            // setState({
                                            //   currToPrioritizeOrder: item,
                                            //   visible: true,
                                            // });
                                            setCurrOrderIndex(index);
                                            setCurrToPrioritizeOrder(item);
                                            setVisible(true);
                                          }}
                                          style={[
                                            {
                                              height: '100%',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              backgroundColor: Colors.primaryColor,
                                              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                              width: 75,
                                              marginLeft: 25,
                                              borderRadius: 10,
                                            },
                                            switchMerchant
                                              ? {
                                                width: windowWidth * 0.08,
                                              }
                                              : {},
                                          ]}>
                                          {switchMerchant ? (
                                            <MaterialCommunityIcons
                                              name="message-alert-outline"
                                              size={10}
                                              color={Colors.whiteColor}
                                              style={{ marginTop: 10 }}
                                            />
                                          ) : (
                                            <MaterialCommunityIcons
                                              name="message-alert-outline"
                                              size={40}
                                              color={Colors.whiteColor}
                                              style={{ marginTop: 10 }}
                                            />
                                          )}

                                          <Text
                                            style={[
                                              {
                                                color: Colors.whiteColor,
                                                fontSize: 12,
                                                fontFamily: 'NunitoSans-Regular',
                                                textAlign: 'center',
                                                width: windowWidth <= 1133 ? '85%' : '80%',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`Prioritize\nOrder`}
                                          </Text>
                                        </TouchableOpacity>

                                        {(
                                          ((item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
                                            item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                                            item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                                            item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED || item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) && (item.tableId === ''))
                                          // &&
                                          // item.paymentDetails === null
                                        )
                                          ? (
                                            <TouchableOpacity
                                              onPress={() => {
                                                // setState({
                                                //   currToPrioritizeOrder: item,
                                                //   visible: true,
                                                // });

                                                // setCurrOrderIndex(index);
                                                // setCurrToPrioritizeOrder(item);
                                                // setVisible(true);

                                                CommonStore.update(s => {
                                                  s.isCheckingOutTakeaway = true;

                                                  s.checkingOutTakeawayOrder = item;

                                                  s.timestamp = Date.now();
                                                }, () => {
                                                  navigation.navigate('Table');
                                                });
                                              }}
                                              style={[
                                                {
                                                  height: '100%',
                                                  justifyContent: 'center',
                                                  alignItems: 'center',
                                                  backgroundColor: Colors.tabCyan,
                                                  underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                  width: 75,
                                                  marginLeft: 25,
                                                  borderRadius: 10,
                                                },
                                                switchMerchant
                                                  ? {
                                                    width: windowWidth * 0.08,
                                                  }
                                                  : {},
                                              ]}>
                                              {switchMerchant ? (
                                                <MaterialIcons
                                                  name="payment"
                                                  size={10}
                                                  color={Colors.whiteColor}
                                                  style={{ marginTop: 10 }}
                                                />
                                              ) : (
                                                <MaterialIcons
                                                  name="payment"
                                                  size={40}
                                                  color={Colors.whiteColor}
                                                  style={{ marginTop: 10 }}
                                                />
                                              )}
                                              <Text
                                                style={[
                                                  {
                                                    color: Colors.whiteColor,
                                                    fontSize: 12,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    textAlign: 'center',
                                                    width: '80%',
                                                  },
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10,
                                                    }
                                                    : {},
                                                ]}>
                                                Checkout Order
                                              </Text>
                                            </TouchableOpacity>
                                          ) : null}
                                        {
                                          true ?
                                            <TouchableOpacity
                                              onPress={() => {
                                                // setState({
                                                //   currToPrioritizeOrder: item,
                                                //   visible: true,
                                                // });

                                                // setCurrOrderIndex(index);
                                                // setCurrToCancelOrder(item);
                                                // setModalCancelVisibility(true);

                                                if ((currOutlet && currOutlet.privileges &&
                                                  currOutlet.privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER))
                                                  && privileges && privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER)) {
                                                  if ((item.paymentDetails && (
                                                    item.paymentDetails.channel
                                                    // item.paymentDetails.txn_ID !== undefined ||
                                                    // item.paymentDetails.txnId !== undefined
                                                  ))
                                                    || (item.combinedOrderList && item.combinedOrderList.length > 0)
                                                  ) {
                                                    Alert.alert('Info', 'Paid order is unable to cancel.');
                                                  }
                                                  else {
                                                    setCurrOrderIndex(index);
                                                    setCurrToCancelOrder(item);
                                                    setModalCancelVisibility(true);
                                                  }
                                                }
                                                else {
                                                  global.pinUnlockCallback = async () => {
                                                    if ((item.paymentDetails && (
                                                      item.paymentDetails.channel
                                                      // item.paymentDetails.txn_ID !== undefined ||
                                                      // item.paymentDetails.txnId !== undefined
                                                    ))
                                                      || (item.combinedOrderList && item.combinedOrderList.length > 0)
                                                    ) {
                                                      Alert.alert('Info', 'Paid order is unable to cancel.');
                                                    }
                                                    else {
                                                      setCurrOrderIndex(index);
                                                      setCurrToCancelOrder(item);
                                                      setModalCancelVisibility(true);
                                                    }
                                                  };

                                                  CommonStore.update(s => {
                                                    s.pinUnlockType = PRIVILEGES_NAME.CANCEL_ORDER;
                                                    s.showPinUnlockModal = true;
                                                  });
                                                }
                                              }}
                                              style={[
                                                {
                                                  height: '100%',
                                                  justifyContent: 'center',
                                                  alignItems: 'center',
                                                  // backgroundColor: Colors.primaryColor,
                                                  backgroundColor: '#d90000',
                                                  underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                  width: 75,
                                                  marginLeft: 25,
                                                  borderRadius: 10,
                                                },
                                                switchMerchant
                                                  ? {
                                                    width: windowWidth * 0.08,
                                                  }
                                                  : {},
                                              ]}>
                                              {switchMerchant ? (
                                                <MaterialCommunityIcons
                                                  name="close"
                                                  size={10}
                                                  color={Colors.whiteColor}
                                                  style={{ marginTop: 10 }}
                                                />
                                              ) : (
                                                <MaterialCommunityIcons
                                                  name="close"
                                                  size={40}
                                                  color={Colors.whiteColor}
                                                  style={{ marginTop: 10 }}
                                                />
                                              )}

                                              <Text
                                                style={[
                                                  {
                                                    color: Colors.whiteColor,
                                                    fontSize: 12,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    textAlign: 'center',
                                                    width: windowWidth <= 1133 ? '85%' : '80%',
                                                  },
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10,
                                                    }
                                                    : {},
                                                ]}>
                                                {`Cancel\nOrder`}
                                              </Text>
                                            </TouchableOpacity>
                                            : <></>
                                        }

                                        {/* 2023-01-30 - For reprint kd */}

                                        <TouchableOpacity
                                          onPress={async () => {
                                            ///////////////////////////////////////////////////////////////////////////

                                            Alert.alert('Info', 'Kitchen docket has been added to print queue');

                                            var printTimes = 1;

                                            if (global.outletCategoriesDict) {
                                              if (item.cartItems && item.cartItems.length > 0) {
                                                for (var i = 0; i < item.cartItems.length; i++) {
                                                  if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
                                                    global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
                                                    printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                                  }
                                                }
                                              }

                                              if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
                                                for (var i = 0; i < item.cartItemsCancelled.length; i++) {
                                                  if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
                                                    global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
                                                    printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                                  }
                                                }
                                              }
                                            }

                                            for (var i = 0; i < printTimes; i++) {
                                              logToFile('dine in - printUserOrder - KITCHEN_DOCKET');

                                              // Check if there are selected items in selectedCartItemDict
                                              const selectedCartItemKeys = Object.keys(selectedCartItemDict || {});
                                              const hasSelectedItems = selectedCartItemKeys.length > 0;
                                              let printOrderData = item;
                                              if (hasSelectedItems) {
                                                // Only include selected cart items
                                                const selectedCartItems = item.cartItems.filter(cartItem =>
                                                  selectedCartItemKeys.includes(cartItem.itemId + cartItem.cartItemDate.toString())
                                                );
                                                printOrderData = {
                                                  ...item,
                                                  cartItems: selectedCartItems,
                                                };
                                              }
                                              await printUserOrder(
                                                {
                                                  orderData: printOrderData,
                                                },
                                                false,
                                                [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                false,
                                                false,
                                                false,
                                                { isInternetReachable: true, isConnected: true },
                                                false,
                                                [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                              );

                                              printKDSummaryCategoryWrapper(
                                                {
                                                  orderData: printOrderData,
                                                },
                                                [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                              );

                                              console.log('a======');

                                              if (printOrderData && printOrderData.cartItems && printOrderData.cartItems.length > 0) {
                                                console.log('b======');

                                                const printerIpCountDict = await calcPrintTotalForKdIndividual({
                                                  userOrder: printOrderData,
                                                });
                                                const printerTaskId = uuidv4();
                                                global.printingTaskIdDict[printerTaskId] = {};

                                                for (let bdIndex = 0; bdIndex < printOrderData.cartItems.length; bdIndex++) {
                                                  console.log('c======');
                                                  if (!printOrderData.cartItems[bdIndex].isDocket) {
                                                    console.log('d======');

                                                    await printDocketForKD(
                                                      {
                                                        userOrder: printOrderData,
                                                        cartItem: printOrderData.cartItems[bdIndex],
                                                        printerIpCountDict: printerIpCountDict,
                                                        printerTaskId: printerTaskId,
                                                      },
                                                      // true,
                                                      [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                      // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                      [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                      // deliveredUser,
                                                    );
                                                  }
                                                }

                                                for (let index = 0; index < printOrderData.cartItems.length; index++) {
                                                  console.log('e======');
                                                  if (printOrderData.cartItems[index].isDocket) {
                                                    console.log('f======');
                                                    await printDocket(
                                                      {
                                                        userOrder: printOrderData,
                                                        cartItem: printOrderData.cartItems[index],
                                                      },
                                                      // true,
                                                      [PRINTER_USAGE_TYPE.RECEIPT],
                                                      [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                      // deliveredUser,
                                                    );
                                                  }
                                                }
                                              }
                                            }

                                            // Clear selectedCartItemDict after printing
                                            setSelectedCartItemDict({});

                                            // for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                            //   await printDocketForKD(
                                            //     {
                                            //       userOrder: item,
                                            //       cartItem: item.cartItems[bdIndex],
                                            //     },
                                            //     // true,
                                            //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            //     // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                            //   );
                                            // }

                                            ///////////////////////////////////////////////////////////////////////////

                                            // disconnectPrinter(printer); // no need anymore

                                            // await printUserOrder(
                                            //   {
                                            //     orderId: item.uniqueId,
                                            //     receiptNote: currOutlet.receiptNote || '',
                                            //   },
                                            //   false,
                                            //   [PRINTER_USAGE_TYPE.RECEIPT],
                                            //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            //   false,
                                            //   false,
                                            //   false,
                                            //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                            //   // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
                                            //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            // );

                                            // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
                                            //   await printUserOrder(
                                            //     {
                                            //       orderData: item,
                                            //     },
                                            //     false,
                                            //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            //     false,
                                            //     false,
                                            //     false,
                                            //     { isInternetReachable: true, isConnected: true },
                                            //   );
                                            // }
                                            // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
                                            //   printKDSummaryCategoryWrapper(
                                            //     {
                                            //       orderData: item,
                                            //     },
                                            //   );
                                            // }
                                            // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
                                            //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                            //     await printDocketForKD(
                                            //       {
                                            //         userOrder: item,
                                            //         cartItem: item.cartItems[bdIndex],
                                            //       },
                                            //       // true,
                                            //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                            //     );
                                            //   }
                                            // }
                                          }}
                                          style={[
                                            {
                                              height: '100%',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              backgroundColor: Colors.secondaryColor,
                                              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                              width: 75,
                                              marginLeft: 25,
                                              borderRadius: 10,
                                            },
                                            switchMerchant
                                              ? {
                                                width: windowWidth * 0.08,
                                              }
                                              : {},
                                          ]}>
                                          {switchMerchant ? (
                                            <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                          ) : (
                                            <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                          )}

                                          <Text
                                            style={[
                                              {
                                                color: Colors.whiteColor,
                                                fontSize: 12,
                                                fontFamily: 'NunitoSans-Regular',
                                                textAlign: 'center',
                                                width: windowWidth <= 1133 ? '85%' : '80%',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`Reprint\nKD`}
                                          </Text>
                                        </TouchableOpacity>

                                        {/* 2023-05-08 - For reprint os */}

                                        <TouchableOpacity
                                          onPress={async () => {
                                            ///////////////////////////////////////////////////////////////////////////

                                            Alert.alert('Info', 'Order summary has been added to print queue');

                                            var printTimes = 1;

                                            // if (global.outletCategoriesDict) {
                                            //   if (item.cartItems && item.cartItems.length > 0) {
                                            //     for (var i = 0; i < item.cartItems.length; i++) {
                                            //       if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
                                            //         global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
                                            //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                            //       }
                                            //     }
                                            //   }

                                            //   if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
                                            //     for (var i = 0; i < item.cartItemsCancelled.length; i++) {
                                            //       if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
                                            //         global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
                                            //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                            //       }
                                            //     }
                                            //   }
                                            // }

                                            for (var i = 0; i < printTimes; i++) {
                                              logToFile('dine in - printUserOrder - ORDER_SUMMARY');

                                              await printUserOrder(
                                                {
                                                  orderData: item,
                                                },
                                                false,
                                                [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                                                false,
                                                false,
                                                false,
                                                { isInternetReachable: true, isConnected: true },
                                                true, // for isPrioritized
                                                [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                              );

                                              // printKDSummaryCategoryWrapper(
                                              //   {
                                              //     orderData: item,
                                              //   },
                                              //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                              // );

                                              // if (item && item.cartItems && item.cartItems.length > 0) {
                                              //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                              //     if (!item.cartItems[bdIndex].isDocket) {
                                              //       await printDocketForKD(
                                              //         {
                                              //           userOrder: item,
                                              //           cartItem: item.cartItems[bdIndex],
                                              //         },
                                              //         // true,
                                              //         [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                              //         // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                              //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                              //         // deliveredUser,
                                              //       );
                                              //     }
                                              //   }

                                              //   for (let index = 0; index < item.cartItems.length; index++) {
                                              //     if (item.cartItems[index].isDocket) {
                                              //       await printDocket(
                                              //         {
                                              //           userOrder: item,
                                              //           cartItem: item.cartItems[index],
                                              //         },
                                              //         // true,
                                              //         [PRINTER_USAGE_TYPE.RECEIPT],
                                              //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                              //         // deliveredUser,
                                              //       );
                                              //     }
                                              //   }
                                              // }
                                            }
                                          }}
                                          style={[
                                            {
                                              height: '100%',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              backgroundColor: Colors.secondaryColor,
                                              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                              width: 75,
                                              marginLeft: 25,
                                              borderRadius: 10,
                                            },
                                            switchMerchant
                                              ? {
                                                width: windowWidth * 0.08,
                                              }
                                              : {},
                                          ]}>
                                          {switchMerchant ? (
                                            <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                          ) : (
                                            <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                          )}

                                          <Text
                                            style={[
                                              {
                                                color: Colors.whiteColor,
                                                fontSize: 12,
                                                fontFamily: 'NunitoSans-Regular',
                                                textAlign: 'center',
                                                width: windowWidth <= 1133 ? '85%' : '80%',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`Reprint\nOS`}
                                          </Text>
                                        </TouchableOpacity>

                                        {/* 2024-08-16 - authorize order */}

                                        {
                                          currOutlet.dineInRequiredAuthorization &&
                                            item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED
                                            ?
                                            (
                                              <TouchableOpacity
                                                onPress={() => {
                                                  //   currToPrioritizeOrder: item,
                                                  //   visible: true,
                                                  // });
                                                  setCurrOrderIndex(index);
                                                  setCurrToAuthorizeOrder(item);
                                                  setModalAuthorizeVisibility(true);
                                                }}
                                                style={[
                                                  {
                                                    height: '100%',
                                                    justifyContent: 'center',
                                                    alignItems: 'center',
                                                    backgroundColor: '#8fbc8f',
                                                    //backgroundColor: Colors.tabCyan,
                                                    underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                    width: 75,
                                                    marginLeft: 25,
                                                    borderRadius: 10,
                                                  },
                                                  switchMerchant
                                                    ? {
                                                      width: windowWidth * 0.08,
                                                    }
                                                    : {},
                                                ]}>
                                                {switchMerchant ? (
                                                  <Feather
                                                    name="check"
                                                    size={10}
                                                    color={Colors.whiteColor}
                                                    style={{ marginTop: 10 }}
                                                  />
                                                ) : (
                                                  <Feather
                                                    name="check"
                                                    size={40}
                                                    color={Colors.whiteColor}
                                                    style={{ marginTop: 10 }}
                                                  />
                                                )}

                                                <Text
                                                  style={[
                                                    {
                                                      color: Colors.whiteColor,
                                                      fontSize: 12,
                                                      fontFamily: 'NunitoSans-Regular',
                                                      textAlign: 'center',
                                                      width: '80%',
                                                    },
                                                    switchMerchant
                                                      ? {
                                                        fontSize: 10,
                                                      }
                                                      : {},
                                                  ]}>
                                                  {`Authorize\nOrder`}
                                                </Text>
                                              </TouchableOpacity>
                                            ) : null}
                                      </View>

                                      <View
                                        style={{
                                          justifyContent: 'flex-start',
                                          alignItems: 'center',
                                          flexDirection: 'row',
                                          height: 100,
                                          marginHorizontal: 10,
                                          marginTop: 20,
                                        }}>
                                        <TouchableOpacity
                                          onPress={async () => {
                                            try {
                                              const orderSnapshot = await firestore()
                                                .collection(Collections.UserOrder)
                                                .where('uniqueId', '==', item.uniqueId)
                                                .limit(1)
                                                .get();

                                              if (orderSnapshot && !orderSnapshot.empty) {
                                                const orderDoc = orderSnapshot.docs[0];
                                                const queryOrder = orderDoc.data();

                                                if (queryOrder.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED) {
                                                  const undeliveredCartItems = (queryOrder.cartItems || []).filter(
                                                    cartItem => !cartItem.deliveredAt && (
                                                      userManagedCategory && userManagedCategory.includes(cartItem.categoryId)
                                                    )
                                                  );

                                                  if (undeliveredCartItems.length > 0) {
                                                    const body = {
                                                      orderItemList: undeliveredCartItems.map(cartItem => ({
                                                        ...cartItem,
                                                        userOrderId: queryOrder.uniqueId,
                                                        userOrder: queryOrder,
                                                      })),
                                                      orderIdList: [queryOrder.uniqueId],
                                                      outlet: currOutlet,
                                                    };

                                                    const result = await APILocal.orderDeliverMultipleSummary({ body, uid: firebaseUid });

                                                    if (result && result.status === 'success') {
                                                      await firestore()
                                                        .collection(Collections.UserOrder)
                                                        .doc(item.uniqueId)
                                                        .update({
                                                          notifyAt: Date.now(),
                                                          categoryNamePrepared: undeliveredCartItems
                                                            .map(cartItem => {
                                                              const category = outletCategoriesDict[cartItem.categoryId];
                                                              return category ? category.name : cartItem.categoryName;
                                                            })
                                                            .join(', '),
                                                        })

                                                      Alert.alert('Success', `Order #${item.orderId} has been delivered and notified`,
                                                        [{ text: 'OK' }],
                                                        { cancelable: false }
                                                      );
                                                    }
                                                  }
                                                  else {
                                                    await firestore()
                                                      .collection(Collections.UserOrder)
                                                      .doc(item.uniqueId)
                                                      .update({
                                                        notifyAt: Date.now(),
                                                      });

                                                    Alert.alert('Success', `Order #${item.orderId} has been notified.`,
                                                      [{ text: 'OK' }],
                                                      { cancelable: false }
                                                    );
                                                  }
                                                }
                                                else {
                                                  await firestore()
                                                    .collection(Collections.UserOrder)
                                                    .doc(item.uniqueId)
                                                    .update({
                                                      notifyAt: Date.now(),
                                                    });

                                                  Alert.alert('Success', `Order #${item.orderId} has been notified.`,
                                                    [{ text: 'OK' }],
                                                    { cancelable: false }
                                                  );
                                                }
                                              }
                                              else {
                                                Alert.alert('Error', 'Failed to send notification');
                                                console.log('No matching orders found');
                                              }
                                            }
                                            catch (error) {
                                              console.error('Failed to update timestamp:', error);
                                              Alert.alert(
                                                'Error',
                                                'Failed to send notification',
                                                [{ text: 'OK' }],
                                                { cancelable: false }
                                              );
                                            }
                                          }}
                                          style={[
                                            {
                                              height: '100%',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              backgroundColor: Colors.primaryColor,
                                              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                              width: 75,
                                              marginLeft: 25,
                                              borderRadius: 10,
                                            },
                                            switchMerchant
                                              ? {
                                                width: windowWidth * 0.08,
                                              }
                                              : {},
                                          ]}>
                                          {switchMerchant ? (
                                            <Ionicons
                                              name="notifications"
                                              size={10}
                                              color={Colors.whiteColor}
                                              style={{ marginTop: 10 }}
                                            />
                                          ) : (
                                            <Ionicons
                                              name="notifications"
                                              size={40}
                                              color={Colors.whiteColor}
                                              style={{ marginTop: 10 }}
                                            />
                                          )}

                                          <Text
                                            style={[
                                              {
                                                color: Colors.whiteColor,
                                                fontSize: 12,
                                                fontFamily: 'NunitoSans-Regular',
                                                textAlign: 'center',
                                                width: windowWidth <= 1133 ? '85%' : '80%',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`Notify\nOrder`}
                                          </Text>
                                        </TouchableOpacity>
                                      </View>
                                    </>
                                  ) : (
                                    <></>
                                  )}
                              </View>
                              <View style={{ width: 15 }} />
                              {false
                                // index === item.cartItems.length - 1
                                ? (
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      //backgroundColor: 'yellow',
                                      width: '28%',
                                    }}>
                                    <View
                                      style={{
                                        justifyContent: 'center',
                                        width: '100%',
                                      }}>
                                      <View
                                        style={[
                                          {
                                            flexDirection: 'row',
                                          },
                                          switchMerchant
                                            ? {
                                              // width: '50%'
                                            }
                                            : {},
                                        ]}>
                                        <Text
                                          style={
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                                width: '50%',
                                              }
                                              : {
                                                fontSize: 16,
                                                width: '50%',
                                                fontFamily: 'Nunitosans-Bold',
                                              }
                                          }>
                                          Subtotal:
                                        </Text>
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            width: switchMerchant ? '35.4%' : '50%',
                                            // borderWidth: 1
                                          }}>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? { fontSize: 10 }
                                                : { fontSize: 16 }
                                            }>
                                            RM
                                          </Text>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  width: '55%',
                                                  textAlign: 'right',
                                                }
                                                : { fontSize: 16, paddingRight: 25 }
                                            }>
                                            {((item.isRefundOrder && item.finalPrice <= 0) ? 0 : (
                                              item.totalPrice +
                                              getOrderDiscountInfo(item)
                                            ))
                                              .toFixed(2)
                                              .replace(
                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                '$1,',
                                              )}
                                          </Text>
                                        </View>
                                      </View>
                                      {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                          }}>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  width: '50%',
                                                }
                                                : {
                                                  fontSize: 16,
                                                  width: '50%',
                                                  fontFamily: 'Nunitosans-Bold',
                                                }
                                            }>
                                            Delivery Fee:
                                          </Text>
                                          <View
                                            style={{
                                              flexDirection: 'row',
                                              justifyContent: 'space-between',
                                              width: switchMerchant ? '35.4%' : '50%',
                                              // borderWidth: 1
                                            }}>
                                            <Text
                                              style={
                                                switchMerchant
                                                  ? { fontSize: 10 }
                                                  : { fontSize: 16 }
                                              }>
                                              RM
                                            </Text>
                                            <Text
                                              style={
                                                switchMerchant
                                                  ? {
                                                    fontSize: 10,
                                                    width: '55%',
                                                    textAlign: 'right',
                                                  }
                                                  : { fontSize: 16, paddingRight: 25 }
                                              }>
                                              {item.deliveryFee
                                                .toFixed(2)
                                                .replace(
                                                  /(\d)(?=(\d{3})+(?!\d))/g,
                                                  '$1,',
                                                )}
                                            </Text>
                                          </View>
                                        </View>
                                      ) : (
                                        <></>
                                      )}

                                      <View
                                        style={{
                                          flexDirection: 'row',
                                        }}>
                                        <Text
                                          style={
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                                width: '50%',
                                              }
                                              : {
                                                fontSize: 16,
                                                width: '50%',
                                                fontFamily: 'Nunitosans-Bold',
                                              }
                                          }>
                                          Discount:
                                        </Text>
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            width: switchMerchant ? '35.4%' : '50%',
                                            // borderWidth: 1
                                          }}>
                                          <Text
                                            style={[
                                              {
                                                fontSize: switchMerchant ? 10 : 16,
                                              },
                                              switchMerchant ? {} : {},
                                            ]}>
                                            RM
                                          </Text>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : { fontSize: 16, paddingRight: 25 }
                                            }>
                                            {' '}
                                            {((item.isRefundOrder && item.finalPrice <= 0) ? 0 : (
                                              // item.discount +
                                              (getOrderDiscountInfoInclOrderBased(item))
                                            ))
                                              .toFixed(2)
                                              .replace(
                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                '$1,',
                                              )}
                                          </Text>
                                        </View>
                                      </View>

                                      <View
                                        style={{
                                          flexDirection: 'row',
                                        }}>
                                        <Text
                                          style={
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                                width: '50%',
                                              }
                                              : {
                                                fontSize: 16,
                                                width: '50%',
                                                fontFamily: 'Nunitosans-Bold',
                                              }
                                          }>
                                          Tax:
                                        </Text>
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            width: switchMerchant ? '35.4%' : '50%',
                                            // borderWidth: 1
                                          }}>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  // width: '30%'
                                                }
                                                : { fontSize: 16 }
                                            }>
                                            RM
                                          </Text>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  width: '55%',
                                                  textAlign: 'right',
                                                }
                                                : { fontSize: 16, paddingRight: 25 }
                                            }>
                                            {item.tax
                                              .toFixed(2)
                                              .replace(
                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                '$1,',
                                              )}
                                          </Text>
                                        </View>
                                      </View>
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                        }}>
                                        <Text
                                          style={
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                                width: '50%',
                                              }
                                              : {
                                                fontSize: 16,
                                                width: '50%',
                                                fontFamily: 'Nunitosans-Bold',
                                              }
                                          }>
                                          Service Charge:
                                        </Text>
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            width: switchMerchant ? '35.4%' : '50%',
                                            // borderWidth: 1
                                          }}>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  // width: '30%'
                                                }
                                                : { fontSize: 16 }
                                            }>
                                            RM
                                          </Text>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  width: '55%',
                                                  textAlign: 'right',
                                                }
                                                : { fontSize: 16, paddingRight: 25 }
                                            }>
                                            {(item.sc || 0)
                                              .toFixed(2)
                                              .replace(
                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                '$1,',
                                              )}
                                          </Text>
                                        </View>
                                      </View>
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                        }}>
                                        <Text
                                          style={
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                                width: '50%',
                                              }
                                              : {
                                                fontSize: 16,
                                                width: '50%',
                                                fontFamily: 'Nunitosans-Bold',
                                              }
                                          }>
                                          Rounding:
                                        </Text>
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            width: switchMerchant ? '35.4%' : '50%',
                                            // borderWidth: 1
                                          }}>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  // width: '30%'
                                                }
                                                : { fontSize: 16 }
                                            }>
                                            RM
                                          </Text>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  width: '55%',
                                                  textAlign: 'right',
                                                }
                                                : { fontSize: 16, paddingRight: 25 }
                                            }>
                                            {(item.finalPrice ? (item.finalPrice - item.finalPriceBefore) : 0)
                                              .toFixed(2)
                                              .replace(
                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                '$1,',
                                              )}
                                          </Text>
                                        </View>
                                      </View>

                                      <View
                                        style={{
                                          flexDirection: 'row',
                                        }}>
                                        <Text
                                          style={
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                                width: '50%',
                                              }
                                              : {
                                                fontSize: 16,
                                                width: '50%',
                                                fontFamily: 'Nunitosans-Bold',
                                              }
                                          }>
                                          Total:
                                        </Text>
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            width: switchMerchant ? '35.4%' : '50%',
                                            // borderWidth: 1
                                          }}>
                                          <Text
                                            style={[
                                              {
                                                fontSize: switchMerchant ? 10 : 16,
                                              },
                                              switchMerchant ? {} : {},
                                            ]}>
                                            RM
                                          </Text>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : { fontSize: 16, paddingRight: 25 }
                                            }>
                                            {item.finalPrice
                                              .toFixed(2)
                                              .replace(
                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                '$1,',
                                              )}
                                          </Text>
                                        </View>
                                      </View>
                                    </View>
                                  </View>
                                ) : (
                                  <></>
                                )}
                            </View>
                            {/*modal above here*/}
                            {/* <View style={{alignItems:'flex-end'}}>
                          <View style={{ flexDirection: 'row' }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>Subtotal: {(Math.ceil(cartItem.price * 20-0.05) /20).toFixed(2)}</Text>
                          </View>
                        </View> */}
                            {/* {(cartItem.remarks && cartItem.remarks.length > 0) ?
                        <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                          
                          <View style={{ flex: 1, justifyContent: 'center', }}>
                            <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 15 }}>{cartItem.remarks}</Text>
                          </View>
                          
                        </View>
                        : <></>
                      } */}
                          </View>)
                        );
                      })
                    }
                  </>
                  : <></>}
              </View>
            ) : null}
          </View>
        </TouchableOpacity>
        {/* </Swipeable> */}
      </View>
    );
  };

  const renderModal = (item) => {
    return (
      <View>
        {item && item.uniqueId && (
          <>
            <ModalView
              supportedOrientations={['landscape', 'portrait']}
              style={{
                flex: 1,
                backgroundColor: "rgba(0,0,0,0.5)",
              }}
              visible={visible}
              transparent
              animationType="slide">
              <View
                style={{
                  backgroundColor: "rgba(0,0,0,0.5)",
                  // flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  // minHeight: windowHeight,

                  width: windowWidth,
                  height: windowHeight,

                  ...getTransformForModalFullScreen(),
                }}>
                <View
                  style={[
                    styles.confirmBox,
                    switchMerchant
                      ? {
                        height: windowHeight * 0.25,
                        width: windowWidth * 0.3,

                        // ...getTransformForModalInsideNavigation(),
                      }
                      : {},
                  ]}>
                  <View style={{ marginTop: switchMerchant ? '4%' : 40 }}>
                    <Text
                      style={[
                        {
                          textAlign: 'center',
                          fontWeight: '700',
                          fontSize: 24,
                        },
                        switchMerchant
                          ? {
                            fontSize: 16,
                          }
                          : {},
                      ]}>
                      Prioritize Order
                    </Text>
                  </View>
                  <View style={{ marginTop: switchMerchant ? '2%' : 20 }}>
                    <Text
                      style={[
                        {
                          textAlign: 'center',
                          color: Colors.descriptionColor,
                          fontSize: 18,
                          width: '80%',
                          alignSelf: 'center',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {/* Priotize Takeaway for {item.user == null ? "" : item.user.name}, Order#{item.customTable == "TAKE AWAY" ? "T" : ""}{item.id} */}
                      {/* Priotize Order for {item.userName}, Order#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId} */}
                      Order #{item.orderId}
                    </Text>
                  </View>
                  <View style={{ height: windowHeight * 0.033 }} />
                  <View
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'center',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '50%',
                      alignContent: 'center',
                      zIndex: 6000,
                    }} />
                  <View
                    style={[
                      {
                        alignSelf: 'center',
                        marginTop: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: switchMerchant ? '71.5%' : 250,
                        height: switchMerchant ? 35 : 40,
                        alignContent: 'center',
                        flexDirection: 'row',
                        marginTop: switchMerchant ? '-5%' : windowHeight === 800 && windowWidth === 1280 ? 32 : 40,
                      },
                      switchMerchant
                        ? {
                          marginTop: '13%',
                          position: 'absolute',
                          bottom: 0,
                          alignItems: 'flex-end',
                        }
                        : {},
                    ]}>
                    <TouchableOpacity
                      onPress={() => {
                        prioritizeOrder(item.uniqueId);
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: switchMerchant ? 35 : 80,
                        borderBottomLeftRadius: 10,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text
                        style={[
                          { fontSize: 22, color: Colors.primaryColor },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              // borderWidth: 1,
                              width: '100%',
                              textAlign: 'center',
                            }
                            : {},
                        ]}>
                        Confirm
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        // setState({ visible: false });
                        setVisible(false);
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: switchMerchant ? 35 : 80,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text
                        style={[
                          { fontSize: 22, color: Colors.descriptionColor },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              // borderWidth: 1,
                              width: '100%',
                              textAlign: 'center',
                            }
                            : {},
                        ]}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </ModalView>
          </>
        )}
      </View>
    );
  };

  const renderModalCancel = (item) => {
    return (
      <View>
        {item && item.uniqueId && (
          <>
            <ModalView
              supportedOrientations={['landscape', 'portrait']}
              style={{
                flex: 1,
                backgroundColor: "rgba(0,0,0,0.5)",
              }}
              visible={modalCancelVisibility}
              transparent
              animationType="slide">
              <View
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  // flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  // minHeight: windowHeight,

                  width: windowWidth,
                  height: windowHeight,

                  ...getTransformForModalFullScreen(),
                }}>
                <View
                  style={[
                    styles.confirmBox,
                    switchMerchant
                      ? {
                        height: windowHeight * 0.35,
                        width: windowWidth * 0.4,
                      }
                      : {
                        height: 290,
                      },
                  ]}>
                  <View style={{ marginTop: switchMerchant ? '4%' : 40 }}>
                    <Text
                      style={[
                        {
                          textAlign: 'center',
                          fontWeight: '700',
                          fontSize: 24,
                        },
                        switchMerchant
                          ? {
                            fontSize: 16,
                          }
                          : {},
                      ]}>
                      Cancel Order
                    </Text>
                  </View>
                  <View style={{ marginTop: switchMerchant ? '2%' : 20 }}>
                    <Text
                      style={[
                        {
                          textAlign: 'center',
                          color: Colors.descriptionColor,
                          fontSize: 18,
                          width: '80%',
                          alignSelf: 'center',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {/* Cancel Order for {item.userName}, Order#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId} */}
                      Order #{item.orderId}
                    </Text>
                  </View>
                  <View style={{ marginTop: 20, }}>
                    <TextInput style={{
                      alignSelf: 'center',
                      padding: 5,
                      backgroundColor: Colors.fieldtBgColor,
                      width: '80%',
                      height: 45,
                      borderRadius: 5,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      paddingLeft: 10,
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: 14
                    }}
                      placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
                      placeholder='Remarks...'
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      defaultValue={remark}
                      onChangeText={text => {
                        setRemark(text);
                      }}
                    />
                  </View>
                  <View style={{ height: windowHeight * 0.033 }} />
                  <View
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'center',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '50%',
                      alignContent: 'center',
                      zIndex: 6000,
                    }} />
                  <View
                    style={[
                      {
                        position: 'absolute',
                        bottom: 0,
                        alignSelf: 'center',
                        marginTop: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: switchMerchant ? '71.5%' : 250,
                        height: switchMerchant ? 35 : 40,
                        alignContent: 'center',
                        flexDirection: 'row',
                        marginTop: switchMerchant ? '-5%' : windowHeight === 800 && windowWidth === 1280 ? 32 : 40,
                      },
                      switchMerchant
                        ? {
                          marginTop: '13%',
                          position: 'absolute',
                          bottom: 0,
                          alignItems: 'flex-end',
                        }
                        : {},
                    ]}>
                    <TouchableOpacity
                      onPress={() => {
                        cancelOrder(item);
                        setRemark('');
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: switchMerchant ? 35 : 80,
                        borderBottomLeftRadius: 10,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text
                        style={[
                          { fontSize: 22, color: '#d90000' },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: '100%',
                              textAlign: 'center',
                            }
                            : {},
                        ]}>
                        Confirm
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        // setState({ visible: false });
                        // setVisible(false);
                        setModalCancelVisibility(false);
                        setRemark('');
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: switchMerchant ? 35 : 80,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text
                        style={[
                          { fontSize: 22, color: Colors.descriptionColor },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: '100%',
                              textAlign: 'center',
                            }
                            : {},
                        ]}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </ModalView>
          </>
        )}
      </View>
    );
  };

  const authorizeOrder = async (param, showAlert = true) => {
    var body = {
      orderId: param.uniqueId,
      // tableId: param.tableId,
      waiterId: firebaseUid,
      waiterName: userName,
    };

    // ApiClient.POST(API.authorizeUserOrderByMerchant, body, false)
    APILocal.authorizeUserOrderByMerchant({ body })
      .then(
        async (result) => {
          if (result && result.status === 'success') {
            if (showAlert) {
              Alert.alert(
                'Success',
                'The order has been authorized',
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
              );
              // setState({ visible: false });
              // setVisible(false);
              setModalAuthorizeVisibility(false);
              setButtonAppear(BUTTON_APPEAR.AUTHORIZED);

              for (const ref of refArray) {
                if (
                  refArray.indexOf(ref) === currOrderIndex &&
                  ref &&
                  ref.current
                ) {
                  ref.current.close();
                }
              }
            }

            // const isPrintingReceipt = await AsyncStorage.getItem('isPrintingReceipt');

            // if (isPrintingReceipt === '0' || isPrintingReceipt === null) {
            //   disconnectPrinter(printer);
            // }

            // disconnectPrinter(printer); // no need anymore

            // await printUserOrder(
            //   {
            //     orderId: param.uniqueId,
            //   },
            //   false,
            //   [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //   false,
            //   false,
            //   false,
            //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
            // );

            logToFile('dine in - printUserOrder - authorize order');

            await printUserOrder(
              {
                orderId: param.uniqueId,
              },
              false,
              [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
              false,
              false,
              false,
              netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
              true, // for isPrioritized
            );

            await printUserOrder(
              {
                orderData: param,
              },
              false,
              [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
              false,
              false,
              false,
              { isInternetReachable: true, isConnected: true },
            );

            printKDSummaryCategoryWrapper(
              {
                orderData: param,
              },
            );

            const printerIpCountDict = await calcPrintTotalForKdIndividual({
              userOrder: param,
            });
            const printerTaskId = uuidv4();
            global.printingTaskIdDict[printerTaskId] = {};

            for (let bdIndex = 0; bdIndex < param.cartItems.length; bdIndex++) {
              if (!param.cartItems[bdIndex].isDocket) {
                await printDocketForKD(
                  {
                    userOrder: param,
                    cartItem: param.cartItems[bdIndex],
                    printerIpCountDict: printerIpCountDict,
                    printerTaskId: printerTaskId,
                  },
                  // true,
                  [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                  // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                );
              }
            }

            // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
            //   await printUserOrder(
            //     {
            //       orderData: param,
            //     },
            //     false,
            //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //     false,
            //     false,
            //     false,
            //     { isInternetReachable: true, isConnected: true },
            //   );
            // }
            // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
            //   printKDSummaryCategoryWrapper(
            //     {
            //       orderData: param,
            //     },
            //   );
            // }
            // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
            //   for (let bdIndex = 0; bdIndex < param.cartItems.length; bdIndex++) {
            //     await printDocketForKD(
            //       {
            //         userOrder: param,
            //         cartItem: param.cartItems[bdIndex],
            //       },
            //       // true,
            //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
            //     );
            //   }
            // }

            if (param && param.cartItems) {
              for (let index = 0; index < param.cartItems.length; index++) {
                if (param.cartItems[index].isDocket) {
                  await printDocket(
                    {
                      userOrder: param,
                      cartItem: param.cartItems[index],
                    },
                    // true,
                    [PRINTER_USAGE_TYPE.RECEIPT],
                  );
                }
              }
            }
          } else if (showAlert) {
            Alert.alert(
              'Failed',
              'Your request has failed',
              [{ text: 'OK', onPress: () => { } }],
              { cancelable: false },
            );
            // setState({ visible: false });
            // setVisible(false);
            setModalAuthorizeVisibility(false);
          }
        },
      );
  };

  const renderModalAuthorize = (item) => {
    return (
      <View>
        {item && item.uniqueId && (
          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            style={{
              flex: 1,
              backgroundColor: 'rgba(0,0,0,0.5)',
            }}
            visible={modalAuthorizeVisibility}
            transparent
            animationType="slide">
            <View
              style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                // flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                // minHeight: windowHeight,

                width: windowWidth,
                height: windowHeight,

                ...getTransformForModalFullScreen(),
              }}>
              <View
                style={[
                  styles.confirmBox,
                  switchMerchant
                    ? {
                      height: windowHeight * 0.35,
                      width: windowWidth * 0.4,
                    }
                    : {},
                ]}>
                <View style={{ marginTop: switchMerchant ? '4%' : 40 }}>
                  <Text
                    style={[
                      {
                        textAlign: 'center',
                        fontWeight: '700',
                        fontSize: 24,
                      },
                      switchMerchant
                        ? {
                          fontSize: 16,
                        }
                        : {},
                    ]}>
                    Authorize Order
                  </Text>
                </View>
                <View style={{ marginTop: switchMerchant ? '2%' : 20 }}>
                  <Text
                    style={[
                      {
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 18,
                        width: '80%',
                        alignSelf: 'center',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {/* Authorize Takeaway for {item.userName}, Order#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId} */}
                    Order #{item.orderId}
                  </Text>
                </View>
                <View style={{ height: windowHeight * 0.033 }} />
                <View
                  style={{
                    flexDirection: 'row',
                    alignSelf: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '50%',
                    alignContent: 'center',
                    zIndex: 6000,
                  }} />
                <View
                  style={[
                    {
                      alignSelf: 'center',
                      marginTop: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: switchMerchant ? '71.5%' : 250,
                      height: switchMerchant ? 35 : 40,
                      alignContent: 'center',
                      flexDirection: 'row',
                      marginTop: switchMerchant ? '-5%' : 40,
                    },
                    switchMerchant
                      ? {
                        marginTop: '13%',
                        position: 'absolute',
                        bottom: 0,
                        alignItems: 'flex-end',
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    onPress={() => {
                      authorizeOrder(item);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '70%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 35 : 80,
                      borderBottomLeftRadius: 10,
                      borderRightWidth: StyleSheet.hairlineWidth,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={[
                        { fontSize: 22, color: Colors.tabCyan },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            width: '100%',
                            textAlign: 'center',
                          }
                          : {},
                      ]}>
                      Confirm
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => {
                      // setState({ visible: false });
                      // setVisible(false);
                      setModalAuthorizeVisibility(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '70%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 35 : 80,
                      borderBottomRightRadius: 10,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={[
                        { fontSize: 22, color: Colors.descriptionColor },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            width: '100%',
                            textAlign: 'center',
                          }
                          : {},
                      ]}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </ModalView>
        )}
      </View>
    );
  };

  // 28 May - Component Listener
  // Kd Tech Team Whatsapp, 28 May, 3.46PM: Merchant will easily hit few thousand orders
  const MAX_REALTIME_DINE_IN_ORDERS = 100;
  const unsubscribeRef = useRef(null);

  useEffect(() => {
    if (!isMounted) {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }

      setDineInOrders([]);

      return;
    }

    // const userOrdersArray = [];
    // for (const orders of Object.values(userOrdersTableDict)) {
    //   userOrdersArray.push(...orders);
    // }
    // userOrdersArray.sort((a, b) => b.createdAt - a.createdAt); // Fastest

    // setDineInOrders(userOrdersArray);

    const query = firestore()
      .collection(Collections.UserOrder)
      .where('outletId', '==', outletId)
      // .where('isReservationOrder', '==', false)
      .where('orderType', '==', ORDER_TYPE.DINEIN)
      .where('orderTypeSub', '==', ORDER_TYPE_SUB.NORMAL)
      .where('orderStatus', 'in', [
        USER_ORDER_STATUS.ORDER_RECEIVED,
        USER_ORDER_STATUS.ORDER_AUTHORIZED,
        USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT,
        USER_ORDER_STATUS.ORDER_PREPARING,
        USER_ORDER_STATUS.ORDER_PREPARED,
        USER_ORDER_STATUS.ORDER_DELIVERED,
      ])
      .where(
        'createdAt',
        '>=',
        !currOutlet.toggleOpenOrder
          ? moment().subtract(1, 'day').startOf('day').valueOf()
          : moment().subtract(currOutlet.openOrderDays ? currOutlet.openOrderDays : 30, 'day').startOf('day').valueOf(),
      )
      .orderBy('createdAt', 'desc')
      // .orderBy('updatedAt', 'desc')
      .limit(currOutlet.oLimitD ? currOutlet.oLimitD : 1000);

    const unsubscribe = query.onSnapshot((snapshot) => {
      if (snapshot) {
        console.log('snapshot listened', snapshot.docs.length);

        const orders = [];
        snapshot.forEach(doc => {
          const data = doc.data();
          orders.push(data);
        });

        setDineInOrders(orders);
      }
    });

    unsubscribeRef.current = unsubscribe;

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [
    isMounted,
    // userOrdersTableDict,
  ]);

  return (
    // <View style={styles.container}>
    //   {renderModal(currToPrioritizeOrder)}

    //   <View style={styles.sidebar}>
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {renderModal(currToPrioritizeOrder)}
        {renderModalCancel(currToCancelOrder)}
        {renderModalAuthorize(currToAuthorizeOrder)}
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}
        <View style={{ flex: 1, paddingHorizontal: 30, paddingVertical: 30 }}>
          <View
            style={[
              {
                flexDirection: 'row',
                alignItems: 'center',
                padding: 2,
                justifyContent: 'space-between',
                // borderWidth: 1
              },
              switchMerchant
                ? {
                  marginBottom: windowHeight * 0.08,
                  marginTop: '-3%',
                }
                : {},
            ]}>
            <Text
              style={[
                { fontSize: 26, fontFamily: 'NunitoSans-Bold' },
                switchMerchant
                  ? {
                    // fontSize: 15,
                    //folow dashboard
                    fontSize: windowWidth / 35,
                    // borderWidth: 1,
                    // top: windowHeight * -0.08,
                  }
                  : {},
              ]}>
              {dineInOrders.length} {dineInOrders.length > 1 ? 'Orders' : 'Order'}
            </Text>

            <View style={{ flexDirection: 'row' }}>
              {/* <View style={{ flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingLeft: 10,
                  borderRadius: 5,
                  height: 40,
                  borderRadius: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  backgroundColor: 'white',
                  marginRight: 15,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,

                  }}>
            <Text style={{ fontSize: 16, paddingRight: Platform.OS == 'ios' ? 20 : 20, borderColor: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>
              Sort By
            </Text>
            <DropDownPicker
              // controller={instance => controller1 = instance}
              //controller={instance => setController1(instance)}
              arrowColor={Colors.primaryColor}
              arrowSize={23}
              arrowStyle={{ fontWeight: 'bold' }}
              labelStyle={{ fontFamily: 'NunitoSans-Regular' }}
              itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
              placeholderStyle={{ color: 'black' }}
              style={{ width: 130, borderWidth: 0, paddingHorizontal: 5, paddingVertical: 0, borderRadius: 5, borderColor: '#E5E5E5', borderWidth: 0, borderLeftWidth: 0, }}
              items={[{ label: 'Order ID', value: 1 }, { label: 'Oder Date', value: 2 }, { label: 'Customer', value: 3 }, { label: 'Waiting Time', value: 4 }, { label: 'Order Status', value: 5 }, { label: 'Total Price', value: 6 }]}
              placeholder={"All Orders"}
              onChangeItem={selectedSort => {
                sortingOrders(selectedSort);
              }
              }
              //onOpen={() => controller.close()}
            />
          </View> */}

              <View
                style={[
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    paddingLeft: 10,
                    borderBottomLeftRadius: 5,
                    borderTopLeftRadius: 5,
                    height: 40,
                    borderWidth: 1,
                    borderRightWidth: 0,
                    borderColor: '#E5E5E5',
                    backgroundColor: 'white',
                    // marginRight: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    // left: windowWidth * 0.01,
                  },
                  switchMerchant
                    ? {
                      height: 35,
                      paddingLeft: windowWidth * 0.002,
                      // top: windowHeight * -0.075,
                      // right: windowHeight * 0.08,
                    }
                    : {},
                ]}>
                <Text
                  style={[
                    {
                      fontSize: 16,
                      paddingRight: Platform.OS == 'ios' ? 20 : 20,
                      borderColor: Colors.fieldtTxtColor,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                        // borderWidth: 1
                        // paddingLeft: '10%',
                        // left: windowWidth * 0.005,
                        paddingLeft: '1%',
                      }
                      : {},
                  ]}>
                  Filter
                </Text>
              </View>
              <DropDownPicker
                // controller={instance => controller1 = instance}
                listMode="MODAL"
                modalProps={{ transparent: true }}
                controller={(instance) => setController1(instance)}
                arrowColor={Colors.primaryColor}
                arrowSize={switchMerchant ? 13 : 23}
                arrowStyle={[
                  { fontWeight: 'bold' },
                  switchMerchant
                    ? {
                      top: windowHeight * -0.005,
                      height: '180%',
                      // borderWidth: 1
                    }
                    : {},
                ]}
                labelStyle={[
                  { fontFamily: 'NunitoSans-Regular' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                itemStyle={[
                  { justifyContent: 'flex-start', marginLeft: 5 },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                placeholderStyle={[
                  { color: 'black' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                style={[
                  {
                    width: 140,
                    borderWidth: 0,
                    height: 40,
                    paddingHorizontal: 5,
                    paddingVertical: 0,
                    borderBottomRightRadius: 5,
                    borderTopRightRadius: 5,
                    borderTopLeftRadius: 0,
                    borderBottomLeftRadius: 0,
                    borderColor: '#E5E5E5',
                    borderWidth: 1,
                    borderLeftWidth: 0,
                    paddingLeft: 2,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    marginRight: 15,
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                      width: windowWidth * 0.152,
                    }
                    : {},
                ]}
                dropDownStyle={{
                  paddingLeft: 2,
                  right: 15,
                  width: switchMerchant
                    ? windowWidth * 0.152
                    : 140,
                }}
                items={[
                  { label: 'All Orders', value: 0 },
                  { label: 'Pending', value: 1 },
                  { label: 'Paid', value: 2 },
                ]} //Awaiting Authorization
                placeholder={'All Orders'}
                onChangeItem={(item) => {
                  setFilterType(item.value)
                  // filterOrders(selectedFilter);
                }}
                defaultValue={filterType}
              //onOpen={() => controller.close()}
              />
              {/* </View> */}

              <View style={{}}>
                <View
                  style={[
                    {
                      width: 250,
                      height: 40,
                      backgroundColor: 'white',
                      borderRadius: 5,
                      flexDirection: 'row',
                      alignContent: 'center',
                      alignItems: 'center',

                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                    },
                    switchMerchant
                      ? {
                        height: 35,
                        width: 200,
                        // top: windowHeight * -0.075,
                      }
                      : {},
                  ]}>
                  {switchMerchant ? (
                    <Icon
                      name="search"
                      size={10}
                      color={Colors.primaryColor}
                      style={{ marginLeft: 15 }}
                    />
                  ) : (
                    <Icon
                      name="search"
                      size={18}
                      color={Colors.primaryColor}
                      style={{ marginLeft: 15 }}
                    />
                  )}
                  {switchMerchant ? (
                    <TextInput
                      // editable={!loading}
                      style={[
                        {
                          width: 220,
                          fontSize: 15,
                          fontFamily: 'NunitoSans-Regular',
                          paddingLeft: 5,
                          height: 45,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            // borderWidth:1,
                            width: 180,
                            height: 40,
                          }
                          : {},
                      ]}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setSearch(text);
                      }}
                      value={search}
                    />
                  ) : (
                    <TextInput
                      // editable={!loading}
                      underlineColorAndroid={Colors.whiteColor}
                      style={[
                        {
                          width: 220,
                          fontSize: 15,
                          fontFamily: 'NunitoSans-Regular',
                          paddingLeft: 5,
                          height: 45,
                        },
                        switchMerchant
                          ? {
                            fontSize: 8,
                            // borderWidth:1,
                            width: windowWidth * 0.17,
                            height: windowHeight * 0.18,
                          }
                          : {},
                      ]}
                      clearButtonMode="while-editing"
                      placeholder=" Search"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setSearch(text);
                      }}
                      value={search}
                    />
                  )}
                </View>
              </View>
            </View>
          </View>
          <View style={{ flexDirection: 'row', marginTop: 20, alignItems: 'center', justifyContent: 'flex-end', zIndex: -1, }}>
            <TouchableOpacity
              style={{
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: Colors.primaryColor,
                width: 100,
                marginRight: 10,
                borderRadius: 10,
              }}>
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: 'NunitoSans-SemiBold',
                textAlign: 'center',
              }}>
                Dine In
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                props.navigation.navigate('Takeaway');
              }}
              style={{
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: Colors.secondaryColor,
                width: 100,
                marginRight: 10,
                borderRadius: 10,
              }}>
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: 'NunitoSans-SemiBold',
                textAlign: 'center',
              }}>
                Takeaway
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                props.navigation.navigate('OtherDelivery');
              }}
              style={{
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: Colors.tabCyan,
                width: 100,
                borderRadius: 10,
              }}>
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: 'NunitoSans-SemiBold',
                textAlign: 'center',
              }}>
                Other D
              </Text>
            </TouchableOpacity>
          </View>
          <View
            style={[
              { marginTop: 30, marginBottom: 100, zIndex: -1 },
              switchMerchant
                ? {
                  marginTop: windowHeight * -0.05,
                  marginBottom: windowHeight * 0.15,
                }
                : {},
            ]}>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                paddingBottom: 10,
              }}>
              <View
                style={{
                  // marginHorizontal: 1,
                  width: Platform.OS == 'ios' ? '7%' : '7%',
                  //width: Platform.OS == 'android' ? '10%' : '5%',
                  alignItems: 'center',
                }} />
              <View
                style={{
                  marginHorizontal: 1,
                  width: '8%',
                  alignItems: 'flex-start',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    // if (sort1 === DINE_IN_SORT_FIELD_TYPE.TABLE_CODE_ASC) {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.TABLE_CODE_DESC);
                    // } else {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.TABLE_CODE_ASC);
                    // }
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Table
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: '8%',
                  //paddingRight: Platform.OS == 'ios' ? '3%' : '2.7%',
                  alignItems: 'flex-start',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    // if (sort1 === DINE_IN_SORT_FIELD_TYPE.ORDER_ID_ASC) {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.ORDER_ID_DESC);
                    // } else {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.ORDER_ID_ASC);
                    // }
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Order ID
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: '16%',
                  // alignItems: 'center',
                  // textAlign: 'left',
                  justifyContent: 'flex-start',
                  left: windowWidth === 1280 && windowHeight === 800 ? 5 : 0,
                  // right: Platform.OS == 'ios' ? 30 : 0,
                  // right: Platform.OS == 'android' ? 30 : 0,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    // if (sort1 === DINE_IN_SORT_FIELD_TYPE.ORDER_DATE_ASC) {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.ORDER_DATE_DESC);
                    // } else {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.ORDER_DATE_ASC);
                    // }
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {' '}
                    Date/Time{' '}
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: '14%',
                  //width: Platform.OS == 'android' ? '10%' : '14%',
                  alignItems: 'flex-start',
                  // right: Platform.OS == 'ios' ? 10 : 0,
                  // right: Platform.OS == 'android' ? 10 : 0,
                  // paddingLeft: '0.5%'
                }}>
                <TouchableOpacity
                  onPress={() => {
                    // if (sort1 === DINE_IN_SORT_FIELD_TYPE.WAITER_NAME_ASC) {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.WAITER_NAME_DESC);
                    // } else {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.WAITER_NAME_ASC);
                    // }
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Staff
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: '16%',
                  // width: Platform.OS == 'android' ? '13%' : '12%',
                  alignItems: 'flex-start',
                  // right: Platform.OS == 'ios' ? 5 : 30,
                  // right: Platform.OS == 'android' ? 5 : 30,
                  // paddingLeft: Platform.OS === 'ios' ? '2%' : '1%',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    // if (sort1 === DINE_IN_SORT_FIELD_TYPE.WAITING_TIME_ASC) {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.WAITING_TIME_DESC);
                    // } else {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.WAITING_TIME_ASC);
                    // }
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Waiting Time
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: '15%',
                  alignItems: 'flex-start',
                  left: windowWidth === 1280 && windowHeight === 800 ? 3 : 0,
                  // paddingLeft: '4.5%',
                  // right: Platform.OS == 'ios' ? 10 : 0,
                  // right: Platform.OS == 'android' ? 10 : 0,
                }}>
                <TouchableOpacity onPress={() => { }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Payment Status
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  marginHorizontal: 1,
                  width: '14%',
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                  flexDirection: 'row',
                  // paddingLeft: '5%'
                }}>
                <TouchableOpacity
                  style={{ flexDirection: 'row', alignItems: 'center' }}
                  onPress={() => {
                    // if (sort1 === DINE_IN_SORT_FIELD_TYPE.FINAL_PRICE_ASC) {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.FINAL_PRICE_DESC);
                    // } else {
                    //   setSort1(DINE_IN_SORT_FIELD_TYPE.FINAL_PRICE_ASC);
                    // }
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Total
                  </Text>
                  <Text
                    style={[
                      { color: Colors.primaryColor, fontSize: 9 },
                      switchMerchant
                        ? {
                          fontSize: 8,
                        }
                        : {},
                    ]}>
                    {' '}
                    *incl tax
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            <FlatList
              data={filteredDineInOrders}
              renderItem={renderOrder}
              keyExtractor={(item, index) => String(index)}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                paddingBottom: 80,
              }}
              maxToRenderPerBatch={1}
              windowSize={5}
              removeClippedSubviews={true}
            />
          </View>
        </View>
      </View>
    </UserIdleWrapper>)
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },
  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  confirmBox: {
    width: 350,
    height: 237,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default OrderScreen;
