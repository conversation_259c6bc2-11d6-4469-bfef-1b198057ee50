import { Store } from 'pullstate';
import { OUTLET_DISPLAY_PAIRING_DEVICE, OUTLET_DISPLAY_PAIRING_TYPE } from '../constant/common';

export const OutletStore = new Store({
    outletItems: [],

    sortedOutletItems: [],
    outletItemsDict: {},
    outletItemsSkuDict: {},
    outletCategories: [],
    outletCategoriesDict: {},

    allOutletsItems: [],
    allOutletsItemsSkuDict: {},
    allOutletsCategories: [],
    allOutletsCategoriesNameDict: {},
    allOutletsCategoriesDict: {},

    allOutletsCategoriesUnique: [],

    outletSections: [],
    outletSectionsDict: {},

    outletTables: [],
    outletTablesDict: {},
    outletTablesCodeDict: {},

    outletTableCombinations: [],
    outletTableCombinationsDict: {},

    userOrders: [],
    userOrdersDict: {}, // { user_order_id -> user_order, ... }
    userOrdersTableDict: {}, // { outlet_table_id -> [ user_order, .... ], ... }

    userOrdersAllStatus: [],
    userOrdersPrinting: [],

    ///////////////////////////////////

    userOrdersNormal: [],
    userOrdersTableDictNormal: {},
    userOrdersPrintingNormal: [],

    userOrdersReservation: [],
    userOrdersTableDictReservation: {},
    userOrdersPrintingReservation: [],

    ///////////////////////////////////

    userReservations: [],
    userReservationsDict: {},
    userReservationsUserIdDict: {},

    userReservationsWaitList: [],
    userReservationsWaitListDict: {},
    userReservationsWaitListUserIdDict: {},

    userQueues: [],
    userQueuesDict: {},
    userRings: [],
    userRingsDict: {},

    allOutletsUserOrdersDone: [],
    allOutletsUserOrders: [],

    allOutletsUserOrdersDoneRealTime: [],
    allOutletsUserOrdersRealTime: [],
    allOutletsUserOrdersDoneCache: [],
    allOutletsUserOrdersCache: [],

    allOutletsUserOrdersLoyaltyDone: [],
    allOutletsUserOrdersLoyalty: [],

    allOutletsUserOrdersLoyaltyDoneRealTime: [],
    allOutletsUserOrdersLoyaltyRealTime: [],
    allOutletsUserOrdersLoyaltyDoneCache: [],
    allOutletsUserOrdersLoyaltyCache: [],

    userOrdersExpandedDict: {}, // { user_order_id -> true, ... } | used for KitchenOrder
    // userORdersExpandedFooterDict: {}, // { user_order_id -> true, ... } | used for KitchenScreen, footer

    currOutletShift: {},
    currOutletShiftStatus: 'CLOSED',

    allOutletShifts: [],
    reportOutletShifts: [],

    allOutletsEmployees: [],
    allOutletsEmployeesUserActionsDict: {},

    outletsOpeningDict: {},

    beerDocketCategories: [],
    beerDocketCategoriesDict: {},

    beerDockets: [],
    beerDocketsDict: {},

    userBeerDockets: [],

    userOrderBeerDocketUBDIdDict: {},

    promotions: [],
    promotionsDict: {},

    linkedMerchantIdUsers: [],
    linkedOutletFavoriteUsers: [],
    favoriteMerchantIdUsers: [],
    crmUsersRaw: [],
    crmUsers: [],
    crmUsersDict: {},
    crmUsersNoLimit: [],
    crmUserTags: [],
    crmUserTagsDict: {},
    crmSegments: [],

    crmNextBatch: [],
    crmLastDoc: [],
    lastDocArr: [],

    filteredCRMList: [],

    selectedCustomerOrdersUserId: [],
    selectedCustomerDineInOrdersUserId: [],
    selectedCustomerOrdersPhone: [],
    selectedCustomerDineInOrdersPhone: [],

    selectedCustomerOrders: [],
    selectedCustomerDineInOrders: [],

    selectedCustomerAddresses: [],
    // selectedCustomerPointsTransactions: [],
    // selectedCustomerPointsBalance: 0,
    selectedCustomerReservations: [],
    //selectedCustomerVouchers: [],
    selectedCustomerVoucherRedemptions: [],
    selectedCustomerUserBeerDockets: [],

    selectedCustomerUserBeerDocketsEmail: [],
    selectedCustomerUserBeerDocketsUserId: [],

    selectedCustomerCashbackAmount: 0,

    selectedCustomerLCCTransactions: [], // loyalty campaign credit transactions
    selectedCustomerLCCBalance: 0, // loyalty campaign credit balance

    selectedCustomerLCCTransactionsEmail: [], // loyalty campaign credit transactions
    selectedCustomerLCCBalanceEmail: 0, // loyalty campaign credit balance
    selectedCustomerLCCTransactionsPhone: [], // loyalty campaign credit transactions
    selectedCustomerLCCBalancePhone: 0, // loyalty campaign credit balance
    // selectedCustomerLCCTransactionsUserId: [], // loyalty campaign credit transactions
    // selectedCustomerLCCBalanceUserId: 0, // loyalty campaign credit balance

    selectedCustomerPointsTransactions: [],
    selectedCustomerPointsBalance: 0,
    selectedCustomerPointsTransactionsEmail: [],
    selectedCustomerPointsBalanceEmail: 0,
    selectedCustomerPointsTransactionsPhone: [],
    selectedCustomerPointsBalancePhone: 0,

    selectedCustomerUserLoyaltyCampaigns: [],

    selectedCustomerUserTaggableVouchers: [],
    selectedCustomerUserTaggableVouchersView: [],

    selectedCustomerUserLoyaltyStamps: [],

    preorderPackages: [],

    pointsRedeemPackages: [],
    pointsRedeemPackagesDict: {},

    outletPrinters: [],
    sunmiPrinters: [],
    iminPrinters: [],
    blePrinters: [],
    lanLabelPrinters: [],

    outletPaymentMethods: [],
    outletPaymentMethodsDict: {},

    loyaltyStamps: [],
    loyaltyStampsType: [],

    loyaltyCampaigns: [],
    availableLoyaltyCampaigns: [],

    upsellingCampaigns: [],

    liveProductList: null,
    liveProductListArray: [],

    loyaltyTier: {},

    currTableQRUrl: '',
    currOrderRegisterUrl: '',

    taggableVouchersOutlet: [],
    taggableVouchersMerchant: [],

    taggableVouchers: [],
    availableTaggableVouchers: [],

    employeeClockDict: {},

    userTaggableVouchers: [],

    /////////////////////////////////////////

    selectedCustomerApplicableVoucherIdList: [],

    /////////////////////////////////////////

    topupCreditTypes: [],

    ptTimestamp: Date.now(),
    pteTimestamp: Date.now(),

    payoutTransactions: [],
    payoutTransactionsExtend: [],

    reportDisplayType: 'DAY',

    outletReviews: [],

    reservationConfig: null,

    queueConfig: null,

    recommendedLoyalty: null,
    selectedRecommendedLoyalty: null,

    /////////////////////////////////////////

    odPairingType: OUTLET_DISPLAY_PAIRING_TYPE.A,
    odPairingDevice: OUTLET_DISPLAY_PAIRING_DEVICE.HOST,
    odData: null,
    odDt: Date.now(),

    /////////////////////////////////////////

    outletCatalog: [],

    /////////////////////////////////////////

    ///////////////////////////////

    // 2024-04-25 - delivery credit changes

    deliveryCreditBalance: 0,
    deliveryCreditWalletId: '',

    ///////////////////////////////

    // 2025-08-13 - Tiktok Shop
    isTiktokShopAuthorized: false,

    ///////////////////////////////
});   