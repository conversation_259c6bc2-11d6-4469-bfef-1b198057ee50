import { Text } from "react-native-fast-text";
import React, { Component, useEffect, useReducer, useState, useCallback, useRef } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Modal as ModalComponent,
  PermissionsAndroid,
  Platform,
  useWindowDimensions,
  ActivityIndicator,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Feather';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import Icon2 from 'react-native-vector-icons/EvilIcons';
import Icon3 from 'react-native-vector-icons/Foundation';
import Icon4 from 'react-native-vector-icons/FontAwesome5';
import DropDownPicker from 'react-native-dropdown-picker';
// import { ceil } from 'react-native-reanimated';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from "react-native-modal-datetime-picker";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import moment from 'moment';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles'
import DocumentPicker from 'react-native-document-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
import RNFetchBlob from 'rn-fetch-blob';
import {
  isTablet,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
} from '../util/common';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import { STOCK_TRANSFER_STATUS, STOCK_TRANSFER_STATUS_PARSED, EMAIL_REPORT_TYPE, PURCHASE_ORDER_STATUS, EXPAND_TAB_TYPE, } from '../constant/common';
import { convertArrayToCSV, generateEmailReport } from '../util/common';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import XLSX from 'xlsx';
import { Row } from 'react-native-table-component';
import { v4 as uuidv4 } from 'uuid';
import GCalendar from '../assets/svg/GCalendar'
import GCalendarGrey from '../assets/svg/GCalendarGrey'
import { OutletStore } from '../store/outletStore';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
// import ViewShot from 'react-native-view-shot';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
// import stockTransferOrderAmountsHtml from '../templates/stock_transfer_order_amounts.html';
import { STOCK_TRANSFER_ORDER_AMOUNTS_HTML } from '../templates/stock_transfer_order_amounts';
import { KEYWORDS } from '../templates/keywords';
import APILocal from '../util/apiLocalReplacers';
import { logEventAnalytics } from '../util/common';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';

const RNFS = require('@dr.pogodin/react-native-fs');
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);

const StockTransferProductScreen = props => {
  const {
    navigation,
  } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const viewShotRef = useRef();

  const [stockTransfer, setStockTransfer] = useState(true);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [addStockTransfer, setAddStockTransfer] = useState(false);
  const [stockList, setStockList] = useState([]);
  const [stockTransferList, setStockTransferList] = useState([]);
  const [stockTakeList, setStockTakeList] = useState([]);
  const [orderList, setOrderList] = useState([]);
  const [itemsToOrder, setItemsToOrder] = useState([{}, {}, {}],);
  const [itemsToOrder2, setItemsToOrder2] = useState([{}, {}, {}],);
  const [addStockTransferList, setAddStockTransferList] = useState([{}, {}, {}],);
  const [addCountedStockTakeList, setAddCountedStockTakeList] = useState([{}, {}, {}],);
  const [addUnCountedStockTakeList, setAddUnCountedStockTakeList] = useState([{}, {}, {}],);
  const [productList, setProductList] = useState([]);
  const [isSelected, setIsSelected] = useState(false);
  const [isSelected2, setIsSelected2] = useState(false);
  const [isSelected3, setIsSelected3] = useState(true);
  const [isSelected4, setIsSelected4] = useState(false);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [date, setDate] = useState(Date.now());
  const [date1, setDate1] = useState(Date.now());
  const [createdDate, setCreatedDate] = useState(Date.now());
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState('');
  const [modal, setModal] = useState(false);
  // const [// outletId, set// outletId] = useState(1);
  const [outletId, setOutletId] = useState(User.getOutletId());
  const [search, setSearch] = useState('');
  const [search2, setSearch2] = useState('');
  const [search3, setSearch3] = useState('');
  const [ideal, setIdeal] = useState('');
  const [minimum, setMinimum] = useState('');
  const [itemId, setItemId] = useState('');
  const [choose, setChoose] = useState(null);

  const [loading, setLoading] = useState(false);

  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [rev_date, setRev_date] = useState(moment().subtract(6, 'days').startOf('day'),);
  const [rev_date1, setRev_date1] = useState(moment().endOf(Date.now()).endOf('day'),);
  //////////////////////////////////////////////////////////////////////

  const [poId, setPoId] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [exportEmail, setExportEmail] = useState('');
  const [exportModal, setExportModal] = useState(false);
  const [importModal, setImportModal] = useState(false);

  const [doModal, setDoModal] = useState(false);
  const [doItem, setDoItem] = useState(null);

  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);
  const [isLoadingLocalExcel, setIsLoadingLocalExcel] = useState(false);
  const [isLoadingLocalCsv, setIsLoadingLocalCsv] = useState(false);

  const [poStatus, setPoStatus] = useState(STOCK_TRANSFER_STATUS.CREATED);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [selectedSourceOutletIdPrev, setSelectedSourceOutletIdPrev] = useState('');
  const [selectedSourceOutletId, setSelectedSourceOutletId] = useState('');

  const [outletSupplyItemDropdownList, setOutletSupplyItemDropdownList] = useState([]);

  const [poItems, setPoItems] = useState([
    {
      outletSupplyItemId: '',
      name: '',
      sku: '',
      unit: '',
      skuMerchant: '',
      quantity: 0,
      transferQuantity: 0,
      balance: 0,
      price: 0,
      totalPrice: 0,

      supplyItem: null,
    }
  ]);

  const [subtotal, setSubtotal] = useState(0);
  const [taxTotal, setTaxTotal] = useState(0);
  const [discountTotal, setDiscountTotal] = useState(0);
  const [finalTotal, setFinalTotal] = useState(0);

  const [outletSupplyItems, setOutletSupplyItems] = useState([]);

  // const [supplyItems, setSupplyItems] = useState([]);

  const outletItems = OutletStore.useState(s => s.outletItems);
  const allOutletsItems = OutletStore.useState(s => s.allOutletsItems);

  const supplyItems = CommonStore.useState(s => s.supplyItems);
  const supplyItemsSkuDict = CommonStore.useState(s => s.supplyItemsSkuDict);

  const allOutletsSupplyItemsSkuDict = CommonStore.useState(s => s.allOutletsSupplyItemsSkuDict);
  const allOutletsSupplyItems = CommonStore.useState(s => s.allOutletsSupplyItems);
  const allOutletsSupplyItemsDict = CommonStore.useState(s => s.allOutletsSupplyItemsDict);

  const outletSupplyItemsDict = CommonStore.useState(s => s.outletSupplyItemsDict);
  const outletSupplyItemsSkuDict = CommonStore.useState(s => s.outletSupplyItemsSkuDict);

  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);
  const stockTransfersProduct = CommonStore.useState(s => s.stockTransfersProduct);

  const userName = UserStore.useState(s => s.name);
  const userId = UserStore.useState(s => s.firebaseUid);
  const merchantName = MerchantStore.useState(s => s.name);

  const currOutlet = MerchantStore.useState(s => s.currOutlet);
  const currOutletId = MerchantStore.useState(s => s.currOutletId);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const dropDownRef = React.useRef();
  const dropDownRef1 = React.useRef();

  const selectedStockTransferEdit = CommonStore.useState(s => s.selectedStockTransferEdit);
  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);


  useEffect(() => {
    if (currOutletId !== '' &&
      allOutlets.length > 0 &&
      stockTransfersProduct.length > 0) {
      var stockTransferProductTemp = [];
      for (var i = 0; i < stockTransfersProduct.length; i++) {
        if (moment(rev_date).isSameOrBefore(stockTransfersProduct[i].createdAt) &&
          moment(rev_date1).isAfter(stockTransfersProduct[i].createdAt)
        ) {
          stockTransferProductTemp.push(stockTransfersProduct[i]);
        }
      }
      stockTransferProductTemp.sort((a, b) => b.orderDate - a.orderDate)
      setStockTransferList(stockTransferProductTemp);
    }
  }, [currOutletId, rev_date, rev_date1, stockTransfersProduct])

  useEffect(() => {
    if (selectedStockTransferEdit) {
      // insert info

      setEditMode(false);

      setPoId(selectedStockTransferEdit.stId);
      setPoStatus(selectedStockTransferEdit.status);
      setSelectedSourceOutletId(selectedStockTransferEdit.sourceOutletId);
      setSelectedTargetOutletId(selectedStockTransferEdit.targetOutletId);
      setDate(selectedStockTransferEdit.estimatedArrivalDate);
      setCreatedDate(selectedStockTransferEdit.createdAt);


      if (selectedStockTransferEdit.stItems) {
        setPoItems(selectedStockTransferEdit.stItems);

        // delay the state update
        setTimeout(() => {
          setPoItems(selectedStockTransferEdit.stItems);
        }, 100);
      }
    }
    else {
      // designed to always mounted, thus need clear manually...

      setEditMode(false);

      if (stockTransfersProduct.length > 0) {
        // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
        setPoId(`ST${(stockTransfersProduct.length + 1).toString().padStart(4, '0')}`);
      }
      setPoStatus(STOCK_TRANSFER_STATUS.CREATED);
      setSelectedSourceOutletId(allOutlets[0].uniqueId);
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
      setDate(Date.now());

      if (outletItems.length > 0) {
        setPoItems([
          {
            outletSupplyItemId: outletItems[0].uniqueId,
            name: outletItems[0].name,
            sku: outletItems[0].sku,
            unit: '',
            skuMerchant: outletItems[0].skuMerchant,
            quantity: outletItems[0].stockCount || 0,
            transferQuantity: 0,
            balance: 0,
            price: outletItems[0].price,
            totalPrice: 0,

            supplyItem: outletItems[0],
          }
        ]);
      }

      // if (outletSupplyItems.length > 0 && Object.keys(allOutletsSupplyItemsDict).length > 0) {
      // if (outletSupplyItems.length > 0) {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //       name: outletSupplyItems[0].name,
      //       sku: outletSupplyItems[0].sku,
      //       quantity: outletSupplyItems[0].quantity,
      //       transferQuantity: 0,
      //       price: outletSupplyItems[0].price,
      //       totalPrice: 0,
      //     }
      //   ]);
      // }
      // else {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: '',
      //       name: '',
      //       sku: '',
      //       quantity: 0,
      //       transferQuantity: 0,
      //       price: 0,
      //       totalPrice: 0,
      //     }
      //   ]);
      // }
    }
  }, [selectedStockTransferEdit, addStockTransfer]);

  useEffect(() => {
    if (selectedStockTransferEdit === null && stockTransfersProduct.length > 0) {
      // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
      setPoId(`ST${(stockTransfersProduct.length + 1).toString().padStart(4, '0')}`);
    }
  }, [stockTransfersProduct]);

  useEffect(() => {
    if (outletItems.length > 0) {
      // setPoItems([
      //   {
      //     outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //     name: outletSupplyItems[0].name,
      //     sku: outletSupplyItems[0].sku,
      //     skuMerchant: outletSupplyItems[0].skuMerchant,
      //     quantity: outletSupplyItems[0].quantity,
      //     transferQuantity: outletSupplyItems[0].transferQuantity,
      //     balance: outletSupplyItems[0].balance,
      //     price: outletSupplyItems[0].price,
      //     totalPrice: 0,

      //     supplyItem: supplyItems[0],
      //   }
      // ]);

      // delay the state updating
      // setTimeout(() => {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //       name: outletSupplyItems[0].name,
      //       sku: outletSupplyItems[0].sku,
      //       skuMerchant: outletSupplyItems[0].skuMerchant,
      //       quantity: outletSupplyItems[0].quantity,
      //       transferQuantity: outletSupplyItems[0].transferQuantity,
      //       balance: outletSupplyItems[0].balance,
      //       price: outletSupplyItems[0].price,
      //       totalPrice: 0,

      //       supplyItem: supplyItems[0],
      //     }
      //   ]);
      // }, 500);
    }
    else {
      setPoItems([
        {
          outletSupplyItemId: '',
          name: '',
          sku: '',
          unit: '',
          skuMerchant: '',
          quantity: 0,
          transferQuantity: 0,
          price: 0,
          totalPrice: 0,

          supplyItem: null,
        }
      ]);
    }
  }, [selectedSourceOutletId]);

  useEffect(() => {
    setOutletSupplyItems(allOutletsSupplyItems.filter(outletSupplyItem => {
      if (outletSupplyItem.outletId === selectedSourceOutletId && outletSupplyItem.quantity > 0) {
        return true;
      }
    }));
  }, [allOutletsSupplyItems, selectedSourceOutletId]);

  useEffect(() => {
    const outletDropdownListTemp = allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId }))

    setTargetOutletDropdownList(outletDropdownListTemp);

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
      setSelectedSourceOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  useEffect(() => {
    setOutletSupplyItemDropdownList(outletItems.map(outletSupplyItem => {
      // if (selectedSupplierId === supplyItem.supplierId) {
      //   return { label: supplyItem.name, value: supplyItem.uniqueId };
      // }      

      return { label: outletSupplyItem.name, value: outletSupplyItem.uniqueId };
    }));

    if (outletItems.length > 0 &&
      poItems.length === 1 &&
      poItems[0].outletSupplyItemId === '') {
      setPoItems([
        {
          outletSupplyItemId: outletItems[0].uniqueId,
          name: outletItems[0].name,
          sku: outletItems[0].sku,
          unit: '',
          skuMerchant: outletItems[0].skuMerchant,
          quantity: outletItems[0].stockCount || 0,
          transferQuantity: 0,
          balance: 0,
          price: outletItems[0].price,
          totalPrice: 0,

          supplyItem: outletItems[0],
        }
      ]);
    }
    else if (
      poItems[0].outletSupplyItemId !== ''
      // &&
      // Object.keys(allOutletsSupplyItemsDict).length > 0
    ) {
      if (selectedSourceOutletIdPrev.length > 0 &&
        selectedSourceOutletIdPrev !== selectedSourceOutletId) {
        // reset current outlet supply items

        setPoItems([
          {
            outletSupplyItemId: outletItems[0].uniqueId,
            name: outletItems[0].name,
            unit: '',
            sku: outletItems[0].sku,
            skuMerchant: outletItems[0].skuMerchant,
            quantity: outletItems[0].stockCount || 0,
            transferQuantity: 0,
            balance: 0,
            price: outletItems[0].price,
            totalPrice: 0,

            supplyItem: outletItems[0],
          }
        ]);

        // disabled first, outletSupplyItems might slow to retrieve
        // setSelectedSourceOutletIdPrev(selectedSourceOutletId);
      }
      else {
        var poItemsTemp = [
          ...poItems,
        ];

        for (var i = 0; i < poItemsTemp.length; i++) {
          poItemsTemp[i] = {
            ...poItemsTemp[i],
            // quantity: allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].quantity : 0, // check if the supply item sku for this outlet existed | might changed in real time
            // price: allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].price : 0, // might changed in real time
            quantity: allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId) ? (allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId).stockCount || 0) : 0,
            price: allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId) ? (allOutletsItems.find(item => item.uniqueId === poItemsTemp[i].outletSupplyItemId).price || 0) : 0,
          };
        }

        setPoItems(poItemsTemp);
      }
    }
  }, [
    // outletSupplyItems,
    // allOutletsSupplyItemsDict,
    // supplyItems,
    outletItems,
    allOutletsItems,
    selectedSourceOutletIdPrev
  ]);

  useEffect(() => {
    // console.log('balance');
    // console.log(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
    setSubtotal(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
  }, [poItems]);

  useEffect(() => {
    // console.log('subtotal');
    // console.log(poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0));
    setSubtotal(poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0));
  }, [poItems]);

  // useEffect(() => {
  //   // console.log('taxTotal');
  //   // console.log(subtotal * selectedSupplier.taxRate);
  //   setTaxTotal(subtotal * selectedSupplier.taxRate);
  // }, [subtotal]);

  useEffect(() => {
    // console.log('finalTotal');
    // console.log((subtotal - discountTotal) + taxTotal);
    setFinalTotal((subtotal - discountTotal) + taxTotal);
  }, [subtotal, discountTotal, taxTotal]);

  useEffect(() => {
    requestStoragePermission();

    setPoId(nanoid());
  }, []);


  // useEffect(() => {
  //   if (poItems.find(poItem => poItem.outletSupplyItemId === poItem.outletSupplyItemId)){
  //     Alert.alert(
  //       'Error',
  //       'Same Supply Item.',
  //       [
  //         {
  //           text: "OK", onPress: () => {
  //           }
  //         }
  //       ],
  //     );
  //   return;
  //   }
  // }, [poItems]);

  //////////////////////////////////////////////////////////////////////

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }

          logEventAnalytics({
            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_LOGO,
            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_LOGO,
          })
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Stock Transfer
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_PROFILE,
              eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_PROFILE,
            })
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   // setInterval(() => {
  //   //   getStockOrder()
  //   //   getStockTransfer()
  //   //   getLowStock()
  //   // }, 1000);
  //   getStockOrder()
  //   getStockTransfer()
  //   getLowStock()
  // }

  // async componentWillMount = () => {
  //   await requestStoragePermission()
  // }

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: "Navro Storage Permission",
          message:
            "Navro App needs access to your storage ",
          buttonNegative: "Cancel",
          buttonPositive: "OK"
        }
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        // console.log("Storage permission granted");
      } else {
        // console.log("Storage permission denied");
      }
    } catch (err) {
      console.warn(err);
    }
  }

  const importCSV = () => {
    try {
      const res = DocumentPicker.pickSingle({
        type: [DocumentPicker.types.csv],
      });
      // console.log(
      //   res.uri,
      //   res.type,
      //   res.name,
      //   res.size
      // );
    } catch (err) {
      if (DocumentPicker.isCancel(err)) {
        // User cancelled the picker, exit any dialogs or menus and move on
      } else {
        throw err;
      }
    }
  }

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    for (var i = 0; i < poItems.length; i++) {
      if (i <= 5) {
        var excelColumn = {
          'Product Name': poItems[i].name,
          'SKU': poItems[i].skuMerchant,
          'Unit': poItems[i].unit,
          // 'In stock': poItems[i].quantity,
          'From Stock': poItems[i].quantity,
          'Transfer Qty': poItems[i].transferQuantity,
          'Balance Stock': poItems[i].balance,
          'Cost(RM)': poItems[i].price,
          'Subtotal(RM)': poItems[i].totalPrice,
        }
        excelTemplate.push(excelColumn);

      }
      else {

      }
    }

    // console.log('excelTemplate');
    // console.log(excelTemplate);

    return excelTemplate;

  };

  const importSelectFile = async () => {
    try {
      const res = await DocumentPicker.pickSingle({
        type: [DocumentPicker.types.xlsx],
      });

      // console.log(res)

    } catch (err) {

      if (DocumentPicker.isCancel(err)) {

      } else {
        throw err;
      }
    }
  }

  //error show readAsArrayBuffer not implemented
  const importTemplate = (file) => {
    const promise = new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.readAsArrayBuffer(file);

      fileReader.onload = (e) => {
        const bufferArray = e.target.result;

        const wb = XLSX.read(bufferArray, { type: "buffer" });

        const wsname = wb.SheetNames[0];

        const ws = wb.Sheets[wsname];

        const data = XLSX.utils.sheet_to_json(ws);

        resolve(data);
      };

      fileReader.onerror = (error) => {
        reject(error);
      };
    });

    promise.then((d) => {
      // console.log(d);
    });
  }

  const exportTemplate = () => {
    const excelTemplate = convertTemplateToExcelFormat();

    var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-Inventory-Stock-Transfer${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
    var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
    var excelWorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(excelWorkBook, excelWorkSheet, "Stock Transfer Template");

    const workBookData = XLSX.write(excelWorkBook, {
      type: 'binary',
      bookType: 'xlsx',
    });

    RNFS.writeFile(excelFile, workBookData, 'ascii')
      .then((success) => {
        // console.log(`wrote file ${excelFile}`);

        Alert.alert(
          'Success',
          `Send to ${excelFile}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((err) => {
        // console.log(err.message);
      });
  }

  const downloadCsv = () => {
    if (stockTransfersProduct) {
      const csvData = convertArrayToCSV(stockTransfersProduct);

      const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/koodoo-report-stock-transfer-${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
      // console.log("PATH", pathToWrite)
      RNFetchBlob.fs
        .writeFile(pathToWrite, csvData, 'utf8')
        .then(() => {
          // console.log(`wrote file ${pathToWrite}`);
          // wrote file /storage/emulated/0/Download/data.csv
          Alert.alert(
            'Success',
            `Send to ${pathToWrite}`,
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        })
        .catch(error => console.error(error));
    }

    // var body = {
    //   data: stockTransferList
    // }
    // ApiClient.POST(API.exportDataCSV, body, false).then((result) => {
    //   // console.log("RESULT", result)
    //   if (result !== null) {
    //     const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/StockTransferData.csv`;
    //     // console.log("PATH", pathToWrite)
    //     RNFetchBlob.fs
    //       .writeFile(pathToWrite, result, 'utf8')
    //       .then(() => {
    //         // console.log(`wrote file ${pathToWrite}`);
    //         // wrote file /storage/emulated/0/Download/data.csv
    //         Alert.alert(
    //           'Success',
    //           'The data had exported',
    //           [{ text: 'OK', onPress: () => { } }],
    //           { cancelable: false },
    //         );
    //       })
    //       .catch(error => console.error(error));
    //   }
    // });
  }

  const convertDataToExcelFormat = () => {
    var excelData = [];

    for (var i = 0; i < stockTransferList.length; i++) {
      for (var j = 0; j < stockTransferList[i].stItems.length; j++) {
        var excelRow = {
          'Transfer Product': stockTransferList[i].stItems[j].name,
          'In Stock': stockTransferList[i].stItems[j].quantity ? stockTransferList[i].stItems[j].quantity.toFixed(2) : '0',
          'Transfer Quantity': stockTransferList[i].stItems[j].transferQuantity ? stockTransferList[i].stItems[j].transferQuantity : '0',
          'Balance': stockTransferList[i].stItems[j].balance ? stockTransferList[i].stItems[j].balance.toFixed(2) : '0',
          'Stock Transfer ID': stockTransferList[i].stId,
          'Created Date': moment(stockTransferList[i].orderDate).format('DD/MM/YYYY'),
          'From': stockTransferList[i].sourceOutletName,
          'To': stockTransferList[i].targetOutletName,
          'Status': stockTransferList[i].status,

        };

        excelData.push(excelRow);
      }
    }



    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };

  const handleExportExcel = () => {
    const excelData = convertDataToExcelFormat();

    var ws = XLSX.utils.json_to_sheet(excelData);
    var wb = XLSX.utils.book_new();

    XLSX.utils.book_append_sheet(wb, ws, "KooDoo Transfer Report");
    const wbout = XLSX.write(wb, { type: 'binary', bookType: "xlsx" });
    RNFS.writeFile(`${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.xlsx`, wbout, 'ascii').then((success) => {
      Alert.alert(
        'Success',
        `Exported to ${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.xlsx`,
        [
          {
            text: 'OK',
            onPress: () => {
              CommonStore.update((s) => {
                s.isLoading = false;
              });
              setIsLoadingLocalExcel(false);
              setExportModal(false);

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT,
                eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_SUCCESS_ALERT,
              })
            },
          },
        ],
        { cancelable: false },
      );
      console.log('Success');
    }).catch((e) => {
      console.log('Error', e);
    });
  };

  const handleExportCsv = () => {

    const excelData = convertDataToExcelFormat();

    var ws = XLSX.utils.json_to_sheet(excelData);
    var wb = XLSX.utils.book_new();

    XLSX.utils.book_append_sheet(wb, ws, "KooDoo Transfer Report");
    const wbout = XLSX.write(wb, { type: 'binary', bookType: "csv" });
    RNFS.writeFile(`${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.csv`, wbout, 'ascii').then((success) => {
      Alert.alert(
        'Success',
        `Exported to ${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Transfer-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.csv`,
        [
          {
            text: 'OK',
            onPress: () => {
              CommonStore.update((s) => {
                s.isLoading = false;
              });
              setIsLoadingLocalCsv(false);
              setExportModal(false);

              logEventAnalytics({
                eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT,
                eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_SUCCESS_ALERT,
              })
            },
          },
        ],
        { cancelable: false },
      );
      console.log('Success');
    }).catch((e) => {
      console.log('Error', e);
    });

  };

  const downloadExcel = () => {
    const excelData = convertDataToExcelFormat();

    var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-Stock-Transfer${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
    var excelWorkSheet = XLSX.utils.json_to_sheet(excelData);
    var excelWorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(excelWorkBook, excelWorkSheet, "Stock Transfer");

    const workBookData = XLSX.write(excelWorkBook, {
      type: 'binary',
      bookType: 'xlsx',
    });

    RNFS.writeFile(excelFile, workBookData, 'ascii')
      .then((success) => {
        // console.log(`wrote file ${excelFile}`);

        Alert.alert(
          'Success',
          `Send to ${excelFile}`,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      })
      .catch((err) => {
        // console.log(err.message);
      });
  }

  const importFunc = () => {
    var body = {
      data: stockTransferList
    }
    // ApiClient.POST(API.exportDataCSV, body, false).then((result) => {
    //   // console.log(result)
    // });

    Alert.alert(
      'Success',
      'The data has been imported',
      [
        { text: "OK", onPress: () => { } }
      ],
      { cancelable: false },
    );
  }

  const getLowStock = () => {
    // ApiClient.GET(API.getLowStock + outletId).then((result) => {

    //   setState({ stockList: result });
    // });


  }

  const getStockTransfer = () => {
    // ApiClient.GET(API.getTransfer + 1).then((result) => {

    //   setState({ stockTransferList: result, stockTakeList: result });
    // });
  }

  const getStockOrder = () => {
    // ApiClient.GET(API.getStockOrder + outletId).then((result) => {

    //   setState({ orderList: result })

    // });
  }

  // function here
  // const showDateTimePicker = () => {
  //   // setState({ isDateTimePickerVisible: true });
  //   setIsDateTimePickerVisible(true);
  // };

  // const hideDateTimePicker = () => {
  //   // setState({ isDateTimePickerVisible: false });
  //   setIsDateTimePickerVisible(false);
  // };

  // const handleDatePicked = date => {

  //   // setState({ date: date.toString() });
  //   setDate(date);
  // };

  const exportHtmlToPdf = async (item) => {
    var sourceOutlet = allOutlets.find(outlet => outlet.uniqueId === item.sourceOutletId);
    var targetOutlet = allOutlets.find(outlet => outlet.uniqueId === item.targetOutletId);
    var sourceOutletEmail = (sourceOutlet && sourceOutlet.email) ? sourceOutlet.email : 'N/A';
    var sourceOutletPhone = (sourceOutlet && sourceOutlet.phone) ? sourceOutlet.phone : 'N/A';
    var targetOutletEmail = (targetOutlet && targetOutlet.email) ? targetOutlet.email : 'N/A';
    var targetOutletPhone = (targetOutlet && targetOutlet.phone) ? targetOutlet.phone : 'N/A';

    var totalPriceForAll = 0;
    var totalTransferQtyForAll = 0;

    var itemStrList = [];
    for (var i = 0; i < item.stItems.length; i++) {
      // totalPriceForAll += item.stItems[i].totalPrice ? item.stItems[i].totalPrice : 0;
      totalTransferQtyForAll += item.stItems[i].transferQuantity ? item.stItems[i].transferQuantity : 0;

      // itemStrList.push(
      //   `<tr>
      //   <td>
      //     <b>${item.stItems[i].name || '-'}</b>
      //     <br />
      //     ${item.stItems[i].skuMerchant || '-'}
      //   </td>
      //   <td>
      //   ${item.stItems[i].quantity.toFixed(0)}
      //   </td>
      //   <td>
      //   ${item.stItems[i].transferQuantity ? item.stItems[i].transferQuantity.toFixed(0) : '0'}
      //   </td>
      //   <td>
      //   ${item.stItems[i].totalPrice ? item.stItems[i].totalPrice.toFixed(2) : '0.00'}
      //   </td>
      // </tr>`
      // );

      itemStrList.push(
        `<tr>
        <td>
          <b>${item.stItems[i].name || '-'}</b>
          <br />
          ${item.stItems[i].skuMerchant || '-'}
        </td>
        <td>
        ${item.stItems[i].transferQuantity ? item.stItems[i].transferQuantity.toFixed(0) : '0'}
        </td>
      </tr>`
      );
    }

    let replacedHtml = STOCK_TRANSFER_ORDER_AMOUNTS_HTML
      .replaceAll(KEYWORDS.MERCHANT_NAME, merchantName)
      .replaceAll(KEYWORDS.TITLE, 'Delivery Order')
      .replaceAll(KEYWORDS.HEADER_DATE, moment(item.createdAt).format('YYYY-MM-DD'))
      .replaceAll(KEYWORDS.HEADER_ID, item.stId)
      .replaceAll(KEYWORDS.OUTLET_NAME_FROM, item.sourceOutletName)
      .replaceAll(KEYWORDS.OUTLET_EMAIL_FROM, sourceOutletEmail)
      .replaceAll(KEYWORDS.OUTLET_PHONE_FROM, sourceOutletPhone)
      .replaceAll(KEYWORDS.OUTLET_NAME_TO, item.targetOutletName)
      .replaceAll(KEYWORDS.OUTLET_EMAIL_TO, targetOutletEmail)
      .replaceAll(KEYWORDS.OUTLET_PHONE_TO, targetOutletPhone)
      .replaceAll(KEYWORDS.REMARKS, item.remarks ? item.remarks : 'N/A')
      .replaceAll(KEYWORDS.ROW_ITEMS, itemStrList.join(''))
      .replaceAll(KEYWORDS.FOOTER_TOTAL, totalTransferQtyForAll.toFixed(0));

    // return replacedHtml;

    let options = {
      // html: STOCK_TRANSFER_ORDER_AMOUNTS_HTML,
      html: replacedHtml,
      fileName: `stock-transfer-do-${item && item.stId ? moment(item.createdAt).format('YYYY-MM-DD') + '-' + item.stId : item.stId}-${moment().valueOf()}`,
      // directory: Platform.OS === 'ios'
      //   ? RNFS.DocumentDirectoryPath
      //   : RNFS.DownloadDirectoryPath,
      directory: Platform.OS === 'ios'
        ? 'Documents'
        : 'Download',
    };

    let file = await RNHTMLtoPDF.convert(options);

    if (file && file.filePath) {
      Alert.alert(
        'Success',
        `Exported to ${file.filePath}`,
      );
    }
  };

  const renderStockItem = ({ item }) => (

    item.items.map(result => {
      return (
        <View
          style={{
            backgroundColor: '#FFFFFF',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingHorizontal: 20,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#C4C4C4',
          }}>
          <Text style={{ width: '13%', color: Colors.primaryColor }}>{result.name}</Text>
          <Text style={{ width: '12%' }}>{item.type}</Text>
          <Text style={{
            width: '6%',
            color: result.itemInventory.quantity < result.itemInventory.ideal_qty ? '#F51B1B' : Colors.blackColor
          }}>{result.itemInventory.quantity}</Text>
          <View style={{
            width: '12%',
            alignItems: "center",
          }}>
            <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput2}
              placeholder={result.itemInventory.ideal_qty.toString()}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              onChangeText={(text) => {
                setState({ ideal: text.trim(), itemId: result.itemInventory.itemId });
              }}
              value={email}
            />
          </View>
          <View style={{
            width: '12%',
            alignItems: "center",
            marginLeft: 30,
          }}>
            <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput2}
              placeholder={result.itemInventory.minimum_qty.toString()}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              onChangeText={(text) => {
                setState({ minimum: text.trim(), itemId: result.itemInventory.itemId });
              }}
              value={email}
            />
          </View>
          <Text style={{ width: '10%', marginLeft: 40 }}>{result.itemInventory.unit === null ? 0 : result.itemInventory.unit}</Text>
          <Text style={{ width: '11%' }}>15/10/2020</Text>
          <View style={{ width: "15%" }}>

            {result.supplyProducts.map(elements => {
              return (
                <Text>{elements === null ? " " : elements.supplier.name}</Text>
              )
            })}
          </View>
        </View>


      )
    }));

  const renderStockTransferItem = ({ item }) => (
    // item.stockTransferProducts.map((elements, index) => {
    //   return (
    //     <View
    //       style={{
    //         backgroundColor: '#ffffff',
    //         flexDirection: 'row',
    //         paddingVertical: 20,
    //         paddingHorizontal: 20,
    //         borderBottomWidth: StyleSheet.hairlineWidth,
    //         borderBottomColor: '#c4c4c4',
    //       }}>


    //       <Text style={{ width: '20%', color: Colors.primaryColor }}>ST{elements.stId}</Text>
    //       <Text style={{ width: '20%' }}>{moment(item.createdAt).format('DD/MM/YYYY')}</Text>
    //       <Text style={{ width: '20%' }}>
    //         {item.fromOutletId == 1 ? "MyBurgerlab (Seapark)" : item.fromOutletId == 2 ? "MyBurgerlab (OUG)" : "MyBurgerlab (Mytown)"}
    //       </Text>
    //       <Text style={{ width: '20%' }}>{item.toOutletId == 1 ? "MyBurgerlab (Seapark)" : item.toOutletId == 2 ? "MyBurgerlab (OUG)" : "MyBurgerlab (Mytown)"}</Text>
    //       <View style={{
    //         width: '20%',
    //         alignItems: "center",
    //         backgroundColor: item.status == 0 ? '#dedede' : item.status == 1 ? Colors.secondaryColor : Colors.primaryColor,
    //         borderRadius: 10,
    //         padding: 10,
    //         marginLeft: 8
    //       }}>
    //         <Text style={{
    //           color: item.status == 0 ? Colors.blackColor : item.status == 1 ? Colors.blackColor : Colors.whiteColor,
    //         }}>{item.status == 0 ? "Created" : item.status == 1 ? "Shipped" : "Completed"}</Text>
    //       </View>
    //     </View>
    //   )
    // })

    (<TouchableOpacity
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 15,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
        alignItems: 'center',
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
      }} onPress={() => {
        CommonStore.update(s => {
          s.selectedStockTransferEdit = item;
        });

        setStockTransfer(false);
        setAddStockTransfer(true);

        logEventAnalytics({
          eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_ITEM,
          eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_ITEM,
        })
      }}>
      <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '18%', color: Colors.primaryColor, marginRight: 2 }}>
        ST{item.stId}
      </Text>
      <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '12%', marginHorizontal: 0 }}>
        {moment(item.createdAt).format('DD MMM YYYY')}
      </Text>
      <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '20%', marginHorizontal: 0 }}>
        {item.sourceOutletName}
      </Text>
      <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '20%', marginHorizontal: 0 }}>
        {item.targetOutletName}
      </Text>
      <View style={{
        width: '15%',
        marginLeft: 2,
      }}>
        <View style={{
          width: switchMerchant ? 90 : 130,
          alignItems: "center",
          borderRadius: 10,
          padding: 10,
          backgroundColor: item.status == 0 ? '#dedede' : item.status == 1 ? Colors.secondaryColor : Colors.primaryColor,
        }}>
          <Text style={{
            color: item.status == 0 ? Colors.blackColor : item.status == 1 ? Colors.blackColor : Colors.whiteColor,
            fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular',
          }}>
            {/* {item.status == 0 ? "Created" : item.status == 1 ? "Shipped" : "Completed"} */}
            {STOCK_TRANSFER_STATUS_PARSED[item.status]}
            {/* {selectedStockTransferEdit.status} */}
          </Text>
        </View>
      </View>
      <View style={{
        width: '8%',
        marginLeft: 2,
      }}>
        <TouchableOpacity
          style={{
            width: switchMerchant ? 90 : 110,

            justifyContent: 'center',
            flexDirection: 'row',
            borderWidth: 1,
            borderColor: Colors.primaryColor,
            backgroundColor: '#0F1A3C',
            borderRadius: 5,
            //width: 160,
            paddingHorizontal: 10,
            height: switchMerchant ? 35 : 40,
            alignItems: 'center',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 1,
            zIndex: -1,
          }}
          onPress={() => {
            // setExportModal(true)

            exportHtmlToPdf(item);

            // setDoItem(item);
            // setDoModal(true);

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_ITEM_C_EXPORT,
              eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_ITEM_C_EXPORT,
            })
          }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {/* <Icon name="download" size={switchMerchant ? 10 : 20} color={Colors.whiteColor} /> */}
            <Text style={{
              color: Colors.whiteColor,
              // marginLeft: 5,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: 'NunitoSans-Bold',
            }}>
              EXPORT
            </Text>
          </View>
        </TouchableOpacity>

        {/* <View style={{
          width: switchMerchant ? 90 : 130,
          alignItems: "center",
          borderRadius: 10,
          padding: 10,
          backgroundColor: item.status == 0 ? '#dedede' : item.status == 1 ? Colors.secondaryColor : Colors.primaryColor,
        }}>
          <Text style={{
            color: item.status == 0 ? Colors.blackColor : item.status == 1 ? Colors.blackColor : Colors.whiteColor,
            fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular',
          }}>
            {'EXPORT'}
          </Text>
        </View> */}
      </View>
    </TouchableOpacity>)
  );

  const renderStockTakeItem = ({ item }) => (
    <View
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
      }}>

      <View style={{ width: "14%", flexDirection: 'row' }}>
        <Text>{moment(item.createdAt).format('DD/MM/YYYY')}</Text>
        <Text style={{ color: '#969696', marginLeft: 3 }}>{moment(item.createdAt).format('hh:mm')}</Text>
      </View>
      <View style={{ width: "14%", flexDirection: 'row' }}>
        <Text>{moment(item.updatedAt).format('DD/MM/YYYY')}</Text>
        <Text style={{ color: '#969696', marginLeft: 3 }}>{moment(item.updatedAt).format('hh:mm')}</Text>
      </View>
      <Text style={{ width: '25%' }}>

      </Text>
      <Text style={{ width: '18%' }}>{item.toOutletId == 1 ? "MyBurgerlab (Seapark)" : item.toOutletId == 2 ? "MyBurgerlab (OUG)" : "MyBurgerlab (Mytown)"}</Text>
      <Text style={{ width: '15%' }}>{item.fromOutletId == 1 ? "MyBurgerlab (Seapark)" : item.fromOutletId == 2 ? "MyBurgerlab (OUG)" : "MyBurgerlab (Mytown)"}</Text>
      <Text style={{ width: '1%' }}></Text>
      <View style={{
        width: '14%',
        alignItems: "center",
        backgroundColor: item.status ? Colors.primaryColor : '#fc0000',
        borderRadius: 10, padding: 10
      }}>
        <Text style={{ color: item.status ? Colors.whiteColor : Colors.whiteColor }}>{item.status == 0 ? "Cancelled" : "Completed"}</Text>
      </View>
    </View>

  );

  const renderOrderItem = ({ item }) => (
    item.stockOrderProducts.map((elements, index) => {
      return (
        <TouchableOpacity onPress={() => {
          // setState({
          //   lowStockAlert: false,
          //   purchaseOrder: false,
          //   stockTransfer: false,
          //   stockTake: false,
          //   addPurchase: false,
          //   editPurchase: true,
          //   addStockTransfer: false,
          //   addStockTake: false,
          // });
        }}>
          <View
            style={{
              backgroundColor: '#ffffff',
              flexDirection: 'row',
              paddingVertical: 20,
              paddingHorizontal: 20,
              borderBottomWidth: StyleSheet.hairlineWidth,
              borderBottomColor: '#c4c4c4',
            }}>
            <Text style={{ width: '11%', color: Colors.primaryColor }}>PO{item.id}</Text>
            <Text style={{ width: '13%' }}>{moment(item.createdAt).format('DD/MM/YYYY')}</Text>
            <Text style={{ width: '15%' }}>
              {moment(item.eta).format('DD/MM/YYYY')}
            </Text>
            <Text style={{ width: '15%' }}>{item.outlet.name}</Text>
            <Text style={{ width: '17%' }}>{item.supplier === null ? "" : item.supplier.name}</Text>
            <Text style={{ width: '15%' }}>RM{elements.amount}</Text>
            <View style={{
              width: '12%',
              alignItems: "center",
              backgroundColor: item.status == 0 ? '#dedede' : item.status == 1 ? '#969696' : item.status == 2 ? Colors.secondaryColor : Colors.primaryColor,
              borderRadius: 10
            }}>
              <Text style={{
                color: item.status == 0 ? Colors.blackColor : item.status == 1 ? Colors.whiteColor : item.status == 2 ? Colors.blackColor : Colors.whiteColor,
                borderRadius: 10
              }}>
                {item.status == 0 ? "Fail" : item.status == 1 ? "In Progress" : item.status == 2 ? "Arrived" : "Completed"}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      )
    })
  );

  const renderItemsToOrder = ({ item }) => (

    <View
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
      }}>

      <View style={{ width: "8%" }}>
        <CheckBox
          value={isSelected}
          onValueChange={(isSelected) => setState({ isSelected })} />
      </View>
      <Text style={{ width: '14%', color: '#8f8f8f' }}>Chicken patty</Text>
      <Text style={{ width: '16%', color: '#8f8f8f' }}>
        meat
      </Text>
      <Text style={{ width: '14%', color: '#8f8f8f' }}>50</Text>
      <Text style={{ width: '16%', color: '#8f8f8f' }}>50</Text>
      <Text style={{ width: '18%', color: '#8f8f8f' }}>RM6.00</Text>
      <Text style={{ width: '16%', color: '#8f8f8f' }}>RM300.00</Text>
    </View>

  );

  const renderAddPO = ({ item }) => (

    <View
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
      }}>


      <View style={{ width: 120, color: '#969696' }}>
        <DropDownPicker
          items={[
            {
              label: 'Chicken patty',
              value: 'Chicken patty',
            },
            {
              label: 'Beef patty',
              value: 'Beef patty',
            },
            {
              label: 'lamb chop',
              value: 'lamb chop',
            },

          ]}
          defaultValue={''}
          placeholder=""
          containerStyle={{ height: 30 }}
          style={{ backgroundColor: '#FAFAFA' }}
          itemStyle={{
            justifyContent: 'flex-start',
          }}
          dropDownStyle={{ backgroundColor: '#FAFAFA' }}
          onChangeItem={(item) =>
            setState({
              choice4: item.value,
            })
          }
        />
      </View>
      <Text style={{ width: '2%' }}></Text>
      <Text style={{ width: '16%', color: '#8f8f8f' }}>
        meat
      </Text>
      <Text style={{ width: '6%', color: '#8f8f8f' }}>
        13
      </Text>
      <View style={{
        width: '12%',
        alignItems: "center",
        backgroundColor: '#f5f5f5',
        borderRadius: 10,
        padding: 10,
      }}>
        <Text style={{ color: '#8f8f8f' }}>50</Text>
      </View>
      <Text style={{ width: '14%', marginLeft: 50, color: '#8f8f8f' }}>50</Text>
      <Text style={{ width: '18%', color: '#8f8f8f' }}>RM6.00</Text>
      <Text style={{ width: '10%', color: '#8f8f8f' }}>RM300.00</Text>
      <TouchableOpacity style={{ marginLeft: 10 }}
        onPress={() => { }}>
        <Icon name="trash-2" size={20} color="#eb3446" />
      </TouchableOpacity>
    </View>

  );

  const renderAddStock = ({ item, index }) => {

    return (
      (<View
        style={{
          backgroundColor: '#ffffff',
          flexDirection: 'row',
          paddingVertical: 20,
          paddingHorizontal: 10,
          //paddingBottom: 100,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: '#c4c4c4',
          alignItems: 'center',
        }}>
        <View style={{ width: '29%' }}>
          <View style={{
            width: '85%',
            height: 35,
            borderColor: '#E5E5E5',
            borderWidth: 1,
            borderRadius: 5,
            backgroundColor: 'white',
            //alignItems: 'center',
            justifyContent: 'center',
            paddingLeft: 10,
            shadowOpacity: 0,
            shadowColor: '#000',
            shadowOffset: {
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 2,
          }}>
            {outletSupplyItemDropdownList.find(dropdownItem => dropdownItem.value === poItems[index].outletSupplyItemId) ?
              //  <DropDownPicker
              //    items={outletSupplyItemDropdownList}
              //    defaultValue={poItems[index].outletSupplyItemId}
              //    placeholder="Outlet Supply Items"
              //    containerStyle={{ height: 30 }}
              //    style={{ backgroundColor: '#FAFAFA' }}
              //    itemStyle={{
              //    justifyContent: 'flex-start',
              //    }}
              //    dropDownStyle={{ backgroundColor: '#FAFAFA' }}
              //    onChangeItem={(item) =>
              //      setPoItems(poItems.map((poItem, i) => (i === index ? {
              //        ...poItem,
              //        outletSupplyItemId: item.value,
              //        name: allOutletsSupplyItemsDict[item.value].name,
              //        sku: allOutletsSupplyItemsDict[item.value].sku,
              //        skuMerchant: allOutletsSupplyItemsDict[item.value].skuMerchant,
              //        quantity: allOutletsSupplyItemsDict[item.value].quantity,
              //        transferQuantity: 0,
              //        price: allOutletsSupplyItemsDict[item.value].price,
              //        totalPrice: 0,
              //      } : poItem)))
              //    }
              //    searchable={true}
              //  />

              <RNPickerSelect
                placeholder={{}}
                disabled={(selectedStockTransferEdit && (selectedStockTransferEdit.status === STOCK_TRANSFER_STATUS.COMPLETED || selectedStockTransferEdit.status === STOCK_TRANSFER_STATUS.CREATED))}
                useNativeAndroidPickerStyle={false}
                //pickerProps={{ style: { height: 160, overflow: 'hidden',} }}
                style={{
                  inputIOS: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, paddingVertical: 5 },
                  inputAndroid: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, paddingVertical: 5 },
                  inputAndroidContainer: {
                    //backgroundColor: 'red',
                    width: '100%',
                  }
                }}
                contentContainerStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                items={outletSupplyItemDropdownList}
                value={poItems[index].outletSupplyItemId}
                onValueChange={(value) => {
                  // const outletSupplyItemSku = allOutletsSupplyItemsDict[value].sku;

                  // const supplyItem = supplyItemsSkuDict[outletSupplyItemSku] ? supplyItemsSkuDict[outletSupplyItemSku] : null;

                  const supplyItem = allOutletsItems.find(findItem => findItem.uniqueId === value);

                  setPoItems(poItems.map((poItem, i) => (i === index ? {
                    ...poItem,
                    outletSupplyItemId: value,
                    name: supplyItem.name,
                    sku: supplyItem.sku,
                    unit: '',
                    quantity: supplyItem.stockCount || 0,
                    transferQuantity: 0,
                    price: supplyItem.price,
                    totalPrice: 0,

                    supplyItem: supplyItem,
                  } : poItem)));

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_DD_PRODUCT_NAME,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_DD_PRODUCT_NAME,
                  })
                }}
              />
              :
              <Text style={{ width: '100%', color: "#949494", marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                {poItems[index].name || '-'}
                {/* {poItems[index].skuMerchant || poItems[index].sku ? poItems[index].sku : poItems[index].supplyItem.skuMerchant || poItems[index].supplyItem.skuMerchant ? poItems[index].supplyItem.skuMerchant : poItems[index].sku } */}
              </Text>
            }
          </View>
        </View>
        <Text style={{ width: '14%', color: "#949494", marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
          {poItems[index].skuMerchant || '-'}
          {/* {poItems[index].skuMerchant || poItems[index].sku ? poItems[index].sku : poItems[index].supplyItem.skuMerchant || poItems[index].supplyItem.skuMerchant ? poItems[index].supplyItem.skuMerchant : poItems[index].sku } */}
        </Text>
        {/* <Text style={{ width: '9%', color: "#949494", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
          {poItems[index].unit}
        </Text> */}
        <Text style={{ width: '9%', color: "#949494", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
          {poItems[index].quantity}
        </Text>
        <View style={{ width: '11%', }}>
          {(
            (!selectedStockTransferEdit)
            // || (selectedStockTransferEdit && selectedStockTransferEdit.status !== STOCK_TRANSFER_STATUS.COMPLETED)
          )
            ?
            <TextInput
              editable={true}
              underlineColorAndroid={Colors.fieldtBgColor}
              style={{
                backgroundColor: Colors.fieldtBgColor,
                width: switchMerchant ? 55 : 80,
                height: 35,
                borderRadius: 5,
                padding: 5,
                borderWidth: 1,
                borderColor: '#E5E5E5',
                paddingLeft: 10,
                fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
              }}
              placeholder={'0'}
              placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              keyboardType={'decimal-pad'}
              onChangeText={(text) => {
                // setState({ itemName: text });
                setPoItems(poItems.map((poItem, i) => (i === index ? {
                  ...poItem,
                  transferQuantity: text.length > 0 ? (parseInt(text) >= poItem.quantity ? Alert.alert('Quantity is more than Stock') : parseInt(text)) : 0, // will cause bugs, some outlet item usage consume 0.01, 0.05, etc
                  //balance: poItem.quantity - parseInt(text),
                  balance: parseInt(text) < poItem.quantity ? poItem.quantity - parseInt(text) : 0,
                  totalPrice: parseInt(text) * poItem.price,
                } : poItem)))

                logEventAnalytics({
                  eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_NEW_STOCK_TO_TRANSFER_TB_TRANSFER_QTY,
                  eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_NEW_STOCK_TO_TRANSFER_TB_TRANSFER_QTY,
                })
              }}
              value={poItems[index].transferQuantity ? poItems[index].transferQuantity.toFixed(0) : '0'}
            />
            :
            <TextInput
              editable={false}
              underlineColorAndroid={Colors.fieldtBgColor}
              style={{
                backgroundColor: Colors.fieldtBgColor,
                width: switchMerchant ? 55 : 80,
                height: 35,
                borderRadius: 5,
                padding: 5,
                borderWidth: 1,
                borderColor: '#E5E5E5',
                paddingLeft: 10,
                fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
              }}
              placeholder={'0'}
              placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              keyboardType={'decimal-pad'}
              onChangeText={(text) => {
                // setState({ itemName: text });
                setPoItems(poItems.map((poItem, i) => (i === index ? {
                  ...poItem,
                  transferQuantity: text.length > 0 ? (parseInt(text) >= poItem.quantity ? poItem.quantity : parseInt(text)) : 0, // will cause bugs, some outlet item usage consume 0.01, 0.05, etc
                  balance: poItem.quantity - parseInt(text),
                  totalPrice: parseInt(text) * poItem.price,
                } : poItem)))

                logEventAnalytics({
                  eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_EDIT_STOCK_TO_TRANSFER_TB_TRANSFER_QTY,
                  eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_EDIT_STOCK_TO_TRANSFER_TB_TRANSFER_QTY,
                })
              }}
              value={poItems[index].transferQuantity.toFixed(0)}
            />
          }
        </View>
        <Text style={{ width: '13%', color: "#949494", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
          {/* {allOutletsSupplyItemsDict[poItems[index].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItems[index].outletSupplyItemId].quantity : 0} */}
          {/* {outletSupplyItemsSkuDict[poItems[index].sku] ? outletSupplyItemsSkuDict[poItems[index].sku].quantity : 0} */}
          {allOutletsItems.find(item => item.uniqueId === poItems[index].outletSupplyItemId) ? (allOutletsItems.find(item => item.uniqueId === poItems[index].outletSupplyItemId).stockCount || 0) : 0}
        </Text>
        <Text style={{ width: '10%', color: "#949494", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
          {poItems[index].price}
        </Text>
        <Text style={{ width: '11%', color: "#949494", fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
          {poItems[index].totalPrice}
        </Text>
        <TouchableOpacity
          style={{ marginRight: 20, left: Platform.OS === 'ios' ? 0 : '-10%' }}
          onPress={() => {
            setPoItems([
              ...poItems.slice(0, index),
              ...poItems.slice(index + 1),
            ]);

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_DELETE,
              eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_DELETE,
            })
          }}>
          <Icon name="trash-2" size={switchMerchant ? 15 : 20} color="#eb3446" />
        </TouchableOpacity>
      </View>)
    );
  };

  const renderAddCountedStockTake = ({ item }) => (

    <View
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
        backgroundColor: "#ffe8e8"
      }}>


      <Text style={{ width: '12%', color: "#949494" }}>100 plus</Text>
      <Text style={{ width: '12%', color: "#949494" }}>soda</Text>
      <Text style={{ width: '12%', color: "#949494" }}>RM2</Text>
      <Text style={{ width: '12%', color: "#949494" }}>50</Text>
      <View style={{ width: '11%', backgroundColor: Colors.whiteColor, borderRadius: 10, padding: 10 }}>
        <Text style={{ color: "#e02424" }}>30</Text>
      </View>
      <Text style={{ width: '12%', marginLeft: 20, color: "#949494" }}>-20</Text>
      <Text style={{ width: '12%', color: "#949494" }}>-RM40</Text>
      <TouchableOpacity style={{ width: '12%', flexDirection: "row" }}>
        <Icon2 name="redo" size={25} color={Colors.primaryColor} />
        <Text style={{ marginLeft: 8, color: Colors.primaryColor, fontSize: 16 }}>undo</Text>
      </TouchableOpacity>

    </View>
  );

  const renderAddUncountedStockTake = ({ item }) => (

    <View
      style={{
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#c4c4c4',
      }}>


      <Text style={{ width: '16%', color: "#949494" }}>Mustard</Text>
      <Text style={{ width: '16%', color: "#949494" }}>sauce</Text>
      <Text style={{ width: '16%', color: "#949494" }}>12345</Text>
      <Text style={{ width: '16%', color: "#949494" }}>0</Text>
      <View style={{ width: '10%', color: "#949494", padding: 8, backgroundColor: "#f5f5f5", borderRadius: 10 }}>
        <Text >0</Text>
      </View>
      <View style={{ width: '16%', marginLeft: 80 }}>
        <TouchableOpacity style={{ flexDirection: "row" }} onPress={() => { setState({ modal: true }); }}>
          <CheckBox
            value={isSelected2}
            onValueChange={(isSelected2) => setState({ isSelected2 })} />
          <Text style={{ marginLeft: 8, color: Colors.primaryColor, fontSize: 16 }}>Confirmed</Text>
        </TouchableOpacity>
      </View>

    </View>
  );

  const email = () => {
    var body = {
      stockTransferId: 1,
      email: Email
    }
    // ApiClient.POST(API.emailStockTransfer, body, false).then((result) => {

    //   if (result == true) {

    //     Alert.alert(
    //       'Success',
    //       'The email had sent',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  }


  const CheckStockOrder = () => {
    var body = {
      stockOrderId: 1,
      counted: [{
        id: 1,
        soId: 2,
        itemId: 4,
        name: 'food',
        quantity: -118,
        amount: 2
      }]
    }
    // ApiClient.POST(API.checkStockOrder, body, false).then((result) => {

    //   if (result !== null) {

    //     Alert.alert(
    //       'Success',
    //       'The quantity keeped',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  }

  const CheckStockOrderDelete = () => {
    var body = {
      stockOrderId: 1,
      counted: [{
        id: 1,
        soId: 2,
        itemId: 4,
        name: 'food',
        quantity: -118,
        amount: 2
      }]
    }
    // ApiClient.POST(API.checkStockOrderDelete, body, false).then((result) => {

    //   if (result !== null) {

    //     Alert.alert(
    //       'Success',
    //       'the quantity reduce to 0',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  }

  const createStockOrder = () => {
    var body = {
      supplierId: choice7,
      outletId: choice8,
      eta: moment(date).format('YYYY-MM-DD' + " " + 'hh:mm:ss'),
      items: [{
        id: 5,
        sku: "papaya",
        quantity: 50
      },
      {
        id: 6,
        sku: "mango",
        quantity: 20
      }
      ],
      remarks: 'Order Stock',
    }
    // ApiClient.POST(API.createStockOrder, body).then((result) => {
    //   if (result !== null) {

    //     Alert.alert(
    //       'Success',
    //       'The order has created',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  }

  const createStockTransfer = (stockTransferStatus = STOCK_TRANSFER_STATUS.CREATED) => {
    // if (poItems.find(poItem => poItem.outletSupplyItemId)){
    //   for(var i = 0; i < poItems.length; i++){
    //     for(var j = 0; j < poItems.length; j++){
    //       if(poItems[i].outletSupplyItemId === poItems[j].outletSupplyItemId) {
    //         Alert.alert(
    //           'Error',
    //           'Same Supply Item.',
    //           [
    //             {
    //               text: "OK", onPress: () => {
    //               }
    //             }
    //           ],
    //         );
    //         return;
    //       }
    //     }
    //   }
    // }

    if (selectedStockTransferEdit === null) {
      var body = {
        stId: poId,
        stItems: poItems,
        tax: +(parseFloat(taxTotal).toFixed(2)),
        discount: +(parseFloat(discountTotal).toFixed(2)),
        totalPrice: +(parseFloat(subtotal).toFixed(2)),
        finalTotal: +(parseFloat(finalTotal).toFixed(2)),
        estimatedArrivalDate: moment(date).valueOf(),

        // status: STOCK_TRANSFER_STATUS.CREATED,
        status: stockTransferStatus,

        sourceOutletId: selectedSourceOutletId,
        targetOutletId: selectedTargetOutletId,
        sourceOutletName: allOutlets.find(outlet => outlet.uniqueId === selectedSourceOutletId).name,
        targetOutletName: allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId).name,

        merchantId: merchantId,
        remarks: '',

        staffName: userName,
        staffId: userId,
      };

      // console.log(body);

      APILocal.createStockTransferProduct({ body: body }).then((result) => {
        // ApiClient.POST(API.createStockTransferProduct, body).then((result) => {
        // console.log("Result", result)
        if (result && result.status === 'success') {

          Alert.alert(
            'Success',
            'Stock transfer has been created',
            [
              {
                text: "OK", onPress: () => {
                  // setState({ addStockTransfer: false, stockTransfer: true })
                  setAddStockTransfer(false);
                  setStockTransfer(true);

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE_CREATE_SUCCESS_ALERT,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE_CREATE_SUCCESS_ALERT,
                  })
                }
              }
            ],
            { cancelable: false },
          );
        }
        else {
          Alert.alert(
            'Error',
            'Failed to create stock transfer',
            [
              { text: "OK", onPress: () => { } }
            ],
            { cancelable: false },
          );
        }
      });
    }
    else {
      var body = {
        stId: poId,
        stItems: poItems,
        tax: +(parseFloat(taxTotal).toFixed(2)),
        discount: +(parseFloat(discountTotal).toFixed(2)),
        totalPrice: +(parseFloat(subtotal).toFixed(2)),
        finalTotal: +(parseFloat(finalTotal).toFixed(2)),
        estimatedArrivalDate: moment(date).valueOf(),

        // status: STOCK_TRANSFER_STATUS.CREATED,
        status: stockTransferStatus,

        sourceOutletId: selectedSourceOutletId,
        targetOutletId: selectedTargetOutletId,
        sourceOutletName: allOutlets.find(outlet => outlet.uniqueId === selectedSourceOutletId).name,
        targetOutletName: allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId).name,

        merchantId: merchantId,
        remarks: '',

        staffName: userName,
        staffId: userId,

        uniqueId: selectedStockTransferEdit.uniqueId,
      };

      // console.log(body);

      APILocal.updateStockTransferProduct({ body: body }).then((result) => {
        // ApiClient.POST(API.updateStockTransferProduct, body).then((result) => {
        // console.log("Result", result)
        if (result && result.status === 'success') {

          Alert.alert(
            'Success',
            'Stock transfer has been updated',
            [
              {
                text: "OK", onPress: () => {
                  // setState({ addStockTransfer: false, stockTransfer: true })
                  setAddStockTransfer(false);
                  setStockTransfer(true);

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE_UPDATE_SUCCESS_ALERT,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE_UPDATE_SUCCESS_ALERT,
                  })
                }
              }
            ],
            { cancelable: false },
          );
        }
        else {
          Alert.alert(
            'Error',
            'Failed to update stock transfer',
            [
              { text: "OK", onPress: () => { } }
            ],
            { cancelable: false },
          );
        }
      });
    }
  }

  const editStockOrder = () => {
    var body = {
      productId: 2,
      supplierId: 3,
      remarks: "meat",
      eta: "10-1-2020 01:46:22",
      itemId: 11,
      name: 'chicken',
      quantity: 100
    }
    ApiClient.POST(API.editStockOrder, body).then((result) => {

      if (result.success !== null) {

        Alert.alert(
          'Success',
          'Stock Order has been edited',
          [
            { text: "OK", onPress: () => { } }
          ],
          { cancelable: false },
        );
      }
    });
  }

  const editStockTransfer = () => {
    var body = {
      stProduct: 1,
      toOutlet: 2,
      email: Email,
      itemId: 61,
      quantity: 120,
      remarks: 'Stock Transfer',
    }
    // ApiClient.POST(API.editStockTransfer, body).then((result) => {

    //   if (result.success !== null) {

    //     Alert.alert(
    //       'Success',
    //       'The Stock Transfer has edited',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  }



  return (
    (<UserIdleWrapper disabled={!isMounted}>
      <View style={[styles.container, !isTablet() ? {
        transform: [
          { scaleX: 1 },
          { scaleY: 1 },
        ],
      } : {},
      {
        ...getTransformForScreenInsideNavigation(),
      }]}>
        {/* <View style={[styles.sidebar, !isTablet() ? {
          width: Dimensions.get('screen').width * 0.08,
        } : {}, switchMerchant ? {
          // width: '10%'
        } : {},
        {
          width: windowWidth * 0.08,
        }]}>
          <SideBar navigation={props.navigation} selectedTab={3} expandInventory={true} />
        </View> */}
        <ScrollView
          scrollEnabled={switchMerchant}
          horizontal={true}
        // contentContainerStyle={{backgroundColor: 'red'}}
        // style={{ backgroundColor: 'red'}}
        >
          <View style={[styles.content, {
            padding: 16,
            width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
          }]}>

            <ModalView
              supportedOrientations={['portrait', 'landscape']}
              style={{
                // flex: 1
              }}
              visible={doModal}
              transparent={true}
              animationType={'fade'}
            >
              <View style={{
                flex: 1,
                backgroundColor: Colors.modalBgColor,
                alignItems: 'center',
                justifyContent: 'center',

                top:
                  Platform.OS === 'android'
                    ? 0
                    : keyboardHeight > 0
                      ? -keyboardHeight * 0.45
                      : 0,
              }}>
                <View style={{
                  height: Dimensions.get('screen').width * 0.3,
                  width: Dimensions.get('screen').width * 0.4,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 12,
                  padding: Dimensions.get('screen').width * 0.03,
                  alignItems: 'center',
                  justifyContent: 'center',

                  // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                  ...getTransformForModalInsideNavigation(),
                }}>
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      position: 'absolute',
                      right: Dimensions.get('screen').width * 0.02,
                      top: Dimensions.get('screen').width * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setDoModal(false);
                    }}>
                    <AntDesign name="closecircle" size={switchMerchant ? 15 : 25} color={Colors.fieldtTxtColor} />
                  </TouchableOpacity>
                  <View style={{
                    alignItems: 'center',
                    top: '20%',
                    position: 'absolute',
                  }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      fontSize: switchMerchant ? 18 : 20,
                    }}>
                      Download Delivery Order
                    </Text>
                  </View>
                  <View style={{ top: switchMerchant ? '14%' : '10%', }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold' }}>Email Address:</Text>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 240 : 370,
                        height: switchMerchant ? 35 : 50,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        fontSize: switchMerchant ? 10 : 16,
                      }}
                      autoCapitalize='none'
                      placeholderStyle={{ padding: 5 }}
                      placeholder="Enter your email"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setExportEmail(text);
                      }}
                      value={exportEmail}
                    />
                    <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginTop: 15 }}>Send As:</Text>


                    <View style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      //top: '10%',
                      flexDirection: 'row',
                      marginTop: 10,
                    }}>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={async () => {
                          if (exportEmail.length > 0) {
                            if (doItem && doItem.uniqueId) {
                              CommonStore.update(s => {
                                s.isLoading = true;
                              });

                              var body = {
                                emailToSent: exportEmail,
                                htmlContent: await exportHtmlToPdf(doItem),
                                emailTitle: `KooDoo Stock Transfer DO - ${doItem && doItem.stId ? moment(doItem.createdAt).format('YYYY-MM-DD') + ' - ' + doItem.stId : doItem.stId}.pdf`,
                                fileName: `stock-transfer-do-${doItem && doItem.stId ? moment(doItem.createdAt).format('YYYY-MM-DD') + '-' + doItem.stId : doItem.stId}.pdf`,
                              };

                              ApiClient.POST(API.sendDeliveryOrder, body).then((result) => {
                                if (result && result.status === 'success') {
                                  Alert.alert('Success', 'Delivery order will be sent to the email address shortly');

                                  CommonStore.update(s => {
                                    s.isLoading = false;
                                  });

                                  setDoModal(false);
                                }
                                else {
                                  Alert.alert(
                                    'Error',
                                    'Failed to send delivery order',
                                    [
                                      { text: "OK", onPress: () => { } }
                                    ],
                                    { cancelable: false },
                                  );

                                  CommonStore.update(s => {
                                    s.isLoading = false;
                                  });

                                  setDoModal(false);
                                }
                              });
                            }
                            else {
                              Alert.alert('Info', 'Invalid stock transfer to send.');
                            }
                          }
                          else {
                            Alert.alert('Info', 'Invalid email address');
                          }
                        }}>
                        {
                          isLoadingExcel
                            ?
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                            :
                            <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                              SEND</Text>

                        }
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </View>
            </ModalView>

            <ModalView
              supportedOrientations={['portrait', 'landscape']}
              style={{
                // flex: 1
              }}
              visible={exportModal}
              transparent={true}
              animationType={'fade'}
            >
              <View style={{
                flex: 1,
                backgroundColor: Colors.modalBgColor,
                alignItems: 'center',
                justifyContent: 'center',

                top:
                  Platform.OS === 'android'
                    ? 0
                    : keyboardHeight > 0
                      ? -keyboardHeight * 0.45
                      : 0,
              }}>
                <View style={{
                  height: Dimensions.get('screen').width * 0.32,
                  width: Dimensions.get('screen').width * 0.4,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 12,
                  padding: Dimensions.get('screen').width * 0.03,
                  alignItems: 'center',
                  justifyContent: 'center',

                  // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                  ...getTransformForModalInsideNavigation(),
                }}>
                  <TouchableOpacity
                    disabled={isLoading}
                    style={{
                      position: 'absolute',
                      right: Dimensions.get('screen').width * 0.02,
                      top: Dimensions.get('screen').width * 0.02,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      setExportModal(false);

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_C_CLOSE,
                      })
                    }}>
                    <AntDesign name="closecircle" size={switchMerchant ? 15 : 25} color={Colors.fieldtTxtColor} />
                  </TouchableOpacity>
                  <View style={{
                    alignItems: 'center',
                    top: 20,
                    position: 'absolute',
                  }}>
                    <Text style={{
                      fontFamily: 'NunitoSans-Bold',
                      textAlign: 'center',
                      fontSize: switchMerchant ? 18 : 20,
                    }}>
                      Download Report
                    </Text>
                  </View>
                  <View style={{ top: switchMerchant ? '14%' : '10%', }}>
                    <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold' }}>Email Address:</Text>
                    <TextInput
                      underlineColorAndroid={Colors.fieldtBgColor}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 240 : 370,
                        height: switchMerchant ? 35 : 50,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        fontSize: switchMerchant ? 10 : 16,
                      }}
                      autoCapitalize='none'
                      placeholderStyle={{ padding: 5 }}
                      placeholder="Enter your email"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      onChangeText={(text) => {
                        setExportEmail(text);

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                        })
                      }}
                      value={exportEmail}
                    />
                    <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginTop: 15 }}>Send As:</Text>


                    <View style={{
                      alignItems: 'center',
                      justifyContent: 'center',
                      //top: '10%',
                      flexDirection: 'row',
                      marginTop: 10,
                    }}>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={() => {
                          if (exportEmail.length > 0) {
                            CommonStore.update(s => {
                              s.isLoading = true;
                            });
                            setIsLoadingExcel(true);
                            const excelData = convertDataToExcelFormat();

                            if (excelData && excelData.length > 0) {
                              generateEmailReport(
                                EMAIL_REPORT_TYPE.EXCEL,
                                excelData,
                                'KooDoo Stock Transfer Report',
                                'KooDoo Stock Transfer Report.xlsx',
                                `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                exportEmail,
                                'KooDoo Stock Transfer Report',
                                'KooDoo Stock Transfer Report',
                                () => {
                                  CommonStore.update(s => {
                                    s.isLoading = false;
                                  });
                                  setIsLoadingExcel(false);

                                  Alert.alert('Success', 'Report will be sent to the email address shortly');

                                  setExportModal(false);
                                },
                              );
                            }
                            else {
                              Alert.alert('Info', 'Empty data to export.');

                              CommonStore.update(s => {
                                s.isLoading = false;
                              });
                              setIsLoadingExcel(false);
                            }
                          }
                          else {
                            Alert.alert('Info', 'Invalid email address');
                          }

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL,
                          })
                        }}>
                        {
                          isLoadingExcel
                            ?
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                            :
                            <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                              EXCEL</Text>

                        }
                      </TouchableOpacity>

                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          if (exportEmail.length > 0) {
                            CommonStore.update(s => {
                              s.isLoading = true;
                            });
                            setIsLoadingCsv(true);
                            const csvData = convertArrayToCSV(stockTransferList);

                            generateEmailReport(
                              EMAIL_REPORT_TYPE.CSV,
                              csvData,
                              'KooDoo Stock Transfer Report',
                              'KooDoo Stock Transfer Report.csv',
                              `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                              exportEmail,
                              'KooDoo Stock Transfer Report',
                              'KooDoo Stock Transfer Report',
                              () => {
                                CommonStore.update(s => {
                                  s.isLoading = false;
                                });
                                setIsLoadingCsv(false);
                                Alert.alert('Success', 'Report will be sent to the email address shortly');

                                setExportModal(false);
                              },
                            );
                          }
                          else {
                            Alert.alert('Info', 'Invalid email address');
                          }

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_SEND_AS_C_CSV,
                          })
                        }}>
                        {
                          isLoadingCsv
                            ?
                            <ActivityIndicator
                              size={'small'}
                              color={Colors.whiteColor}
                            />
                            :
                            <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                              CSV
                            </Text>
                        }
                      </TouchableOpacity>
                    </View>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        marginTop: 15,
                      }}>
                      Download As:
                    </Text>
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'row',
                        marginTop: 10,
                      }}>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.isLoading = true;
                          });
                          setIsLoadingLocalExcel(true);
                          handleExportExcel();

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL,
                          })
                        }}>
                        {isLoadingLocalExcel ? (
                          <ActivityIndicator
                            size={'small'}
                            color={Colors.whiteColor}
                          />
                        ) : (
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            EXCEL
                          </Text>
                        )}
                      </TouchableOpacity>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: switchMerchant ? 100 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.isLoading = true;
                          });
                          setIsLoadingLocalCsv(true);
                          handleExportCsv();

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV,
                          })
                        }}>
                        {isLoadingLocalCsv ? (
                          <ActivityIndicator
                            size={'small'}
                            color={Colors.whiteColor}
                          />
                        ) : (
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            CSV
                          </Text>
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </View>
            </ModalView>

            <ModalView
              style={{
                // flex: 1
              }}
              supportedOrientations={['portrait', 'landscape']}
              visible={importModal}
              transparent={true}
              animationType={'fade'}
            >
              <View style={styles.modalContainer}>
                <View style={[styles.modalViewImport, {
                  top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                  ...getTransformForModalInsideNavigation(),
                }]}>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => {
                      // setState({ changeTable: false });
                      setImportModal(false);
                    }}>
                    <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                  </TouchableOpacity>
                  <View style={{ padding: 10, margin: 30 }}>
                    <View style={[styles.modalTitle1, { justifyContent: 'center', alignItems: 'center' }]}>
                      <Text style={[styles.modalTitleText1, { fontSize: 16, fontWeight: '500' }]}>
                        Imported List
                      </Text>
                    </View>
                    {/* <View style={{
                    heigth: 70,
                    marginVertical: 10,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    height: '80%'
                  }}>
                  <Table borderStyle={{ borderWidth: 1 }}>
                    <Row data={TableData.tableHead} flexArr={[1, 2, 1, 1]} style={{}}/>
                    <TableWrapper style={{}}>
                    <Col data={TableData.tableTitle} style={{flex: 1}} heightArr={[28, 28, 28, 28]} textStyle={{}}/>
                    <Rows data={TableData.tableData} flexArr={[1, 2, 1, 1]} style={{height: 28}} textStyle={{textAlign: 'center'}}/>
                    </TableWrapper>
                  </Table>
                  </View> */}
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                      <View
                        style={{
                          backgroundColor: Colors.primaryColor,
                          width: 150,
                          height: 40,
                          marginVertical: 15,
                          borderRadius: 5,
                          alignSelf: 'center',
                        }}>
                        <TouchableOpacity onPress={() => { importSelectFile() }}>
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              alignSelf: 'center',
                              marginVertical: 10,
                            }}>
                            IMPORT
                          </Text>
                        </TouchableOpacity>
                      </View>
                      <View style={{ flexDirection: 'row' }}>
                        <View
                          style={{
                            backgroundColor: Colors.whiteColor,
                            width: 150,
                            height: 40,
                            marginVertical: 15,
                            borderRadius: 5,
                            alignSelf: 'center',
                          }}>
                          <TouchableOpacity onPress={() => { setImportTemplate(false) }}>
                            <Text
                              style={{
                                color: Colors.primaryColor,
                                alignSelf: 'center',
                                marginVertical: 10,
                              }}>
                              CANCEL
                            </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            backgroundColor: Colors.primaryColor,
                            width: 150,
                            height: 40,
                            marginVertical: 15,
                            borderRadius: 5,
                            alignSelf: 'center',
                          }}>
                          <TouchableOpacity onPress={() => { }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                alignSelf: 'center',
                                marginVertical: 10,
                              }}>
                              SAVE
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </ModalView>

            <DateTimePickerModal
              isVisible={showDateTimePicker}
              mode={'date'}
              onConfirm={(text) => {
                setRev_date(moment(text).startOf('day'));
                setShowDateTimePicker(false);
              }}
              onCancel={() => {
                setShowDateTimePicker(false);
              }}
              maximumDate={moment(rev_date1).toDate()}
              date={moment(rev_date).toDate()}
            />

            <DateTimePickerModal
              isVisible={showDateTimePicker1}
              mode={'date'}
              onConfirm={(text) => {
                setRev_date1(moment(text).endOf('day'));
                setShowDateTimePicker1(false);
              }}
              onCancel={() => {
                setShowDateTimePicker1(false);
              }}
              minimumDate={moment(rev_date).toDate()}
              date={moment(rev_date1).toDate()}
            />

            {/* <View style={{ flexDirection: 'row', marginBottom: Platform.OS == 'ios' ? 0 : 10, width: '100%', justifyContent: 'space-between' }} >
            <View style={{}}>
              <View style={{ flexDirection: 'row', flex: 1, }}> */}
            {/* <TouchableOpacity style={styles.submitText} onPress={() => { importCSV() }}>
                  <View style={{ flexDirection: 'row' }}>
                    <Icon name="download" size={20} color={Colors.primaryColor} />
                    <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                      Import
                  </Text>
                  </View>
                </TouchableOpacity> */}
            {/* <TouchableOpacity style={[styles.submitText, { height: 40 }]} onPress={() => { setExportModal(true) }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Icon name="download" size={20} color={Colors.primaryColor} />
                    <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                      Download
                    </Text>
                  </View>
                </TouchableOpacity> */}
            {/* <TouchableOpacity style={styles.submitText} onPress={() => { setExportModal(true) }}>
                  <View style={{ flexDirection: 'row' }}>
                    <Icon name="upload" size={20} color={Colors.primaryColor} />
                    <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                      Export
                  </Text>
                  </View>
                </TouchableOpacity> */}
            {/* </View>
            </View> */}
            {/* <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput}
              placeholder="🔍  Search"
              onChangeText={(text) => {
                setState({ search: text.trim() });
              }}
              value={email}
            /> */}

            {/* <Ionicons
              name="search-outline"
              size={20}
              style={styles.searchIcon}
            />
            <TextInput
              editable={loading}
              clearButtonMode="while-editing"
              style={[styles.textInput, { fontFamily: "NunitoSans-Bold", }]}
              placeholder="Search"
              onChangeText={(text) => {
                setState({
                  search: text.trim(),
                });
              }}
              value={email}
            /> */}
            {/* 
            <View
              style={[{
                width: '28%',
                height: 40,

              }, !isTablet() ? {
                marginLeft: 0,
              } : {}]}>
              <View style={{
                width: 250,
                height: 40,
                backgroundColor: 'white',
                borderRadius: 10,
                // marginLeft: '53%',
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',
                alignSelf: 'flex-end',

                // marginRight: Dimensions.get('screen').width * Styles.sideBarWidth,

                position: 'absolute',
                //right: '35%',

                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
              }}>
                <Icon name="search" size={18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />
                <TextInput
                  editable={!loading}
                  underlineColorAndroid={Colors.whiteColor}
                  style={{
                    width: 250,
                    fontSize: 15,
                    fontFamily: 'NunitoSans-Regular',
                  }}
                  clearButtonMode="while-editing"
                  placeholder=" Search"
                  onChangeText={(text) => {
                    setSearch(text.trim());
                  }}
                  value={search}
                />
              </View>
            </View> */}

            {/* </View> */}

            {/*  <View
            style={{
              flexDirection: 'row',
              backgroundColor: Colors.highlightColor,
              padding: 12,
            }}>
            <TouchableOpacity
              onPress={() => {
                setState({
                  lowStockAlert: true,
                  purchaseOrder: false,
                  stockTransfer: false,
                  stockTake: false,
                  addPurchase: false,
                  editPurchase: false,
                  addStockTransfer: false,
                  addStockTake: false,
                });
              }}>
              <Text>Low Stock Alerts</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  lowStockAlert: false,
                  purchaseOrder: true,
                  stockTransfer: false,
                  stockTake: false,
                  addPurchase: false,
                  editPurchase: false,
                  addStockTransfer: false,
                  addStockTake: false,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Purchase Order</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  lowStockAlert: false,
                  purchaseOrder: false,
                  stockTransfer: true,
                  stockTake: false,
                  addPurchase: false,
                  editPurchase: false,
                  addStockTransfer: false,
                  addStockTake: false,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Stock Transfer</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  lowStockAlert: false,
                  purchaseOrder: false,
                  stockTransfer: false,
                  stockTake: true,
                  addPurchase: false,
                  editPurchase: false,
                  addStockTransfer: false,
                  addStockTake: false,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Stock Take</Text>
            </TouchableOpacity>
          </View> */}




            {stockTransfer ? (
              // <ScrollView 
              //   scrollEnabled={switchMerchant}
              //   horizontal={true}
              //   contentContainerStyle={{ paddingRight: switchMerchant ? '7%' : 0, }}
              // >
              (<ScrollView
                nestedScrollEnabled={true}
                contentContainerStyle={{ paddingBottom: switchMerchant ? '20%' : '5%', }}
                //style={{ backgroundColor: 'red' }}
                scrollEnabled={switchMerchant}
              >
                <View style={{
                  height: Platform.OS === 'ios' ? Dimensions.get('window').height - 200 : Dimensions.get('screen').height * 0.73,
                  // width: Dimensions.get('screen').width * 0.87,
                }}>

                  <View style={{ width: Dimensions.get('screen').width * 0.87, alignSelf: 'center' }}>
                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                      <View style={{ alignItems: 'center', flexDirection: 'row', }}>
                        <Text style={{  //fontSize: 30,
                          fontSize: switchMerchant ? 20 : 26,
                          fontFamily: 'NunitoSans-Bold'
                        }}>
                          {stockTransferList.length} Stock Transfer
                        </Text>
                      </View>

                      <View style={{ flexDirection: 'row', }}>
                        <View style={{ flexDirection: 'row', }}>
                          {/* {isTablet() && ( */}
                          <View
                            style={{ alignItem: 'center', }}>
                            <TouchableOpacity
                              style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                backgroundColor: '#0F1A3C',
                                borderRadius: 5,
                                //width: 160,
                                paddingHorizontal: 10,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,
                              }}
                              onPress={() => {
                                setExportModal(true);

                                logEventAnalytics({
                                  eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_DOWNLOAD,
                                  eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_DOWNLOAD,
                                })
                              }}>
                              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                <Icon name="download" size={switchMerchant ? 10 : 20} color={Colors.whiteColor} />
                                <Text style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  DOWNLOAD
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>
                          {/* )} */}
                        </View>

                        <View style={{ flexDirection: 'row', }}>
                          {/* {isTablet() && ( */}
                          <View
                            style={{
                              alignSelf: 'flex-start',
                            }}>

                            <TouchableOpacity
                              style={{
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                backgroundColor: '#0F1A3C',
                                borderRadius: 5,
                                //width: 160,
                                paddingHorizontal: switchMerchant ? 5 : 10,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,
                                marginHorizontal: switchMerchant ? 10 : 15,
                              }}
                              onPress={() => {
                                CommonStore.update(s => {
                                  s.selectedStockTransferEdit = null;
                                });

                                setStockTransfer(false);
                                setAddStockTransfer(true);

                                logEventAnalytics({
                                  eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_STOCK_TRANSFER,
                                  eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_STOCK_TRANSFER,
                                })
                              }}>

                              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                <AntDesign name="pluscircle" size={switchMerchant ? 10 : 20} color={Colors.whiteColor} />
                                <Text style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  STOCK TRANSFER
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>
                          {/* )} */}
                        </View>

                        <View style={{ flexDirection: 'row', }}>
                          <View
                            style={[{
                              height: 40,

                            }, !isTablet() ? {
                              marginLeft: 0,
                            } : {}]}>
                            <View style={{
                              width: Dimensions.get('screen').width <= 1024 ? 160 : 250,
                              height: switchMerchant ? 35 : 40,
                              backgroundColor: 'white',
                              borderRadius: 5,
                              // marginLeft: '53%',
                              flexDirection: 'row',
                              alignContent: 'center',
                              alignItems: 'center',
                              alignSelf: 'flex-end',
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                            }}>
                              <Icon name="search" size={switchMerchant ? 13 : 18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />
                              <TextInput
                                editable={!loading}
                                underlineColorAndroid={Colors.whiteColor}
                                style={{
                                  width: Dimensions.get('screen').width <= 1024 ? 140 : 220,
                                  fontSize: switchMerchant ? 10 : 15,
                                  fontFamily: 'NunitoSans-Regular',
                                  paddingLeft: 5,
                                  height: 45,
                                }}
                                clearButtonMode="while-editing"
                                placeholder="Search"
                                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                onChangeText={(text) => {
                                  // setSearch(text.trim());
                                  // setList1(false);
                                  // setSearchList(true);
                                  // setSearch(text.trim());
                                  setSearch(text);

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_TB_SEARCH,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_TB_SEARCH,
                                  })
                                }}
                                value={search}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                    <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                      <View
                        style={[
                          {
                            //marginRight: Platform.OS === 'ios' ? 0 : 10,
                            // paddingLeft: 15,
                            paddingHorizontal: 15,
                            flexDirection: 'row',
                            alignItems: 'center',
                            borderRadius: 10,
                            paddingVertical: 10,
                            justifyContent: 'center',
                            backgroundColor: Colors.whiteColor,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            marginBottom: 10,
                            marginTop: 20,
                          },
                        ]}>
                        <View
                          style={{ alignSelf: 'center', marginRight: 5 }}
                          onPress={() => {
                            setState({
                              pickerMode: 'date',
                              showDateTimePicker: true,
                            });
                          }}>
                          {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                          <GCalendar
                            width={switchMerchant ? 15 : 20}
                            height={switchMerchant ? 15 : 20}
                          />
                        </View>

                        <TouchableOpacity
                          onPress={() => {
                            setShowDateTimePicker(true);
                            setShowDateTimePicker1(false);

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_START_DATE,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_START_DATE,
                            })
                          }}
                          style={{
                            marginHorizontal: 4,
                          }}>
                          <Text
                            style={
                              switchMerchant
                                ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                : { fontFamily: 'NunitoSans-Regular' }
                            }>
                            {moment(rev_date).format('DD MMM yyyy')}
                          </Text>
                        </TouchableOpacity>

                        <Text
                          style={
                            switchMerchant
                              ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                              : { fontFamily: 'NunitoSans-Regular' }
                          }>
                          -
                        </Text>

                        <TouchableOpacity
                          onPress={() => {
                            setShowDateTimePicker(false);
                            setShowDateTimePicker1(true);

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_C_END_DATE,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_C_END_DATE,
                            })
                          }}
                          style={{
                            marginHorizontal: 4,
                          }}>
                          <Text
                            style={
                              switchMerchant
                                ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                : { fontFamily: 'NunitoSans-Regular' }
                            }>
                            {moment(rev_date1).format('DD MMM yyyy')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>

                    {/* <View
                  style={{
                    backgroundColor: '#ffffff',
                    flexDirection: 'row',
                    paddingVertical: 20,
                    paddingHorizontal: 20,
                    marginTop: 10,
                    borderBottomWidth: StyleSheet.hairlineWidth,
                    elevation: 1,
                    zIndex: 10
                  }}>
                  <Text style={{ width: '10%', alignSelf: 'center', color: '#969696' }}>
                    ID
                    </Text>
                  <Text style={{ width: '14%', alignSelf: 'center', color: '#969696' }}>
                    Created Date
                    </Text>
                  <View style={{ width: '14%', alignSelf: 'center', color: '#969696' }}>
                    <Text style={{ color: '#969696' }}>
                      From
                      </Text>
                    <View style={{ width: 140, }}>
                      <DropDownPicker
                        items={[
                          {
                            label: 'All Stores',
                            value: 'All Stores',
                          },
                          {
                            label: 'MyBurgerlab (SeaPark)',
                            value: 'MyBurgerlab (SeaPark)',
                          },
                          {
                            label: 'MyBurgerlab (OUG)',
                            value: 'MyBurgerlab (OUG)',
                          },
                          {
                            label: 'MyBurgerlab (Mytown)',
                            value: 'MyBurgerlab (Mytown)',
                          },

                        ]}
                        defaultValue={''}
                        placeholder=""
                        containerStyle={{ height: 30 }}
                        style={{ backgroundColor: '#FAFAFA' }}
                        itemStyle={{
                          justifyContent: 'flex-start',
                        }}
                        dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                        onChangeItem={(item) =>
                          setState({
                            choice6: item.value,
                          })
                        }
                      />
                    </View>
                  </View>
                  <View style={{ width: '14%', alignSelf: 'center' }}>
                    <Text style={{ marginLeft: 55, width: 120, color: '#969696' }}>To</Text>
                    <View style={{ marginLeft: 55, width: 120, }}>
                      <DropDownPicker
                        items={[
                          {
                            label: 'All Stores',
                            value: 'All Stores',
                          },
                          {
                            label: 'MyBurgerlab (SeaPark)',
                            value: 'MyBurgerlab (SeaPark)',
                          },
                          {
                            label: 'MyBurgerlab (OUG)',
                            value: 'MyBurgerlab (OUG)',
                          },
                          {
                            label: 'MyBurgerlab (Mytown)',
                            value: 'MyBurgerlab (Mytown)',
                          },

                        ]}
                        defaultValue={''}
                        placeholder=""
                        containerStyle={{ height: 30 }}
                        style={{ backgroundColor: '#FAFAFA' }}
                        itemStyle={{
                          justifyContent: 'flex-start',
                        }}
                        dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                        onChangeItem={(item) =>
                          setState({
                            choice5: item.value,
                          })
                        }
                      />
                    </View>
                  </View>
                  <View style={{ width: '14%', alignSelf: 'center' }}>
                    <Text style={{ marginLeft: 90, width: 120, color: '#969696' }}>Status </Text>
                    <View style={{ marginLeft: 90, width: 120, }}>
                      <DropDownPicker
                        items={[
                          {
                            label: 'All Stores',
                            value: 'All Stores',
                          },
                          {
                            label: 'MyBurgerlab (SeaPark)',
                            value: 'MyBurgerlab (SeaPark)',
                          },
                          {
                            label: 'MyBurgerlab (OUG)',
                            value: 'MyBurgerlab (OUG)',
                          },
                          {
                            label: 'MyBurgerlab (Mytown)',
                            value: 'MyBurgerlab (Mytown)',
                          },

                        ]}
                        defaultValue={''}
                        placeholder=""
                        containerStyle={{ height: 30 }}
                        style={{ backgroundColor: '#FAFAFA' }}
                        itemStyle={{
                          justifyContent: 'flex-start',
                        }}
                        dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                        onChangeItem={(item) =>
                          setState({
                            choice9: item.value,
                          })
                        }
                      />
                    </View>
                  </View>
                  <View style={{ width: '10%', alignSelf: 'center', marginLeft: 130 }}>
                    <Text style={{ color: '#969696' }}>Actions</Text>


                    <TextInput

                      editable={!loading}
                      underlineColorAndroid={Colors.whiteColor}
                      clearButtonMode="while-editing"
                      style={styles.textInput1}
                      placeholder="🔍 Search"
                      onChangeText={(text) => {
                        setState({ search2: text.trim() });
                      }}
                      value={email}
                    />
                  </View>
                </View> */}
                    <View style={{
                      backgroundColor: Colors.whiteColor,
                      width: Dimensions.get('screen').width * 0.87,
                      height: Dimensions.get('screen').height * 0.67,
                      marginTop: 10,
                      marginHorizontal: 30,
                      marginBottom: 30,
                      alignSelf: 'center',
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                    }}>
                      <View
                        style={{
                          borderTopLeftRadius: 10,
                          borderTopRightRadius: 10,
                          backgroundColor: '#ffffff',
                          flexDirection: 'row',
                          paddingVertical: 20,
                          paddingHorizontal: 15,
                          //marginTop: 10,
                          borderBottomWidth: StyleSheet.hairlineWidth,
                        }}>
                        <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '18%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginRight: 2 }}>
                          ID
                        </Text>
                        <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '12%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 0 }}>
                          Created Date
                        </Text>
                        <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '20%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 0 }}>
                          From
                        </Text>
                        <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '20%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 0 }}>
                          To
                        </Text>
                        <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '15%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginLeft: 2, }}>
                          Status
                        </Text>
                        <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '8%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginLeft: 2, }}>
                          Action
                        </Text>
                      </View>

                      <FlatList
                        nestedScrollEnabled={true}
                        showsVerticalScrollIndicator={false}
                        data={stockTransferList.filter(item => {
                          if (search !== '') {
                            const searchLowerCase = search.toLowerCase();

                            return item.sourceOutletName.toLowerCase().includes(searchLowerCase) || item.targetOutletName.toLowerCase().includes(searchLowerCase);
                          }
                          else {
                            return true;
                          }
                        })}
                        extraData={stockTransferList.filter(item => {
                          if (search !== '') {
                            const searchLowerCase = search.toLowerCase();

                            return item.sourceOutletName.toLowerCase().includes(searchLowerCase) || item.targetOutletName.toLowerCase().includes(searchLowerCase);
                          }
                          else {
                            return true;
                          }
                        })}
                        renderItem={renderStockTransferItem}
                        keyExtractor={(item, index) => String(index)}
                      />

                    </View>
                  </View>
                </View>
              </ScrollView>)
              // </ScrollView>

            ) : null}

            {addStockTransfer ? (
              <View style={{ height: Dimensions.get('window').height }}>
                <View
                  style={{
                    paddingBottom: Dimensions.get('screen').height * 0.3,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.32,
                    shadowRadius: 3.22,
                    elevation: 1,
                  }}
                  contentContainerStyle={{
                    // top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                  }}>
                  {/* <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginHorizontal: 0, }}> */}

                  <View style={{
                    width: switchMerchant ? null : Dimensions.get('screen').width * 0.886,
                    alignSelf: switchMerchant ? null : 'center',
                    marginBottom: switchMerchant ? 5 : 0,
                    marginTop: Platform.OS === 'ios' ? 0 : -5,
                    alignItems: 'baseline',
                  }}>
                    <TouchableOpacity
                      style={{
                        marginBottom: switchMerchant ? 0 : 10,
                        flexDirection: 'row', alignItems: 'center',
                        marginTop: switchMerchant ? 0 : 10,
                      }}
                      onPress={() => {
                        CommonStore.update(s => {
                          s.selectedStockTransferEdit = null;
                        });
                        setStockTransfer(true);
                        setAddStockTransfer(false);

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_BACK,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_BACK,
                        })
                      }}>
                      <Icon name="chevron-left" size={switchMerchant ? 20 : 30} color={Colors.primaryColor} />
                      <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 14 : 17, color: Colors.primaryColor, marginBottom: Platform.OS === 'android' ? 2 : 0, }}>Back</Text>
                    </TouchableOpacity>
                  </View>

                  {/* <ScrollView horizontal={true}
                  scrollEnabled={switchMerchant}
                  nestedScrollEnabled={true}
                  contentContainerStyle={{
                    paddingTop: switchMerchant? 30 : 0,
                    paddingRight: switchMerchant? '5%' : 0,
                  }}
                > */}
                  <View style={{
                    backgroundColor: Colors.whiteColor,
                    width: Dimensions.get('screen').width * 0.87,
                    height: Dimensions.get('screen').height * 0.69,
                    //marginTop: 10,
                    marginHorizontal: 30,
                    marginLeft: switchMerchant ? 31 : 29,
                    marginBottom: 30,
                    alignSelf: 'center',
                    //borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    borderRadius: 5,
                  }}>

                    {/* <ViewShot ref={viewShotRef} options={{
                      fileName: `stock-transfer-do-${selectedStockTransferEdit && selectedStockTransferEdit.stId ? moment(selectedStockTransferEdit.createdAt).format('YYYY-MM-DD') + '-' + selectedStockTransferEdit.stId : selectedStockTransferEdit.stId}-${moment().valueOf()}`,
                      format: 'png',
                      quality: 0.9,
                    }}>
                      
                    </ViewShot> */}

                    <KeyboardAwareScrollView
                      showsVerticalScrollIndicator={false}
                      style={{
                        // height: Dimensions.get('window').height * 0.75,
                        // backgroundColor: Colors.whiteColor,
                        // borderWidth: 1,
                        // borderColor: '#E5E5E5',
                        // borderRadius: 10,
                        //  shadowOffset: {
                        //   width: 0,
                        //   height: 2,
                        //   },
                        //   shadowOpacity: 0.42,
                        //   shadowRadius: 3.22,
                        //   elevation: 2,
                      }}>
                      <View style={{}}>

                        <View style={{ position: 'absolute', alignSelf: 'flex-end', marginTop: 30, zIndex: 10000, }}>
                          {(selectedStockTransferEdit &&
                            selectedStockTransferEdit.status === STOCK_TRANSFER_STATUS.CREATED &&
                            currOutletId === selectedStockTransferEdit.targetOutletId) ?
                            <>
                              <View
                                style={{

                                }}>
                                <TouchableOpacity
                                  style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: '#0F1A3C',
                                    borderRadius: 5,
                                    width: switchMerchant ? 160 : 230,
                                    paddingHorizontal: 10,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                    marginRight: 20,
                                    marginBottom: 10,
                                  }}
                                  onPress={() => {
                                    createStockTransfer(STOCK_TRANSFER_STATUS.COMPLETED);

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_EDIT_C_MARK_COMPLETED,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_EDIT_C_MARK_COMPLETED,
                                    })
                                  }}
                                  disabled={selectedStockTransferEdit ? false : true}>
                                  <Text style={{
                                    color: Colors.whiteColor,
                                    //marginLeft: 5,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                    {'MARK COMPLETED'}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </>
                            :
                            <></>}

                          {(selectedStockTransferEdit && selectedStockTransferEdit.status === STOCK_TRANSFER_STATUS.COMPLETED) ?
                            <></>
                            :
                            <View
                              style={{

                              }}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#0F1A3C',
                                  borderRadius: 5,
                                  width: (selectedStockTransferEdit &&
                                    selectedStockTransferEdit.status === STOCK_TRANSFER_STATUS.CREATED &&
                                    currOutletId === selectedStockTransferEdit.targetOutletId && !switchMerchant) ? 230 :
                                    selectedStockTransferEdit &&
                                      selectedStockTransferEdit.status === STOCK_TRANSFER_STATUS.CREATED &&
                                      currOutletId === selectedStockTransferEdit.targetOutletId && switchMerchant ? 160 : 120,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  marginRight: 20,
                                }}
                                onPress={() => {
                                  createStockTransfer();

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_C_SAVE,
                                  })
                                }}>
                                <Text style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  SAVE
                                </Text>
                              </TouchableOpacity>
                            </View>
                          }

                          {/* {(selectedStockTransferEdit && selectedStockTransferEdit.uniqueId) ?
                            <View
                              style={{

                              }}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#0F1A3C',
                                  borderRadius: 5,
                                  width: (selectedStockTransferEdit &&
                                    selectedStockTransferEdit.status === STOCK_TRANSFER_STATUS.CREATED &&
                                    currOutletId === selectedStockTransferEdit.targetOutletId && !switchMerchant) ? 230 :
                                    selectedStockTransferEdit &&
                                      selectedStockTransferEdit.status === STOCK_TRANSFER_STATUS.CREATED &&
                                      currOutletId === selectedStockTransferEdit.targetOutletId && switchMerchant ? 160 : 120,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  marginRight: 20,
                                }}
                                onPress={() => {
                                  if (viewShotRef && viewShotRef.current) {
                                    viewShotRef.current.capture().then(uri => {
                                      console.log('uri');
                                      console.log(uri);

                                      RNFetchBlob.fs.readFile(uri, 'base64').then(async data => {
                                        var filePath = `${Platform.OS === 'ios'
                                          ? RNFS.DocumentDirectoryPath
                                          : RNFS.DownloadDirectoryPath
                                          }/stock-transfer-do-${selectedStockTransferEdit && selectedStockTransferEdit.stId ? moment(selectedStockTransferEdit.createdAt).format('YYYY-MM-DD') + '-' + selectedStockTransferEdit.stId : selectedStockTransferEdit.stId}-${moment().valueOf()}.png`;

                                        RNFS.writeFile(filePath, data, 'base64')
                                          .then(async (success) => {

                                            Alert.alert(
                                              'Success',
                                              `Exported to ${filePath}`,
                                            );
                                          })
                                          .catch((err) => {
                                            // console.log(err.message);

                                            Alert.alert(
                                              'Error',
                                              'Failed to export the stock transfer.',
                                            );
                                          });
                                      });
                                    });
                                  }
                                }}>
                                <Text style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  EXPORT
                                </Text>
                              </TouchableOpacity>
                            </View>
                            :
                            <></>
                          } */}
                        </View>

                        <View style={{ justifyContent: 'center', alignItems: 'center' }}>
                          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                            <Text style={{ alignSelf: "center", marginTop: 30, fontSize: switchMerchant ? 20 : 40, fontWeight: '600' }}>
                              Stock Transfer
                            </Text>
                          </View>
                          <Text style={{ alignSelf: "center", fontSize: switchMerchant ? 10 : 16, color: '#adadad' }}>
                            Fill In The Stock Transfer Information
                          </Text>
                        </View>

                        <View style={{ flexDirection: "row", marginTop: 55, justifyContent: 'space-between', width: '80%', alignSelf: 'center', }}>
                          <View style={{ flexDirection: "row", width: '45%', alignItems: 'center' }}>
                            <Text style={{ width: '42%', fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, fontWeight: '400' }}>
                              ID
                            </Text>
                            <View style={{ width: '58%', justifyContent: 'space-between', flexDirection: 'row', alignItems: 'center' }}>
                              <View style={{ justifyContent: 'center', borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 35, width: '90%', backgroundColor: Colors.fieldtBgColor, paddingHorizontal: 10 }}>
                                {editMode ?
                                  <TextInput
                                    //editable={false}
                                    style={{
                                      // backgroundColor: Colors.fieldtBgColor,
                                      // borderRadius: 5,
                                      // padding: 5,
                                      // borderWidth: 1,
                                      // borderColor: '#E5E5E5',
                                      //paddingLeft:10,
                                      fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                                      backgroundColor: 'red',
                                    }}
                                    placeholder='ID (Max Length 12)'
                                    placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                    onChangeText={(text) => {
                                      setPoId(text);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_EDIT_TB_ID,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_EDIT_TB_ID,
                                      })
                                    }}
                                    maxLength={12}
                                  //value={`ST${poId}`}
                                  />
                                  :
                                  <Text style={{ fontSize: 15, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                                    {`${poId}`}
                                  </Text>
                                }
                              </View>
                              {selectedStockTransferEdit ?
                                <></>
                                :
                                <TouchableOpacity
                                  style={{
                                    marginLeft: 5
                                  }}
                                  onPress={() => {
                                    setEditMode(!editMode);

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_ID_C_EDIT,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_ID_C_EDIT,
                                    })
                                  }}>
                                  {/* <MaterialCommunityIcons name="pencil" size={20} color={Colors.primaryColor} /> */}
                                  <Icon4 name='edit' size={switchMerchant ? 15 : 18} color={Colors.primaryColor} />
                                </TouchableOpacity>
                              }
                            </View>
                          </View>

                          <View style={{ flexDirection: "row", width: '50%', alignItems: 'center' }}>
                            <Text style={{ width: '38%', fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, fontWeight: '400' }}>
                              Source Store
                            </Text>

                            <View style={{ width: '62%', }}>
                              {targetOutletDropdownList.find(item => item.value === selectedSourceOutletId) ?
                                <DropDownPicker
                                  containerStyle={{ height: 35, zIndex: 2 }}
                                  arrowColor={'black'}
                                  arrowSize={20}
                                  arrowStyle={{ fontWeight: 'bold' }}
                                  labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                                  style={{ width: switchMerchant ? 170 : 220, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}
                                  placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  disabled={selectedStockTransferEdit ? true : false}
                                  items={targetOutletDropdownList}
                                  itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2 }}
                                  placeholder={"Product"}
                                  customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                                  onChangeItem={(item) => {
                                    setSelectedSourceOutletIdPrev(selectedSourceOutletId);
                                    setSelectedSourceOutletId(item.value);

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_DD_SOURCE_STORE,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_DD_SOURCE_STORE,
                                    })
                                  }}
                                  defaultValue={selectedSourceOutletId}
                                  dropDownMaxHeight={150}
                                  dropDownStyle={{ width: switchMerchant ? 170 : 220, height: 80, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, textAlign: 'left', zIndex: 2 }}
                                // disabled={selectedStockTransferEdit ? true : false}
                                // items={targetOutletDropdownList}
                                // defaultValue={selectedSourceOutletId}
                                // arrowSize={20}
                                // controller={(instance) => dropDownRef.current = instance}
                                // // placeholder="Source Store"
                                // containerStyle={{ height: 35, width: 220 }}
                                // style={{ backgroundColor: '#FAFAFA', paddingVertical: 0, }}
                                // itemStyle={{
                                //   justifyContent: 'flex-start', marginLeft: 5
                                // }}
                                // dropDownStyle={{ backgroundColor: '#FAFAFA', width: 220 }}
                                // onChangeItem={(item) => {
                                //   setSelectedSourceOutletIdPrev(selectedSourceOutletId);
                                //   setSelectedSourceOutletId(item.value);
                                // }}
                                // onOpen={() => { dropDownRef1.current.close() }}
                                // ref={dropDownRef}
                                />
                                : <></>
                              }
                            </View>
                            {/* <View style={{ width: '62%' }}>
                          <View style={{ width: switchMerchant ? 170 : 220, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}>
                            <Text>
                              {selectedSourceOutletId}
                            </Text>
                          </View>
                        </View> */}
                          </View>
                        </View>
                      </View>

                      <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-between', alignSelf: 'center', marginTop: 50, zIndex: -1, width: '80%' }}>
                        <View style={{ flexDirection: "row", width: '45%', alignItems: 'center' }}>
                          {/* <Text style={{ fontSize: 16, width: '40%', textAlign: 'left' }}>Current status</Text>
                        <View style={{ paddingHorizontal: 18, paddingVertical: 10, alignItems: "center", backgroundColor: '#838387', borderRadius: 10, width: '40%' }}>
                          <Text style={{ color: Colors.whiteColor }}>{PURCHASE_ORDER_STATUS_PARSED[poStatus]}</Text>
                        </View> */}

                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '42%', fontWeight: '400' }}>
                            Created Date
                          </Text>
                          <View style={{ width: '58%', alignItems: 'baseline' }}>
                            <View style={{ paddingHorizontal: 10, borderColor: '#E5E5E5', borderWidth: 1, borderRadius: 5, width: '90.5%', height: 35, justifyContent: 'space-between', backgroundColor: Colors.fieldtBgColor, flexDirection: 'row', alignItems: 'center' }}>
                              <GCalendarGrey width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} style={{ marginRight: 5, }} />
                              <Text style={{
                                marginRight: '18%',
                                fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                              }}>
                                {selectedStockTransferEdit ?
                                  moment(createdDate).format('DD MMM YYYY') :
                                  moment(date1).format('DD MMM YYYY')
                                }
                              </Text>
                            </View>
                          </View>

                        </View>

                        <View style={{ flexDirection: "row", width: '50%', alignItems: 'center' }}>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '38%', fontWeight: '400' }}>
                            Destination Store
                          </Text>
                          <View style={{ width: '62%' }}>

                            {targetOutletDropdownList.find(item => item.value === selectedTargetOutletId) ?
                              // <RNPickerSelect
                              //   disabled={selectedStockTransferEdit ? true : false}
                              //   items={targetOutletDropdownList}
                              //   defaultValue={selectedTargetOutletId}
                              //   arrowSize={20}
                              //   arrowStyle={{ paddingVertical: 0 }}
                              //   // placeholder="Target Store"
                              //   containerStyle={{ height: 35, width: 180, }}
                              //   style={{ backgroundColor: '#FAFAFA', paddingVertical: 0 }}
                              //   itemStyle={{
                              //     justifyContent: 'flex-start', marginLeft: 5
                              //   }}
                              //   dropDownStyle={{ backgroundColor: '#FAFAFA', width: 180 }}
                              //   onValueChange={(item) =>
                              //     // setState({
                              //     //   choice7: item.value,
                              //     // })
                              //     // setSelectedSupplierId(item.value)
                              //     setSelectedTargetOutletId(item.value)
                              //   }
                              // />
                              <DropDownPicker
                                containerStyle={{ height: 35, zIndex: 2 }}
                                arrowColor={'black'}
                                arrowSize={20}
                                arrowStyle={{ fontWeight: 'bold' }}
                                labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                                style={{ width: switchMerchant ? 170 : 220, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}
                                placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                disabled={selectedStockTransferEdit ? true : false}
                                items={targetOutletDropdownList}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2 }}
                                placeholder={"Product"}
                                customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                                onChangeItem={(item) => {
                                  setSelectedTargetOutletId(item.value);

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_DD_DESTINATION_STORE,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_DD_DESTINATION_STORE,
                                  })
                                }}
                                defaultValue={selectedTargetOutletId}
                                dropDownMaxHeight={150}
                                dropDownStyle={{ width: switchMerchant ? 170 : 220, height: 80, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, textAlign: 'left', zIndex: 2 }}
                              // disabled={selectedStockTransferEdit ? true : false}
                              // items={targetOutletDropdownList}
                              // defaultValue={selectedTargetOutletId}
                              // arrowSize={20}
                              // arrowStyle={{ paddingVertical: 0 }}
                              // controller={(instance) => dropDownRef1.current = instance}
                              // // placeholder="Target Store"
                              // containerStyle={{ height: 35, width: 220, }}
                              // style={{ backgroundColor: '#FAFAFA', paddingVertical: 0 }}
                              // itemStyle={{
                              //   justifyContent: 'flex-start', marginLeft: 5
                              // }}
                              // dropDownStyle={{ backgroundColor: '#FAFAFA', width: 220 }}
                              // onChangeItem={(item) =>
                              //   setSelectedTargetOutletId(item.value)
                              // }
                              // onOpen={() => { dropDownRef.current.close() }}
                              // ref={dropDownRef1}
                              />
                              : <></>

                            }
                          </View>
                          {/* <View style={{ width: '62%' }}>
                          <View style={{ width: switchMerchant ? 170 : 220, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}>
                            <Text>
                              {selectedSourceOutletId}
                            </Text>
                          </View>
                        </View> */}
                        </View>
                      </View>

                      <View style={{ flexDirection: "row", justifyContent: "space-between", alignSelf: 'center', marginTop: 50, marginBottom: 40, zIndex: -2, width: '80%' }}>
                        <View style={{ flexDirection: "row", width: '45%', alignItems: 'center' }}>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '42%', fontWeight: '400' }}>
                            Shipped Date
                          </Text>
                          {/* <Text style={{ color: '#adadad', marginLeft: 80, fontSize: 16, }}>8/10/2020</Text> */}
                          <View style={{
                            paddingHorizontal: 0,
                            flexDirection: 'row',
                            alignItems: 'center',
                            width: '57%',
                          }}>
                            <View style={{
                              // width: 140,
                              width: '90.5%',
                              height: 35,
                              backgroundColor: Colors.fieldtBgColor,
                              paddingHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              borderRadius: 5,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                            }}>
                              <TouchableOpacity
                                style={{
                                  marginRight: 5,
                                }}
                                disabled={poStatus === PURCHASE_ORDER_STATUS.COMPLETED}
                                onPress={() => {
                                  setIsDateTimePickerVisible(true);

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_SHIPPED_DATE_C_CALENDAR,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_SHIPPED_DATE_C_CALENDAR,
                                  })
                                }}>
                                {poStatus === PURCHASE_ORDER_STATUS.COMPLETED ?
                                  <GCalendarGrey width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} />
                                  :
                                  <GCalendar width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} />
                                }
                              </TouchableOpacity>
                              <Text style={{
                                //textAlign: 'left',
                                marginRight: '18%',
                                fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                              }}>
                                {/* {moment(date).format('DD/MM/YYYY')}  */}
                                {date ? moment(date).format('DD MMM YYYY') : 'Shipping Date'}
                              </Text>
                            </View>

                            <DateTimePickerModal
                              isVisible={isDateTimePickerVisible}
                              mode='date'
                              onConfirm={(text) => {
                                setDate(text);
                                setIsDateTimePickerVisible(false);
                              }}
                              onCancel={() => {
                                setIsDateTimePickerVisible(false);
                              }}
                            />
                          </View>
                        </View>
                      </View>
                      <View style={{ borderWidth: 1, borderColor: '#E5E5E5' }} />

                      {/* <ScrollView horizontal={true}
                    nestedScrollEnabled={true}
                    scrollEnabled={switchMerchant}
                    style={{ flexDirection: 'column' }}
                    contentContainerStyle={{
                      flexDirection: 'column',
                      paddingRight: switchMerchant ? 15 : 0,
                      width: switchMerchant ? Dimensions.get('screen').width * 0.97 : Dimensions.get('screen').width * 0.87,
                      //maxHeight: switchMerchant ? Dimensions.get('screen').height * 0.9 : null,
                    }}
                  > */}
                      <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: Platform.OS == 'ios' ? 0 : 10 }}>
                        <Text style={{ alignSelf: "center", marginTop: 30, fontSize: switchMerchant ? 15 : 20, fontWeight: 'bold' }}>Stock to Transfer</Text>
                      </View>

                      <View
                        style={{
                          backgroundColor: '#ffffff',
                          flexDirection: 'row',
                          paddingVertical: 20,
                          paddingHorizontal: 10,
                          marginTop: 10,
                          borderBottomWidth: StyleSheet.hairlineWidth,
                        }}>
                        <Text style={{ width: '29%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Product Name
                        </Text>
                        <Text style={{ width: '14%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500', marginLeft: 10, }}>
                          SKU
                        </Text>
                        {/* <Text style={{ width: '9%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500', }}>
                        Unit
                      </Text> */}
                        <Text style={{ width: '9%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          {/* In Stock */}
                          From Stock
                        </Text>
                        <Text style={{ width: '11%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Transfer Qty
                        </Text>
                        <Text style={{ width: '13%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Balance Stock
                        </Text>
                        <Text style={{ width: '10%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Cost (RM)
                        </Text>
                        <Text style={{ width: '12%', fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Bold', fontWeight: '500' }}>
                          Subtotal (RM)
                        </Text>
                      </View>

                      {outletItems.length > 0 ?
                        <FlatList
                          nestedScrollEnabled={true}
                          showsVerticalScrollIndicator={false}
                          data={poItems}
                          extraData={poItems}
                          renderItem={renderAddStock}
                          keyExtractor={(item, index) => String(index)}
                        />
                        :
                        <View style={{ alignItems: 'center', marginVertical: 20, marginTop: 50 }}>
                          <Text style={{ color: Colors.descriptionColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
                            No supply items In current store
                          </Text>
                        </View>
                      }

                      {outletItems.length > 0 ?
                        <View style={{ flexDirection: 'row', marginBottom: 20, justifyContent: 'space-between' }}>
                          {!selectedStockTransferEdit ?
                            <TouchableOpacity
                              style={[
                                styles.submitText2,
                              ]}
                              onPress={() => {
                                if (outletItems.length > 0) {
                                  setPoItems([
                                    ...poItems,
                                    {
                                      // supplyItemId: '',
                                      // name: '',
                                      // sku: '',
                                      // quantity: '',
                                      // insertQuantity: 0,
                                      outletSupplyItemId: outletItems[0].uniqueId,
                                      name: outletItems[0].name,
                                      sku: outletItems[0].sku,
                                      unit: '',
                                      skuMerchant: outletItems[0].skuMerchant,
                                      quantity: outletItems[0].stockCount || 0,
                                      transferQuantity: 0,
                                      balance: 0,
                                      price: outletItems[0].price,
                                      totalPrice: 0,

                                      supplyItem: outletItems[0],
                                    },
                                  ]);
                                }
                                else {
                                  Alert.alert('Error', 'No supplier items added')
                                }

                                logEventAnalytics({
                                  eventName: ANALYTICS.MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_ADD_PRODUCT_SLOT,
                                  eventNameParsed: ANALYTICS_PARSED.MODULE_INVENTORY_STOCK_TRANSFER_INFO_STOCK_TO_TRANSFER_C_ADD_PRODUCT_SLOT,
                                })
                              }}

                            >

                              <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 5, marginLeft: 0 }}>
                                <Icon1 name="plus-circle" size={switchMerchant ? 15 : 20} color={Colors.primaryColor} />
                                <Text style={{ marginLeft: 10, color: Colors.primaryColor, marginBottom: Platform.OS === 'ios' ? 0 : 1, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                                  Add Product Slot
                                </Text>
                              </View>

                            </TouchableOpacity>
                            :
                            <></>
                          }


                          <View style={{ alignItems: 'flex-end', marginTop: 5, marginRight: 20 }}>
                            <Text style={{ fontWeight: 'bold', fontSize: switchMerchant ? 10 : 16 }}>{poItems.totalPrice}</Text>
                          </View>
                        </View>
                        :
                        null
                      }
                      {/* </ScrollView> */}

                      {/* {!selectedStockTransferEdit || (selectedStockTransferEdit && selectedStockTransferEdit.status !== STOCK_TRANSFER_STATUS.COMPLETED) ?
                    <>
                      <View style={{ flexDirection: "row", alignSelf: "center", justifyContent: "space-evenly", marginTop: 20 }}>
                        

                        <View
                          style={{
                            backgroundColor: Colors.primaryColor,
                            width: 200,
                            height: 40,
                            marginVertical: 15,
                            borderRadius: 5,
                            alignSelf: 'center',
                            marginLeft: 40,
                            display: selectedStockTransferEdit ? 'flex' : 'none',
                          }}>
                          <TouchableOpacity onPress={() => {
                            createStockTransfer(STOCK_TRANSFER_STATUS.COMPLETED);
                          }} disabled={selectedStockTransferEdit ? false : true}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                alignSelf: 'center',
                                marginVertical: 10,
                              }}>
                              {'SAVE & MARK COMPLETED'}
                            </Text>
                          </TouchableOpacity>
                          <ModalView
                            style={{ flex: 1 }}
                            visible={visible}
                            transparent={true}
                            animationType="slide">
                            <View
                              style={{
                                backgroundColor: 'rgba(0,0,0,0.5)',
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                minHeight: Dimensions.get('window').height,
                              }}>
                              <View style={styles.confirmBox}>
                                <TouchableOpacity
                                  onPress={() => {
                                    setState({ visible: false });
                                  }}>
                                  <View
                                    style={{
                                      alignSelf: 'flex-end',
                                      padding: 16,
                                    }}>
                                    <Close name="closecircle" size={28} />
                                  </View>
                                </TouchableOpacity>
                                <View>
                                  <Text
                                    style={{
                                      textAlign: 'center',
                                      fontWeight: '700',
                                      fontSize: 28,
                                    }}>
                                    Stock Transfer
                                  </Text>
                                </View>
                                <View style={{ marginTop: 20 }}>
                                  <Text
                                    style={{
                                      textAlign: 'center',
                                      color: Colors.descriptionColor,
                                      fontSize: 25,
                                    }}>
                                    Fill in the email information
                                  </Text>
                                </View>
                                <View style={{ backgroundColor: 'white', alignSelf: 'center', flexDirection: "row" }}>
                                  <Text style={{ fontSize: 20, marginTop: 70 }}>
                                    Email:
                                  </Text>
                                  <View style={{ marginTop: 60, backgroundColor: '#f7f5f5', marginLeft: 10 }}>
                                    <TextInput
                                      editable={!loading}
                                      underlineColorAndroid={Colors.fieldtBgColor}
                                      clearButtonMode="while-editing"
                                      style={styles.textCapacity}
                                      placeholder="<EMAIL>"
                                      onChangeText={(text) => {
                                        setState({ Email: text });
                                      }}
                                      value={Email}
                                    />
                                  </View>
                                </View>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    alignSelf: 'center',
                                    marginTop: 20,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    width: '50%',
                                    alignContent: 'center',
                                    zIndex: 6000,
                                  }}>

                                </View>
                                <View
                                  style={{
                                    alignSelf: 'center',
                                    marginTop: 20,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    width: 250,
                                    height: 40,
                                    alignContent: 'center',
                                    flexDirection: "row",
                                    marginTop: 40
                                  }}>
                                  <TouchableOpacity
                                    onPress={() => {
                                      email(),
                                        setState({ visible: false });
                                    }}
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: '60%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      alignContent: 'center',
                                      borderRadius: 10,
                                      height: 60,
                                    }}>
                                    <Text style={{ fontSize: 28, color: Colors.primaryColor }}>
                                      Send
                                    </Text>
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    onPress={() => {
                                      setState({ visible: false });
                                    }}
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: '60%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      alignContent: 'center',
                                      borderRadius: 10,
                                      height: 60,
                                      marginLeft: 30
                                    }}>
                                    <Text style={{ fontSize: 28, color: Colors.primaryColor }}>
                                      No
                                    </Text>
                                  </TouchableOpacity>
                                </View>
                              </View>
                            </View>
                          </ModalView>
                        </View>
                      </View>

                    </>
                    :
                    <></>
                  } */}

                      {/* <View
                    style={{
                      flexDirection: 'row',
                      backgroundColor: '#ffffff',
                      justifyContent: 'center',
                      padding: 18,
                    }}>
                    <View style={{ alignItems: 'center' }}>
                      <Text style={{ fontSize: 30, fontWeight: 'bold' }}>
                        {stockTransfers.length}
                      </Text>
                      <Text>STOCK TRANSFERS</Text>
                    </View>
                  </View> */}
                      <View style={{ height: 50, }}>
                      </View>
                    </KeyboardAwareScrollView>
                  </View>
                  {/* </ScrollView> */}
                </View>
              </View>
            ) : null}
          </View>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row'
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
  },
  submitText: {
    height: Platform.OS == 'ios' ? Dimensions.get('screen').height * 0.06 : Dimensions.get('screen').height * 0.05,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: "flex-end",
    marginRight: 20,
    // marginTop: 15,
    height: 40,
    width: 220,
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    alignSelf: "flex-end",
    marginRight: 20,
    marginTop: 15
  },
  /* textInput: {
    width: 300,
    height: '10%',
    padding: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 20,
  }, */

  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    marginRight: '35%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'flex-end',

    shadowOffset: Platform.OS == 'ios' ? {
      width: 0,
      height: 0,
    } : {
      width: 0,
      height: 7,
    },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset: Platform.OS == 'ios' ? {
      width: 0,
      height: 0,
    } : {
      width: 0,
      height: 7,
    },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    width: 100,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
    textAlign: 'center'
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 80,
    width: 900,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center'
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalView: {
    height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalView1: {
    //height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalViewImport: {
    height: Dimensions.get('screen').width * 0.6,
    width: Dimensions.get('screen').width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('screen').width * 0.03,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.02,
    top: Dimensions.get('screen').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'

  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: "20%",
  },
  modalSaveButton: {
    width: Dimensions.get('screen').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
});
export default StockTransferProductScreen;
