import React, { Component } from 'react'
import { StyleSheet, ScrollView, Image, Text, View, TextInput, TouchableOpacity, Alert, Modal, Dimensions } from 'react-native'
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import auth from '@react-native-firebase/auth';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as Token from '../util/Token';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage'
import { FlatList } from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import moment from 'moment'


class PaymentScreen extends Component {
    constructor({ navigation, props }) {
        super(props)
        this.state = {
            orderInfo: [],
            cashInsert: false,
            cashAmount: "0",
        }
    }

    componentDidMount() {
        const { orderInfo } = this.props.route.params
        console.log('orderIN', orderInfo)
        this.setState({
            orderInfo: orderInfo,
            orderId: orderInfo.id,
            orderItems: orderInfo.orderItems,
            subtotal: orderInfo.price,
            total: orderInfo.finalPrice,
            taxRate: orderInfo.outletTax.rate * 10,
            tax: orderInfo.tax

        })
        this.getPaymentMethods()
    }

    getPaymentMethods() {
        ApiClient.GET(API.getDefaultPayment).then((result) => {
            this.setState({
                paymentMethods: result,
                selectedPay: result[0].name
            })
        }).catch(err => { console.log(err) })
    }

    completePayment() {
        var body = {
            orderId: this.state.orderId
        }
        ApiClient.POST(API.orderDonePayment, body, false).then(result => {
            console.log("res", result)
            if (result) {
                this.setState({ refresh: true })
            }
        }).catch(err => { console.log(err) })

    }

    renderTableOrder = ({ item, index }) => {
        return (
            <View style={{ paddingHorizontal: 30, paddingVertical: 30 }}>
                <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
                    <View style={{ flex: 2, justifyContent: 'center', alignItems: 'flex-start', }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.035 }}>{item.name}</Text>
                    </View>
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.035 }}>x {item.quantity}</Text>
                    </View>
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.035 }}>RM {item.price}</Text>
                    </View>
                </View>
            </View>
        )
    }

    renderPaymentMethods = ({ item, index }) => {
        return (
            <TouchableOpacity style={{ justifyContent: 'flex-start', alignItems: 'center', flexDirection: 'row', paddingVertical: 8, paddingHorizontal: 25, borderWidth: 1, margin: 5, borderRadius: 7, borderColor: this.state.selectedPay == item.name ? Colors.primaryColor : Colors.tabGrey }}
                onPress={() => {
                    this.setState({ selectedPay: item.name, selectedImage: item.image }), item.name == "Cash" ? this.setState({ cashInsert: true }) : null
                }}>
                <View style={{ margin: 5, marginRight: 15, justifyContent: 'center', alignItems: 'center', width: 15, height: 15, borderRadius: 20, borderWidth: 1, borderColor: this.state.selectedPay == item.name ? Colors.primaryColor : null }}>
                    <View style={{ justifyContent: 'center', alignItems: 'center', width: 9, height: 9, borderRadius: 10, backgroundColor: this.state.selectedPay == item.name ? Colors.primaryColor : null }} />
                </View>

                {item.name == 'Credit Card' ?
                    <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
                        <Text style={{ fontFamily: 'NunitoSans-Bold', color: this.state.selectedPay == item.name ? Colors.primaryColor : Colors.tabGrey }}>{item.name}</Text>
                        <Image source={{ uri: item.image }} style={{ resizeMode: 'contain', width: 100, height: 30 }} />
                    </View>
                    : item.name == "Cash" ?
                        <View>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', color: this.state.selectedPay == item.name ? Colors.primaryColor : Colors.tabGrey }}>{item.name}</Text>
                        </View>
                        : (item.name == "Paypal" || item.name == "GrabPay") ?
                            <View>
                                <Image source={{ uri: item.image }} style={{ resizeMode: 'contain', width: 80, height: 30 }} />
                            </View>
                            : null}
            </TouchableOpacity>
        )
    }

    cashInsertBtn(key) {
        if (key >= 0) {
            if (this.state.cashAmount.length < 8)
                this.setState({ cashAmount: this.state.cashAmount + key }, function () {
                })
        } else {
            if (this.state.cashAmount.length > 0)
                this.setState({ cashAmount: this.state.cashAmount.slice(0, key) }, function () {
                })
        }
        if (this.state.cashAmount.length > 0) {
            console.log('this', this.state.cashAmount)
            const displayAmount = (parseFloat(this.state.cashAmount) / 100).toLocaleString('en-US')
            this.setState({ cashDisplay: displayAmount })
        }
    }



    render() {
        return (

            <View style={{ flex: 1, paddingBottom: 50, paddingTop: 10 }}>
                <ScrollView>
                    <View style={{ backgroundColor: Colors.whiteColor, width: Dimensions.get('screen').width * 0.95, alignSelf: 'center', }}>
                        <View style={{ width: "100%", height: Dimensions.get('screen').height * 0.065, backgroundColor: Colors.lightPrimary, justifyContent: 'center', paddingHorizontal: 40, }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.04 }}>Transaction Summary</Text>
                        </View>
                        <View>
                            <FlatList
                                nestedScrollEnabled={true}
                                data={this.state.orderItems}
                                renderItem={this.renderTableOrder}
                                keyExtractor={(item, index) => String(index)} />
                        </View>
                        <View style={{ width: "100%", height: Dimensions.get('screen').height * 0.065, backgroundColor: Colors.lightPrimary, justifyContent: 'center', paddingHorizontal: 40, }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 18 }}>Payment</Text>
                        </View>
                        <View style={{ marginTop: 20, paddingHorizontal: 30 }}>
                            <View style={{ justifyContent: 'space-between', flexDirection: 'row', paddingVertical: 1 }}>
                                <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: Dimensions.get('screen').width * 0.035, color: "#7B8E92" }}>Subtotal</Text>
                                <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: Dimensions.get('screen').width * 0.035, color: "#7B8E92" }}>RM {this.state.subtotal}</Text>
                            </View>
                            <View style={{ justifyContent: 'space-between', flexDirection: 'row', paddingVertical: 1 }}>
                                <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: Dimensions.get('screen').width * 0.035, color: "#7B8E92" }}>Discount</Text>
                                <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: Dimensions.get('screen').width * 0.035, color: "#7B8E92" }}> {this.state.discount}</Text>
                            </View>
                            <View style={{ justifyContent: 'space-between', flexDirection: 'row', paddingVertical: 1 }}>
                                <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: Dimensions.get('screen').width * 0.035, color: "#7B8E92" }}>Tax ({Math.trunc(this.state.taxRate)}%)</Text>
                                <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: Dimensions.get('screen').width * 0.035, color: "#7B8E92" }}>RM {this.state.tax}</Text>
                            </View>
                            <View style={{ justifyContent: 'space-between', flexDirection: 'row', paddingVertical: 1, marginTop: 30 }}>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.035, color: Colors.blackColor }}>TOTAL</Text>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.035, color: Colors.blackColor }}>RM {this.state.total}</Text>
                            </View>
                        </View>
                        <View style={{ width: "100%", height: Dimensions.get('screen').height * 0.065, backgroundColor: Colors.lightPrimary, justifyContent: 'center', paddingHorizontal: 40, marginTop: 40 }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 18 }}>Payment Method</Text>
                        </View>
                        <View style={{ justifyContent: 'center', alignItems: 'center', marginTop: 30 }}>
                            <FlatList
                                showsHorizontalScrollIndicator={false}
                                data={this.state.paymentMethods}
                                renderItem={this.renderPaymentMethods}
                                keyExtractor={(item, index) => String(index)}
                            />
                        </View>
                        <View style={{ width: "100%", height: Dimensions.get('screen').height * 0.065, backgroundColor: Colors.lightPrimary, justifyContent: 'center', paddingHorizontal: 40, marginTop: 40 }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 18 }}>Redeem Points/Promo Code</Text>
                        </View>
                        <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
                            <View style={{ margin: 20, borderRadius: 8, height: Dimensions.get('screen').width * 0.11, width: Dimensions.get('screen').width * 0.38, alignItems: 'center', justifyContent: 'center', backgroundColor: Colors.fieldtBgColor, flexDirection: 'row' }}>
                                <TextInput

                                    onChangeText={text => this.setState({ promoCode: text })}
                                    placeholder="Promo Code"
                                    placeholderTextColor={Colors.fieldtTxtColor}
                                />
                                <TouchableOpacity style={{ alignItems: 'center', justifyContent: 'center', backgroundColor: Colors.primaryColor, height: Dimensions.get('screen').height * 0.04, width: Dimensions.get('screen').width * 0.15, borderRadius: 8 }} onPress={() => { }}>
                                    <Text style={{ color: Colors.whiteColor, fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.025 }}>APPLY</Text>
                                </TouchableOpacity>
                            </View>
                            <View style={{ margin: 20, borderRadius: 8, height: Dimensions.get('screen').width * 0.11, width: Dimensions.get('screen').width * 0.38, alignItems: 'center', justifyContent: 'center', backgroundColor: Colors.fieldtBgColor, flexDirection: 'row', paddingHorizontal: 10 }}>
                                <TextInput
                                    keyboardType={"numeric"}
                                    maxLength={5}
                                    onChangeText={text => this.setState({ points: text })}
                                    placeholder="500 points"
                                    placeholderTextColor={Colors.fieldtTxtColor}
                                />
                                <TouchableOpacity style={{ alignItems: 'center', justifyContent: 'center', backgroundColor: Colors.primaryColor, height: Dimensions.get('screen').height * 0.04, width: Dimensions.get('screen').width * 0.15, borderRadius: 8 }} onPress={() => { }}>
                                    <Text style={{ color: Colors.whiteColor, fontFamily: 'NunitoSans-Bold', fontSize: Dimensions.get('screen').width * 0.025 }}>REDEEM</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View style={{ flexDirection: 'row', alignSelf: 'flex-end', marginRight: 30, marginVertical: 20, marginBottom: 40 }}>
                            <Text style={{ color: Colors.primaryColor, fontFamily: 'NunitoSans-Bold', fontSize: 20, marginRight: 80, }}>TOTAL</Text>
                            <Text style={{ color: Colors.primaryColor, fontFamily: 'NunitoSans-Bold', fontSize: 20 }}>RM {this.state.total}</Text>
                        </View>
                    </View>
                    <View style={{ width: "60%", marginTop: 40, alignSelf: 'flex-end', alignItems: 'center' }}>
                        <TouchableOpacity
                            onPress={() => {
                                this.setState({ checkOutTime: moment().format('lll'), renderReceipt: true }); this.completePayment()
                            }}
                            style={{ borderRadius: 8, height: Dimensions.get('screen').width * 0.12, width: Dimensions.get('screen').width * 0.4, backgroundColor: Colors.primaryColor, alignItems: 'center', justifyContent: 'center' }}>
                            <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: 20, color: Colors.whiteColor }}>Check Out</Text>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
                <Modal
                    supportedOrientations={['landscape', 'portrait']}
                    style={{ flex: 1 }}
                    visible={this.state.cashInsert}
                    transparent={true}
                    animationType="slide">
                    <View
                        style={{
                            backgroundColor: Colors.modalBgColor,
                            flex: 1,
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}>
                        <View style={{ height: Dimensions.get('screen').height * 0.75, width: Dimensions.get('screen').width * 0.85, backgroundColor: 'white', padding: 20, alignItems: 'center' }}>
                            <View style={{ justifyContent: 'center', alignItems: 'center', flex: 1 }}>
                                <Text style={{ fontSize: Dimensions.get('screen').width * 0.13, color: Colors.fieldtTxtColor }}>
                                    {(this.state.cashDisplay == null || this.state.cashDisplay == 0) ? "$0.00" : "$" + this.state.cashDisplay}
                                </Text>
                            </View>
                            <View style={{ justifyContent: 'center', alignItems: 'center', flex: 2 }}>
                                <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(1) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>1</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(2) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>2</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(3) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>3</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(4) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>4</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(5) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>5</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(6) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>6</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(7) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>7</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(8) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>8</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(9) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>9</Text></View>
                                    </TouchableOpacity>
                                    <View style={[styles.pinBtn, { backgroundColor: 'white', borderWidth: 0 }]} />
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(0) }}>
                                        <View style={styles.pinBtn}><Text style={styles.pinNo}>0</Text></View>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => { this.cashInsertBtn(-1) }}>
                                        <View style={styles.pinBtn}><Icon name="backspace-outline" size={30} /></View>
                                    </TouchableOpacity>
                                </View>
                            </View>
                            <View style={{ justifyContent: 'space-around', alignItems: 'center', flex: 1, width: '100%' }}>
                                <TouchableOpacity
                                    style={{ alignItems: 'center', width: "70%", borderColor: Colors.fieldtTxtColor, borderRadius: 8, paddingVertical: 10, borderWidth: 1 }}
                                    onPress={() => this.setState({ cashInsert: false })}>
                                    <Text style={{ color: Colors.fieldtTxtColor, fontSize: 18 }}>Cancel</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={{ alignItems: 'center', width: "70%", backgroundColor: Colors.primaryColor, borderColor: Colors.fieldtTxtColor, borderRadius: 8, paddingVertical: 10, borderWidth: 1 }}
                                    onPress={() => { this.setState({ cashInsert: false, renderReceipt: true, renderPaymentSummary: false }); }}
                                >
                                    <Text style={{ color: Colors.whiteColor, fontSize: 18 }}>Receive</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </Modal>

            </View>
        )
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff'
    },
    pinBtn: {
        backgroundColor: Colors.lightPrimary,
        width: Dimensions.get('screen').width * 0.23,
        height: Dimensions.get('screen').width * 0.12,
        marginBottom: 16,
        alignContent: 'center',
        justifyContent: 'center',
        borderColor: Colors.fieldtTxtColor,
        alignItems: 'center',
        borderRadius: 10,
    },
    pinNo: {
        fontSize: Dimensions.get('screen').width * 0.04,
        fontFamily: 'NunitoSans-Bold'

    }
})
export default PaymentScreen
