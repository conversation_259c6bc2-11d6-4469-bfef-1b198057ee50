import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  Switch,
  Modal as ModalComponent,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { TextInput, FlatList, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from '@react-native-community/checkbox';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Styles from '../constant/Styles';
import {
  isTablet,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  logEventAnalytics,
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Upload from '../assets/svg/Upload';
import Download from '../assets/svg/Download';
import RNFetchBlob from 'rn-fetch-blob';
import {
  listenToUserChangesMerchant,
  listenToMerchantIdChangesMerchant,
  listenToCurrOutletIdChangesWaiter,
  listenToAllOutletsChangesMerchant,
  listenToCommonChangesMerchant,
  listenToSelectedOutletItemChanges,
  convertArrayToCSV,
  listenToSelectedOutletTableIdChanges,
  requestNotificationsPermission,
} from '../util/common';
import Feather from 'react-native-vector-icons/Feather';
import DropDownPicker from 'react-native-dropdown-picker';
import Ionicon from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import { useKeyboard } from '../hooks';
import AIcon from 'react-native-vector-icons/AntDesign';
import {
  SEGMENT_SORT_FIELD_TYPE,
  SEGMENT_SORT_FIELD_TYPE_VALUE,
  REPORT_SORT_FIELD_TYPE_COMPARE,
  REPORT_SORT_COMPARE_OPERATOR,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';

////////////////////////////////////
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

///////////////////////////////////

///////////////////////////////////

const ADD_SEGMENT_SCREEN = {
  ADD_SEGMENT: 'ADD_SEGMENT_SCREEN.ADD_SEGMENT',
  SEGMENT_LIST: 'ADD_SEGMENT_SCREEN.SEGMENT_LIST',
  CREATE_TAG: 'ADD_SEGMENT_SCREEN.CREATE_TAG'
};

const SegmentScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  //////////////////////////////////// UseState Here
  const [keyboardHeight] = useKeyboard();

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const [list1, setList1] = useState(true);
  const [customerList, setCustomerList] = useState([]);

  const [perPage, setPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageCount, setPageCount] = useState(0);
  const [page, setPage] = useState(0);

  const [search, setSearch] = useState('');

  const [visible, setVisible] = useState(false);

  const [controller, setController] = useState({});
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [addSegmentScreen, setAddSegmentScreen] = useState(
    ADD_SEGMENT_SCREEN.SEGMENT_LIST,
  );
  const [userTagName, setUserTagName] = useState('');
  const [userTagList, setUserTagList] = useState([]);
  const [userTagDropdownList, setUserTagDropdownList] = useState([]);
  const [selectedUserTagList, setSelectedUserTagList] = useState([]);
  const [searchingUserTagText, setSearchingUserTagText] = useState('');

  const [segmentName, setSegmentName] = useState('');

  const [temp, setTemp] = useState('');
  //////////////////////////////////////////////////////////////

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const crmUserTagsDict = OutletStore.useState((s) => s.crmUserTagsDict);
  const selectedCustomerEdit = CommonStore.useState(
    (s) => s.selectedSegmentEdit,
  );
  const selectedSegmentEdit = CommonStore.useState(
    (s) => s.selectedSegmentEdit,
  );

  const crmUsers = OutletStore.useState((s) => s.crmUsers);
  const crmSegments = OutletStore.useState((s) => s.crmSegments);

  const [currSummarySort, setCurrSummarySort] = useState('');

  // const selectedCustomerDineInOrders = OutletStore.useState(s => s.selectedCustomerDineInOrders);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const [pushPagingToTop, setPushPagingToTop] = useState(false);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const deleteCRMSegments = (segment) =>{
    var body = {
      crmSegmentIdList: [segment.uniqueId]
    }

        APILocal.deleteCRMSegments({ body: body})
            .then((result) => {
                if (result && result.status === 'success') {
                    Alert.alert('Success', 'Segment has been removed');
                }
            })
            .catch((err) => {
                console.log(err);
            });
    
};
  const [isDeleteON, setIsDeleteON] = useState(false);
  const [isSelectedAll, setIsSelectedAll] = useState(true); // To control 'Select All' checkbox

  useEffect(() => {
    // console.log('================================');
    // console.log('selectedSegmentEdit');
    // console.log(selectedSegmentEdit);

    if (selectedSegmentEdit) {
      // insert info

      setSegmentName(selectedSegmentEdit.name);
      setSelectedUserTagList(selectedCustomerEdit.crmUserTagIdList || []);
      setSearchingUserTagText('');
    } else {
      // designed to always mounted, thus need clear manually...

      setSegmentName('');
      setSelectedUserTagList([]);
      setSearchingUserTagText('');
    }
  }, [selectedSegmentEdit, addSegmentScreen]);

  //////////////////////////////////////////////////////////////

  //////////////////////////////////// UseEffect here

  useEffect(() => {
    if (crmUserTags.length > 0) {
      setUserTagDropdownList(
        crmUserTags
          .filter((item) => {
            var isExisted = false;

            for (var i = 0; i < userTagList.length; i++) {
              if (userTagList[i].uniqueId === item.uniqueId) {
                isExisted = true;
                break;
              }
            }

            return !isExisted;
          })
          .map((item) => {
            return { label: item.name, value: item.uniqueId };
          }),
      );

      // if (selectedTax === '') {
      //   setSelectedTax(currOutletTaxes[0].uniqueId);

      //   for (var i = 0; i < currOutletTaxes.length; i++) {
      //     if (currOutletTaxes[i].uniqueId === currOutletTaxes[0].uniqueId) {
      //       setSelectedTaxName(currOutletTaxes[i].name);
      //       setSelectedTaxRate(currOutletTaxes[i].rate);
      //       break;
      //     }
      //   }
      // }
    }
  }, [crmUserTags, userTagList]);

  useEffect(() => {
    setCurrentPage(1);
    setPageCount(Math.ceil(crmSegments.length / perPage));
  }, [crmSegments.length]);

  ///////// SORT //////////
  const sortSegment = (dataList, segmentSortFieldType) => {
    var dataListTemp = [...dataList];
    // console.log('dataList');
    // console.log(dataList);

    // console.log('segmentSortFieldType');
    // console.log(segmentSortFieldType);

    const segmentSortFieldTypeValue =
      SEGMENT_SORT_FIELD_TYPE_VALUE[segmentSortFieldType];
    const segmentSortFieldTypeCompare =
      REPORT_SORT_FIELD_TYPE_COMPARE[segmentSortFieldType];

    if (segmentSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (segmentSortFieldType === SEGMENT_SORT_FIELD_TYPE.SEGMENT_GROUP_ASC) {
        dataListTemp.sort((a, b) =>
          (a[segmentSortFieldTypeValue]
            ? a[segmentSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[segmentSortFieldTypeValue] ? b[segmentSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[segmentSortFieldTypeValue] ? a[segmentSortFieldTypeValue] : '') -
            (b[segmentSortFieldTypeValue] ? b[segmentSortFieldTypeValue] : ''),
        );
      }
    } else if (
      segmentSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
    ) {
      if (segmentSortFieldType === SEGMENT_SORT_FIELD_TYPE.SEGMENT_GROUP_DESC) {
        dataListTemp.sort((a, b) =>
          (b[segmentSortFieldTypeValue]
            ? b[segmentSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[segmentSortFieldTypeValue] ? a[segmentSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[segmentSortFieldTypeValue] ? b[segmentSortFieldTypeValue] : '') -
            (a[segmentSortFieldTypeValue] ? a[segmentSortFieldTypeValue] : ''),
        );
      }
    }
    return dataListTemp;
  };

  //////////////////////////////////// Page standardize pattern here

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }

          logEventAnalytics({
            eventName: ANALYTICS.MODULE_CRM_SEGMENT_C_LOGO,
            eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_C_LOGO
          })
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Segment
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_CRM_SEGMENT_C_PROFILE,
              eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_C_PROFILE
            })
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  ///////////////////////////////////////////////////////Function here

  const nextPage = () => {
    setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
  };

  const prevPage = () => {
    setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
  };

  const createCRMUserTag = async () => {
    if (
      //!userTagName
      !searchingUserTagText
    ) {
      Alert.alert(
        'Info',
        'Tag name cannot be empty',
        [
          {
            text: 'OK',
            onPress: () => {
              //   navigation.goBack();
            },
          },
        ],
        { cancelable: false },
      );
    } else {
      var isExisted = false;

      for (var i = 0; i < crmUserTags.length; i++) {
        if (crmUserTags[i].name === searchingUserTagText) {
          isExisted = true;
          break;
        }
      }

      if (isExisted) {
        Alert.alert(
          'Info',
          'Tag exists',
          [
            {
              text: 'OK',
              onPress: () => {
                // navigation.goBack();
              },
            },
          ],
          { cancelable: false },
        );
      } else {
        var body = {
          merchantId: merchantId,
          //tagName: userTagName,
          tagName: searchingUserTagText,
        };

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(API.createCRMUserTag, body, false)
        APILocal.createCRMUserTag({ body: body, uid: userId })
          .then((result) => {
            if (result && result.status === 'success') {
              Alert.alert(
                'Success',
                'Tag has been created',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      // navigation.goBack();
                    },
                  },
                ],
                { cancelable: false },
              );
            }

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          });
      }
    }
  };

  const addUserToCRMUserTag = async () => {
    if (selectedUserTagList.length === 0) {
      Alert.alert(
        'Info',
        'Please select at least one tag to be added',
        [
          {
            text: 'OK',
            onPress: () => {
              navigation.goBack();
            },
          },
        ],
        { cancelable: false },
      );
    } else {
      var body = {
        crmUserTagIdList: selectedUserTagList,
        merchantId: merchantId,

        tokenFcm: selectedCustomerEdit.tokenFcm || '',
        email: selectedCustomerEdit.email || '',
        phone: selectedCustomerEdit.number || '',
      };

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      // ApiClient.POST(API.addUserToCRMUserTag, body, false)
      APILocal.addUserToCRMUserTag({ body: body, uid: userId })
        .then((result) => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Tag(s) has been added to user',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    // navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    }
  };

  const createCRMUserTagOrAddUserToCRMUserTag = async () => {
    var body = {
      crmUserTagIdList: selectedUserTagList,
      merchantId: merchantId,

      tokenFcm: selectedCustomerEdit.tokenFcm || '',
      email: selectedCustomerEdit.email || '',
      phone: selectedCustomerEdit.number || '',

      searchingUserTagText: searchingUserTagText,
    };

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.createCRMUserTagOrAddUserToCRMUserTag, body, false)
    APILocal.createCRMUserTagOrAddUserToCRMUserTag({ body: body, uid: userId })
      .then(
        (result) => {
          if (result && result.status === 'success') {
            Alert.alert(
              'Success',
              'Tag(s) has been added to user',
              [
                {
                  text: 'OK',
                  onPress: () => {
                    // navigation.goBack();
                  },
                },
              ],
              { cancelable: false },
            );
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        },
      );
  };

  const deleteTagsFunction = async (tagList) => {
    try {
      await Promise.all(
        tagList.map(async userTagId => {
          // Declare the body object
          const body = {
            tagUniqueId: userTagId,
          };
  
          // Call the API with the body and uid
          return APILocal.deleteTagById({ body: body, uid: userId })
            .then(response => {
              if (response.status === 'success') {
                console.log(`Tag with ID ${userTagId} deleted successfully`);
              } else {
                console.error(`Failed to delete tag with ID ${userTagId}`);
              }
            })
            .catch(error => console.error(`Error deleting tag ${userTagId}:`, error));
        })
      );
  
      // After all deletions, gather the tag names
      const tagNames = tagList
        .map(userTagId => crmUserTagsDict[userTagId]?.name || 'N/A')
        .filter(tagName => tagName !== 'N/A');
  
      // Show the success alert with all tag names
      if (tagNames.length > 0) {
        Alert.alert(
          'Success',
          `The following tags have been deleted: ${tagNames.join(', ')}`,
          [
            { text: 'OK' }
          ],
          { cancelable: false }
        );
      }
    } catch (error) {
      console.error('Error deleting tags:', error);
    }
  };
  

  const deleteSegmentGroup = async (segmentUniqueId, segmentName, tagList) => {
    const allTags = tagList && tagList.map((userTagId) => {
      return crmUserTagsDict[userTagId] ? crmUserTagsDict[userTagId].name : 'N/A';
    });
    
    const validTags = allTags.filter(tag => tag !== 'N/A');

    var body = {segmentUniqueId};

    APILocal.deleteCRMSegment({ body: body, uid: userId })
    .then((result) => {
      if (result && result.status === 'success') {
        Alert.alert(
          'Success',
          `${segmentName} has been deleted`,  // Segment deletion confirmation
          [
            {
              text: 'OK',
              onPress: () => {
                // After confirming the segment deletion, ask if they want to delete the tags
                if (validTags.length > 0) {
                  Alert.alert(
                    'Delete Tags',
                    `Do you want to delete the associated tags: ${validTags.join(', ')}?`,
                    [
                      { text: 'No', style: 'cancel' },
                      { 
                        text: 'Yes', 
                        onPress: () => deleteTagsFunction(tagList),
                        style: 'destructive', 
                      }
                    ],
                    { cancelable: false }
                  );
                }
              }
            },
          ],
          { cancelable: false },
        );
      } else {
        Alert.alert(
          'Error',
          'Failed to delete the segment',
          [
            {
              text: 'OK',
            },
          ],
          { cancelable: false },
        );
      }
    })
    .catch((error) => {
      Alert.alert(
        'Error',
        'An error occurred while deleting the segment',
        [
          {
            text: 'OK',
          },
        ],
        { cancelable: false },
      );
    });
  };

  const removeUserFromCRMUserTag = async (userTag) => {
    var body = {
      crmUserTagId: userTag.uniqueId,
      merchantId: merchantId,

      tokenFcm: selectedCustomerEdit.tokenFcm || '',
      email: selectedCustomerEdit.email || '',
      phone: selectedCustomerEdit.number || '',
    };

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.removeUserFromCRMUserTag, body, false)
    APILocal.removeUserFromCRMUserTag({ body: body, uid: userId })
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Tag has been removed from user',
            [
              {
                text: 'OK',
                onPress: () => {
                  // navigation.goBack();
                },
              },
            ],
            { cancelable: false },
          );
        }

        CommonStore.update((s) => {
          s.isLoading = false;
        });
      });
  };

  const renderUserTag = ({ item }) => {
    return (
      <View
        style={{
          borderWidth: 1,
          borderColor: 'darkgreen',
          borderRadius: 5,
          padding: 5,
          marginLeft: 10,
          alignSelf: 'baseline',
          justifyContent: 'space-between',
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <Text style={{ fontWeight: '500', color: 'green' }}>{item.name}</Text>
        <TouchableOpacity
          style={{ marginLeft: 5 }}
          onPress={() => {
            Alert.alert(
              'Info',
              `Are you sure you want to remove ${item.name} tag?`,
              [
                { text: 'NO', onPress: () => { }, style: 'cancel' },
                {
                  text: 'YES',
                  onPress: () => {
                    removeUserFromCRMUserTag(item);
                  },
                },
              ],
              { cancelable: false },
            );
          }}>
          <AntDesign name="closecircle" color={Colors.fieldtTxtColor} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          CommonStore.update((s) => {
            s.selectedSegmentEdit = item;
          });

          setAddSegmentScreen(ADD_SEGMENT_SCREEN.ADD_SEGMENT);
        }}>
        <View
          style={{
            //backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
            backgroundColor: '#FFFFFF',
            paddingVertical: 15,
            paddingHorizontal: 5,
            //paddingLeft: 1,
            borderColor: '#BDBDBD',
            borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
            borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
            // width: '100%',
          }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', }}>
            {/* {isDeleteON ?
              <View style={{ width: '3%' }}>
                <CheckBox
                  // value={filteredCRMList[index].isChecked} // checkbox state for each user
                  onValueChange={(value) => {

                  }}
                />
              </View>
              : <></>} */}
            <View style={{ width: '30%' }}>
              <Text
                style={
                  switchMerchant
                    ? {
                      fontWeight: '600',
                      color: Colors.primaryColor,
                      fontSize: 10,
                      fontFamily: 'NunitoSans-Regular',
                    }
                    : {
                      fontWeight: '600',
                      color: Colors.primaryColor,
                      fontSize: 14,
                      fontFamily: 'NunitoSans-Regular',
                    }
                }>
                {item.name}
              </Text>
            </View>
            <View style={{ width: '65%', marginLeft: 10, flexDirection: 'row' }}>
              {item.crmUserTagIdList &&
                item.crmUserTagIdList.map((userTagId, i) => {
                  var tagText = 'N/A';

                  if (crmUserTagsDict[userTagId]) {
                    tagText = crmUserTagsDict[userTagId].name;
                  }

                  return (
                    <View style={{ alignItems: 'baseline', marginRight: 5 }}>
                      <Text
                        style={
                          switchMerchant
                            ? {
                              borderColor: 'green',
                              borderWidth: 1,
                              borderRadius: 5,
                              fontWeight: '500',
                              color: Colors.primaryColor,
                              padding: 3,
                              paddingLeft: 4,
                              alignItems: 'baseline',
                              fontSize: 10,
                              fontFamily: 'NunitoSans-Regular',
                            }
                            : {
                              borderColor: 'green',
                              borderWidth: 1,
                              borderRadius: 5,
                              fontWeight: '500',
                              color: Colors.primaryColor,
                              padding: 3,
                              paddingLeft: 4,
                              alignItems: 'baseline',
                              fontSize: 14,
                              fontFamily: 'NunitoSans-Regular',
                            }
                        }>
                        {tagText}
                      </Text>
                    </View>
                  );
                })}
              {/* <View style={{ alignItems: 'baseline' }}>
                <Text style={{ borderColor: 'green', borderWidth: 1, borderRadius: 5, fontWeight: '500', color: Colors.primaryColor, padding: 3, alignItems: 'baseline' }}>
                  Coffee Lover
                </Text>
              </View> */}
            </View>
            <TouchableOpacity style={{width:'30%'}} onPress={() => 
              {
                Alert.alert(
                  'Delete Segment Group',
                  `Are you sure you want to delete ${item.name}?`,
                  [
                    {
                      text: 'Cancel',
                      onPress: () => console.log('Deletion Cancelled'),
                      style: 'cancel',
                    },
                    {
                      text: 'Delete',
                      onPress: () => deleteSegmentGroup(item.uniqueId, item.name, item.crmUserTagIdList),
                      style: 'destructive',
                    },
                  ],
                  { cancelable: true }
                );  
              }
            }>
              <View
                style={{ flexDirection: 'row', alignItems: 'center', justifyContent:'flex-end' }}>
                <Icon name="trash-2" size={20} color={Colors.tabRed} />
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  const renderTags = ({ item, index }) => {
    return (
      <View
        style={{
          //backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          backgroundColor: '#FFFFFF',
          paddingVertical: 15,
          paddingHorizontal: 5,
          //paddingLeft: 1,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          // width: '100%',
        }}>
        <View style={{ flexDirection: 'row' }}>
          <View style={{ width: '30%' }}>
            <Text
              style={
                switchMerchant
                  ? {
                    fontWeight: '600',
                    color: Colors.primaryColor,
                    fontSize: 10,
                    fontFamily: 'NunitoSans-Regular',
                  }
                  : {
                    fontWeight: '600',
                    color: Colors.primaryColor,
                    fontSize: 14,
                    fontFamily: 'NunitoSans-Regular',
                  }
              }>
              {item.name}
            </Text>
          </View>
          <View style={{ width: '30%', marginLeft: 10, flexDirection: 'row' }}>
            {item.crmUserTagIdList && item.crmUserTagIdList.map((userTagId, i) => {
              var tagText = 'N/A';

              if (crmUserTagsDict[userTagId]) {
                tagText = item.name;
              }

              return (
                <View style={{ alignItems: 'baseline', marginRight: 5 }}>
                  <Text
                    style={
                      switchMerchant
                        ? {
                          borderColor: 'green',
                          borderWidth: 1,
                          borderRadius: 5,
                          fontWeight: '500',
                          color: Colors.primaryColor,
                          padding: 3,
                          paddingLeft: 4,
                          alignItems: 'baseline',
                          fontSize: 10,
                          fontFamily: 'NunitoSans-Regular',
                        }
                        : {
                          borderColor: 'green',
                          borderWidth: 1,
                          borderRadius: 5,
                          fontWeight: '500',
                          color: Colors.primaryColor,
                          padding: 3,
                          paddingLeft: 4,
                          alignItems: 'baseline',
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Regular',
                        }
                    }>
                    {tagText}
                  </Text>
                </View>
              );
            })}
            {/* <View style={{ alignItems: 'baseline' }}>
                <Text style={{ borderColor: 'green', borderWidth: 1, borderRadius: 5, fontWeight: '500', color: Colors.primaryColor, padding: 3, alignItems: 'baseline' }}>
                  Coffee Lover
                </Text>
              </View> */}
          </View>
        </View>
      </View>
    );
  };

  //   Create New Segment
  const createCRMSegmentOrAddTagToCRMSegment = async () => {
    if (!segmentName) {
      Alert.alert(
        'Info',
        'Segment name cannot be empty',
        [
          {
            text: 'OK',
            onPress: () => {
              //navigation.goBack();
            },
          },
        ],
        { cancelable: false },
      );
    } else {
      if (selectedSegmentEdit === null) {
        var isExisted = false;

        for (var i = 0; i < crmSegments.length; i++) {
          if (crmSegments[i].name === segmentName) {
            isExisted = true;
            break;
          }
        }

        if (isExisted) {
          Alert.alert(
            'Info',
            'Segment exists',
            [
              {
                text: 'OK',
                onPress: () => {
                  //navigation.goBack();
                },
              },
            ],
            { cancelable: false },
          );
        } else {
          var body = {
            merchantId: merchantId,
            segmentName: segmentName,

            crmUserTagIdList: selectedUserTagList,

            searchingUserTagText: searchingUserTagText,
          };

          CommonStore.update((s) => {
            s.isLoading = true;
          });

          // ApiClient.POST(
          //   API.createCRMSegmentOrAddTagToCRMSegment,
          //   body,
          //   false,
          // )
          APILocal.createCRMSegmentOrAddTagToCRMSegment({ body: body, uid: userId })
            .then((result) => {
              if (result && result.status === 'success') {
                Alert.alert(
                  'Success',
                  'Segment has been created',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        // navigation.goBack();
                        setAddSegmentScreen(ADD_SEGMENT_SCREEN.SEGMENT_LIST);
                      },
                    },
                  ],
                  { cancelable: false },
                );
              }

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            });
        }
      } else {
        var body = {
          merchantId: merchantId,
          segmentName: segmentName,

          crmUserTagIdList: selectedUserTagList,

          searchingUserTagText: searchingUserTagText,

          segmentId: selectedSegmentEdit.uniqueId,
        };

        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(
        //   API.updateCRMSegmentOrAddTagToCRMSegment,
        //   body,
        //   false,
        // )
        APILocal.updateCRMSegmentOrAddTagToCRMSegment({ body: body, uid: userId })
          .then((result) => {
            if (result && result.status === 'success') {
              Alert.alert(
                'Success',
                'Segment has been updated',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      // navigation.goBack();
                      setAddSegmentScreen(ADD_SEGMENT_SCREEN.SEGMENT_LIST);
                    },
                  },
                ],
                { cancelable: false },
              );
            }

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          });
      }
    }
  };

  /////Manage Filter

  ///////////////////////////////////////////////////////

  return (
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={6}
            expandCRM={true}
          />
        </View> */}
        <ScrollView showsVerticalScrollIndicator={false} style={{}}>
          <ScrollView
            horizontal={true}
            scrollEnabled={switchMerchant}
            showsHorizontalScrollIndicator={false}>
            <ModalView
              supportedOrientations={['landscape', 'portrait']}
              style={{ flex: 1 }}
              visible={visible}
              transparent={true}
              animationType="slide">
              <KeyboardAvoidingView
                behavior="padding"
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  // minHeight: windowHeight,
                }}>
                <View
                  style={[
                    styles.ManageFilterBox,
                    { borderRadius: 12, padding: 30, paddingHorizontal: 50, ...getTransformForModalInsideNavigation(), },
                  ]}>
                  <View
                    style={{
                      justifyContent: 'flex-end',
                      alignItems: 'flex-end',
                      marginTop: -15,
                      marginRight: -35,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        setVisible(false);
                      }}>
                      <AntDesign
                        name={'closecircle'}
                        size={25}
                        color={'#858C94'}
                      />
                    </TouchableOpacity>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      //marginTop: 10
                    }}>
                    <Text style={{ fontSize: 26, fontFamily: 'Nunitosans-Bold' }}>
                      Manage Filter
                    </Text>
                  </View>
                  <View
                    style={{
                      borderColor: '#E5E5E5',
                      borderWidth: 1,
                      marginHorizontal: -20,
                      marginBottom: 15,
                    }}
                  />

                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      height: 35,
                      marginVertical: 10,
                    }}>
                    <DropDownPicker
                      // controller={instance => controller = instance}
                      controller={(instance) => setController(instance)}
                      arrowColor={'#BDBDBD'}
                      arrowSize={23}
                      arrowStyle={{ paddingVertical: 0 }}
                      style={{
                        width: 180,
                        height: 35,
                        backgroundColor: '#F2F3F7',
                        borderRadius: 6,
                        paddingVertical: 0,
                      }}
                      itemStyle={{ justifyContent: 'flex-start' }}
                      placeholderStyle={{ color: '#B6B6B6' }}
                      //change value below (Eason)
                      items={[
                        { label: 'A', value: 1 },
                        { label: 'B', value: 2 },
                      ]}
                      placeholder={'Product Category'}
                      labelStyle={{ fontSize: 12.5 }}
                      onChangeItem={(selectedSort) => {
                        // setState({ sort: selectedSort }),
                        //sortingOrders(selectedSort);
                      }}
                    />
                    <DropDownPicker
                      // controller={instance => controller = instance}
                      controller={(instance) => setController(instance)}
                      arrowColor={'#BDBDBD'}
                      arrowSize={23}
                      arrowStyle={{ paddingVertical: 0 }}
                      style={{
                        width: 90,
                        backgroundColor: '#F2F3F7',
                        borderRadius: 6,
                        marginLeft: 20,
                        paddingVertical: 0,
                      }}
                      dropDownStyle={{
                        width: 90,
                        borderRadius: 6,
                        marginLeft: 20,
                        paddingVertical: 0,
                      }}
                      itemStyle={{ justifyContent: 'flex-start' }}
                      placeholderStyle={{ color: '#B6B6B6' }}
                      //change value below (Eason)
                      items={[
                        { label: 'A', value: 1 },
                        { label: 'B', value: 2 },
                      ]}
                      placeholder={'Is'}
                      labelStyle={{ fontSize: 12.5 }}
                      onChangeItem={(selectedSort) => {
                        // setState({ sort: selectedSort }),
                        //sortingOrders(selectedSort);
                      }}
                    //onOpen={() => controller1.close()}
                    />
                  </View>
                  <View
                    style={{
                      marginTop: 20,
                      height: 40,
                      zIndex: -1,
                    }}>
                    <TextInput
                      style={{
                        borderRadius: 5,
                        borderColor: '#E5E5E5',
                        borderWidth: 1,
                        height: 35,
                        width: 200,
                        backgroundColor: Colors.fieldtBgColor,
                        paddingLeft: 5,
                      }}
                      placeholder="Enter Condition"
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      placeholderStyle={{ paddingLeft: 5 }}
                      onChangeText={() => { }}
                    />
                  </View>
                  {/* <View style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 1,
                borderColor: '#E0E0E0',
                backgroundColor: '#F2F3F7',
                width: 100,
                height: 40,
                borderRadius: 6,
                marginTop: 15,
              }}>
                <Text style={{
                  fontSize: 12.5,
                  Color: '#B6B6B6',
                }}>
                  Package A
                </Text>
                <TouchableOpacity
                  onPress={() => {

                  }}
                  style={{
                    marginLeft: 5
                  }}
                >
                  <AntDesign name={"close"} size={16} color={'#B6B6B6'} />
                </TouchableOpacity>
              </View> */}

                  <View
                    style={{
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      marginVertical: 20,
                      marginHorizontal: -20,
                      zIndex: -1,
                    }}
                  />

                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                      alignItems: 'flex-end',
                      zIndex: -1,
                    }}>
                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        width: 110,
                        height: 40,
                        borderColor: '#0F1A3C',
                        borderRadius: 8,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        setVisible(false);
                      }}>
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: 17,
                          color: '#0F1A3C',
                          fontFamily: 'Nunitosans-Bold',
                        }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        width: 110,
                        height: 40,
                        borderColor: '#0F1A3C',
                        borderRadius: 8,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#0F1A3C',
                        marginLeft: 10,
                      }}
                      onPress={() => { }}>
                      <Text
                        style={{
                          color: Colors.primaryColor,
                          fontSize: 17,
                          color: '#FFFFFF',
                          fontFamily: 'Nunitosans-Bold',
                        }}>
                        Apply
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </KeyboardAvoidingView>
            </ModalView>

            <KeyboardAvoidingView
              style={
                {
                  //top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 1 : 0,
                }
              }>
              {addSegmentScreen === ADD_SEGMENT_SCREEN.SEGMENT_LIST ? (
                <View>
                  {/* <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              width: Platform.OS=='ios'?windowWidth * 0.87:windowWidth * 0.9,
              margin: 10, padding: 10
            }}>
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginTop: 10,
                width: '100%',
              }}>
                <View style={{ justifyContent: 'center' }}>
                  <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 30 }}>
                    Segments List
                  </Text>
                </View>

                <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                <TouchableOpacity
                   style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#0F1A3C',
                    borderRadius: 5,
                    //width: 160,
                    paddingHorizontal: 10,
                    height: 40,
                    alignItems: 'center',
                    shadowOffset: {
                        width: 0,
                        height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    marginRight: 10,
                }}
                  onPress={() => {
                    CommonStore.update(s => {
                      s.selectedSegmentEdit = null;
                    });

                    setAddSegmentScreen(ADD_SEGMENT_SCREEN.ADD_SEGMENT)
                  }}>
                      <AntDesign
                        name="pluscircle"
                        size={20}
                        color='#FFFFFF'
                      />
                    <Text style={{
                        color: Colors.whiteColor,
                        marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                    }}>
                      ADD SEGMENT
                    </Text>
                </TouchableOpacity>
                <View style={{
                  width: 250,
                  height: 40,
                  backgroundColor: 'white',
                  borderRadius: 5,
                  flexDirection: 'row',
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                  //marginTop: 10,
                  marginRight:Platform.OS=='ios'?0:'20%',
                }}>
                  <Icon name="search" size={18} color={Colors.primaryColor} style={{ marginLeft: 10 }} />
                  <TextInput
                    underlineColorAndroid={Colors.whiteColor}
                    style={{
                      width: 220,
                      fontSize: 15,
                      fontFamily: 'NunitoSans-Regular',
                      paddingLeft: 5,
                    }}
                    clearButtonMode="while-editing"
                    placeholder=' Search'
                    onChangeText={(text) => {
                      setSearch(text);
                    }}
                    defaultValue={search}
                  />
                </View>
                </View>

              </View>
            </View> */}
                  {/* <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              width: Platform.OS == 'ios' ? windowWidth * 0.87 : windowWidth * 0.9,
              margin: 10, padding: 10
            }}> */}
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginTop: 20,
                      marginBottom: 20,
                      width: switchMerchant
                        ? windowWidth * 0.8
                        : windowWidth * 0.87,
                      //marginTop: 30,
                      alignSelf: 'center',
                      alignItems: 'center',
                    }}>
                    <View style={{ justifyContent: 'center' }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 20 : 26,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Segments List
                      </Text>
                    </View>

                    <View
                      style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                      {/* <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: '#eb3446',
                          backgroundColor: '#eb3446',
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,

                          marginRight: 10,
                        }}
                        onPress={() => {
                          setIsDeleteON(!isDeleteON)
                        }}>
                        {!isDeleteON ?
                          <View
                            style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Icon name="trash-2" size={20} color={Colors.whiteColor} />
                          </View>
                          :
                          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              CANCEL
                            </Text>
                          </View>}
                      </TouchableOpacity> */}

                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          marginRight: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 15,
                        }}
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.selectedSegmentEdit = null;
                          });

                          setAddSegmentScreen(ADD_SEGMENT_SCREEN.ADD_SEGMENT);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_CRM_SEGMENT_C_NEW_SEGMENT,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_C_NEW_SEGMENT
                          })
                        }}>
                        <AntDesign
                          name="pluscircle"
                          size={switchMerchant ? 10 : 20}
                          style={{ color: Colors.whiteColor }}
                        />
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          {/* ADD SEGMENT */}
                          SEGMENT
                        </Text>
                      </TouchableOpacity>
                      {/* <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#0F1A3C',
                        borderRadius: 5,
                        //width: 160,
                        paddingHorizontal: 10,
                        marginRight: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: 15,
                      }}
                      onPress={() => {
                        CommonStore.update((s) => {
                          s.selectedSegmentEdit = null;
                        });

                        setAddSegmentScreen(ADD_SEGMENT_SCREEN.CREATE_TAG);
                      }}>
                      <AntDesign
                        name="pluscircle"
                        size={switchMerchant ? 10 : 20}
                        style={{color: Colors.whiteColor}}
                      />
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        TAG
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#0F1A3C',
                        borderRadius: 5,
                        //width: 160,
                        paddingHorizontal: 10,
                        marginRight: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: 15,
                      }}
                      onPress={() => {
                        CommonStore.update((s) => {
                          s.selectedSegmentEdit = null;
                        });

                      }}>
                      {/* <AntDesign
                        name="pluscircle"
                        size={switchMerchant ? 10 : 20}
                        style={{color: Colors.whiteColor}}
                      /> */}
                      {/*<Text
                        style={{
                          color: Colors.whiteColor,
                          marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        ALL TAGS
                      </Text>
                    </TouchableOpacity> */}

                      <View
                        style={{
                          width: switchMerchant ? 200 : 250,
                          height: switchMerchant ? 35 : 40,
                          backgroundColor: 'white',
                          borderRadius: 5,
                          flexDirection: 'row',
                          alignItems: 'center',
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          //marginTop: 10,
                        }}>
                        <Icon
                          name="search"
                          size={switchMerchant ? 10 : 18}
                          color={Colors.primaryColor}
                          style={{ marginLeft: 15 }}
                        />
                        <View style={{ flex: 4 }}>
                          <TextInput
                            underlineColorAndroid={Colors.whiteColor}
                            style={{
                              width: switchMerchant ? 160 : 220,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Regular',
                              paddingLeft: 5,
                              height: 45,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            clearButtonMode="while-editing"
                            placeholder=" Search"
                            onChangeText={(text) => {
                              setSearch(text);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_CRM_SEGMENT_TB_SEARCH,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_TB_SEARCH
                              })
                            }}
                            defaultValue={search}
                          />
                        </View>
                      </View>
                    </View>
                  </View>
                  {/* </View> */}
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: switchMerchant
                        ? windowWidth * 0.8
                        : windowWidth * 0.87,
                      height: switchMerchant
                        ? windowHeight * 0.55
                        : windowHeight * 0.65,
                      //marginTop: 30,
                      //marginHorizontal: 30,
                      //alignSelf: 'center',
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                      marginHorizontal: switchMerchant ? 30 : 35,
                      //marginBottom: switchMerchant ? 30 : 0,
                    }}>
                    <View
                      style={{
                        width: '40%',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                      }}>
                      {/* Filter bar  */}
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          width: '38%',
                        }}
                        onPress={() => {
                          //setVisible(true);
                        }}>
                        {/* <View style={{ justifyContent: 'center', width: '100%', flexDirection: 'row', borderWidth: 1, borderColor: '#0F1A3C', borderRadius: 3, height: 35, alignItems: 'center' }}>
                      <Feather name='filter' size={18} color='#0F1A3C' />
                      <Text style={{
                        color: '#0F1A3C',
                        fontSize: 14,
                        fontFamily: 'Nunitosans-bold',
                        marginLeft: 7
                      }}>
                        Manage Filter
                      </Text>
                    </View> */}
                      </TouchableOpacity>
                      {/* Type 1 Search Bar */}
                      {/* <View style={{ flexDirection: 'row', justifyContent: 'center', width: '53%', height: 40, alignItems: 'center', borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, alignSelf: 'center' }}>
                    <View style={{ flex: 3 }}>
                      <TextInput
                        placeholderTextColor='#737373'
                        style={{ marginLeft: 10, color: '#737373' }}
                        onChangeText={(text) => {
                          setSearch(text);
                        }}
                        defaultValue={search}
                      />
                    </View>
                    <View style={{ flex: 1, height: '100%', justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.primaryColor, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5 }}>
                      <Icon name='search' size={20} color={Colors.whiteColor} />
                    </View>
                  </View> */}

                      {/* Type 2 Search Bar */}
                    </View>

                    {/****************List View Here****************/}

                    <View style={{ width: '100%', marginTop: 0 }}>
                      <View
                        style={{
                          backgroundColor: Colors.whiteColor,
                          padding: 5,
                          paddingTop: 0,
                          height: '99%',
                          paddingHorizontal: 20,
                          borderRadius: 5,
                        }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            borderBottomWidth: 1,
                            borderColor: '#E5E5E5',
                            height: 40,
                            alignItems: 'center',
                          }}>
                          {/* {isDeleteON ?
                            <View style={{ width: '3%', alignItems: 'center', marginLeft: 5, }}>
                              <CheckBox
                                value={isSelectedAll} // 'Select All' checkbox state
                                onValueChange={(value) => {
                                  setIsSelectedAll(value);
                                }}
                              />
                            </View>
                            :
                            <></>} */}
                          <View
                            style={{
                              flexDirection: 'row',
                              width: '30%',
                              borderRightWidth: 0,
                              borderRightColor: 'lightgrey',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                              marginLeft: 0,
                            }}>
                            <View style={{ flexDirection: 'column' }}>
                              <TouchableOpacity
                                onPress={() => {
                                  if (
                                    currSummarySort ===
                                    SEGMENT_SORT_FIELD_TYPE.SEGMENT_GROUP_ASC
                                  ) {
                                    setCurrSummarySort(
                                      SEGMENT_SORT_FIELD_TYPE.SEGMENT_GROUP_DESC,
                                    );
                                  } else {
                                    setCurrSummarySort(
                                      SEGMENT_SORT_FIELD_TYPE.SEGMENT_GROUP_ASC,
                                    );
                                  }

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_CRM_SEGMENT_LIST_C_SEGMENT_GROUP,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_LIST_C_SEGMENT_GROUP
                                  })
                                }}>
                                <Text
                                  numberOfLines={1}
                                  style={{
                                    paddingLeft: 5,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Bold',
                                    color: 'black',
                                    fontWeight: '600',
                                  }}>
                                  Segment Group
                                </Text>
                                {/* <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text> */}
                              </TouchableOpacity>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 9 : 14}
                                color={
                                  currSummarySort ===
                                    SEGMENT_SORT_FIELD_TYPE.SEGMENT_GROUP_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                }></Entypo>

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 9 : 14}
                                color={
                                  currSummarySort ===
                                    SEGMENT_SORT_FIELD_TYPE.SEGMENT_GROUP_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                }></Entypo>
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              width: '40%',
                              borderRightWidth: 0,
                              borderRightColor: 'lightgrey',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                              marginLeft: 10,
                            }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={1}
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontFamily: 'NunitoSans-Bold',
                                  color: 'black',
                                  fontWeight: '600',
                                  width: 80,
                                }}>
                                Tags
                              </Text>
                              {/* <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text> */}
                            </View>
                          </View>
                        </View>
                        {list1 ? (
                          // <View style={{ borderTopWidth: 1 }}>
                          (<FlatList
                            ////THIS IS FOR SEARCH////
                            //   data={crmUsers
                            //     .filter(item => {
                            //       if (search !== '') {
                            //         const searchLowerCase = search.toLowerCase();

                            //         if (item.email.toLowerCase().includes(searchLowerCase) ||
                            //           item.name.toLowerCase().includes(searchLowerCase) ||
                            //           item.uniqueName.toLowerCase().includes(searchLowerCase) ||
                            //           item.number.toLowerCase().includes(searchLowerCase)) {
                            //           return true;
                            //         }

                            //         return false;
                            //       }
                            //       else {
                            //         return true;
                            //       }
                            //     }).slice((currentPage - 1) * perPage, currentPage * perPage)
                            //   }
                            showsVerticalScrollIndicator={false}
                            data={sortSegment(
                              crmSegments,
                              currSummarySort,
                            ).filter((item, i) => {
                              const searchLowerCase = search.toLowerCase();

                              if (
                                item.name.toLowerCase().includes(searchLowerCase)
                              ) {
                                return true;
                              }

                              if (item.crmUserTagIdList) {
                                for (
                                  var i = 0;
                                  i < item.crmUserTagIdList.length;
                                  i++
                                ) {
                                  var tagText = '';

                                  if (crmUserTagsDict[item.crmUserTagIdList[i]]) {
                                    tagText =
                                      crmUserTagsDict[item.crmUserTagIdList[i]]
                                        .name;
                                  }

                                  if (
                                    tagText
                                      .toLowerCase()
                                      .includes(searchLowerCase)
                                  ) {
                                    return true;
                                  }
                                }
                              } else {
                                return false;
                              }
                            })}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => String(index)}
                            style={{ marginTop: 0 }}
                          // initialNumToRender={4}
                          />)
                        ) : // </View>

                          null}
                      </View>
                    </View>
                    {/* <View style={{
              flexDirection: 'row',
              marginTop: 10,
              width: '100%',
              alignItems: 'center',
              justifyContent: 'flex-end',
              top: Platform.OS == 'ios' ? pushPagingToTop && keyboardHeight > 0 ? -keyboardHeight * 0.94 : 0 : 0,
              backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
              borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
              borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
              borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
              paddingHorizontal: pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
              shadowOffset: {
                width: 0,
                height: 1,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
            }}>
              <Text style={{ marginRight: '1%' }}>Page</Text>
              <View style={{
                width: 65,
                height: 35,
                backgroundColor: Colors.whiteColor,
                borderRadius: 10,
                justifyContent: 'center',
                paddingHorizontal: 22,
                borderWidth: 1,
                borderColor: '#E5E5E5',
              }}>
                {console.log('currentPage')}
                {console.log(currentPage)}

                <TextInput
                  onChangeText={(text) => {
                    var currentPageTemp = text.length > 0 ? parseInt(text) : 1;

                    setCurrentPage(currentPageTemp > pageCount ? pageCount : (currentPageTemp < 1 ? 1 : currentPageTemp));

                  }}
                  placeholder={currentPage.toString()}
                  placeholderTextColor={'black'}
                  style={{
                    color: Colors.fontDark,
                    fontFamily: 'NunitoSans-SemiBold',
                    fontSize: 14,
                    marginTop: Platform.OS === 'ios' ? 0 : -15,
                    marginBottom: Platform.OS === 'ios' ? 0 : -15,
                    textAlign: 'center',
                    width: '100%',
                  }}
                  value={currentPage.toString()}
                  defaultValue={currentPage.toString()}
                  keyboardType={'numeric'}
                  onFocus={() => { setPushPagingToTop(true) }}
                />
              </View>
              <Text style={{ marginLeft: '1%', marginRight: '1%' }}>of {pageCount}</Text>
              <TouchableOpacity style={{ width: 45, height: 28, backgroundColor: Colors.primaryColor, alignItems: 'center', justifyContent: "center" }}
                onPress={() => { prevPage() }}>
                <MaterialIcons name='keyboard-arrow-left' size={25} style={{ color: Colors.whiteColor }} />
              </TouchableOpacity>
              <TouchableOpacity style={{ width: 45, height: 28, backgroundColor: Colors.primaryColor, alignItems: 'center', justifyContent: "center" }} onPress={() => { nextPage() }}>
                <MaterialIcons name='keyboard-arrow-right' size={25} style={{ color: Colors.whiteColor }} />
              </TouchableOpacity>
            </View> */}
                  </View>
                  <View
                    style={{
                      position: 'relative',
                      //left: 160,
                      flexDirection: 'row',
                      marginTop: 10,
                      width: switchMerchant
                        ? windowWidth * 0.8
                        : windowWidth * 0.87,
                      alignItems: 'center',
                      alignSelf: 'center',
                      justifyContent: 'flex-end',
                      top:
                        Platform.OS == 'ios'
                          ? pushPagingToTop && keyboardHeight > 0
                            ? -keyboardHeight * 1
                            : 0
                          : 0,
                      // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                      // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                      // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                      borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                      paddingHorizontal:
                        pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                      // shadowOffset: {
                      //   width: 0,
                      //   height: 1,
                      // },
                      // shadowOpacity: 0.22,
                      // shadowRadius: 3.22,
                      // elevation: 1,
                      marginBottom: switchMerchant
                        ? windowHeight * 0.1
                        : 10,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginRight: '1%',
                      }}>
                      Page
                    </Text>
                    <View
                      style={{
                        width: switchMerchant ? 65 : 70,
                        height: switchMerchant ? 20 : 35,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 10,
                        justifyContent: 'center',
                        paddingHorizontal: 22,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                      }}>
                      {console.log('currentPage')}
                      {console.log(currentPage)}

                      <TextInput
                        onChangeText={(text) => {
                          var currentPageTemp =
                            text.length > 0 ? parseInt(text) : 1;

                          setCurrentPage(
                            currentPageTemp > pageCount
                              ? pageCount
                              : currentPageTemp < 1
                                ? 1
                                : currentPageTemp,
                          );

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_CRM_SEGMENT_TB_PAGE,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_TB_PAGE
                          })
                        }}
                        placeholder={currentPage.toString()}
                        placeholderStyle={{
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                        }}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={{
                          color: 'black',
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          marginTop: Platform.OS === 'ios' ? 0 : -15,
                          marginBottom: Platform.OS === 'ios' ? 0 : -15,
                          textAlign: 'center',
                          width: '100%',
                        }}
                        value={currentPage.toString()}
                        defaultValue={currentPage.toString()}
                        keyboardType={'numeric'}
                        onFocus={() => {
                          setPushPagingToTop(true);
                        }}
                      />
                    </View>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Bold',
                        marginLeft: '1%',
                        marginRight: '1%',
                      }}>
                      of {pageCount}
                    </Text>
                    <TouchableOpacity
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        prevPage();

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_CRM_SEGMENT_C_PREV_BUTTON,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_C_PREV_BUTTON
                        })


                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-left"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{
                        width: switchMerchant ? 30 : 45,
                        height: switchMerchant ? 20 : 28,
                        backgroundColor: Colors.primaryColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        nextPage();

                        logEventAnalytics({
                          eventName: ANALYTICS.MODULE_CRM_SEGMENT_C_NEW_SEGMENT,
                          eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_C_NEW_SEGMENT
                        })
                      }}>
                      <MaterialIcons
                        name="keyboard-arrow-right"
                        size={switchMerchant ? 20 : 25}
                        style={{ color: Colors.whiteColor }}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              ) : null}

              {/* **********************************New Segment Page*********************************** */}
              {addSegmentScreen === ADD_SEGMENT_SCREEN.ADD_SEGMENT ? (
                <View style={{ marginTop: 15 }}>
                  <TouchableOpacity
                    style={
                      switchMerchant
                        ? {
                          marginLeft: 25,
                          flexDirection: 'row',
                          //alignContent: 'center',
                          //alignItems: 'center',
                          marginHorizontal: 30,
                          //alignItems: 'center'
                        }
                        : {
                          marginLeft: 20,
                          flexDirection: 'row',
                          marginHorizontal: 30,
                          alignItems: 'center',
                        }
                    }
                    onPress={() => {
                      setAddSegmentScreen(ADD_SEGMENT_SCREEN.SEGMENT_LIST);

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_CRM_SEGMENT_ADD_SEGMENT_C_BACK,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_ADD_SEGMENT_C_BACK
                      })
                    }}>
                    <Icon
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 17,
                        color: Colors.primaryColor,
                        bottom: switchMerchant
                          ? 0
                          : Platform.OS === 'android'
                            ? 1
                            : 0,
                      }}>
                      Back
                    </Text>
                  </TouchableOpacity>
                  <View
                    style={[
                      switchMerchant
                        ? [styles.list1, { marginTop: 10, padding: 20 }]
                        : [
                          styles.list,
                          { marginTop: 15, padding: 20, marginBottom: 5, },
                        ],
                      {
                        width: Dimensions.get('window').width * 0.87,
                        height: Dimensions.get('window').height * 0.65,
                      }
                    ]}>
                    <View
                      style={{
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'Nunitosans-bold',
                          fontSize: switchMerchant ? 15 : 20,
                          position: 'relative',
                          left: 10,
                        }}>
                        Add Segment
                      </Text>

                      <TouchableOpacity
                        style={{
                          position: 'relative',
                          right: 10,
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: switchMerchant ? 80 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 30 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        disabled={isLoading}
                        onPress={() => {
                          createCRMSegmentOrAddTagToCRMSegment();
                        }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                          }}>
                          {isLoading ? (
                            <ActivityIndicator
                              color={Colors.whiteColor}
                              size={'small'}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {selectedSegmentEdit ? 'UPDATE' : 'SAVE'}
                            </Text>
                          )}
                        </View>
                      </TouchableOpacity>
                    </View>
                    <View
                      style={{
                        borderColor: '#E5E5E5',
                        borderWidth: 1,
                        marginVertical: 15,
                      }}
                    />

                    <View
                      style={{
                        alignItems: 'center',
                        marginTop: switchMerchant ? 10 : 15,
                      }}>
                      <View
                        style={{
                          width: switchMerchant
                            ? windowWidth * 0.6
                            : windowWidth * 0.49,
                          height: windowHeight * 0.4,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }}>
                        <View>
                          <View>
                            <View
                              style={[
                                styles.modalView,
                                {
                                  //height: windowWidth * 0.3,
                                  // top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                                  backgroundColor: 'transparent',
                                  padding: 20,
                                },
                              ]}>
                              {/* <View style={{ marginBottom: 8 }}>
                              <Text style={{ fontWeight: '600', fontSize: switchMerchant ? 20 : 24 }}>
                                Segment
                              </Text>
                            </View> */}

                              <View
                                style={{
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  marginBottom: 15,
                                }}>
                                <Text
                                  style={{
                                    fontWeight: '500',
                                    marginRight: 10,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  Segment Name:
                                </Text>
                                <TextInput
                                  placeholder={'Segment Name'}
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: switchMerchant
                                      ? windowWidth * 0.17
                                      : windowWidth * 0.15,
                                    height: switchMerchant ? 35 : 40,
                                    borderRadius: 5,
                                    padding: 5,
                                    marginVertical: 5,
                                    borderWidth: 1,
                                    borderColor: '#E5E5E5',
                                    paddingLeft: 10,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  //iOS
                                  // clearTextOnFocus={true}
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(segmentName)
                                  //   setSegmentName('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (segmentName == '') {
                                  //     setSegmentName(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setSegmentName(text);

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_CRM_SEGMENT_ADD_SEGMENT_TB_SEGMENT_NAME,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_ADD_SEGMENT_TB_SEGMENT_NAME
                                    })
                                  }}
                                  defaultValue={segmentName}
                                />
                              </View>

                              <View
                                style={{
                                  //backgroundColor: Colors.fieldtBgColor,
                                  //width: 180,
                                  height: switchMerchant ? 35 : 45,
                                  borderRadius: 5,
                                  paddingVertical: 3,
                                  paddingLeft: 0,
                                  marginVertical: 5,
                                  //borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  //width: '100%'
                                  paddingTop: 10,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    zIndex: 1,
                                    marginLeft: 0,
                                  }}>
                                  <AntDesign
                                    name="tags"
                                    size={switchMerchant ? 15 : 24}
                                    style={{ color: Colors.primaryColor }}
                                  />
                                  <DropDownPicker
                                    containerStyle={{
                                      height: switchMerchant ? 35 : 40,
                                      zIndex: 2,
                                      marginLeft: 5,
                                    }}
                                    arrowColor={'black'}
                                    arrowSize={switchMerchant ? 10 : 20}
                                    arrowStyle={{ fontWeight: 'bold' }}
                                    labelStyle={{
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    style={{
                                      width: switchMerchant
                                        ? windowWidth * 0.17
                                        : windowWidth * 0.15,
                                      paddingVertical: 0,
                                      backgroundColor: 'white',
                                      borderRadius: 5,
                                      borderWidth: 1,
                                    }}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    items={userTagDropdownList}
                                    itemStyle={{
                                      justifyContent: 'flex-start',
                                      marginLeft: 5,
                                      zIndex: 2,
                                      width: 180,
                                    }}
                                    placeholder={'Tag(s)'}
                                    customTickIcon={(press) => (
                                      <View style={{ alignItems: 'center', width: '35%' }}>
                                        <Ionicon
                                          name={'checkbox-outline'}
                                          color={
                                            press
                                              ? Colors.fieldtBgColor
                                              : Colors.primaryColor
                                          }
                                          size={switchMerchant ? 20 : 25}
                                        // style={{width: '100%'}}
                                        />
                                      </View>
                                    )}
                                    onChangeItem={(items) => {
                                      setSelectedUserTagList(items);
                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_CRM_SEGMENT_ADD_SEGMENT_TAGS_C_TAG_SELECT,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_ADD_SEGMENT_TAGS_C_TAG_SELECT
                                      })
                                    }}
                                    onSearch={text => {
                                      setSearchingUserTagText(text);
                                    }}
                                    multiple={true}
                                    searchable={true}
                                    textInput={{ paddingLeft: 5 }}
                                    searchableStyle={{ paddingLeft: 6 }}
                                    multipleText={'%d tag(s) selected'}
                                    defaultValue={selectedUserTagList}
                                    dropDownMaxHeight={180}
                                    dropDownStyle={{
                                      width: switchMerchant
                                        ? windowWidth * 0.17
                                        : windowWidth * 0.15,
                                      height: switchMerchant ? 150 : 250,
                                      borderRadius: 5,
                                      borderWidth: 1,
                                      textAlign: 'left',
                                      zIndex: 2,
                                      borderColor: '#E5E5E5',
                                    }}

                                  // style={{
                                  //   height: 45,
                                  //   width: 205,
                                  //   marginLeft: 5,
                                  //   paddingVertical: 0,
                                  //   borderColor: '#E5E5E5',
                                  // }}
                                  //   dropDownStyle={{ marginLeft: 5, marginTop: 0, borderWidth: 1.5, borderColor: '#E5E5E5', width: 205, }}
                                  //   arrowSize={20}
                                  //   arrowStyle={{ paddingVertical: -10 }}
                                  //   items={userTagDropdownList}
                                  //   onChangeItem={(items) => {
                                  //     setSelectedUserTagList(items);
                                  //   }}
                                  //   placeholder={'Select Tag(s)'}
                                  //   defaultValue={selectedUserTagList}
                                  //   multiple={true}
                                  //   searchable={true}
                                  //   dropDownMaxHeight={210}
                                  //   // onSearch={text => {
                                  //   //   setSearchingUserTagText(text);
                                  //   // }}
                                  //   customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                                  />
                                </View>

                                {/* <View style={{
                                            flexDirection: 'row',
                                            justifyContent: 'flex-end',
                                            alignItems: 'center',
                                            marginLeft: 5
                                            //zIndex: -1,
                                        }}>
                                            <TouchableOpacity
                                            style={{
                                                backgroundColor: Colors.primaryColor, width: 65, height: 34, justifyContent: 'center', alignItems: 'center', borderRadius: 5,

                                                flexDirection: 'row',
                                            }}
                                            disabled={isLoading}
                                            onPress={createCRMUserTag}
                                            //onPress={createCRMUserTagOrAddUserToCRMUserTag}
                                            >
                                            <Text style={{ fontSize: 17, fontWeight: '600', color: 'white' }}>
                                                {isLoading ? '' : 'Add'}
                                            </Text>

                                            {isLoading ?
                                                <ActivityIndicator color={Colors.whiteColor} size={'small'} />
                                                : <></>
                                            }
                                            </TouchableOpacity>
                                        </View> */}
                              </View>
                            </View>
                            <View
                              style={{
                                marginVertical: 12,
                                zIndex: -1,
                              }}>
                              {/* <FlatList
                            data={userTagList}
                            renderItem={renderUserTag}
                            keyExtractor={(item, index) => String(index)}
                            style={{
                              paddingVertical: 5,
                              paddingHorizontal: 5,
                            }}
                            horizontal={true}
                            showsHorizontalScrollIndicator={false}
                          /> */}
                              {/* <Text style={{ fontSize: 14, fontFamily: 'Nunitosans-Regular', color: "black", fontFamily: 'Nunitosans-Regular' }}>
                                {
                                    userTagList.length > 0
                                    ?
                                    userTagList.map(tag => tag.name).join(', ')
                                    :
                                    'No tags yet.'
                                }
                                </Text> */}
                              {/* <View style={{ flexDirection: 'row'}}>
                            <Text>
                              {segmentName}
                            </Text>

                            <Text>
                              {selectedUserTagList}
                            </Text>
                          </View> */}
                            </View>
                          </View>
                        </View>
                      </View>

                    </View>
                  </View>
                  <View
                    style={
                      switchMerchant
                        ? {
                          height: 60,
                        }
                        : {}
                    }></View>
                </View>
              ) : null}

              {/* **********************************Create Tag Page*********************************** */}
              {addSegmentScreen === ADD_SEGMENT_SCREEN.CREATE_TAG ? (
                <View style={{ marginTop: 15 }}>
                  <TouchableOpacity
                    style={
                      switchMerchant
                        ? {
                          marginLeft: 25,
                          flexDirection: 'row',
                          //alignContent: 'center',
                          //alignItems: 'center',
                          marginHorizontal: 30,
                          //alignItems: 'center'
                        }
                        : {
                          marginLeft: 20,
                          flexDirection: 'row',
                          marginHorizontal: 30,
                          alignItems: 'center',
                        }
                    }
                    onPress={() => {
                      setAddSegmentScreen(ADD_SEGMENT_SCREEN.SEGMENT_LIST);
                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_CRM_SEGMENT_ADD_SEGMENT_C_BACK,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_CRM_SEGMENT_ADD_SEGMENT_C_BACK
                      })

                    }}>
                    <Icon
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 14 : 17,
                        color: Colors.primaryColor,
                        bottom: switchMerchant
                          ? 0
                          : Platform.OS === 'android'
                            ? 1
                            : 0,
                      }}>
                      Back
                    </Text>
                  </TouchableOpacity>
                  <View
                    style={
                      switchMerchant
                        ? [styles.list1, { marginTop: 10, padding: 20 }]
                        : [
                          styles.list,
                          { marginTop: 15, padding: 20, marginBottom: 5, },
                        ]
                    }>
                    <View
                      style={{
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'Nunitosans-bold',
                          fontSize: switchMerchant ? 15 : 20,
                          position: 'relative',
                          left: 10,
                        }}>
                        Create Tag
                      </Text>

                      <TouchableOpacity
                        style={{
                          position: 'relative',
                          right: 10,
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: switchMerchant ? 80 : 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 30 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        disabled={isLoading}
                        onPress={() => {
                          createCRMSegmentOrAddTagToCRMSegment();
                        }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                          }}>
                          {isLoading ? (
                            <ActivityIndicator
                              color={Colors.whiteColor}
                              size={'small'}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              {selectedSegmentEdit ? 'UPDATE' : 'SAVE'}
                            </Text>
                          )}
                        </View>
                      </TouchableOpacity>
                    </View>
                    <View
                      style={{
                        borderColor: '#E5E5E5',
                        borderWidth: 1,
                        marginVertical: 15,
                      }}
                    />

                    <View
                      style={{
                        alignItems: 'center',
                        marginTop: switchMerchant ? 10 : 15,
                      }}>
                      <View
                        style={{
                          width: switchMerchant
                            ? windowWidth * 0.6
                            : windowWidth * 0.49,
                          height: windowHeight * 0.4,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                        }}>
                        <View>
                          <View>
                            <View
                              style={[
                                styles.modalView,
                                {
                                  //height: windowWidth * 0.3,
                                  // top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                                  backgroundColor: 'transparent',
                                  padding: 20,
                                },
                              ]}>
                              {/* <View style={{ marginBottom: 8 }}>
                            <Text style={{ fontWeight: '600', fontSize: switchMerchant ? 20 : 24 }}>
                              Segment
                            </Text>
                          </View> */}

                              <View
                                style={{
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  marginBottom: 15,
                                }}>
                                <Text
                                  style={{
                                    fontWeight: '500',
                                    marginRight: 10,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  Tag Name:
                                </Text>
                                <TextInput
                                  placeholder={'Enter a name'}
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: switchMerchant
                                      ? windowWidth * 0.17
                                      : windowWidth * 0.15,
                                    height: switchMerchant ? 35 : 40,
                                    borderRadius: 5,
                                    padding: 5,
                                    marginVertical: 5,
                                    borderWidth: 1,
                                    borderColor: '#E5E5E5',
                                    paddingLeft: 10,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  onChangeText={(text) => {
                                    setSegmentName(text);
                                  }}
                                  defaultValue={segmentName}
                                />
                              </View>

                              <Text
                                style={{
                                  fontWeight: '500',
                                  marginRight: 10,
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Existing Tags
                              </Text>
                              <View
                                style={{
                                  //backgroundColor: Colors.fieldtBgColor,
                                  //width: 180,
                                  height: windowHeight * 0.25,
                                  borderRadius: 5,
                                  paddingVertical: 3,
                                  paddingLeft: 0,
                                  marginVertical: 5,
                                  //borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  //width: '100%'
                                  paddingTop: 10,
                                }}>
                                <FlatList
                                  showsVerticalScrollIndicator={false}
                                  data={sortSegment(crmSegments, currSummarySort,).filter((item, i) => {
                                    const searchLowerCase = search.toLowerCase();

                                    if (
                                      item.name.toLowerCase().includes(searchLowerCase)
                                    ) {
                                      return true;
                                    }

                                    if (item.crmUserTagIdList) {
                                      for (
                                        var i = 0;
                                        i < item.crmUserTagIdList.length;
                                        i++
                                      ) {
                                        var tagText = '';

                                        if (crmUserTagsDict[item.crmUserTagIdList[i]]) {
                                          tagText = crmUserTagsDict[item.crmUserTagIdList[i]].name;
                                        }

                                        if (
                                          tagText
                                            .toLowerCase()
                                            .includes(searchLowerCase)
                                        ) {
                                          return true;
                                        }
                                      }
                                    } else {
                                      return false;
                                    }
                                  })}
                                  renderItem={renderTags}
                                  keyExtractor={(item, index) => String(index)}
                                  style={{ marginTop: 0 }}
                                // initialNumToRender={4}
                                />
                                {/* <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                zIndex: 1,
                                marginLeft: 0,
                              }}>
                              <AntDesign
                                name="tags"
                                size={switchMerchant ? 15 : 24}
                                style={{color: Colors.primaryColor}}
                              />
                              <DropDownPicker
                                containerStyle={{
                                  height: switchMerchant ? 35 : 40,
                                  zIndex: 2,
                                  marginLeft: 5,
                                }}
                                arrowColor={'black'}
                                arrowSize={switchMerchant ? 10 : 20}
                                arrowStyle={{fontWeight: 'bold'}}
                                labelStyle={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                style={{
                                  width: switchMerchant
                                    ? windowWidth * 0.17
                                    : windowWidth * 0.15,
                                  paddingVertical: 0,
                                  backgroundColor: 'white',
                                  borderRadius: 5,
                                  borderWidth: 1,
                                }}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}
                                items={userTagDropdownList}
                                itemStyle={{
                                  justifyContent: 'flex-start',
                                  marginLeft: 5,
                                  zIndex: 2,
                                  width: 180,
                                }}
                                placeholder={'Tag(s)'}
                                customTickIcon={(press) => (
                                  <Ionicon
                                    name={'checkbox-outline'}
                                    color={
                                      press
                                        ? Colors.fieldtBgColor
                                        : Colors.primaryColor
                                    }
                                    size={switchMerchant ? 20 : 25}
                                  />
                                )}
                                onChangeItem={(items) => {
                                  setSelectedUserTagList(items);
                                }}
                                onSearch={text => {
                                  setSearchingUserTagText(text);
                                }}
                                multiple={true}
                                searchable={true}
                                textInput={{paddingLeft: 5}}
                                searchableStyle={{paddingLeft: 6}}
                                multipleText={'%d segment(s) selected'}
                                defaultValue={selectedUserTagList}
                                dropDownMaxHeight={180}
                                dropDownStyle={{
                                  width: switchMerchant
                                    ? windowWidth * 0.17
                                    : windowWidth * 0.15,
                                  height: switchMerchant ? 150 : 250,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  textAlign: 'left',
                                  zIndex: 2,
                                  borderColor: '#E5E5E5',
                                }}

                                // style={{
                                //   height: 45,
                                //   width: 205,
                                //   marginLeft: 5,
                                //   paddingVertical: 0,
                                //   borderColor: '#E5E5E5',
                                // }}
                                //   dropDownStyle={{ marginLeft: 5, marginTop: 0, borderWidth: 1.5, borderColor: '#E5E5E5', width: 205, }}
                                //   arrowSize={20}
                                //   arrowStyle={{ paddingVertical: -10 }}
                                //   items={userTagDropdownList}
                                //   onChangeItem={(items) => {
                                //     setSelectedUserTagList(items);
                                //   }}
                                //   placeholder={'Select Tag(s)'}
                                //   defaultValue={selectedUserTagList}
                                //   multiple={true}
                                //   searchable={true}
                                //   dropDownMaxHeight={210}
                                //   // onSearch={text => {
                                //   //   setSearchingUserTagText(text);
                                //   // }}
                                //   customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                              />
                            </View> */}

                                {/* <View style={{
                                          flexDirection: 'row',
                                          justifyContent: 'flex-end',
                                          alignItems: 'center',
                                          marginLeft: 5
                                          //zIndex: -1,
                                      }}>
                                          <TouchableOpacity
                                          style={{
                                              backgroundColor: Colors.primaryColor, width: 65, height: 34, justifyContent: 'center', alignItems: 'center', borderRadius: 5,

                                              flexDirection: 'row',
                                          }}
                                          disabled={isLoading}
                                          onPress={createCRMUserTag}
                                          //onPress={createCRMUserTagOrAddUserToCRMUserTag}
                                          >
                                          <Text style={{ fontSize: 17, fontWeight: '600', color: 'white' }}>
                                              {isLoading ? '' : 'Add'}
                                          </Text>

                                          {isLoading ?
                                              <ActivityIndicator color={Colors.whiteColor} size={'small'} />
                                              : <></>
                                          }
                                          </TouchableOpacity>
                                      </View> */}
                              </View>
                            </View>
                            <View
                              style={{
                                marginVertical: 12,
                                zIndex: -1,
                              }}>
                              {/* <FlatList
                          data={userTagList}
                          renderItem={renderUserTag}
                          keyExtractor={(item, index) => String(index)}
                          style={{
                            paddingVertical: 5,
                            paddingHorizontal: 5,
                          }}
                          horizontal={true}
                          showsHorizontalScrollIndicator={false}
                        /> */}
                              {/* <Text style={{ fontSize: 14, fontFamily: 'Nunitosans-Regular', color: "black", fontFamily: 'Nunitosans-Regular' }}>
                              {
                                  userTagList.length > 0
                                  ?
                                  userTagList.map(tag => tag.name).join(', ')
                                  :
                                  'No tags yet.'
                              }
                              </Text> */}
                              {/* <View style={{ flexDirection: 'row'}}>
                          <Text>
                            {segmentName}
                          </Text>

                          <Text>
                            {selectedUserTagList}
                          </Text>
                        </View> */}
                            </View>
                          </View>
                        </View>
                      </View>

                    </View>
                  </View>
                  <View
                    style={
                      switchMerchant
                        ? {
                          height: 60,
                        }
                        : {}
                    }></View>
                </View>
              ) : null}
            </KeyboardAvoidingView>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    height: Dimensions.get('window').height * 0.65,
    //marginTop: 30,
    marginHorizontal: 30,
    //alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  list1: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.8,
    height: Dimensions.get('window').height * 0.7,
    //marginTop: 30,
    marginHorizontal: 30,
    marginBottom: 60,
    //alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  titleList: {
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 20,
    //marginTop: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,

    alignItems: 'center',

    // shadowOpacity: 0,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.22,
    // shadowRadius: 3.22,
    // elevation: 3,
  },
  textTitle: {
    fontSize: 16,
    fontFamily: 'NunitoSans-Bold',
  },
  textItem: {
    fontSize: 14,
    fontFamily: 'NunitoSans-Regular',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    // alignContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 20,
    paddingTop: 15,
    marginTop: 0,
    width: '100%',

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 7,
  },
  addVoucher: {
    marginTop: 0,
    //justifyContent: 'center',
    alignItems: 'center',
    //alignContent: 'center',
    width: Dimensions.get('window').width * 0.78,
    backgroundColor: Colors.whiteColor,
    // marginRight: 100,

    borderRadius: 4,

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  textInput1: {
    width: 250,
    height: 40,
    backgroundColor: 'white',
    borderRadius: 10,
    // marginLeft: '53%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',

    marginRight: Dimensions.get('window').width * Styles.sideBarWidth,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 10,
    alignSelf: 'flex-end',
    width: '60%',
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: '#0F1A3C',
    borderRadius: 5,
    width: 160,
    height: 40,
    alignItems: 'center',
    marginRight: Platform.OS == 'ios' ? 15 : 0,
  },
  editButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: '#0F1A3C',
    borderRadius: 5,
    width: 74,
    height: 35,
    alignItems: 'center',
  },
  ManageFilterBox: {
    //width: windowWidth * 0.4,
    //height: windowHeight * 0.7,
    //borderRadius: 10,
    backgroundColor: Colors.whiteColor,
    //justifyContent: 'space-between'
  },
  ManageFilterBox1: {
    width: Dimensions.get('window').width * 0.35,
    height: Dimensions.get('window').height * 0.55,
    //borderRadius: 10,
    backgroundColor: Colors.whiteColor,
    justifyContent: 'center',
    borderColor: '#E5E5E5',
    borderWidth: 1,
    alignItems: 'center',
    alignSelf: 'center',
    //padding: 10,
    margin: 15,
  },
});

export default SegmentScreen;
