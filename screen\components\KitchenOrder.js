import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Button,
  Modal as ModalComponent,
  TextInput,
  Animated,
  Easing,
  Picker,
  ActivityIndicator,
  Dimensions,
  Platform,
  useWindowDimensions,
  InteractionManager,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../../constant/Colors';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Styles from '../../constant/Styles';
import Countdown from '../../assets/svg/countdown.svg';
import { OutletStore } from '../../store/outletStore';
import {
  ORDER_TYPE,
  TICK_LIST_TYPE,
  LALAMOVE_STATUS_PARSED,
  COURIER_INFO_DICT,
  COURIER_CODE,
  COURIER_DROPDOWN_LIST,
  MRSPEEDY_STATUS_PARSED,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  PRIVILEGES_NAME,
  ROLE_TYPE,
  KD_ITEM_STATUS,
  KD_PRINT_EVENT_TYPE,
} from '../../constant/common';
import moment from 'moment';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { PageStore } from '../../store/pageStore';
import ApiClient from '../../util/ApiClient';
import API from '../../constant/API';
import CheckBox from '@react-native-community/checkbox';
import { CommonStore } from '../../store/commonStore';
import {
  getTransformForModalInsideNavigation,
  isTablet,
  logToFile
} from '../../util/common';
import { useNetInfo } from "@react-native-community/netinfo";
import APILocal from '../../util/apiLocalReplacers';
import { MerchantStore } from '../../store/merchantStore';
import AntDesign from 'react-native-vector-icons/AntDesign';
import SmoothPicker from 'react-native-smooth-picker';
import { UserStore } from '../../store/userStore';
import { calcPrintTotalForKdIndividualCancelled, printDocketCancelled, printDocketForKDCancelled, printKDSummaryCategoryWrapper, printUserOrder } from "../../util/printer";
import { PRINTER_USAGE_TYPE } from "../../constant/printer";
import firestore from '@react-native-firebase/firestore';
import { Collections } from "../../constant/firebase";
import { TableStore } from "../../store/tableStore";
// import { storageMMKV } from "../../util/storageMMKV";
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const KitchenOrder = (props) => {
  const {
    navigation,
    viewOptions,
    // checkOrderItem, // no need first
    checkSelectedItem,
    completeOrder,
    setScroll,
    search,
    scroll,
    order, // after order changed, should reset checklist to empty
    selectedCat,
    onTimesUp,
    refreshRate,
  } = props;

  const netInfo = useNetInfo();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [countdown, setCountdown] = useState(-1);
  const [countdownInterval, setCountdownInterval] = useState(null);
  const [orderItems, setOrderItems] = useState([]);
  const [isLoadingLocal, setIsLoadingLocal] = useState(false);

  const [orderEstimatedTime, setOrderEstimatedTime] = useState(0);

  // const [currTickListType, setCurrTickListType] = useState(TICK_LIST_TYPE.UNTICK);

  // const [fakeCurrentDate, setFakeCurrentDate] = useState(new Date());

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const userOrdersExpandedDict = OutletStore.useState(
    (s) => s.userOrdersExpandedDict,
  );
  const outletTablesDict = OutletStore.useState((s) => s.outletTablesDict);

  const kitchenScrollItem = PageStore.useState((s) => s.kitchenScrollItem);

  //////////////////////////////////////////////////////////////////////////////

  const [selectedOrderItemList, setSelectedOrderItemList] = useState([]);
  const [selectedOrderItemCancelledList, setSelectedOrderItemCancelledList] =
    useState([]);
  const [selectedOrderItemDeliveredList, setSelectedOrderItemDeliveredList] =
    useState([]);
  // const [selectedOrder, setSelectedOrder] = useState({});

  const [selectedOrderItemCheckDict, setSelectedOrderItemCheckDict] = useState(
    {},
  );
  const [
    selectedOrderItemCancelledCheckDict,
    setSelectedOrderItemCancelledCheckDict,
  ] = useState({});
  const [
    selectedOrderItemDeliveredCheckDict,
    setSelectedOrderItemDeliveredCheckDict,
  ] = useState({});

  //////////////////////////////////////////////////////////////////////////////

  // animations

  const [animAppear, setAnimAppear] = useState(new Animated.Value(0));

  const animAppearEntity = Animated.timing(animAppear, {
    toValue: 1,
    duration: 1000,
    useNativeDriver: true,
  });

  const [deliverModal, setDeliverModal] = useState(false);
  const [cancelModal, setCancelModal] = useState(false);

  const selectedOutletCategoryList = CommonStore.useState(s => s.selectedOutletCategoryList);

  //////////////////////////////////////////////////////////////////////////////  

  const opacities = {
    0: 1,
    1: 1,
    2: 0.6,
    3: 0.3,
    4: 0.1,
  };
  const sizeText = {
    0: !isTablet() ? 14 : 20,
    1: !isTablet() ? 10 : 15,
    2: !isTablet() ? 6 : 10,
  };

  const SPItem = React.memo(({ opacity, selected, vertical, fontSize, name }) => {
    return (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          marginTop: 10,
          marginBottom: 10,
          paddingTop: 10,
          paddingBottom: 10,
          paddingLeft: 30,
          paddingRight: 30,
          height: 50,
          borderWidth: 3,
          borderRadius: 10,
          opacity,
          borderColor: selected ? '#ABC9AF' : 'transparent',
          width: vertical ? 190 : 'auto'
        }}
      >
        <Text style={{ fontSize }}>
          {name}
        </Text>
      </View>
    );
  });

  const renderSPItem = ({ item, index }, indexSelected, vertical) => {
    const selected = index === indexSelected;
    const gap = Math.abs(index - indexSelected);

    let opacity = opacities[gap];
    if (gap > 3) {
      opacity = opacities[4];
    }
    let fontSize = sizeText[gap];
    if (gap > 1) {
      fontSize = sizeText[2];
    }

    return <SPItem opacity={opacity} selected={selected} vertical={vertical} fontSize={fontSize} name={item} />;
  };

  const [selected, setSelected] = useState(1);

  const [spDeliverSelected, setSPDeliverSelected] = useState(0);
  const [spRejectSelected, setSPRejectSelected] = useState(0);

  const [spDataNumberList, setSPDataNumberList] = useState(['1']);

  const refPicker = useRef(null);

  //////////////////////////////////////////////////////////////////////////////

  // // console.log('estimatedPreparedDate');
  // // console.log(order.estimatedPreparedDate);
  // // console.log(moment().valueOf());
  // // console.log(orderEstimatedTime);

  // useEffect(() => {
  //     setTimeout(() => {
  //         setFakeCurrentDate(new Date());
  //     }, 0);
  // }, [fakeCurrentDate]);

  // useEffect(() => {
  //     const result = Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId);
  //     const resultDelivered = Object.values(selectedOrderItemDeliveredCheckDict).find(item => item && item.itemId);
  //     const resultCancelled = Object.values(selectedOrderItemCancelledCheckDict).find(item => item && item.itemId);

  //     // console.log('currTickListType');

  //     var currTickListTypeTemp = TICK_LIST_TYPE.UNTICK;

  //     if (result) {
  //         currTickListTypeTemp = TICK_LIST_TYPE.NORMAL;
  //     }
  //     else if (resultDelivered) {
  //         currTickListTypeTemp = TICK_LIST_TYPE.DELIVERED;
  //     }
  //     else if (resultCancelled) {
  //         currTickListTypeTemp = TICK_LIST_TYPE.CANCELLED;
  //     }
  //     else {
  //         currTickListTypeTemp = TICK_LIST_TYPE.UNTICK;
  //     }

  //     // console.log(currTickListTypeTemp);
  //     setCurrTickListType(currTickListTypeTemp);
  //     setTimeout(() => {
  //         setCurrTickListType(currTickListTypeTemp);
  //     }, 100);
  // }, [
  //     selectedOrderItemCheckDict,
  //     selectedOrderItemDeliveredCheckDict,
  //     selectedOrderItemCancelledCheckDict,
  // ]);

  useEffect(() => {
    // // console.log('refreshed');

    setOrderEstimatedTime(
      // (moment().valueOf() - order.estimatedPreparedDate) / (1000 * 60),
      (moment().valueOf() - order.createdAt) / (1000 * 60),
    );
  }, [refreshRate]);

  const privileges_state = UserStore.useState((s) => s.privileges);

  const [privileges, setPrivileges] = useState([]);
  const role = UserStore.useState((s) => s.role);
  const pinNo = UserStore.useState(s => s.pinNo);

  useEffect(() => {
    const useEffectCallback = async () => {
      // admin full access

      // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
      // const enteredPinNo = storageMMKV.getString('enteredPinNo');
      const enteredPinNo = await global.watermelonDBDatabase.localStorage.get('enteredPinNo');

      if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
        setPrivileges([
          "EMPLOYEES",
          "OPERATION",
          "PRODUCT",
          "INVENTORY",
          "INVENTORY_COMPOSITE",
          "DOCKET",
          "VOUCHER",
          "PROMOTION",
          "CRM",
          "LOYALTY",
          "TRANSACTIONS",
          "REPORT",
          "RESERVATIONS",

          // for action
          'REFUND_ORDER',

          'SETTINGS',

          'QUEUE',

          'OPEN_CASH_DRAWER',

          'KDS',

          'UPSELLING',

          // for Kitchen

          'REJECT_ITEM',
          'CANCEL_ORDER',
          //'REFUND_tORDER',

          'MANUAL_DISCOUNT',
        ]);
      } else {
        setPrivileges(privileges_state || []);
      }
    };

    useEffectCallback();
  }, [role, privileges_state, pinNo]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var tempSelectedOrderItemList = [];
      var tempSelectedOrderItemCancelledList = [];
      var tempSelectedOrderItemDeliveredList = [];

      if (order && order.uniqueId) {
        for (var k = 0; k < order.cartItems.length; k++) {
          if (order.cartItems[k].deliveredAt === null) {
            tempSelectedOrderItemList.push(order.cartItems[k]);
          } else {
            tempSelectedOrderItemDeliveredList.push(order.cartItems[k]);
          }
        }

        if (order.cartItemsCancelled) {
          for (var k = 0; k < order.cartItemsCancelled.length; k++) {
            tempSelectedOrderItemCancelledList.push(order.cartItemsCancelled[k]);
          }
        }
      }

      setSelectedOrderItemList(tempSelectedOrderItemList);
      setSelectedOrderItemCancelledList(tempSelectedOrderItemCancelledList);
      setSelectedOrderItemDeliveredList(tempSelectedOrderItemDeliveredList);
    });
  }, [order]);

  ////////////////////////////////////////////////////////////

  // decide number picker min and max based on selected cart items quantity

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      var minNumber = 1;
      var maxNumber = 1;

      Object.values(selectedOrderItemCheckDict).map(item => {
        var cartItem = order.cartItems.find(cartItem => cartItem.itemId === item.itemId && cartItem.cartItemDate === item.cartItemDate);

        if (cartItem && cartItem.quantity > maxNumber) {
          maxNumber = cartItem.quantity;
        }
      });

      Object.values(selectedOrderItemDeliveredCheckDict).map(item => {
        var cartItem = order.cartItems.find(cartItem => cartItem.itemId === item.itemId && cartItem.cartItemDate === item.cartItemDate);

        if (cartItem && cartItem.quantity > maxNumber) {
          maxNumber = cartItem.quantity;
        }
      });

      Object.values(selectedOrderItemCancelledCheckDict).map(item => {
        var cartItem = (order.cartItemsCancelled || []).find(cartItem => cartItem.itemId === item.itemId && cartItem.cartItemDate === item.cartItemDate);

        if (cartItem && cartItem.quantity > maxNumber) {
          maxNumber = cartItem.quantity;
        }
      });

      var spDataNumberListTemp = [];

      for (var i = minNumber; i <= maxNumber; i++) {
        spDataNumberListTemp.push(i.toString());
      }

      setSPDataNumberList(spDataNumberListTemp);

      setSPDeliverSelected(spDataNumberListTemp.length - 1);
      setSPRejectSelected(spDataNumberListTemp.length - 1);
    });
  }, [selectedOrderItemCheckDict, selectedOrderItemDeliveredCheckDict, selectedOrderItemCancelledCheckDict]);

  ////////////////////////////////////////////////////////////

  const setState = () => { };

  const checkOrderItemFunc = (item, orderItem) => {
    // props.checkOrderItem(order.uniqueId, orderItem.uniqueId); // Update api
    // // Update local state
    // orderItem.isCheck = !orderItem.isCheck;
    // const pendingItems = order.cartItems.filter(o => o.isCheck === false);
    // let countdown = countdown;
    // if (pendingItems.length === 0) {
    //     if (countdown === -1) {
    //         countdown = 9;
    //         // Start countdown
    //         countdownInterval = setInterval(() => {
    //             countdown -= 1;
    //             setState({ countdown });
    //             if (countdown === 0) {
    //                 clearInterval(countdownInterval);
    //                 if (props.onTimesUp) {
    //                     props.onTimesUp(order);
    //                 }
    //             }
    //         }, 0)
    //     }
    // }
    // else {
    //     countdown = -1;
    //     if (countdownInterval) {
    //         clearInterval(countdownInterval);
    //     }
    // }
    // setState({ countdown })

    setIsLoadingLocal(true);

    props.checkOrderItem(item, orderItem, () => {
      setTimeout(() => {
        setIsLoadingLocal(false);
      }, 0);
    });
  };

  /////////////////////////////////////////////////////////

  // reject/undo functions

  function cancelUserOrder() {
    var body = {
      orderItemList: Object.entries(selectedOrderItemCheckDict).map(
        ([key, value]) => value,
      ),
      orderId: order.uniqueId,

      outlet: currOutlet,

      quantity: parseInt(spDataNumberList[spRejectSelected]),

      orderIdHuman: `#${(order.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + order.orderId}`,

      tableId: order.tableId,
    };

    // console.log(body, 'here');

    setIsLoadingLocal(true);

    setCancelModal(false);

    // ApiClient.POST(API.cancelUserOrderItem, body)
    APILocal.cancelUserOrderItem({ body, uid: firebaseUid })
      // (
      //   netInfo.isInternetReachable && netInfo.isConnected
      //     ?
      //     ApiClient.POST(API.cancelUserOrderItem, body)
      //     :
      //     APILocal.cancelUserOrderItem({ body: body })
      // )
      .then(async (result) => {
        if (result && result.status === 'success') {
          // Alert.alert('Success', 'Cancel successfully');

          //////////////////////////////////////////////////////////

          if (result.toCloseModal) {
            TableStore.update(s => {
              s.viewTableOrderModal = false;
            });
          }

          try {
            let orderNew = null;

            const userOrderSnapshot = await firestore()
              .collection(Collections.UserOrder)
              .where('uniqueId', '==', order.uniqueId)
              .limit(1)
              .get();

            if (!userOrderSnapshot.empty) {
              orderNew = userOrderSnapshot.docs[0].data();
            }

            if (orderNew) {
              logToFile('kitchen order - printUserOrder - KITCHEN_DOCKET cancelled');

              if (global.currOutlet.cancelRejectPrintOs) {
                printUserOrder(
                  {
                    orderData: orderNew,
                  },
                  false,
                  [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                  false,
                  false,
                  false,
                  { isInternetReachable: true, isConnected: true },
                  true, // for isPrioritized
                );
              }

              if (global.outletKdEventTypes.includes(KD_PRINT_EVENT_TYPE.REJECT)) {
                await printUserOrder(
                  {
                    // orderId: orderIdList[i],
                    orderData: orderNew,
                    // receiptNote: currOutlet.receiptNote || '',
                  },
                  false,
                  [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                  false,
                  false,
                  false,
                  netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                  true,
                  [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                  // cancelUser,
                );

                printKDSummaryCategoryWrapper(
                  {
                    // orderId: orderIdList[i],
                    orderData: orderNew,
                  },
                  [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                  // cancelUser,
                );

                const printerIpCountDict = await calcPrintTotalForKdIndividualCancelled({
                  userOrder: orderNew,
                });
                const printerTaskId = uuidv4();
                global.printingTaskIdDict[printerTaskId] = {};

                if (orderNew && orderNew.cartItemsCancelled && orderNew.cartItemsCancelled.length > 0) {
                  for (let bdIndex = 0; bdIndex < orderNew.cartItemsCancelled.length; bdIndex++) {
                    if (!orderNew.cartItemsCancelled[bdIndex].isDocket) {
                      await printDocketForKDCancelled(
                        {
                          userOrder: orderNew,
                          cartItem: orderNew.cartItemsCancelled[bdIndex],
                          printerIpCountDict: printerIpCountDict,
                          printerTaskId: printerTaskId,
                        },
                        // true,
                        [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                        // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                        [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                        // cancelUser,
                      );
                    }
                  }

                  for (let index = 0; index < orderNew.cartItemsCancelled.length; index++) {
                    if (orderNew.cartItemsCancelled[index].isDocket) {
                      await printDocketCancelled(
                        {
                          userOrder: orderNew,
                          cartItem: orderNew.cartItemsCancelled[index],
                        },
                        // true,
                        [PRINTER_USAGE_TYPE.RECEIPT],
                        [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                        // cancelUser,
                      );
                    }
                  }
                }
              }
            }
          }
          catch (ex) {
            console.error(ex);

            logToFile(ex);
          }

          //////////////////////////////////////////////////////////

          setSelectedOrderItemCheckDict({});
          setSelectedOrderItemDeliveredCheckDict({});
          setSelectedOrderItemCancelledCheckDict({});
        } else {
          Alert.alert('Error', 'Failed to cancel');
        }

        setTimeout(() => {
          setIsLoadingLocal(false);
        }, 0);
      })
      .catch((err) => {
        Alert.alert('Error', 'Something went wrong');

        setTimeout(() => {
          setIsLoadingLocal(false);
        }, 0);
      });
  }

  const undoUserOrderCancelled = () => {
    var body = {
      orderItemList: Object.entries(selectedOrderItemCancelledCheckDict).map(
        ([key, value]) => value,
      ),
      orderId: order.uniqueId,

      outlet: currOutlet,

      orderIdHuman: `#${(order.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + order.orderId}`,
    };

    // console.log(body);

    setIsLoadingLocal(true);

    // ApiClient.POST(API.undoUserOrderItemCancelled, body)
    APILocal.undoUserOrderItemCancelled({ body, uid: firebaseUid })
      // (
      //   netInfo.isInternetReachable && netInfo.isConnected
      //     ?
      //     ApiClient.POST(API.undoUserOrderItemCancelled, body)
      //     :
      //     APILocal.undoUserOrderItemCancelled({ body: body })
      // )
      .then((result) => {
        if (result && result.status === 'success') {
          // Alert.alert('Success', 'Undo successfully');
          setSelectedOrderItemCheckDict({});
          setSelectedOrderItemDeliveredCheckDict({});
          setSelectedOrderItemCancelledCheckDict({});
        } else {
          Alert.alert('Error', 'Failed to undo');
        }

        setTimeout(() => {
          setIsLoadingLocal(false);
        }, 0);
      })
      .catch((err) => {
        Alert.alert('Error', 'Something went wrong');

        setTimeout(() => {
          setIsLoadingLocal(false);
        }, 0);
      });
  };

  const deliverUserOrder = () => {
    var body = {
      orderItemList: Object.entries(selectedOrderItemCheckDict).map(
        ([key, value]) => value,
      ),
      orderId: order.uniqueId,

      outlet: currOutlet,

      quantity: parseInt(spDataNumberList[spDeliverSelected]),
    };

    // console.log(body, 'here');

    setIsLoadingLocal(true);

    setDeliverModal(false);

    (
      // netInfo.isInternetReachable && netInfo.isConnected
      //   ?
      //   ApiClient.POST(API.orderDeliverMultiple, body)
      //   :
      (APILocal.orderDeliverMultiple({ body, uid: firebaseUid }))
    )
      .then((result) => {
        if (result && result.status === 'success') {
          // Alert.alert('Success', 'Delivered successfully');
          setSelectedOrderItemCheckDict({});
          setSelectedOrderItemDeliveredCheckDict({});
          setSelectedOrderItemCancelledCheckDict({});
        } else {
          Alert.alert('Error', 'Failed to deliver');
        }

        setTimeout(() => {
          setIsLoadingLocal(false);
        }, 0);

        // animAppearEntity.start();        
      })
      .catch((err) => {
        Alert.alert('Error', 'Something went wrong');

        setTimeout(() => {
          setIsLoadingLocal(false);
        }, 0);
      });

    // ApiClient.POST(API.orderDeliverMultiple, body)
    //   .then((result) => {
    //     if (result && result.status === 'success') {
    //       // Alert.alert('Success', 'Delivered successfully');
    //       setSelectedOrderItemCheckDict({});
    //       setSelectedOrderItemDeliveredCheckDict({});
    //       setSelectedOrderItemCancelledCheckDict({});
    //     } else {
    //       Alert.alert('Error', 'Failed to deliver');
    //     }

    //     setTimeout(() => {
    //       setIsLoadingLocal(false);
    //     }, 0);

    //     // animAppearEntity.start();
    //   })
    //   .catch((err) => {
    //     Alert.alert('Error', 'Something went wrong');

    //     setTimeout(() => {
    //       setIsLoadingLocal(false);
    //     }, 0);
    //   });
  };

  const deliverUserOrderUndo = () => {
    var body = {
      orderItemList: Object.entries(selectedOrderItemDeliveredCheckDict).map(
        ([key, value]) => value,
      ),
      orderId: order.uniqueId,

      outlet: currOutlet,
    };

    // console.log(body);

    setIsLoadingLocal(true);

    // APILocal.orderDeliverUndoMultiple({ body: body })
    (
      // netInfo.isInternetReachable && netInfo.isConnected
      //   ?
      //   ApiClient.POST(API.orderDeliverUndoMultiple, body)
      //   :
      (APILocal.orderDeliverUndoMultiple({ body, uid: firebaseUid }))
    )
      .then((result) => {
        if (result && result.status === 'success') {
          // Alert.alert('Success', 'Undo successfully');
          setSelectedOrderItemCheckDict({});
          setSelectedOrderItemDeliveredCheckDict({});
          setSelectedOrderItemCancelledCheckDict({});
        } else {
          Alert.alert('Error', 'Failed to undo');
        }

        setTimeout(() => {
          setIsLoadingLocal(false);
        }, 0);
      })
      .catch((err) => {
        Alert.alert('Error', 'Something went wrong');

        setTimeout(() => {
          setIsLoadingLocal(false);
        }, 0);
      });

    // ApiClient.POST(API.orderDeliverUndoMultiple, body)
    //   .then((result) => {
    //     if (result && result.status === 'success') {
    //       // Alert.alert('Success', 'Undo successfully');
    //       setSelectedOrderItemCheckDict({});
    //       setSelectedOrderItemDeliveredCheckDict({});
    //       setSelectedOrderItemCancelledCheckDict({});
    //     } else {
    //       Alert.alert('Error', 'Failed to undo');
    //     }

    //     setTimeout(() => {
    //       setIsLoadingLocal(false);
    //     }, 0);
    //   })
    //   .catch((err) => {
    //     Alert.alert('Error', 'Something went wrong');

    //     setTimeout(() => {
    //       setIsLoadingLocal(false);
    //     }, 0);
    //   });
  };

  const checkOrderItem = (cartItem, orderItem) => {
    // console.log('check!');

    // var body = {
    //   itemId: item.itemId,
    //   cartItemDate: item.cartItemDate,
    //   orderId: orderItem.uniqueId,
    // };

    // ApiClient.POST(API.orderDeliver, body).then(result => {
    // }).catch(err => Alert('Error', 'Something went wrong'));

    setSelectedOrderItemCancelledCheckDict({});
    setSelectedOrderItemDeliveredCheckDict({});

    setSelectedOrderItemCheckDict({
      ...selectedOrderItemCheckDict,
      [cartItem.itemId + cartItem.cartItemDate.toString()]: {
        itemId: cartItem.itemId,
        cartItemDate: cartItem.cartItemDate,
      },
    });

    // CommonStore.update(s => {
    //     s.summaryCheckDict = {};
    // });
  };

  const uncheckOrderItem = (cartItem, orderItem) => {
    // console.log('uncheck!');

    // var body = {
    //   itemId: item.itemId,
    //   cartItemDate: item.cartItemDate,
    //   orderId: orderItem.uniqueId,
    // };

    // ApiClient.POST(API.orderDeliverUndo, body).then(result => {
    // }).catch(err => Alert('Error', 'Something went wrong'));

    setSelectedOrderItemCheckDict({
      ...selectedOrderItemCheckDict,
      [cartItem.itemId + cartItem.cartItemDate.toString()]: false,
    });
  };

  const checkOrderItemCancelled = (cartItem, orderItem) => {
    // console.log('check!');

    // var body = {
    //   itemId: item.itemId,
    //   cartItemDate: item.cartItemDate,
    //   orderId: orderItem.uniqueId,
    // };

    // ApiClient.POST(API.orderDeliver, body).then(result => {
    // }).catch(err => Alert('Error', 'Something went wrong'));

    setSelectedOrderItemCheckDict({});
    setSelectedOrderItemDeliveredCheckDict({});

    setSelectedOrderItemCancelledCheckDict({
      ...selectedOrderItemCancelledCheckDict,
      [cartItem.itemId + cartItem.cartItemDate.toString()]: {
        itemId: cartItem.itemId,
        cartItemDate: cartItem.cartItemDate,
      },
    });

    // CommonStore.update(s => {
    //     s.summaryCancelledCheckDict = {};
    // });
  };

  const uncheckOrderItemCancelled = (cartItem, orderItem) => {
    // console.log('uncheck!');

    // var body = {
    //   itemId: item.itemId,
    //   cartItemDate: item.cartItemDate,
    //   orderId: orderItem.uniqueId,
    // };

    // ApiClient.POST(API.orderDeliverUndo, body).then(result => {
    // }).catch(err => Alert('Error', 'Something went wrong'));

    setSelectedOrderItemCancelledCheckDict({
      ...selectedOrderItemCancelledCheckDict,
      [cartItem.itemId + cartItem.cartItemDate.toString()]: false,
    });
  };

  const checkOrderItemDelivered = (cartItem, orderItem) => {
    // console.log('check!');

    // var body = {
    //   itemId: item.itemId,
    //   cartItemDate: item.cartItemDate,
    //   orderId: orderItem.uniqueId,
    // };

    // ApiClient.POST(API.orderDeliver, body).then(result => {
    // }).catch(err => Alert('Error', 'Something went wrong'));

    setSelectedOrderItemCheckDict({});
    setSelectedOrderItemCancelledCheckDict({});

    setSelectedOrderItemDeliveredCheckDict({
      ...selectedOrderItemDeliveredCheckDict,
      [cartItem.itemId + cartItem.cartItemDate.toString()]: {
        itemId: cartItem.itemId,
        cartItemDate: cartItem.cartItemDate,
      },
    });

    // CommonStore.update(s => {
    //     s.summaryDeliveredCheckDict = {};
    // });
  };

  const uncheckOrderItemDelivered = (cartItem, orderItem) => {
    // console.log('uncheck!');

    // var body = {
    //   itemId: item.itemId,
    //   cartItemDate: item.cartItemDate,
    //   orderId: orderItem.uniqueId,
    // };

    // ApiClient.POST(API.orderDeliverUndo, body).then(result => {
    // }).catch(err => Alert('Error', 'Something went wrong'));

    setSelectedOrderItemDeliveredCheckDict({
      ...selectedOrderItemDeliveredCheckDict,
      [cartItem.itemId + cartItem.cartItemDate.toString()]: false,
    });
  };

  /////////////////////////////////////////////////////////

  const checkSameItem = (cartItems) => {
    var tempCheckSekectedItem = props.checkSelectedItem;
    if (
      props.checkSelectedItem != undefined &&
      props.checkSelectedItem.length == 0
    ) {
      tempCheckSekectedItem = undefined;
    }
    if (props.checkSelectedItem != undefined) {
      for (const item of cartItems) {
        if (props.checkSelectedItem.includes(item.name)) {
          item.isCheck = true;
        }
      }
    }
  };

  // const { order } = state;
  // if (!order) {
  //     return null
  // }

  // const itemAlmostFiltered = order.cartItems.filter(i => i.item != null)
  // const itemFiltered = itemAlmostFiltered.filter(i => props.selectedCat.includes(i.item.category.name))
  // order.spinValue = new Animated.Value(0)

  // Animated.timing(
  //     order.spinValue,
  //     {
  //         toValue: 1,
  //         duration: 1000,
  //         easing: Easing.linear,
  //         useNativeDriver: true
  //     }
  // ).start()

  // const spin = order.spinValue.interpolate({
  //     inputRange: [0, 1],
  //     outputRange: ['0deg', '360deg']
  // })

  // const cartItems = props.selectedCat.length != 0 ? (props.search = "" ? itemFiltered : itemFiltered.filter(item => item && item.name.toLowerCase().match(props.search.toLowerCase()))) : (props.search == "" ? order.cartItems : order.cartItems.filter(item => item.name.toLowerCase().match(props.search.toLowerCase())))

  // checkSameItem(cartItems)

  // var iconUse = ''
  // if (order.customTable === "TAKE AWAY") {
  //     if (orderEstimatedTime >= 20 || orderEstimatedTime < 15) {
  //         iconUse = '../../assets/image/takeawayWhite.png'
  //     }
  // }
  // else {
  //     if (orderEstimatedTime >= 20 || orderEstimatedTime < 15) {
  //         iconUse = '../../assets/image/dineinWhite1.png'
  //     }
  // }

  //////////////////////////////////////////////

  // use render cycle to update, state will delay

  var currTickListType = TICK_LIST_TYPE.UNTICK;
  if (
    Object.values(selectedOrderItemCheckDict).find(
      (item) => item && item.itemId,
    )
  ) {
    currTickListType = TICK_LIST_TYPE.NORMAL;
  } else if (
    Object.values(selectedOrderItemDeliveredCheckDict).find(
      (item) => item && item.itemId,
    )
  ) {
    currTickListType = TICK_LIST_TYPE.DELIVERED;
  } else if (
    Object.values(selectedOrderItemCancelledCheckDict).find(
      (item) => item && item.itemId,
    )
  ) {
    currTickListType = TICK_LIST_TYPE.CANCELLED;
  }

  //////////////////////////////////////////////

  // 2022-11-24 - Show cart item's printer area

  //////////////////////////////////////////////

  return (
    <View
      style={[
        {
          marginHorizontal: 5,
          marginTop: 10,
          padding: 5,
          width: windowWidth / 5 + 55,
          alignSelf: 'flex-start',
          // height: (props.viewOptions || userOrdersExpandedDict[order.uniqueId] === true) ? "auto" :
          //     (windowHeight / 3),
          height: props.viewOptions
            ? windowHeight / 1.5
            : userOrdersExpandedDict[order.uniqueId] === true
              ? windowHeight / 1.5
              : windowHeight / 2,

          // backgroundColor: 'red',
          // borderWidth: 2,
          // borderColor: 'red',
          // shadowColor: '#000',
          // shadowOffset: {
          //     width: 0,
          //     height: 2,
          // },
          // shadowOpacity: 0.22,
          // shadowRadius: 3.22,
          // elevation: 3,
        },
        !isTablet()
          ? {
            height: props.viewOptions
              ? windowHeight * 0.512
              : userOrdersExpandedDict[order.uniqueId] === true
                ? windowHeight / 1.5
                : windowHeight / 2,
          }
          : {},
        // windowHeight >= 1400 ?{height: windowHeight * 0.73} : {height: windowHeight * 0.65},
      ]}>
      <View
        style={[
          {
            height: !isTablet() ? windowHeight * 0.12 : '20%',
            backgroundColor:
              orderEstimatedTime >= 20
                ? Colors.tabRed
                : orderEstimatedTime < 20 && orderEstimatedTime >= 10
                  ? Colors.tabYellow
                  : Colors.tabGrey,
            borderTopLeftRadius: 15,
            borderTopRightRadius: 15,
            flexDirection: 'row',
            padding: 10,

            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 3,

            ...(kitchenScrollItem.uniqueId === order.uniqueId && {
              borderWidth: 1.5,
              borderBottomWidth: 0,
              borderColor: Colors.tabGold,
            }),
          },
        ]}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            flex: 4,
            marginRight: 15,
            left: 5,
          }}>
          {/* <Icon name={order.customTable === "TAKE AWAY" ? "shopping-outline" : "silverware-variant"} size={40} color={(orderEstimatedTime >= 20 || orderEstimatedTime < 15) ? Colors.whiteColor : Colors.blackColor} style={{ margin: Platform.OS == 'ios' ? 5 : 10 }} /> */}
          {order.courierId ? (
            <Image
              style={[
                {
                  width: 30,
                  height: 30,
                },
                !isTablet()
                  ? {
                    width: 20,
                    height: 20,
                  }
                  : {},
              ]}
              source={COURIER_INFO_DICT[order.courierCode].img}
            />
          ) : order.orderType === ORDER_TYPE.DINEIN ? (
            <Image
              style={[
                {
                  width: 30,
                  height: 30,
                  marginLeft: 0,
                },
                !isTablet()
                  ? {
                    width: 20,
                    height: 20,
                  }
                  : {},
              ]}
              resizeMode="contain"
              source={require('../../assets/image/dineinWhite1.png')}
            />
          ) : (
            <Image
              style={[
                {
                  width: 30,
                  height: 30,
                  //marginLeft: 0,
                  //left: 30,
                },
                !isTablet()
                  ? {
                    width: 20,
                    height: 20,
                    // top: windowHeight * 0.009,
                  }
                  : {},
              ]}
              resizeMode="contain"
              source={require('../../assets/image/takeawayWhite.png')}
            />
          )}
          {/* <Image
            style={{
              width: !isTablet() ? 20 : 30,
              height: !isTablet() ? 20 : 30,
              marginLeft: 0,
              //   borderWidth: 10
            }}
            resizeMode="contain"
            source={
              order.courierCode !== ORDER &&
                (orderEstimatedTime >= 20 || orderEstimatedTime < 15)
                ? require('../../assets/image/takeawayWhite1.png')
                : order.courierCode !== ORDER_TYPE.DINEIN &&
                  (orderEstimatedTime >= 20 || orderEstimatedTime < 15)
                  ? require('../../assets/image/dineinWhite1.png')
                  : order.courierCode !== ORDER_TYPE.DINEIN &&
                    orderEstimatedTime < 20 &&
                    orderEstimatedTime >= 15
                    ? require('../../assets/image/takeawayWhite1.png')
                    : // : order.orderType !== ORDER_TYPE.DINEIN && (orderEstimatedTime < 20 && orderEstimatedTime >= 15) ? require('../../assets/image/takeawayBlack1.png')
                    require('../../assets/image/dineinWhite1.png')
            }
          /> */}
          {/* <Text style={{ fontFamily: 'NunitoSans-Bold', paddingHorizontal: 5, color: (orderEstimatedTime >= 20 || orderEstimatedTime < 15) ? Colors.whiteColor : Colors.blackColor, fontSize: 22, borderLeftWidth: StyleSheet.hairlineWidth, borderColor: (orderEstimatedTime >= 20 || orderEstimatedTime < 15) ? Colors.whiteColor : Colors.blackColor }} numberOfLines={1}>#{order.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{order.orderId}</Text> */}
        </View>
        <View
          style={{
            justifyContent: 'center',
            flex: 30,
            paddingLeft: 10,
            paddingBottom: 5,
            paddingTop: 5,
            // backgroundColor: 'red'
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'flex-start',
              borderBottomWidth: StyleSheet.hairlineWidth,
              borderBottomColor:
                orderEstimatedTime >= 20 || orderEstimatedTime < 15
                  ? Colors.whiteColor
                  : Colors.blackColor,
              // backgroundColor: 'blue',
            }}>
            <Text
              style={[{
                fontFamily: 'NunitoSans-Regular',
                color:
                  orderEstimatedTime >= 20 || orderEstimatedTime < 15
                    ? Colors.whiteColor
                    : Colors.blackColor,
                fontSize: !isTablet() ? windowWidth / 90 : 13,
                width: '30%',
                top: Platform.OS == 'ios' ? 5 : 0,
                marginRight: 10,
                marginBottom:
                  Platform.OS == 'ios'
                    ? outletTablesDict[order.tableId]
                      ? 0
                      : 5
                    : 0,
              },
              !switchMerchant && windowWidth < 1000 ?
                {
                  width: '40%',
                }
                : {}
              ]}>{`Table`}</Text>
            <Text
              // adjustsFontSizeToFit={true}
              // numberOfLines={1}
              style={[
                {
                  fontFamily: 'NunitoSans-Bold',
                  width: '70%',
                  textAlign: 'left',
                  fontSize: !isTablet() ? windowWidth / 90 : 18,
                  color:
                    orderEstimatedTime >= 20 || orderEstimatedTime < 15
                      ? Colors.whiteColor
                      : Colors.blackColor,
                  //marginLeft: -75,
                  // backgroundColor: 'blue',
                },
                !isTablet()
                  ? {
                    fontSize: windowWidth / 90,
                    bottom: '1%',
                  }
                  : {},
                Platform.OS === 'android'
                  ? {
                    bottom: '1%',
                  }
                  : {},
                !switchMerchant && windowWidth < 1000 ?
                  {
                    width: '60%',
                  }
                  : {},
              ]}>
              {outletTablesDict[order.tableId]
                ? outletTablesDict[order.tableId].code
                : ''}
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'flex-start',
              borderBottomWidth: StyleSheet.hairlineWidth,
              borderBottomColor:
                orderEstimatedTime >= 20 || orderEstimatedTime < 15
                  ? Colors.whiteColor
                  : Colors.blackColor,
              // backgroundColor: 'blue',
            }}>
            <Text
              style={[{
                fontFamily: 'NunitoSans-Regular',
                color:
                  orderEstimatedTime >= 20 || orderEstimatedTime < 15
                    ? Colors.whiteColor
                    : Colors.blackColor,
                fontSize: !isTablet() ? windowWidth / 90 : 13,
                width: '30%',
                top: Platform.OS == 'ios' ? 4 : 0,
                marginRight: 10,
              },
              !switchMerchant && windowWidth < 1000 ?
                {
                  width: '40%',
                }
                : {}
              ]}>
              Order ID
            </Text>
            <Text
              style={[
                {
                  fontFamily: 'NunitoSans-Bold',
                  width: '70%',
                  textAlign: 'left',
                  fontSize: !isTablet() ? windowWidth / 90 : 18,
                  color:
                    orderEstimatedTime >= 20 || orderEstimatedTime < 15
                      ? Colors.whiteColor
                      : Colors.blackColor,
                  //marginLeft: -75,
                },
                !isTablet()
                  ? {
                    bottom: '1%',
                    fontSize: windowWidth / 90,
                  }
                  : {},
                Platform.OS === 'android'
                  ? {
                    bottom: '1%',
                  }
                  : {},
                !switchMerchant && windowWidth < 1000 ?
                  {
                    width: '60%',
                  }
                  : {}
              ]}>
              {order.orderType !== ORDER_TYPE.DINEIN
                ? order.orderType === ORDER_TYPE.DELIVERY
                  ? '#D'
                  : '#T'
                : '#'}
              {order.orderId}
            </Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
            <Text
              style={[{
                fontFamily: 'NunitoSans-Regular',
                width: '30%',
                color:
                  orderEstimatedTime >= 20 || orderEstimatedTime < 15
                    ? Colors.whiteColor
                    : Colors.blackColor,
                fontSize: !isTablet() ? windowWidth / 90 : 11,
                marginRight: 10,
                marginTop: Platform.OS == 'ios' ? 4 : 0,
                bottom: Platform.OS == 'ios' ? 3 : 0,
                // borderWidth: 1
              },
              !switchMerchant && windowWidth < 1000 ?
                {
                  width: '40%',
                }
                : {}
              ]}>
              Wait Time
            </Text>
            <Text
              style={[
                {
                  //left: Platform.OS == 'ios' ? -7 : 0,
                  fontFamily: 'NunitoSans-Bold',
                  width: '70%',
                  textAlign: 'left',
                  fontSize:
                    orderEstimatedTime < 1000
                      ? windowWidth <= 1133
                        ? 13
                        : 14
                      : windowWidth <= 1133
                        ? 13
                        : 14,
                  color:
                    orderEstimatedTime >= 20 || orderEstimatedTime < 15
                      ? Colors.whiteColor
                      : Colors.blackColor,
                  bottom: Platform.OS == 'ios' ? 1 : 0,
                },
                !isTablet()
                  ? {
                    fontSize: windowWidth / 90,
                    // borderWidth: 1,
                    // left: '-110%',
                    // bottom: '1%'
                  }
                  : {},
                Platform.OS === 'android' && isTablet()
                  ? {
                    left: windowWidth * -0.001,
                    bottom: '1%',
                  }
                  : {},
                !switchMerchant && windowWidth < 1000 ?
                  {
                    width: '60%',
                  }
                  : {}
              ]}>
              {orderEstimatedTime < 0
                ? '0 mins'
                : orderEstimatedTime > 120
                  ? 'OverTime'
                  : `${orderEstimatedTime.toFixed(0)}mins`}
            </Text>
          </View>
        </View>
      </View>

      <View
        style={{
          // display: !isLoadingLocal ? 'flex' : 'none',
          // opacity: !isLoadingLocal ? 1 : 0,
          justifyContent: 'space-between',
          flex: 1,
          backgroundColor: Colors.whiteColor,
          borderBottomLeftRadius: 15,
          borderBottomRightRadius: 15,
          // height: props.viewOptions ? (windowHeight / 2 + 80) : userOrdersExpandedDict[order.uniqueId] === true ? (order.cartItems.length + (order.cartItemsCancelled ? order.cartItemsCancelled.length : 0)) * (windowHeight * 0.2) : (order.cartItems.length + (order.cartItemsCancelled ? order.cartItemsCancelled.length : 0)) * (windowHeight * 0.1),
          height: props.viewOptions
            ? windowHeight / 2 + 80
            : userOrdersExpandedDict[order.uniqueId] === true &&
              order.cartItems.length +
              (order.cartItemsCancelled
                ? order.cartItemsCancelled.length
                : 0) >
              5
              ? windowHeight / 2 + 80
              : userOrdersExpandedDict[order.uniqueId] === true &&
                order.cartItems.length +
                (order.cartItemsCancelled
                  ? order.cartItemsCancelled.length
                  : 0) <=
                5
                ? 'auto'
                : windowHeight / 4,
          // height: props.viewOptions ? (windowHeight / 2 + 80) : 'auto',

          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,

          ...(kitchenScrollItem.uniqueId === order.uniqueId && {
            borderWidth: 3,
            borderTopWidth: 0,
            borderColor: Colors.tabGold,
          }),
        }}>
        <ScrollView
          style={[
            {
              // paddingBottom: 100,
              // marginBottom: 50,
              marginBottom: 0,
              paddingBottom: 0,
              // borderWidth: 1,
              // backgroundColor: 'purple',
              // opacity: !isLoadingLocal ? 1 : 0,
              display: isLoadingLocal ? 'none' : 'flex',
            },
          ]}
          nestedScrollEnabled
          // scrollEnabled={props.viewOptions == true ? true : props.scroll}
          scrollEnabled>
          <View
            style={{
              // paddingBottom: 100,
              // marginBottom: 50,
              marginBottom: 0,
              paddingBottom: 0,

              // backgroundColor: 'purple',
            }}
          // nestedScrollEnabled={true}
          // scrollEnabled={props.viewOptions == true ? true : props.scroll}
          >
            {
              // order.cartItems.map((orderItem, index) => {
              selectedOrderItemList.map((orderItem, index) => {
                if (selectedOutletCategoryList.length === 0 || selectedOutletCategoryList.includes(orderItem.categoryId)) {
                  return (
                    <View
                      style={{
                        // borderBottomWidth: index + 1 === order.cartItems.length ? null : StyleSheet.hairlineWidth,
                        padding: 15,
                        paddingHorizontal: 5,
                        // backgroundColor: 'green',
                      }}>
                      <TouchableOpacity
                        disabled={selectedOutletCategoryList.length > 0 && !selectedOutletCategoryList.includes(orderItem.categoryId)}
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          width: '100%',
                          // backgroundColor: 'yellow',
                        }}
                        onPress={() => {
                          if (
                            selectedOrderItemCheckDict[
                            orderItem.itemId +
                            orderItem.cartItemDate.toString()
                            ]
                          ) {
                            uncheckOrderItem(orderItem, order);
                          } else {
                            checkOrderItem(orderItem, order);
                          }
                        }
                        }
                      >
                        {/* <View style={{ flexDirection: 'row', alignItems: 'center' }}></View> */}
                        <View
                          style={{
                            width: '15%',
                            height: '100%',
                            //marginRight: 5,
                            padding: 8,
                            // backgroundColor: 'blue',
                          }}>
                          {/* <TouchableOpacity onPress={() => {
                                                          checkOrderItemFunc(orderItem, order);
                                                      }}>
                                                          <View style={{
                                                              borderRadius: 5,
                                                              backgroundColor: orderItem.isChecked == true ? Colors.primaryColor : Colors.fieldtBgColor,
                                                              marginRight: 2,
                                                              marginLeft: 2,
                                                              alignItems: 'center',
                                                              justifyContent: 'center',
                                                              width: 34,
                                                              height: 34
                                                          }}>
                                                              <Icon name="check" size={20} color={orderItem.isChecked == true ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                                                          </View>
                                                      </TouchableOpacity> */}
                          <TouchableOpacity>
                            <CheckBox
                              disabled={selectedOutletCategoryList.length > 0 && !selectedOutletCategoryList.includes(orderItem.categoryId)}
                              style={{
                                ...(Platform.OS === 'ios' && {
                                  width: !isTablet() ? 10 : 16,
                                  height: !isTablet() ? 10 : 16,
                                }),
                              }}
                              value={
                                selectedOrderItemCheckDict[
                                orderItem.itemId +
                                orderItem.cartItemDate.toString()
                                ] !== false &&
                                selectedOrderItemCheckDict[
                                orderItem.itemId +
                                orderItem.cartItemDate.toString()
                                ] !== undefined
                              }
                              onValueChange={(value) => {
                                if (
                                  selectedOrderItemCheckDict[
                                  orderItem.itemId +
                                  orderItem.cartItemDate.toString()
                                  ]
                                ) {
                                  uncheckOrderItem(orderItem, order);
                                } else {
                                  checkOrderItem(orderItem, order);
                                }
                              }}
                            />
                          </TouchableOpacity>
                        </View>

                        <View style={{
                          width: '5%',
                        }} />

                        <View style={{ flexDirection: 'column', width: '60%' }}>
                          <View style={{}}>
                            <Text
                              style={[
                                {
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: !isTablet() ? 10 : 14,
                                },
                                !isTablet()
                                  ? {
                                    marginLeft: '5%',
                                  }
                                  : {},
                              ]}>
                              {orderItem.name}{orderItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[orderItem.unitType]})` : ''}
                            </Text>
                          </View>
                          {orderItem.remarks && orderItem.remarks.length > 0 ? (
                            <View style={{}}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-SemiBold',
                                  fontSize: !isTablet() ? 8 : 12,
                                }}>
                                Remarks: {orderItem.remarks}
                              </Text>
                            </View>
                          ) : (
                            <></>
                          )}
                        </View>

                        <View style={{ width: '20%', alignItems: 'center' }}>
                          <Text
                            style={[
                              {
                                fontFamily: 'NunitoSans-Bold',
                                color: Colors.fieldtTxtColor,
                              },
                              !isTablet()
                                ? {
                                  fontSize: windowWidth / 90,
                                }
                                : {},
                            ]}>
                            x{orderItem.quantity}
                          </Text>
                        </View>

                      </TouchableOpacity>

                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          width: '100%',
                          // backgroundColor: 'yellow',

                          paddingHorizontal: 15,
                        }}>
                        <View style={{ flexDirection: 'column', width: '100%' }}>
                          <View style={{}}>
                            <Text
                              adjustsFontSizeToFit
                              numberOfLines={1}
                              style={[
                                {
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: !isTablet() ? 10 : 14,
                                  color: Colors.descriptionColor,
                                },
                                !isTablet()
                                  ? {
                                    marginLeft: '5%',
                                  }
                                  : {},
                              ]}>
                              {`(${(orderItem.printerAreaList && orderItem.printerAreaList.length > 0 && orderItem.printerAreaList.join) ? orderItem.printerAreaList.join(', ') : 'N/A'})`}
                            </Text>
                          </View>
                        </View>
                      </View>

                      <View
                        style={{
                          width: '100%',
                          alignItems: 'flex-start',
                          //paddingHorizontal: 20,
                          marginTop: 10,
                          // backgroundColor: 'red',
                        }}>
                        {orderItem.addOns && orderItem.addOns.map((o) => {
                          return (
                            <View style={{ flexDirection: 'row', marginLeft: 5 }}>
                              {/* <Text style={{ fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor }}>x{o.quantity}</Text> */}
                              <View style={{ width: '15%' }} />
                              <View style={{ width: '30%' }}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Regular',
                                      color: Colors.fieldtTxtColor,
                                      width: '100%',
                                    },
                                    !isTablet()
                                      ? {
                                        fontSize: windowWidth / 90,
                                      }
                                      : {},
                                  ]}>
                                  {o.name}:
                                </Text>
                                {/* {o.choiceNames.map(choice => {
                                                                                      return (
                                                                                          <Text style={{ marginLeft: 10, fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor, textDecorationLine: 'line-through', color: Colors.tabRed }}>{choice}</Text>
                                                                                      )
  
                                                                                  })} */}
                              </View>

                              <View style={{ width: '35%' }}>
                                {o.choiceNames && o.choiceNames.map((choice) => {
                                  return (
                                    <Text
                                      style={[
                                        {
                                          marginLeft: 10,
                                          fontFamily: 'NunitoSans-Regular',
                                          color: Colors.fieldtTxtColor,
                                        },
                                        !isTablet()
                                          ? {
                                            fontSize: windowWidth / 90,
                                          }
                                          : {},
                                      ]}>
                                      {choice}
                                    </Text>
                                  );
                                })}
                              </View>

                              <View style={{ width: '20%', alignItems: 'center' }}>
                                {o.quantities ? (
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-Regular',
                                        color: Colors.fieldtTxtColor,
                                      },
                                      !isTablet()
                                        ? {
                                          fontSize: windowWidth / 90,
                                        }
                                        : {},
                                    ]}>
                                    x{o.quantities[0]}
                                  </Text>
                                ) : (
                                  <></>
                                )}
                              </View>
                            </View>
                          );
                        })}
                      </View>
                    </View>
                  );
                }
                else {
                  return <></>;
                }
              })
            }

            {/* {countdown > -1 &&
                                <Animated.View style={{ width: 100, height: 100, alignSelf: 'center', transform: [{ rotate: spin }], alignItems: 'center', justifyContent: 'center' }}>
                                    <Icon name={"reload"} size={70} color={Colors.primaryColor} />
                                </Animated.View>} */}
          </View>

          {
            // props.viewOptions || userOrdersExpandedDict[order.uniqueId]
            true ? (
              <>
                <View
                  style={{
                    height: 1.5,
                    width: '100%',
                    backgroundColor: 'black',
                    // marginTop: 8,
                    // marginBottom: 4,
                    opacity: 0.1,
                  }} />

                <View
                  style={{
                    // height: 1.5,
                    width: '100%',
                    // backgroundColor: 'black',
                    // marginTop: 8,
                    // marginBottom: 4,
                    // opacity: 0.1,
                    alignItems: 'center',
                    marginTop: 15,
                    marginBottom: 15,
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-SemiBold',
                      fontSize: !isTablet() ? 10 : 16,
                      color: Colors.blackColor,
                    }}>
                    {'Delivered'}
                  </Text>
                </View>

                <View
                  style={
                    {
                      // paddingBottom: 50,
                      // backgroundColor: 'red'
                    }
                  }
                //scrollEnabled={props.viewOptions == true ? true : props.scroll}
                >
                  {
                    // order.cartItems.map((orderItem, index) => {
                    selectedOrderItemDeliveredList.map((orderItem, index) => {
                      return (
                        <Animated.View
                          style={{
                            // borderBottomWidth: index + 1 === order.cartItems.length ? null : StyleSheet.hairlineWidth,
                            padding: 15,
                            paddingTop: index === 0 ? 0 : 15,
                            paddingHorizontal: 5,
                            // opacity: animAppear,
                          }}
                          nestedScrollEnabled>
                          <TouchableOpacity
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              width: '100%',
                            }}
                            onPress={() => {
                              if (
                                selectedOrderItemDeliveredCheckDict[
                                orderItem.itemId +
                                orderItem.cartItemDate.toString()
                                ]
                              ) {
                                uncheckOrderItemDelivered(orderItem, order);
                              } else {
                                checkOrderItemDelivered(orderItem, order);
                              }
                            }
                            }
                          >
                            {/* <View style={{ flexDirection: 'row', alignItems: 'center' }}></View> */}
                            <View
                              style={{
                                width: '15%',
                                height: '100%',
                                //marginRight: 5,
                                padding: 8,
                              }}>
                              {/* <TouchableOpacity onPress={() => {
                                                        checkOrderItemFunc(orderItem, order);
                                                    }}>
                                                        <View style={{
                                                            borderRadius: 5,
                                                            backgroundColor: orderItem.isChecked == true ? Colors.primaryColor : Colors.fieldtBgColor,
                                                            marginRight: 2,
                                                            marginLeft: 2,
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            width: 34,
                                                            height: 34
                                                        }}>
                                                            <Icon name="check" size={20} color={orderItem.isChecked == true ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                                                        </View>
                                                    </TouchableOpacity> */}
                              <TouchableOpacity>
                                <CheckBox
                                  style={{
                                    ...(Platform.OS === 'ios' && {
                                      width: !isTablet() ? 10 : 16,
                                      height: !isTablet() ? 10 : 16,
                                    }),
                                  }}
                                  value={
                                    selectedOrderItemDeliveredCheckDict[
                                    orderItem.itemId +
                                    orderItem.cartItemDate.toString()
                                    ] !== false &&
                                    selectedOrderItemDeliveredCheckDict[
                                    orderItem.itemId +
                                    orderItem.cartItemDate.toString()
                                    ] !== undefined
                                  }
                                  onValueChange={(value) => {
                                    if (
                                      selectedOrderItemDeliveredCheckDict[
                                      orderItem.itemId +
                                      orderItem.cartItemDate.toString()
                                      ]
                                    ) {
                                      uncheckOrderItemDelivered(orderItem, order);
                                    } else {
                                      checkOrderItemDelivered(orderItem, order);
                                    }
                                  }}
                                />
                              </TouchableOpacity>
                            </View>

                            <View style={{
                              width: '5%',
                            }} />

                            <View
                              style={{ flexDirection: 'column', width: '60%' }}>
                              <View style={{}}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 14,
                                      textDecorationLine: 'line-through',
                                      color: Colors.primaryColor,
                                    },
                                    !isTablet()
                                      ? {
                                        fontSize: windowWidth / 90,
                                        marginLeft: '5%',
                                      }
                                      : {},
                                  ]}>
                                  {orderItem.name}{orderItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[orderItem.unitType]})` : ''}
                                </Text>
                              </View>
                              {orderItem.remarks &&
                                orderItem.remarks.length > 0 ? (
                                <View style={{}}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-SemiBold',
                                        fontSize: 12,
                                        textDecorationLine: 'line-through',
                                        color: Colors.primaryColor,
                                      },
                                      !isTablet()
                                        ? {
                                          fontSize: 8,
                                        }
                                        : {},
                                    ]}>
                                    Remarks: {orderItem.remarks}
                                  </Text>
                                </View>
                              ) : (
                                <></>
                              )}
                            </View>

                            <View style={{ width: '20%', alignItems: 'center' }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    color: Colors.fieldtTxtColor,
                                  },
                                  !isTablet()
                                    ? {
                                      fontSize: windowWidth / 90,
                                    }
                                    : {},
                                ]}>
                                x{orderItem.quantity}
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              width: '100%',
                              // backgroundColor: 'yellow',

                              paddingHorizontal: 15,
                            }}>
                            <View style={{ flexDirection: 'column', width: '100%' }}>
                              <View style={{}}>
                                <Text
                                  adjustsFontSizeToFit
                                  numberOfLines={1}
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: !isTablet() ? 10 : 14,
                                      color: Colors.descriptionColor,
                                    },
                                    !isTablet()
                                      ? {
                                        marginLeft: '5%',
                                      }
                                      : {},
                                  ]}>
                                  {`(${(orderItem.printerAreaList && orderItem.printerAreaList.length > 0 && orderItem.printerAreaList.join) ? orderItem.printerAreaList.join(', ') : 'N/A'})`}
                                </Text>
                              </View>
                            </View>
                          </View>

                          <View
                            style={{
                              width: '100%',
                              alignItems: 'flex-start',
                              //paddingHorizontal: 20,
                              marginTop: 10,
                            }}>
                            {orderItem.addOns && orderItem.addOns.map((o) => {
                              return (
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    marginLeft: 5,
                                  }}>
                                  {/* <Text style={{ fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor }}>x{o.quantity}</Text> */}
                                  <View style={{ width: '15%' }} />
                                  <View style={{ width: '30%' }}>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Regular',
                                          color: Colors.primaryColor,
                                          textDecorationLine: 'line-through',
                                          width: '100%',
                                        },
                                        !isTablet()
                                          ? {
                                            fontSize: windowWidth / 90,
                                          }
                                          : {},
                                      ]}>
                                      {o.name}:
                                    </Text>
                                    {/* {o.choiceNames.map(choice => {
                                                                                    return (
                                                                                        <Text style={{ marginLeft: 10, fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor, textDecorationLine: 'line-through', color: Colors.tabRed }}>{choice}</Text>
                                                                                    )

                                                                                })} */}
                                  </View>

                                  <View style={{ width: '35%' }}>
                                    {o.choiceNames && o.choiceNames.map((choice) => {
                                      return (
                                        <Text
                                          style={[
                                            {
                                              marginLeft: 10,
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.primaryColor,
                                              textDecorationLine:
                                                'line-through',
                                            },
                                            !isTablet()
                                              ? {
                                                fontSize: windowWidth / 90,
                                              }
                                              : {},
                                          ]}>
                                          {choice}
                                        </Text>
                                      );
                                    })}
                                  </View>

                                  <View
                                    style={{
                                      width: '20%',
                                      alignItems: 'center',
                                    }}>
                                    {o.quantities ? (
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.primaryColor,
                                          },
                                          !isTablet()
                                            ? {
                                              fontSize: windowWidth / 90,
                                            }
                                            : {},
                                        ]}>
                                        x{o.quantities[0]}
                                      </Text>
                                    ) : (
                                      <></>
                                    )}
                                  </View>
                                </View>
                              );
                            })}
                          </View>
                        </Animated.View>
                      );
                    })
                  }

                  {/* {countdown > -1 &&
                                <Animated.View style={{ width: 100, height: 100, alignSelf: 'center', transform: [{ rotate: spin }], alignItems: 'center', justifyContent: 'center' }}>
                                    <Icon name={"reload"} size={70} color={Colors.primaryColor} />
                                </Animated.View>} */}
                </View>

                {/* //////////////////////////////////////////////////////////////////////////////////////////// */}

                <View
                  style={{
                    height: 1.5,
                    width: '100%',
                    backgroundColor: 'black',
                    // marginTop: 8,
                    // marginBottom: 4,
                    opacity: 0.1,
                  }} />

                <View
                  style={{
                    // height: 1.5,
                    width: '100%',
                    // backgroundColor: 'black',
                    // marginTop: 8,
                    // marginBottom: 4,
                    // opacity: 0.1,
                    alignItems: 'center',
                    marginTop: 15,
                    marginBottom: 15,
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-SemiBold',
                      fontSize: !isTablet() ? 10 : 16,
                      color: Colors.blackColor,
                    }}>
                    {'Cancelled'}
                  </Text>
                </View>

                <View
                  style={
                    {
                      // paddingBottom: 50,
                      // backgroundColor: 'red'
                    }
                  }
                // nestedScrollEnabled={true} scrollEnabled={props.viewOptions == true ? true : props.scroll}
                >
                  {
                    // order.cartItems.map((orderItem, index) => {
                    selectedOrderItemCancelledList.map((orderItem, index) => {
                      return (
                        <View
                          style={{
                            // borderBottomWidth: index + 1 === order.cartItems.length ? null : StyleSheet.hairlineWidth,
                            padding: 15,
                            paddingTop: index === 0 ? 0 : 15,
                            paddingHorizontal: 5,
                          }}>
                          <TouchableOpacity
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              width: '100%',
                            }}
                            onPress={() => {
                              if (
                                selectedOrderItemCancelledCheckDict[
                                orderItem.itemId +
                                orderItem.cartItemDate.toString()
                                ]
                              ) {
                                uncheckOrderItemCancelled(orderItem, order);
                              } else {
                                checkOrderItemCancelled(orderItem, order);
                              }
                            }
                            }
                          >
                            {/* <View style={{ flexDirection: 'row', alignItems: 'center' }}></View> */}
                            <View
                              style={{
                                width: '15%',
                                height: '100%',
                                //marginRight: 5,
                                padding: 8,
                              }}>
                              {/* <TouchableOpacity onPress={() => {
                                                        checkOrderItemFunc(orderItem, order);
                                                    }}>
                                                        <View style={{
                                                            borderRadius: 5,
                                                            backgroundColor: orderItem.isChecked == true ? Colors.primaryColor : Colors.fieldtBgColor,
                                                            marginRight: 2,
                                                            marginLeft: 2,
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            width: 34,
                                                            height: 34
                                                        }}>
                                                            <Icon name="check" size={20} color={orderItem.isChecked == true ? Colors.whiteColor : Colors.fieldtTxtColor} style={{ margin: 2 }} />
                                                        </View>
                                                    </TouchableOpacity> */}
                              <TouchableOpacity>
                                <CheckBox
                                  style={{
                                    ...(Platform.OS === 'ios' && {
                                      width: !isTablet() ? 10 : 16,
                                      height: !isTablet() ? 10 : 16,
                                    }),
                                  }}
                                  value={
                                    selectedOrderItemCancelledCheckDict[
                                    orderItem.itemId +
                                    orderItem.cartItemDate.toString()
                                    ] !== false &&
                                    selectedOrderItemCancelledCheckDict[
                                    orderItem.itemId +
                                    orderItem.cartItemDate.toString()
                                    ] !== undefined
                                  }
                                  onValueChange={(value) => {
                                    if (
                                      selectedOrderItemCancelledCheckDict[
                                      orderItem.itemId +
                                      orderItem.cartItemDate.toString()
                                      ]
                                    ) {
                                      uncheckOrderItemCancelled(orderItem, order);
                                    } else {
                                      checkOrderItemCancelled(orderItem, order);
                                    }
                                  }}
                                />
                              </TouchableOpacity>
                            </View>

                            <View style={{
                              width: '5%',
                            }} />

                            <View
                              style={{ flexDirection: 'column', width: '60%' }}>
                              <View style={{}}>
                                <Text
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: 14,
                                      textDecorationLine: 'line-through',
                                      color: Colors.tabRed,
                                    },
                                    !isTablet()
                                      ? {
                                        fontSize: windowWidth / 90,
                                        marginLeft: '5%',
                                      }
                                      : {},
                                  ]}>
                                  {orderItem.name}{orderItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[orderItem.unitType]})` : ''}
                                </Text>
                              </View>
                              {orderItem.remarks &&
                                orderItem.remarks.length > 0 ? (
                                <View style={{}}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-SemiBold',
                                        fontSize: windowWidth / 90,
                                        textDecorationLine: 'line-through',
                                        color: Colors.tabRed,
                                      },
                                      !isTablet()
                                        ? {
                                          fontSize: 8,
                                        }
                                        : {},
                                    ]}>
                                    Remarks: {orderItem.remarks}
                                  </Text>
                                </View>
                              ) : (
                                <></>
                              )}
                            </View>

                            <View style={{ width: '20%', alignItems: 'center' }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: 'NunitoSans-Bold',
                                    color: Colors.fieldtTxtColor,
                                  },
                                  !isTablet()
                                    ? {
                                      fontSize: windowWidth / 90,
                                    }
                                    : {},
                                ]}>
                                x{orderItem.quantity}
                              </Text>
                            </View>
                          </TouchableOpacity>

                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              width: '100%',
                              // backgroundColor: 'yellow',

                              paddingHorizontal: 15,
                            }}>
                            <View style={{ flexDirection: 'column', width: '100%' }}>
                              <View style={{}}>
                                <Text
                                  adjustsFontSizeToFit
                                  numberOfLines={1}
                                  style={[
                                    {
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: !isTablet() ? 10 : 14,
                                      color: Colors.descriptionColor,
                                    },
                                    !isTablet()
                                      ? {
                                        marginLeft: '5%',
                                      }
                                      : {},
                                  ]}>
                                  {`(${(orderItem.printerAreaList && orderItem.printerAreaList.length > 0 && orderItem.printerAreaList.join) ? orderItem.printerAreaList.join(', ') : 'N/A'})`}
                                </Text>
                              </View>
                            </View>
                          </View>

                          <View
                            style={{
                              width: '100%',
                              alignItems: 'flex-start',
                              //paddingHorizontal: 20,
                              marginTop: 10,
                            }}>
                            {orderItem.addOns && orderItem.addOns.map((o) => {
                              return (
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    marginLeft: 5,
                                  }}>
                                  {/* <Text style={{ fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor }}>x{o.quantity}</Text> */}
                                  <View style={{ width: '15%' }} />
                                  <View style={{ width: '30%' }}>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Regular',
                                          color: Colors.fieldtTxtColor,
                                          textDecorationLine: 'line-through',
                                          color: Colors.tabRed,
                                          width: '100%',
                                        },
                                        !isTablet()
                                          ? {
                                            fontSize: windowWidth / 90,
                                          }
                                          : {},
                                      ]}>
                                      {o.name}:
                                    </Text>
                                    {/* {o.choiceNames.map(choice => {
                                                                                    return (
                                                                                        <Text style={{ marginLeft: 10, fontFamily: "NunitoSans-Regular", color: Colors.fieldtTxtColor, textDecorationLine: 'line-through', color: Colors.tabRed }}>{choice}</Text>
                                                                                    )

                                                                                })} */}
                                  </View>

                                  <View style={{ width: '35%' }}>
                                    {o.choiceNames && o.choiceNames.map((choice) => {
                                      return (
                                        <Text
                                          style={[
                                            {
                                              marginLeft: 10,
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.fieldtTxtColor,
                                              textDecorationLine:
                                                'line-through',
                                              color: Colors.tabRed,
                                            },
                                            !isTablet()
                                              ? {
                                                fontSize: windowWidth / 90,
                                              }
                                              : {},
                                          ]}>
                                          {choice}
                                        </Text>
                                      );
                                    })}
                                  </View>

                                  <View
                                    style={{
                                      width: '20%',
                                      alignItems: 'center',
                                    }}>
                                    {o.quantities ? (
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.fieldtTxtColor,
                                          },
                                          !isTablet()
                                            ? {
                                              fontSize: windowWidth / 90,
                                            }
                                            : {},
                                        ]}>
                                        x{o.quantities[0]}
                                      </Text>
                                    ) : (
                                      <></>
                                    )}
                                  </View>
                                </View>
                              );
                            })}
                          </View>
                        </View>
                      );
                    })
                  }

                  {/* {countdown > -1 &&
                                <Animated.View style={{ width: 100, height: 100, alignSelf: 'center', transform: [{ rotate: spin }], alignItems: 'center', justifyContent: 'center' }}>
                                    <Icon name={"reload"} size={70} color={Colors.primaryColor} />
                                </Animated.View>} */}
                </View>
              </>
            ) : (
              <></>
            )
          }
        </ScrollView>

        <View
          style={{
            // opacity: !isLoadingLocal ? 1 : 0,
            display: isLoadingLocal ? 'none' : 'flex',
          }}>
          {
            // (order.cartItems.length + (order.cartItemsCancelled ? order.cartItemsCancelled.length : 0)) > 1 &&
            props.viewOptions == false &&
              (userOrdersExpandedDict[order.uniqueId] === null ||
                userOrdersExpandedDict[order.uniqueId] === undefined) ? (
              <TouchableOpacity
                onPress={() => {
                  props.setScroll(true);

                  // order.expandView = true;

                  OutletStore.update((s) => {
                    s.userOrdersExpandedDict = {
                      ...userOrdersExpandedDict,
                      [order.uniqueId]: true,
                    };
                  });
                }}
                style={[
                  {
                    alignItems: 'center',
                    // position: 'absolute',
                    width: '100%',
                    // bottom: windowHeight * 0.04,
                    backgroundColor: Colors.whiteColor,
                    paddingBottom: 10,
                  },
                  !isTablet()
                    ? {
                      position: 'relative',
                      top: '5%',
                      // bottom: '1%'
                    }
                    : {},
                ]}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.fieldtTxtColor,
                    fontSize: windowWidth / 90,
                  }}>
                  Show Details
                </Text>
                <Icon
                  name="chevron-down-circle"
                  size={!isTablet() ? 20 : 35}
                  style={{ color: Colors.tabGold, padding: 4, elevation: 2 }}
                />
              </TouchableOpacity>
            ) : null
          }

          {userOrdersExpandedDict[order.uniqueId] === true &&
            props.viewOptions == false ? (
            <TouchableOpacity
              onPress={() => {
                props.setScroll(false);

                // order.expandView = null

                OutletStore.update((s) => {
                  s.userOrdersExpandedDict = {
                    ...userOrdersExpandedDict,
                    [order.uniqueId]: undefined,
                  };
                });
              }}
              style={[
                {
                  alignItems: 'center',
                  // position: 'absolute',
                  width: '100%',
                  // bottom: windowHeight * 0.05,
                  backgroundColor: Colors.whiteColor,
                  paddingBottom: 10,
                },
                !isTablet()
                  ? {
                    position: 'relative',
                    top: '5%',
                    // bottom: '1%'
                  }
                  : {},
              ]}>
              <Text
                style={[
                  {
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.fieldtTxtColor,
                    fontSize: windowWidth / 90,
                  },
                  !isTablet()
                    ? {
                      fontSize: windowWidth / 90,
                    }
                    : {},
                ]}>
                Show less
              </Text>
              <Icon
                name="chevron-up-circle"
                size={!isTablet() ? 20 : 35}
                style={{ color: Colors.tabGold, padding: 4, elevation: 2 }}
              />
            </TouchableOpacity>
          ) : null}

          {(countdown > -1 && props.viewOptions == true) ||
            (countdown > -1 &&
              props.viewOptions == false &&
              userOrdersExpandedDict[order.uniqueId] === true) ? (
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                width: 100,
                height: 100,
                position: 'absolute',
                alignSelf: 'center',
                bottom:
                  userOrdersExpandedDict[order.uniqueId] === true ? 120 : 70,
                alignContent: 'center',
              }}>
              <Animated.View
                style={{
                  width: !isTablet() ? 90 : 100,
                  height: !isTablet() ? 90 : 100,
                  alignSelf: 'center',
                  transform: [{ rotate: spin }],
                  justifyContent: 'center',
                  position: 'absolute',
                  alignItems: 'center',
                }}>
                <View style={{ position: 'absolute', right: 3 }}>
                  <Countdown
                    width={!isTablet() ? 90 : 100}
                    height={!isTablet() ? 90 : 100}
                  />
                </View>
              </Animated.View>
              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'absolute',
                }}>
                <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      color: '#37b2a7',
                      fontSize: 30,
                      width: 18,
                    },
                    !isTablet()
                      ? {
                        fontSize: windowWidth / 90,
                      }
                      : {},
                  ]}>
                  {countdown}
                </Text>
                <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      color: '#37b2a7',
                      fontSize: windowWidth / 90,
                      width: 18,
                    },
                    !isTablet()
                      ? {
                        fontSize: 8,
                      }
                      : {},
                  ]}>
                  sec
                </Text>
              </View>
            </View>
          ) : null}

          {/* //////////////////////////////////////////////////////////////////////////////// */}

          {/* {(Object.values(selectedOrderItemCheckDict).find(item => item && item.itemId) ||
                            Object.values(selectedOrderItemDeliveredCheckDict).find(item => item && item.itemId) ||
                            Object.values(selectedOrderItemCancelledCheckDict).find(item => item && item.itemId)) */}
          {currTickListType !== TICK_LIST_TYPE.UNTICK ? (
            <View
              style={{
                // bottom: selectedOutletCategoryList.length > 0 ? windowHeight * 0.001 : windowHeight * 0.05,
                bottom: windowHeight * 0.05,
                // backgroundColor: 'red',
                // backgroundColor: Colors.fieldtBgColor,
                position: 'absolute',
                width: '100%',
                height: !isTablet()
                  ? windowHeight * 0.12
                  : windowHeight * 0.08,

                flexDirection: 'row',
                alignItems: 'center',
                // borderWidth: 1,

                paddingBottom: 10,
                paddingTop: !isTablet() ? 0 : 10,
                paddingHorizontal: 15,

                ...(selectedOutletCategoryList.length > 0) & {
                  opacity: 0,
                },
              }}>
              {currTickListType !== TICK_LIST_TYPE.CANCELLED ? (
                <>
                  <TouchableOpacity
                    style={[
                      {
                        width:
                          currTickListType === TICK_LIST_TYPE.NORMAL
                            ? '30%'
                            : '45%',
                        height: !isTablet()
                          ? windowHeight * 0.05
                          : windowHeight * 0.04,

                        alignItems: 'center',
                        justifyContent: 'center',

                        backgroundColor:
                          currTickListType === TICK_LIST_TYPE.NORMAL
                            ? Colors.primaryColor
                            : Colors.tabRed,
                        borderRadius: 8,

                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 1,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 2.22,
                        elevation: 3,
                      },
                      !isTablet()
                        ? {
                          position: 'relative',
                          bottom: '6%',
                        }
                        : {},
                    ]}
                    onPress={() => {
                      if (currTickListType === TICK_LIST_TYPE.NORMAL) {
                        //deliverUserOrder();
                        setDeliverModal(true)
                      } else {
                        deliverUserOrderUndo();
                      }
                    }}>
                    <Text
                      style={[
                        {
                          color: Colors.whiteColor,
                          fontFamily: 'NunitoSans-SemiBold',
                          fontSize: 14,
                        },
                        !isTablet()
                          ? {
                            fontSize: windowWidth / 90,
                            bottom: Platform.OS === 'android' ? 1 : 0,
                          }
                          : {},
                      ]}>
                      {currTickListType === TICK_LIST_TYPE.NORMAL
                        ? 'DELIVER'
                        : 'UNDO'}
                    </Text>
                  </TouchableOpacity>

                  <View
                    style={{
                      width:
                        currTickListType === TICK_LIST_TYPE.NORMAL
                          ? '5%'
                          : '10%',
                    }} />
                </>
              ) : (
                <></>
              )}

              {/* //////////////////////////////////////////////////////////////////// */}

              {currTickListType !== TICK_LIST_TYPE.DELIVERED ? (
                <>
                  {
                    (currOutlet && currOutlet.privileges &&
                      currOutlet.privileges.includes(PRIVILEGES_NAME.REJECT_ITEM))
                      && privileges && privileges.includes(PRIVILEGES_NAME.REJECT_ITEM) ?
                      <TouchableOpacity
                        style={[
                          {
                            width:
                              currTickListType === TICK_LIST_TYPE.NORMAL
                                ? '30%'
                                : '45%',
                            height: !isTablet()
                              ? windowHeight * 0.05
                              : windowHeight * 0.04,

                            alignItems: 'center',
                            justifyContent: 'center',

                            backgroundColor:
                              currTickListType === TICK_LIST_TYPE.NORMAL
                                ? Colors.tabRed
                                : Colors.primaryColor,
                            borderRadius: 8,

                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 1,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 2.22,
                            elevation: 3,
                          },
                          !isTablet()
                            ? {
                              position: 'relative',
                              bottom: '6%',
                            }
                            : {},
                        ]}
                        onPress={() => {
                          if (order.paymentDetails && (
                            order.paymentDetails.channel
                            // order.paymentDetails.txn_ID !== undefined ||
                            // order.paymentDetails.txnId !== undefined
                          )) {
                            Alert.alert('Info', 'Paid item is unable to be rejected.');
                          }
                          else if (currTickListType === TICK_LIST_TYPE.NORMAL) {
                            //cancelUserOrder();
                            setCancelModal(true);
                          } else {
                            undoUserOrderCancelled();
                          }
                        }}>
                        <Text
                          style={[
                            {
                              color: Colors.whiteColor,
                              fontFamily: 'NunitoSans-SemiBold',
                              fontSize: 14,
                            },
                            !isTablet()
                              ? {
                                fontSize: windowWidth / 90,
                                bottom: Platform.OS === 'android' ? 1 : 0,
                              }
                              : {},
                          ]}>
                          {currTickListType === TICK_LIST_TYPE.NORMAL
                            ? 'REJECT'
                            : 'UNDO'}
                        </Text>
                      </TouchableOpacity>
                      :
                      <>
                        <TouchableOpacity
                          style={[
                            {
                              width:
                                currTickListType === TICK_LIST_TYPE.NORMAL
                                  ? '30%'
                                  : '45%',
                              height: !isTablet()
                                ? windowHeight * 0.05
                                : windowHeight * 0.04,

                              alignItems: 'center',
                              justifyContent: 'center',
                            },
                            !isTablet()
                              ? {
                                position: 'relative',
                                bottom: '6%',
                              }
                              : {},
                          ]} />
                      </>
                  }
                  <View
                    style={{
                      width:
                        currTickListType === TICK_LIST_TYPE.NORMAL
                          ? '5%'
                          : '10%',
                    }} />
                </>
              ) : (
                <></>
              )}

              {/* //////////////////////////////////////////////////////////////////// */}

              <TouchableOpacity
                style={[
                  {
                    width:
                      currTickListType === TICK_LIST_TYPE.NORMAL
                        ? '30%'
                        : '45%',
                    height: !isTablet()
                      ? windowHeight * 0.05
                      : windowHeight * 0.04,

                    alignItems: 'center',
                    justifyContent: 'center',

                    backgroundColor: Colors.lightGrey,
                    borderRadius: 8,

                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 1,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 2.22,
                    elevation: 3,
                  },
                  !isTablet()
                    ? {
                      position: 'relative',
                      bottom: '6%',
                    }
                    : {},
                ]}
                onPress={() => {
                  setSelectedOrderItemCheckDict({});
                  setSelectedOrderItemDeliveredCheckDict({});
                  setSelectedOrderItemCancelledCheckDict({});
                }}>
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontFamily: 'NunitoSans-SemiBold',
                      fontSize: 16,
                    },
                    !isTablet()
                      ? {
                        fontSize: windowWidth / 80,
                        bottom: Platform.OS === 'android' ? 1 : 0,
                      }
                      : {},
                  ]}>
                  CANCEL
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            <></>
          )}

          {/* //////////////////////////////////////////////////////////////////////////////// */}

          <View
            style={[
              {
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 15,
                width: '100%',
                justifyContent: 'space-between',
              },
              !isTablet()
                ? {
                  paddingTop: windowHeight * 0.019,
                }
                : {},
            ]}>
            {selectedOutletCategoryList.length <= 0 ?
              <TouchableOpacity
                onPress={() => {
                  Alert.alert(
                    'Info',
                    'Are you sure you want to complete this order?',
                    [
                      {
                        text: 'YES',
                        onPress: () => {
                          setIsLoadingLocal(true);

                          props.completeOrder(order.uniqueId, () => {
                            setTimeout(() => {
                              setIsLoadingLocal(false);
                            }, 0);
                          }, order);
                        },
                      },
                      {
                        text: 'NO',
                        onPress: () => { },
                      },
                    ],
                    { cancelable: false },
                  );
                }}
                style={[
                  {
                    marginBottom: 10,
                    height: windowWidth <= 1133 ? 28 : 35,
                    borderRadius: 8,
                    alignSelf: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                    // width: (windowWidth / 5) - 10,
                    width: order.isPrioritizedOrder ? '85%' : '100%',
                    backgroundColor: Colors.fieldtBgColor,
                    borderWidth: 1,
                  },
                  !isTablet()
                    ? {
                      height: 35,
                    }
                    : {},
                ]}>
                <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      color: Colors.fieldtTxtColor,
                      fontWeight: 'bold',
                    },
                    !isTablet()
                      ? {
                        fontSize: windowWidth / 60,
                      }
                      : {},
                  ]}>
                  DONE
                </Text>
              </TouchableOpacity>
              :
              <TouchableOpacity
                disabled
                onPress={() => {
                  Alert.alert(
                    'Info',
                    'Are you sure you want to complete this order?',
                    [
                      {
                        text: 'YES',
                        onPress: () => {
                          setIsLoadingLocal(true);

                          props.completeOrder(order.uniqueId, () => {
                            setTimeout(() => {
                              setIsLoadingLocal(false);
                            }, 0);
                          }, order);
                        },
                      },
                      {
                        text: 'NO',
                        onPress: () => { },
                      },
                    ],
                    { cancelable: false },
                  );
                }}
                style={[
                  {
                    marginBottom: 10,
                    height: windowWidth <= 1133 ? 28 : 35,
                    borderRadius: 8,
                    alignSelf: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                    // width: (windowWidth / 5) - 10,
                    width: order.isPrioritizedOrder ? '85%' : '100%',
                    // backgroundColor: Colors.fieldtBgColor,
                    // borderWidth: 1,
                  },
                  !isTablet()
                    ? {
                      height: 35,
                    }
                    : {},
                ]}>
                {/* <Text
                  style={[
                    {
                      fontFamily: 'NunitoSans-Bold',
                      color: Colors.fieldtTxtColor,
                      fontWeight: 'bold',
                    },
                    !isTablet()
                      ? {
                        fontSize: windowWidth / 60,
                      }
                      : {},
                  ]}>
                  DONE
                </Text> */}
              </TouchableOpacity>
            }

            {order.isPrioritizedOrder && (
              <View
                style={{
                  // opacity: order.isPrioritizedOrder ? 100 : 0,
                  // position: 'absolute',
                  // right: '25%',
                  top: -5,
                }}>
                <FontAwesome
                  name={'star'}
                  size={!isTablet() ? 15 : 26}
                  color={Colors.primaryColor}
                />
              </View>
            )}
          </View>
        </View>

        {isLoadingLocal ? (
          <View
            style={{
              // opacity: isLoadingLocal ? 1 : 0,
              display: isLoadingLocal ? 'flex' : 'none',
              position: 'absolute',
              justifyContent: 'center',
              alignItems: 'center',
              width: '100%',
              height: '100%',
              paddingBottom: '10%',
            }}>
            <ActivityIndicator color={Colors.primaryColor} size={'large'} />
          </View>
        ) : (
          <></>
        )}
      </View>

      {/* <View
        style={{
          // display: isLoadingLocal ? 'flex' : 'none',
          opacity: isLoadingLocal ? 1 : 0,
          position: 'absolute',
          justifyContent: 'center',
          alignItems: 'center',
          flex: 1,
          backgroundColor: Colors.whiteColor,
          borderBottomLeftRadius: 15,
          borderBottomRightRadius: 15,
          height: '100%',
          width: '100%',
          // height: props.viewOptions ? (windowHeight / 2 + 80) : userOrdersExpandedDict[order.uniqueId] === true ? (order.cartItems.length + (order.cartItemsCancelled ? order.cartItemsCancelled.length : 0)) * (windowHeight * 0.2) : (order.cartItems.length + (order.cartItemsCancelled ? order.cartItemsCancelled.length : 0)) * (windowHeight * 0.1),
          // height: props.viewOptions
          //   ? windowHeight / 2 + 80
          //   : userOrdersExpandedDict[order.uniqueId] === true &&
          //     order.cartItems.length +
          //     (order.cartItemsCancelled
          //       ? order.cartItemsCancelled.length
          //       : 0) >
          //     5
          //     ? windowHeight / 2 + 80
          //     : userOrdersExpandedDict[order.uniqueId] === true &&
          //       order.cartItems.length +
          //       (order.cartItemsCancelled
          //         ? order.cartItemsCancelled.length
          //         : 0) <=
          //       5
          //       ? 'auto'
          //       : windowHeight / 4,
          // height: props.viewOptions ? (windowHeight / 2 + 80) : 'auto',

          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 1,

          ...(kitchenScrollItem.uniqueId === order.uniqueId && {
            borderWidth: 3,
            borderTopWidth: 0,
            borderColor: Colors.tabGold,
          }),
        }}>
        <ActivityIndicator color={Colors.primaryColor} size={'large'} />
      </View> */}

      <ModalView
        style={{}}
        supportedOrientations={['portrait', 'landscape']}
        visible={deliverModal}
        transparent
        animationType={'fade'}
      >
        <View style={{
          flex: 1,
          backgroundColor: Colors.modalBgColor,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <View style={{
            height: windowWidth * 0.33,
            width: windowWidth * 0.35,
            backgroundColor: Colors.whiteColor,
            borderRadius: 12,
            padding: windowWidth * 0.03,
            alignItems: 'center',
            justifyContent: 'center',

            ...getTransformForModalInsideNavigation(),
          }}>
            <TouchableOpacity
              style={{
                position: 'absolute',
                right: windowWidth * 0.02,
                top: windowWidth * 0.02,
                elevation: 1000,
                zIndex: 1000,
              }}
              onPress={() => {
                setDeliverModal(false);
              }}>
              <AntDesign name="closecircle" size={switchMerchant ? 20 : 25} color={Colors.fieldtTxtColor} />
            </TouchableOpacity>
            <View style={{
              alignItems: 'center',
              flex: 1,
            }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                textAlign: 'center',
                fontSize: switchMerchant ? 14 : 24,
              }}>
                Deliver Quantity
              </Text>
            </View>
            <View style={{ alignItems: 'center', flex: 1, justifyContent: 'center' }}>
              <View style={{
                width: 250,
                height: switchMerchant ? 100 : 200,
                justifyContent: 'center',
                alignItems: 'center',
                margin: 'auto',
                color: 'black',
              }}>
                <SmoothPicker
                  initialScrollToIndex={spDeliverSelected}
                  onScrollToIndexFailed={() => { }}
                  keyExtractor={(_, index) => index.toString()}
                  showsVerticalScrollIndicator={false}
                  data={spDataNumberList}
                  scrollAnimation
                  onSelected={({ item, index }) => setSPDeliverSelected(index)}
                  renderItem={option => renderSPItem(option, spDeliverSelected, true)}
                  magnet
                />
              </View>
            </View>
            <View style={{ alignItems: 'center', flex: 1, justifyContent: 'flex-end' }}>
              <TouchableOpacity
                onPress={() => { deliverUserOrder(); }}
                style={{ backgroundColor: Colors.primaryColor, padding: 10, paddingHorizontal: 25, borderRadius: 8, }}>
                <Text style={{ color: Colors.whiteColor, fontSize: switchMerchant ? 12 : 16, fontFamily: 'NunitoSans-Bold' }}>DONE</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ModalView>
      <ModalView
        style={{}}
        supportedOrientations={['portrait', 'landscape']}
        visible={cancelModal}
        transparent
        animationType={'fade'}
      >
        <View style={{
          flex: 1,
          backgroundColor: Colors.modalBgColor,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <View style={{
            height: windowWidth * 0.33,
            width: windowWidth * 0.35,
            backgroundColor: Colors.whiteColor,
            borderRadius: 12,
            padding: windowWidth * 0.03,
            alignItems: 'center',
            justifyContent: 'center',

            ...getTransformForModalInsideNavigation(),
          }}>
            <TouchableOpacity
              style={{
                position: 'absolute',
                right: windowWidth * 0.02,
                top: windowWidth * 0.02,
                elevation: 1000,
                zIndex: 1000,
              }}
              onPress={() => {
                setCancelModal(false);
              }}>
              <AntDesign name="closecircle" size={switchMerchant ? 20 : 25} color={Colors.fieldtTxtColor} />
            </TouchableOpacity>
            <View style={{
              alignItems: 'center',
              flex: 0.7,
            }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                textAlign: 'center',
                fontSize: switchMerchant ? 14 : 24,
              }}>
                Reject Quantity
              </Text>
            </View>
            <View style={{ alignItems: 'center', flex: 1, justifyContent: 'center' }}>
              <View style={{
                width: 250,
                height: switchMerchant ? 100 : 200,
                justifyContent: 'center',
                alignItems: 'center',
                margin: 'auto',
                color: 'black',
              }}>
                <SmoothPicker
                  initialScrollToIndex={spRejectSelected}
                  onScrollToIndexFailed={() => { }}
                  keyExtractor={(_, index) => index.toString()}
                  showsVerticalScrollIndicator={false}
                  data={spDataNumberList}
                  scrollAnimation
                  onSelected={({ item, index }) => setSPRejectSelected(index)}
                  renderItem={option => renderSPItem(option, spRejectSelected, true)}
                  magnet
                />
              </View>
            </View>
            <View style={{ alignItems: 'center', flex: 1, justifyContent: 'flex-end' }}>
              <TouchableOpacity
                onPress={() => { cancelUserOrder(); }}
                style={{ backgroundColor: Colors.primaryColor, padding: 10, paddingHorizontal: 25, borderRadius: 8, }}>
                <Text style={{ color: Colors.whiteColor, fontSize: switchMerchant ? 12 : 16, fontFamily: 'NunitoSans-Bold' }}>DONE</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ModalView>
    </View>
  );
};

export default KitchenOrder;
