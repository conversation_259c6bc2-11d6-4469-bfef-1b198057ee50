import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Dimensions,
    Modal,
    Platform,
    useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import { TextInput, FlatList } from 'react-native-gesture-handler';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from '../util/User';
import LoginScreen from './LoginScreen';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from 'react-native-check-box';
import Close from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import Styles from '../constant/Styles';
import { RadioButton } from "react-native-paper";
import moment, { isDate } from 'moment';
import Barcode from "react-native-barcode-svg";
import Switch from 'react-native-switch-pro';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
    getTransformForScreenInsideNavigation,
    isTablet
} from '../util/common';
import { UserStore } from '../store/userStore';
import QRCode from 'react-native-qrcode-svg';
import { MerchantStore } from '../store/merchantStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import AsyncImage from '../components/asyncImage';
import { EXPAND_TAB_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import {
    BARCODE_SETTING_LABEL_SIZE_LIST,
    BARCODE_SETTING_BARCODE_TYPE,
} from '../constant/common';

const SettingBarcodeScreen = props => {
    const {
        navigation,
    } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const [keyboardHeight] = useKeyboard();

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();


    const [loading, setLoading] = useState(false);
    const [showDistance, setShowDistance] = useState('');
    const [expiryPeriod, setExpiryPeriod] = useState('');
    const [extentionCharges, setExtentionCharges] = useState('');
    const [extentionDuration, setExtentionDuration] = useState('');
    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [pickerMode, setPickerMode] = useState('datetime');
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const [merchantDisplay, setMerchantDisplay] = useState(false);
    const [shift, setShift] = useState(false);
    const [tax, setTax] = useState(false);
    const [sample, setSample] = useState(true);
    const [redemption, setRedemption] = useState(false);
    const [redemptionList, setRedemptionList] = useState(true);
    const [redemptionAdd, setRedemptionAdd] = useState(false);
    const [order, setOrder] = useState(false);
    const [previousState, setPreviousState] = useState(false);
    const [receipt, setReceipt] = useState([]);
    const [detail, setDetail] = useState([]);
    const [merchantInfo, setMerchantInfo] = useState([]);
    const [outlet, setOutlet] = useState([]);
    const [outletInfo, setOutletInfo] = useState([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [merInfo, setMerInfo] = useState([]);
    const [merchantId, setMerchantId] = useState([]);
    const [show, setShow] = useState(false);
    const [showModal3, setShowModal3] = useState(false);
    const [showModal4, setShowModal4] = useState(false);
    const [showModal5, setShowModal5] = useState(false);
    const [closingAmount, setClosingAmount] = useState('');
    const [options, setOptions] = useState([]);
    const [shift1, setShift1] = useState([]);
    const [status, setStatus] = useState(false);
    const [logo, setLogo] = useState('');
    const [cover, setCover] = useState('');
    const [name, setName] = useState('');
    const [tname, setTname] = useState('');
    const [rate, setRate] = useState('');
    const [address, setAddress] = useState('');
    const [phone, setPhone] = useState('');
    const [payment, setPayment] = useState('');
    const [time, setTime] = useState('');
    const [statue, setStatue] = useState('');
    const [status1, setStatus1] = useState(false);
    const [outlets, setOutlets] = useState([]);
    const [outletId, setOutletId] = useState(null);
    const [myTextInput, setMyTextInput] = useState(React.createRef());
    const [start_time, setStart_time] = useState(false);
    const [end_time, setEnd_time] = useState(false);
    const [rev_time, setRev_time] = useState('');
    const [category, setCategory] = useState('');
    const [close, setClose] = useState('Closed');
    const [showNote, setShowNote] = useState(false);
    const [expandView, setExpandView] = useState(false);
    const [value, setValue] = useState('');
    const [extendOption, setExtendOption] = useState([
        { optionId: 1, price: 20, day: 7, days: false },
    ]);
    const [redemptionInfo, setRedemptionInfo] = useState([]);
    const [alloutlet, setAlloutlet] = useState([]);
    const [discount, setDiscount] = useState('');
    const [amount1, setAmount1] = useState('');
    const [selectedCategoryId, setSelectedCategoryId] = useState('');
    const [categoryOutlet, setCategoryOutlet] = useState([]);
    const [extend, setExtend] = useState([]);
    const [outletss, setOutletss] = useState([]);
    const [redemptionDetail, setRedemptionDetail] = useState([]);
    const [outletInfo1, setOutletInfo1] = useState([]);
    const [category1, setCategory1] = useState([]);
    // const [merchantName, setMerchantName] = useState('');
    const [addOutletName, setAddOutletName] = useState('');
    const [addOutletWindow, setAddOutletWindow] = useState(false);
    const [taxList, setTaxList] = useState([]);
    const [note1, setNote1] = useState("");
    const [note2, setNote2] = useState("");
    const [note3, setNote3] = useState("");
    const [openings, setOpenings] = useState([]);
    const [bcFormat, setBcFormat] = useState('barcode');

    ////////////////////////////////////////////////////////////////

    const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
    const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

    const allOutlets = MerchantStore.useState(s => s.allOutlets);

    const userName = UserStore.useState(s => s.name);
    const merchantName = MerchantStore.useState(s => s.name);

    const currOutlet = MerchantStore.useState(s => s.currOutlet);
    const currOutletId = MerchantStore.useState(s => s.currOutletId);

    const [temp, setTemp] = useState('');

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const merchantLogo = MerchantStore.useState(s => s.logo);
    const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

    const [bcLabelSize, setBcLabelSize] = useState('30MMX20MM');
    const [viewSize, setViewSize] = useState({ width: 240, height: 170 }); // Default view size
    const [bcType, setBcType] = useState('CODE39');

    useEffect(() => {
        const getViewSize = () => {
            switch (bcLabelSize) {
                case '30MMX20MM':
                    return { width: 240, height: 170 }; // Set the width and height for the selected label size
                case '35MMX25MM':
                    return { width: 250, height: 180 };
                case '40MMX25MM':
                    return { width: 260, height: 180 };
                case '40MMX30MM':
                    return { width: 260, height: 190 };
                case '50MMX30MM':
                    return { width: 280, height: 190 };
                case '50MMX40MM':
                    return { width: 280, height: 210 };
                case '60MMX40MM':
                    return { width: 300, height: 210 };
                case '65MMX40MM':
                    return { width: 310, height: 210 };
                case '70MMX30MM':
                    return { width: 320, height: 190 };
                case '80MMX60MM':
                    return { width: 340, height: 250 };
                case '80MMX85MM':
                    return { width: 340, height: 300 };
                case '100MMX50MM':
                    return { width: 380, height: 230 };

                // Add cases for other label sizes
                default:
                    return { width: 240, height: 170 }; // Default width and height
            }
        };

        // Update the view size when the label size changes
        setViewSize(getViewSize());
    }, [bcLabelSize]);

    useEffect(() => {
        setLogo(merchantLogo);
    }, [merchantLogo]);

    useEffect(() => {
        setName(merchantName);
    }, [merchantName]);

    useEffect(() => {
        const selectedTargetOutlet = allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId);

        if (selectedTargetOutlet) {
            setPhone(selectedTargetOutlet.phone);
            setAddress(selectedTargetOutlet.address);
        }
    }, [selectedTargetOutletId]);

    useEffect(() => {
        if (currOutlet) {
            setPhone(currOutlet.phone);
            setName(currOutlet.name);
        }
    }, [currOutlet]);

    useEffect(() => {
        setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

        if (selectedTargetOutletId === '' && allOutlets.length > 0) {
            setSelectedTargetOutletId(allOutlets[0].uniqueId);
        }
    }, [allOutlets]);

    const setState = () => { };

    // navigation.dangerouslyGetParent().setOptions({
    //   tabBarVisible: false,
    // });

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //   ? { right: windowWidth * 0.1 }
                    //   : {},
                    // windowWidth <= 768
                    //   ? { right: 20 }
                    //   : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Barcode Settings
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    // componentDidMount = () => {

    //   ApiClient.GET(API.getOutletByMerchant + User.getMerchantId()).then((result) => {
    //     setState({ outletInfo: result });
    //     result.map((element) => {
    //       setState({
    //         outletId: element.id,
    //         outletName: element.name,
    //         merchantName: element.merchant.name
    //       });
    //     });
    //   });

    //   ApiClient.GET(API.getAllSettingRedemption + User.getMerchantId()).then((result) => {
    //     setState({ redemptionInfo: result });
    //   });

    //   outlet()
    //   ApiClient.GET(API.getOutletCategory + User.getOutletId()).then((result) => {
    //     if (result !== undefined) {
    //       setState({ categoryOutlet: result });
    //     }

    //   });
    // }

    const outletById = (param) => {
        //<= outletId is not used in the param
        ApiClient.GET(API.outlet2 + param).then((result) => {
            //<= all you need to do is add parameter here
            // console.log("RESULT OUTLET", result)
            setState({ outlet: result, openings: result.openings });
            // console.log("openings", openings)
            setState({ status: result.status });
            setState({ merInfo: result.merchant[0] });
            setState({ cover: result.cover });
            setState({ logo: result.merchant[0].logo });
            setState({ openings: result.openings });

            myTextInput.current.clear();
        });
    }

    // jian
    function ChangeTextInReceipt() {
        submitData.current = true
        add(function (prevData) {
            return [
                {
                    id: v4(),
                    note,
                    date,
                    time,
                },
                ...prevData,
            ];
        });
    }

    const editOutlet = (param) => {
        // var body = {
        //   outletId: param,
        //   coverUrl: cover,
        //   logoUrl: logo,
        //   address: address,
        //   name: name,
        //   latlng: '',
        //   phone: phone,
        //   taxId: '',
        //   status: '1',
        //   isBusy: '1',
        //   reservationStatus: true,
        //   openings: [
        //     {
        //       week: 'Monday',
        //       startTime: s_time,
        //       endTime: e_time,
        //     },
        //     {
        //       week: 'Tuesday',
        //       startTime: s_time1,
        //       endTime: e_time1,
        //     },
        //     {
        //       week: 'Wednesday',
        //       startTime: s_time2,
        //       endTime: e_time2,
        //     },
        //     {
        //       week: 'Thursday',
        //       startTime: s_time3,
        //       endTime: e_time3,
        //     },
        //     {
        //       week: 'Friday',
        //       startTime: s_time4,
        //       endTime: e_time4,
        //     },
        //     {
        //       week: 'Saturday',
        //       startTime: s_time5,
        //       endTime: e_time5,
        //     },
        //     {
        //       week: 'Sunday',
        //       startTime: s_time6,
        //       endTime: e_time6,
        //     },
        //   ],
        //   payments: [
        //     {
        //       name: payment,
        //     },
        //   ],
        // };

        Alert.alert(
            'Success',
            'Updated successfully',
            [
                {
                    text: 'OK',
                    onPress: () => { },
                },
            ],
            { cancelable: false },
        );

        // ApiClient.PATCH(API.editOutlet, body, false).then((result) => {
        //   if (result.id != null) {
        //     Alert.alert(
        //       'Congratulation!',
        //       'You Have Successfully Inserted',
        //       [
        //         {
        //           text: 'OK',
        //           onPress: () => { },
        //         },
        //       ],
        //       { cancelable: false },
        //     );
        //   }
        // });

        outletById(param);
        myTextInput.current.clear();
    }

    const handleChoosePhoto = () => {
        const options = {
            mediaType: 'photo',
            quality: 0.5,
            includeBase64: false,
            maxWidth: 512,
            maxHeight: 512,
        }
        launchImageLibrary(options, (response) => {
            if (response.didCancel) {
            } else if (response.error) {
                Alert.alert(response.error.toString());
            } else {
                setMerchantLogoImage(response.uri);
                setMerchantLogoImageType(response.uri.slice(response.uri.lastIndexOf('.')));
            }
        });
    }

    const _logout = async () => {
        await AsyncStorage.clear();
        User.setlogin(false);
        User.getRefreshMainScreen();
    };

    const addSection = () => {
        // setState({ showNote: true });
        setShowNote(true);
    }

    const updateReceiptDetails = async (param) => {
        setLoading(true);

        var body = {
            outletId: currOutletId,

            merchantId: merchantId,
            phone: phone,

            outletName: name,

            receiptNote: note1 || '',
        };

        // console.log('body', body);

        ApiClient.POST(API.updateReceiptDetails, body, false).then((result) => {

        });

        Alert.alert(
            'Success',
            'Receipt details has been updated.',
            [
                {
                    text: 'OK',
                    onPress: () => {
                        setLoading(false);
                    },
                },
            ],
            { cancelable: false },
        );
    };

    const updateOutletDetails = async (param) => {
        setLoading(true);

        var body = {
            bcLabelSize: bcLabelSize,
            bcFormat: bcFormat,
            bcType: bcType,
        };


        APILocal.updateOutletDetails({ body }).then((result) => {
            // ApiClient.POST(API.updateOutletDetails, body, false).then((result) => {
            if (result && result.status === 'success') {
                Alert.alert(
                    'Success',
                    'Outlet details has been updated, and will be synced to devices across the outlet.',
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                setLoading(false);
                            },
                        },
                    ],
                    { cancelable: false },
                );
            }
        });
    };

    // function end

    const week = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

    return (
        // <View style={styles.container}>
        //   <View style={styles.sidebar}>
        <UserIdleWrapper disabled={!isMounted}>
            <View style={[styles.container, !isTablet() ? {
                transform: [
                    { scaleX: 1 },
                    { scaleY: 1 },
                ],
            } : {},
            {
                ...getTransformForScreenInsideNavigation(),
            }
            ]}>
                {/* <View style={[styles.sidebar, !isTablet() ? {
                    width: windowWidth * 0.08,
                } : {}, switchMerchant ? {
                    // width: '10%'
                } : {}, {
                    width: windowWidth * 0.08,
                }]}>
                    <SideBar navigation={props.navigation} selectedTab={10} expandSettings={true} />
                </View> */}

                <ScrollView
                    showsVerticalScrollIndicator={false}
                    // scrollEnabled={switchMerchant}
                    style={{}}
                    contentContainerStyle={{
                        flex: 1,
                        paddingBottom: switchMerchant ? windowHeight * 0.001 : windowHeight * 0.03,
                        backgroundColor: Colors.highlightColor,
                    }}
                >
                    <ScrollView horizontal={true}>

                        <View style={[styles.content, {
                            width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
                        }]}>

                            <View
                                style={{
                                    backgroundColor: Colors.whiteColor,
                                    // height: windowHeight - 120,
                                    height: '100%',

                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    // elevation: 1,
                                    elevation: 3,
                                    borderRadius: 5,
                                    // borderRadius: 8,
                                }}>
                                <KeyboardAwareScrollView
                                    showsVerticalScrollIndicator={false}
                                    contentContainerStyle={{
                                        //top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                                    }}>
                                    <View style={{ flexDirection: 'row', padding: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 25 : 30 }}>
                                        <View
                                            style={{
                                                height: '100%',
                                                width: '50%',
                                                // flex: 2,
                                            }}>
                                            {/* left */}

                                            <View style={{ marginTop: 20, alignItems: 'center', }}>

                                                <View style={{

                                                    marginBottom: 20,
                                                    borderWidth: 2,
                                                    borderColor: Colors.blackColor,
                                                    borderRadius: 5,
                                                    width: viewSize.width,
                                                    height: viewSize.height,

                                                }}>

                                                    <View style={{
                                                        alignItems: 'center', marginBottom: 3
                                                    }}>
                                                        <Text style={{
                                                            Colors: Colors.blackColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',

                                                        }}>
                                                            SKU
                                                        </Text>
                                                    </View>
                                                    <View style={{
                                                        marginBottom: 3,
                                                        borderBottomWidth: 1,  // Adjust the width of the line as needed
                                                        borderColor: Colors.blackColor,  // Adjust the Colors of the line as needed
                                                        alignSelf: 'flex-end',  // Align the line to the bottom of the container
                                                        width: '100%',  // Adjust the width of the line as needed
                                                    }} />

                                                    <View style={[{ marginTop: 5, alignItems: 'center' }]}>
                                                        {bcFormat === 'barcode' ? (
                                                            // <Barcode value="Hellod" format="CODE128" width={viewSize.width * 0.0085} height={viewSize.height - 60} />
                                                            <Barcode
                                                                value="Hellod"          // 条码内容
                                                                format={bcType}       // 支持 CODE128, CODE39, EAN13, etc.
                                                                width={2}               // 条码线条宽度（单位 px）
                                                                height={viewSize.height - 60} // 条码高度
                                                                lineColor="#000000"     // 条码颜色
                                                                background="#ffffff"    // 背景颜色
                                                            />
                                                        ) : (
                                                            <QRCode
                                                                //    value
                                                                size={viewSize.width * 0.46}
                                                                logoBackgroundColor="transparent"

                                                            //    getRef={(ref) => {
                                                            //      setQRCodeTakeawayRef(ref);

                                                            // if (!qrCodeTakeawayRef) {
                                                            //   TableStore.update(s => {
                                                            //     s.qrCodeTakeawayRef = ref;
                                                            //   });
                                                            // }
                                                            //     }}
                                                            />
                                                        )}
                                                    </View>
                                                </View>

                                            </View>
                                        </View>
                                        {/* right */}
                                        <View
                                            style={{
                                                width: '50%',
                                                borderLeftWidth: StyleSheet.hairlineWidth,

                                            }}>

                                            <View style={{ marginLeft: 50, }}>
                                                <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginBottom: 5, marginTop: 3 }}>
                                                    Label Size
                                                </Text>
                                                <DropDownPicker
                                                    // controller={instance => setController1(instance)}
                                                    arrowColor={Colors.primaryColor}
                                                    arrowSize={switchMerchant ? 13 : 23}
                                                    arrowStyle={[
                                                        { fontWeight: 'bold' },
                                                        switchMerchant
                                                            ? {
                                                                top: windowHeight * -0.005,
                                                                height: '180%',
                                                                // borderWidth: 1
                                                            }
                                                            : {},
                                                    ]}
                                                    labelStyle={[
                                                        { fontFamily: 'NunitoSans-Regular' },
                                                        switchMerchant
                                                            ? {
                                                                fontSize: 10,
                                                            }
                                                            : {},
                                                    ]}
                                                    itemStyle={[
                                                        { justifyContent: 'flex-start', marginLeft: 5 },
                                                        switchMerchant
                                                            ? {
                                                                fontSize: 10,
                                                            }
                                                            : {},
                                                    ]}
                                                    placeholderStyle={[
                                                        { Colors: 'black' },
                                                        switchMerchant
                                                            ? {
                                                                fontSize: 10,
                                                            }
                                                            : {},
                                                    ]}
                                                    style={[
                                                        {

                                                            width: 180,

                                                            height: 40,
                                                            paddingHorizontal: 5,
                                                            paddingVertical: 0,
                                                            borderRadius: 5,
                                                            borderColor: '#E5E5E5',
                                                            borderWidth: 1,

                                                            marginRight: 2,
                                                            paddingLeft: 2,
                                                            shadowColor: '#000',
                                                            shadowOffset: {
                                                                width: 0,
                                                                height: 2,
                                                            },
                                                            shadowOpacity: 0.22,
                                                            shadowRadius: 3.22,
                                                            elevation: 3,
                                                        },
                                                        switchMerchant
                                                            ? {
                                                                fontSize: 10,
                                                                width: windowWidth * 0.152,
                                                            }
                                                            : {},
                                                    ]}
                                                    dropDownStyle={{
                                                        paddingLeft: 2,
                                                        width: switchMerchant
                                                            ? windowWidth * 0.152
                                                            : 180,
                                                    }}
                                                    items={BARCODE_SETTING_LABEL_SIZE_LIST}
                                                    placeholder='Select Label Size'
                                                    defaultValue={bcLabelSize}
                                                    onChangeItem={(item) => {
                                                        setBcLabelSize(item.value);
                                                    }}
                                                />

                                                <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginBottom: 5, marginTop: 20 }}>
                                                    Type
                                                </Text>
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        setBcFormat('barcode')

                                                    }}>
                                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                        <RadioButton
                                                            status={bcFormat === 'barcode' ? 'checked' : 'unchecked'
                                                            }
                                                        />
                                                        <Text style={{
                                                            fontSize: 14,
                                                            Colors: 'black',
                                                            fontFamily: 'Nunitosans-Regular',
                                                            marginLeft: 2,

                                                        }}>
                                                            Barcode
                                                        </Text>
                                                    </View>

                                                </TouchableOpacity>
                                                <TouchableOpacity
                                                    onPress={() => { setBcFormat('qrcode') }}>
                                                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                        <RadioButton
                                                            status={bcFormat === 'qrcode' ? 'checked' : 'unchecked'
                                                            }
                                                        />
                                                        <Text style={{
                                                            fontSize: 14,
                                                            Colors: 'black',
                                                            fontFamily: 'Nunitosans-Regular',
                                                            marginLeft: 2,

                                                        }}>
                                                            QR Code
                                                        </Text>
                                                    </View>
                                                </TouchableOpacity>

                                                {bcFormat === 'barcode' ?
                                                    <View>
                                                        <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginBottom: 5, marginTop: 20 }}>
                                                            Barcode Type
                                                        </Text>
                                                        <DropDownPicker
                                                            // controller={instance => setController1(instance)}
                                                            arrowColor={Colors.primaryColor}
                                                            arrowSize={switchMerchant ? 13 : 23}
                                                            arrowStyle={[
                                                                { fontWeight: 'bold' },
                                                                switchMerchant
                                                                    ? {
                                                                        top: windowHeight * -0.005,
                                                                        height: '180%',
                                                                        // borderWidth: 1
                                                                    }
                                                                    : {},
                                                            ]}
                                                            labelStyle={[
                                                                { fontFamily: 'NunitoSans-Regular' },
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                    }
                                                                    : {},
                                                            ]}
                                                            itemStyle={[
                                                                { justifyContent: 'flex-start', marginLeft: 5 },
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                    }
                                                                    : {},
                                                            ]}
                                                            placeholderStyle={[
                                                                { Colors: 'black' },
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                    }
                                                                    : {},
                                                            ]}
                                                            style={[
                                                                {

                                                                    width: 180,

                                                                    height: 40,
                                                                    paddingHorizontal: 5,
                                                                    paddingVertical: 0,
                                                                    borderRadius: 5,
                                                                    borderColor: '#E5E5E5',
                                                                    borderWidth: 1,

                                                                    marginRight: 2,
                                                                    paddingLeft: 2,
                                                                    shadowColor: '#000',
                                                                    shadowOffset: {
                                                                        width: 0,
                                                                        height: 2,
                                                                    },
                                                                    shadowOpacity: 0.22,
                                                                    shadowRadius: 3.22,
                                                                    elevation: 3,
                                                                },
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                        width: windowWidth * 0.152,
                                                                    }
                                                                    : {},
                                                            ]}
                                                            dropDownStyle={{
                                                                paddingLeft: 2,
                                                                width: switchMerchant
                                                                    ? windowWidth * 0.152
                                                                    : 180,
                                                                height: 100,
                                                            }}
                                                            items={BARCODE_SETTING_BARCODE_TYPE}
                                                            placeholder='Select Barcode Type'
                                                            defaultValue={bcType}
                                                            onChangeItem={(item) => {
                                                                setBcType(item.value);
                                                            }}
                                                        />
                                                    </View>
                                                    : null}

                                                {/* <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginTop: 20, marginBottom: 5 }}>
                                                        Source
                                                    </Text>
                                                    <TouchableOpacity
                                                        onPress={() => {

                                                        }}>
                                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                            <RadioButton
                                                            //status={

                                                            // }
                                                            />
                                                            <Text style={{
                                                                fontSize: 14,
                                                                Colors: 'black',
                                                                fontFamily: 'Nunitosans-Regular',
                                                                marginLeft: 2,

                                                            }}>
                                                                iSKU
                                                            </Text>
                                                        </View>

                                                    </TouchableOpacity>
                                                    <TouchableOpacity
                                                        onPress={() => { }}>
                                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                            <RadioButton
                                                            //status={

                                                            // }
                                                            />
                                                            <Text style={{
                                                                fontSize: 14,
                                                                Colors: 'black',
                                                                fontFamily: 'Nunitosans-Regular',
                                                                marginLeft: 2,

                                                            }}>
                                                                UPC
                                                            </Text>
                                                        </View>
                                                    </TouchableOpacity> */}
                                                <View style={{ zIndex: -1, }}>
                                                    <TouchableOpacity
                                                        style={{
                                                            marginTop: 40,
                                                            justifyContent: 'center',
                                                            flexDirection: 'row',
                                                            borderWidth: 1,
                                                            borderColor: Colors.primaryColor,
                                                            backgroundColor: '#4E9F7D',
                                                            borderRadius: 5,
                                                            width: 120,
                                                            paddingHorizontal: 10,
                                                            height: switchMerchant ? 35 : 40,
                                                            alignItems: 'center',
                                                            shadowOffset: {
                                                                width: 0,
                                                                height: 2,
                                                            },
                                                            shadowOpacity: 0.22,
                                                            shadowRadius: 3.22,
                                                            elevation: 1,
                                                            zIndex: -1,
                                                        }}
                                                        disabled={loading}
                                                        onPress={() => {
                                                            updateOutletDetails();

                                                        }}>
                                                        <Text style={{
                                                            Colors: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                            {loading ? 'LOADING...' : 'SAVE'}
                                                        </Text>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>


                                        </View>
                                    </View>
                                </KeyboardAwareScrollView>
                            </View>
                        </View>
                    </ScrollView>
                </ScrollView >
            </View >
        </UserIdleWrapper >
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        fontFamily: 'NunitoSans-Regular',
    },
    iosStyle: {
        paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    headerLogo1: {
        width: '100%',
        height: '100%',
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    listItem: {
        fontFamily: 'NunitoSans-Regular',
        marginLeft: 20,
        Colors: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        //width: windowWidth * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 20,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
        // backgroundColor: 'lightgrey',
        backgroundColor: Colors.highlightColor,
    },
    textInput: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 0,
        flex: 1,
        flexDirection: 'row',
    },
    textInputLocation: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 100,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 10,
    },
    textInput8: {
        fontFamily: 'NunitoSans-Regular',
        width: 75,
        height: 50,
        flex: 1,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
    },
    textInput9: {
        fontFamily: 'NunitoSans-Regular',
        width: 110,
        height: Platform.OS == 'ios' ? 30 : 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    textInput10: {
        fontFamily: 'NunitoSans-Regular',
        width: 200,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        flexDirection: 'row',
        justifyContent: 'center',
    },
    textInput1: {
        fontFamily: 'NunitoSans-Regular',
        width: 250,
        height: 40,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginBottom: 10,
    },
    textInput2: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginRight: 30,
    },
    textInput3: {
        fontFamily: 'NunitoSans-Regular',
        width: '85%',
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        marginTop: 10,
        marginBottom: 10,
        borderRadius: 10,
        alignSelf: 'center',
        paddingHorizontal: 10
    },
    textInputPhone: {
        fontFamily: 'NunitoSans-Regular',
        width: Platform.OS === 'ios' ? '100%' : '85%',
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        marginTop: 10,
        marginBottom: 10,
        borderRadius: 10,
        alignSelf: 'center',
        paddingHorizontal: 10
    },
    textInput4: {
        width: '85%',
        height: 70,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        marginTop: 10,
        marginBottom: 10,
        borderRadius: 10,
    },
    textInput5: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 50,
    },
    textInput6: {
        fontFamily: 'NunitoSans-Regular',
        width: '80 %',
        padding: 16,
        backgroundColor: Colors.fieldtBgColor,
        marginRight: 30,
        borderRadius: 10,
        alignSelf: 'center'
    },
    textInput7: {
        fontFamily: 'NunitoSans-Regular',
        width: 300,
        height: 80,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 7,
        marginRight: 30,
    },
    button: {
        backgroundColor: Colors.primaryColor,
        width: 150,
        padding: 8,
        borderRadius: 10,
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: 20,
    },
    button1: {
        width: '15%',
        padding: 8,
        borderRadius: 10,
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: 20,
    },
    button2: {
        backgroundColor: Colors.primaryColor,
        width: '60%',
        padding: 8,
        borderRadius: 10,
        alignItems: 'center',
        marginLeft: '2%',
    },
    button3: {
        backgroundColor: Colors.primaryColor,
        width: '30%',
        height: 50,
        borderRadius: 10,
        alignItems: 'center',
        alignSelf: 'center',
        marginBottom: 30,
    },
    textSize: {
        fontSize: 19,
        fontFamily: 'NunitoSans-SemiBold'
    },
    viewContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        flex: 0,
        width: '100%',
        marginBottom: 15,
    },
    openHourContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        flex: 1,
        marginBottom: 15,
        width: '100%',
    },
    addButtonView: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 200,
        height: 40,
        alignItems: 'center',
    },
    addButtonView1: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 150,
        height: 40,
        alignItems: 'center',
    },
    addNewView: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        marginBottom: 65,
        marginTop: 7,
        width: '83%',
        alignSelf: 'flex-end',
    },
    addNewView1: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginBottom: 10,
        alignItems: 'center',
    },
    merchantDisplayView: {
        flexDirection: 'row',
        flex: 1,
        marginLeft: '17%'
    },
    shiftView: {
        flexDirection: 'row',
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 50,
        width: 200,
        height: 60,
        alignItems: 'center',
        marginTop: 30,
        alignSelf: 'center',
    },
    shiftText: {
        marginLeft: '15%',
        Colors: Colors.primaryColor,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
    },
    closeView: {
        flexDirection: 'row',
        borderRadius: 5,
        borderColor: Colors.primaryColor,
        borderWidth: 1,
        width: 200,
        height: 40,
        alignItems: 'center',
        marginTop: 30,
        alignSelf: 'center',
    },
    taxView: {
        flexDirection: 'row',
        borderWidth: 2,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 150,
        height: 40,
        alignItems: 'center',
        marginTop: 20,
        alignSelf: 'flex-end',
    },
    sectionView: {
        flexDirection: 'row',
        borderRadius: 5,
        padding: 16,
        alignItems: 'center',
    },
    receiptView: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 200,
        height: 40,
        alignItems: 'center',
        alignSelf: 'flex-end',
    },
    pinBtn: {
        backgroundColor: Colors.fieldtBgColor,
        width: 70,
        height: 70,
        marginBottom: 16,
        alignContent: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
    },
    pinNo: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    confirmBox: {
        width: '30%',
        height: '30%',
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    headerLeftStyle: {
        width: Dimensions.get('screen').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
export default SettingBarcodeScreen;