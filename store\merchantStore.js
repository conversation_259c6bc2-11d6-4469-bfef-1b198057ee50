import { Store } from 'pullstate';

export const MerchantStore = new Store({
    description: '',
    name: '',
    shortcode: '',
    logo: '',

    merchantLastUpdated: Date.now(),

    allOutlets: [],
    allOutletsDict: {},

    currOutletId: '',
    currOutlet: {
        uniqueId: '',
        privileges: [],

        toggleOpenOrder: false,
        openOrderDays: 7,
    },

    poNumber: 0,
    poNumberUpdatedAt: null,
    poNumberProduct: 0,
    poNumberProductUpdatedAt: null,

    isMasterAccount: undefined,
});   