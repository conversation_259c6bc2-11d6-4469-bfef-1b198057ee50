import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import CheckBox from 'react-native-check-box';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Ionicons';
import Icon1 from 'react-native-vector-icons/Feather';
import Ionicon from 'react-native-vector-icons/Ionicons';
import DropDownPicker from 'react-native-dropdown-picker';
import Feather from 'react-native-vector-icons/Feather';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
  getTransformForScreenInsideNavigation,
  isTablet,
  parseImagePickerResponse
} from '../util/common';
import {
  MERCHANT_VOUCHER_CODE_FORMAT,
  ORDER_TYPE_DROP_DOWN_LIST,
  EXPAND_TAB_TYPE,
  LOYALTY_CAMPAIGN_BATCH_TYPE,
} from '../constant/common';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import { launchImageLibrary } from 'react-native-image-picker';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { areArraysEqual, uploadImageToFirebaseStorage, parseValidPriceText } from '../util/common';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import AntDesign from 'react-native-vector-icons/AntDesign';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {
  LOYALTY_CAMPAIGN_DROPDOWN_LIST,
  LOYALTY_PROMOTION_TYPE,
  LOYALTY_PROMOTION_TYPE_DROPDOWN_LIST,
} from '../constant/loyalty';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import RNPickerSelect from 'react-native-picker-select';

//////////////////////////////////////////////////////////////////////////////////////////////////////////

const NewTopupCreditTypeScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [creditTypeName, setCreditTypeName] = useState('');
  const [creditTypePrice, setCreditTypePrice] = useState('');
  const [creditTypeValue, setCreditTypeValue] = useState('');

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);


  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);

  ///////////////////////////////////////////////////////////////////////////////////////////////////////////

  const userId = UserStore.useState((s) => s.firebaseUid);
  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  ///////////////////////////////////////////////////////////////////////////////////////////

  // 2024-10-17 - tag voucher(s)

  const [taggableVoucherDropdownList, setTaggableVoucherDropdownList] = useState([]);
  const taggableVouchers = OutletStore.useState(s => s.taggableVouchers);

  const [smsText, setSmsText] = useState('%outletName%: Hi %userName%, thank you for the topup of %topupAmount%, visit us to redeem your free voucher(s)!');

  const [batchList, setBatchList] = useState([
    {
      batchId: uuidv4(),
      orderIndex: 0,
      batchType: LOYALTY_CAMPAIGN_BATCH_TYPE.SEQUENCE,
      voucherId: '',
      voucherName: '',
      activationDays: '1',
      expirationDays: '14',

      sendDays: '1',
      sendTime: moment().set('hour', 10).valueOf(),
    },
  ]);

  ///////////////////////////////////////////////////////////////////////////////////////////

  const [outletItems, setOutletItems] = useState([]);
  const [outletCategories, setOutletCategories] = useState([]);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);
  const outletCategoriesUnsorted = OutletStore.useState(
    (s) => s.outletCategories,
  );
  const outletCategoriesDict = OutletStore.useState(
    (s) => s.outletCategoriesDict,
  );
  const outletsTaxDict = OutletStore.useState((s) => s.outletsTaxDict);
  const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const selectedTopupCreditTypeEdit = CommonStore.useState(
    (s) => s.selectedTopupCreditTypeEdit,
  );

  const merchantId = UserStore.useState((s) => s.merchantId);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [temp, setTemp] = useState('');

  //////////////////////////////////////////////////////////////////////////////////////////

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////////////////////////

  const setState = () => { };


  /////////////////////////////////////////////////////////////
  const confirmDeletePromotion = () => {
    return Alert.alert(
      'Delete',
      'Are you sure you want to remove this promotion?',
      [
        {
          text: 'YES',
          onPress: () => {
            deletePromotion(item);
          },
        },
        {
          text: 'NO',
          onPress: () => { },
        },
      ],
    );
  };

  useEffect(() => {
    if (
      selectedTopupCreditTypeEdit
    ) {
      // insert info

      setCreditTypeName(selectedTopupCreditTypeEdit.name);
      setCreditTypePrice(selectedTopupCreditTypeEdit.price.toFixed(2));
      setCreditTypeValue(selectedTopupCreditTypeEdit.value.toFixed(2));
      setImage(selectedTopupCreditTypeEdit.image);
      setIsImageChanged(false);

      setSmsText(selectedTopupCreditTypeEdit.smsText ? selectedTopupCreditTypeEdit.smsText : '%outletName%: Hi %userName%, thank you for the topup of %topupAmount%, visit us to redeem your free voucher(s)!');

    } else {
      // designed to always mounted, thus need clear manually...

      setCreditTypeName('');
      setCreditTypePrice('10.00');
      setCreditTypeValue('10.00');
      setImage('');
      setIsImageChanged(false);

      setSmsText('%outletName%: Hi %userName%, thank you for the topup of %topupAmount%, visit us to redeem your free voucher(s)!');
    }

    var batchListTemp = [];

    if (selectedTopupCreditTypeEdit && selectedTopupCreditTypeEdit.uniqueId &&
      selectedTopupCreditTypeEdit.batchList &&
      selectedTopupCreditTypeEdit.batchList.length > 0) {
      batchListTemp = selectedTopupCreditTypeEdit.batchList.map(batch => {
        const foundVoucher = taggableVouchers.find(voucher => voucher.uniqueId === batch.voucherId);

        return {
          batchId: batch.batchId,
          orderIndex: batch.orderIndex,
          batchType: batch.batchType,
          voucherId: foundVoucher ? foundVoucher.uniqueId : '',
          voucherName: foundVoucher ? foundVoucher.campaignName : '',
          activationDays: batch.activationDays.toFixed(0),
          expirationDays: batch.expirationDays.toFixed(0),

          sendDays: batch.sendDays.toFixed(0),
          sendTime: batch.sendTime,
        };
      });
    }
    else {
      batchListTemp = [
        {
          batchId: uuidv4(),
          orderIndex: 0,
          batchType: LOYALTY_CAMPAIGN_BATCH_TYPE.SEQUENCE,
          voucherId: '',
          voucherName: '',
          activationDays: '1',
          expirationDays: '14',

          sendDays: '1',
          sendTime: moment().set('hour', 10).valueOf(),
        },
      ];
    }

    setBatchList(batchListTemp);
  }, [
    selectedTopupCreditTypeEdit,

    taggableVouchers,
  ]);

  useEffect(() => {
    setTaggableVoucherDropdownList(
      [
        {
          label: 'N/A',
          value: '',
        }
      ].concat(
        taggableVouchers.filter(voucher => {
          var endTime = moment(voucher.promoTimeEnd);
          var endDate = moment(voucher.promoDateEnd).set({
            hour: endTime.get('hour'),
            minute: endTime.get('minute'),
          });
          if (!moment().isSameOrAfter(endDate)) {
            return true;
          }
          else {
            return false;
          }
        }).map((item) => ({ label: `${item.campaignName} [${item.voucherType.replaceAll('_', ' ')}]`, value: item.uniqueId })),
      ),
    );
  }, [taggableVouchers]);

  /////////////////////////////////////////////////////////////

  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  //Header
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Credit Type
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  /////////////////////////////////////////////////

  const handleChoosePhoto = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };

    launchImageLibrary(imagePickerOptions, (response) => {
      if (response.didCancel) {
      } else if (response.error) {
        Alert.alert(response.error.toString());
      } else {
        const responseParsed = parseImagePickerResponse(response);

        // setState({ image: response.uri });
        setImage(responseParsed.uri);
        setImageType(responseParsed.uri.slice(responseParsed.uri.lastIndexOf('.')));

        setIsImageChanged(true);
      }
    });
  };

  const createCreditType = async (isAutoPush = false) => {
    var message = '';

    if (!creditTypeName) {
      message += 'Name must be filled in.\n';
    }

    if (!creditTypePrice || isNaN(creditTypePrice)) {
      message += 'Invalid or empty price.\n';
    }

    if (!creditTypeValue || isNaN(!creditTypeValue)) {
      message += 'Invalid or empty point.\n';
    }

    if (message.length > 0) {
      Alert.alert('Info', message);

      return;
    } else {
      ////////////////////////////////////////////////////////////////

      ///////////////////////////////////
      // upload image

      var creditTypeImagePath = '';
      var creditTypeCommonIdLocal = selectedTopupCreditTypeEdit
        ? selectedTopupCreditTypeEdit.commonId
        : uuidv4();

      if (image && imageType) {
        // promotionCommonIdLocal = uuidv4();

        creditTypeImagePath = await uploadImageToFirebaseStorage(
          {
            uri: image,
            type: imageType,
          },
          `/merchant/${merchantId}/topup-credit-type/${creditTypeCommonIdLocal}/image${imageType}`,
        );
      }

      ///////////////////////////////////

      // 2024-03-5 - for batchList post-processing

      const batchListProcessed = batchList.map(batch => {
        return {
          batchId: batch.batchId,
          orderIndex: batch.orderIndex,
          batchType: batch.batchType,
          voucherId: batch.voucherId,
          voucherName: batch.voucherName,
          activationDays: parseInt(batch.activationDays),
          expirationDays: parseInt(batch.expirationDays),
          sendDays: parseInt(batch.sendDays),
          sendTime: batch.sendTime,
        };
      });

      ///////////////////////////////////

      if (selectedTopupCreditTypeEdit === null) {
        // means new item

        var body = {
          merchantId: merchantId,
          merchantName: merchantName,
          outletId: currOutlet.uniqueId,
          outletName: currOutlet.name,

          name: creditTypeName,
          price: parseFloat(creditTypePrice),
          value: parseFloat(creditTypeValue),
          image: creditTypeImagePath,
          isImageChanged: isImageChanged,

          smsText: smsText,

          batchList: batchListProcessed,

          /////////////////////////////////////////////////////////////
        };


        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(API.createTaggableVoucher, body, false)
        APILocal.createTopupCreditType({ body: body, uid: userId })
          .then(
            (result) => {
              if (result && result.status === 'success') {


                Alert.alert(
                  'Success',
                  'Credit type has been created',
                  [
                    {
                      text: 'OK',
                      onPress: () => {

                        props.navigation.navigate('TopupCreditType');
                      },
                    },
                  ],
                  { cancelable: false },
                );
              }

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            },
          );
      } else if (selectedTopupCreditTypeEdit !== null) {
        // means existing item

        var body = {
          creditTypeId: selectedTopupCreditTypeEdit.uniqueId,

          merchantId: merchantId,
          merchantName: merchantName,
          outletId: currOutlet.uniqueId,
          outletName: currOutlet.name,

          name: creditTypeName,
          price: parseFloat(creditTypePrice),
          value: parseFloat(creditTypeValue),
          image: creditTypeImagePath,
          isImageChanged: isImageChanged,

          smsText: smsText,

          batchList: batchListProcessed,

          /////////////////////////////////////////////////////////////
        };


        CommonStore.update((s) => {
          s.isLoading = true;
        });

        // ApiClient.POST(API.updateTaggableVoucher, body, false)
        APILocal.updateTopupCreditType({ body: body, uid: userId })
          .then(
            (result) => {
              if (result && result.status === 'success') {

                Alert.alert(
                  'Success',
                  'Credit type has been updated.',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        // navigation.navigate('PromotionList');

                        // props.navigation.navigate('SettingLoyalty');

                        props.navigation.navigate('TopupCreditType');
                      },
                    },
                  ],
                  { cancelable: false },
                );
              }

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            },
          );
      }
    }
  };

  const renderBatchList = ({ item, index }) => {
    var isValidToRender = true;

    let taggableVoucherDropdownListSub = taggableVoucherDropdownList;
    // let taggableVoucherDropdownListSub = taggableVoucherDropdownList.filter(voucherOption => {
    //   if (voucherOption.value.length > 0) {
    //     if (index === 1) {
    //       console.log('break');
    //     }

    //     if (
    //       // taggableVoucherId === voucherOption.value
    //       // ||
    //       batchList.find(batch => {
    //         if (batch.voucherId === voucherOption.value &&
    //           batch.batchId !== item.batchId) {
    //           // means this voucher got selected in other dropdown already

    //           return true;
    //         }
    //       })
    //     ) {
    //       // can skip if found in batchList

    //       return false;
    //     }
    //     else {
    //       return true;
    //     }
    //   }
    //   else {
    //     return true;
    //   }
    // });

    if (
      isValidToRender
      // true
    ) {
      return (
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingLeft: 10,
            paddingRight: 5,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
            alignItems: 'center',
            width: '100%',
            // height: (Dimensions.get('window').width * 0.1) * 3,

            // marginBottom: (index === batchList.length - 1) ? 200 : 0,

            zIndex: 10000 - index,
          }}>
          <View style={{ width: '40%', zIndex: 10000 - index, }}>
            <View style={{
              width: '80%',
              zIndex: 1,
              height: 35,
              borderRadius: 5,
              justifyContent: 'center',
              backgroundColor: 'white',
              paddingHorizontal: 10,
              borderWidth: 1,
              borderColor: '#E5E5E5',
              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 2,
            }}
            >
              {
                taggableVoucherDropdownListSub.find(option => item.voucherId === option.value)
                  ?
                  <RNPickerSelect
                    placeholder={{}}
                    useNativeAndroidPickerStyle={false}
                    //pickerProps={{ style: { height: 160, overflow: 'hidden',} }}
                    style={{
                      inputIOS: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, paddingVertical: 5 },
                      inputAndroid: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, paddingVertical: 5 },
                      inputAndroidContainer: {
                        //backgroundColor: 'red',
                        width: '100%',
                      }
                    }}
                    //contentContainerStyle={{ fontSize: switchMerchant ? 10 : 14, }}
                    items={taggableVoucherDropdownListSub}
                    value={
                      item.voucherId
                    }
                    onValueChange={(value) => {
                      setBatchList(batchList.map((batch, i) => (i === index ? {
                        ...batch,

                        voucherId: value,
                      } : batch)));
                    }}
                  />
                  :
                  <></>
              }
            </View>

            {/* <View style={{ width: '85%', marginTop: '2%', zIndex: 10000 - index, }}>
              {
                taggableVoucherDropdownListSub.find(option => item.voucherId === option.value)
                  ?
                  <DropDownPicker
                    containerStyle={[
                      { height: 40 },
                      switchMerchant ? { height: 35 } : {},
                    ]}
                    arrowColor={'black'}
                    arrowSize={20}
                    arrowStyle={{ fontWeight: 'bold' }}
                    labelStyle={{
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: 14,
                    }}
                    style={[
                      {
                        width: '100%',
                        paddingVertical: 0,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                      },
                      switchMerchant
                        ? {
                          width: '100%',
                        }
                        : {},
                    ]}
                    placeholderStyle={{
                      color: Colors.fieldtTxtColor,
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: 14,
                    }}
                    items={taggableVoucherDropdownListSub}
                    itemStyle={{
                      justifyContent: 'flex-start',
                      marginLeft: 5,
                      zIndex: 10000 - index,
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: 14,
                    }}
                    placeholder={'Select'}
                    //multiple={true}
                    customTickIcon={(press) => (
                      <Ionicon
                        name={'checkmark-outline'}
                        color={
                          press
                            ? Colors.fieldtBgColor
                            : Colors.primaryColor
                        }
                        size={25}
                      />
                    )}
                    onChangeItem={(option) => {
                      setBatchList(batchList.map((batch, i) => (i === index ? {
                        ...batch,

                        voucherId: option.value,
                      } : batch)));
                    }}
                    defaultValue={item.voucherId}
                    dropDownMaxHeight={150}
                    dropDownStyle={[
                      {
                        width: '100%',
                        height: 150,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        textAlign: 'left',
                        zIndex: 10000 - index,
                      },
                      switchMerchant
                        ? {
                          width: '100%',
                        }
                        : {},
                    ]}
                    globalTextStyle={{
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                  //dropDownStyle={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, width: 200, paddingVertical: 0, margin: 5, marginLeft: 0 }}
                  />
                  :
                  <></>
              }
            </View> */}
          </View>

          <View style={{
            width: '15%',
          }}>
            <TextInput
              style={{
                borderWidth: 1,
                borderColor: '#E5E5E5',
                borderRadius: 5,
                height: switchMerchant ? 35 : 40,
                // width: 80,
                justifyContent: 'center',
                textAlign: 'center',
                marginTop: 5,
                width: '80%',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
                //left: 8
              }}
              placeholder={'0'}
              placeholderTextColor={Platform.select({
                ios: '#a9a9a9',
              })}
              placeholderStyle={{
                justifyContent: 'center',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
              }}
              //iOS
              // clearTextOnFocus
              selectTextOnFocus
              //////////////////////////////////////////////
              //Android
              // onFocus={() => {
              //   setTemp(item.activationDays)
              //   setBatchList(batchList.map((batch, i) => (i === index ? {
              //     ...batch,

              //     activationDays: '',
              //   } : batch)));
              // }}
              ///////////////////////////////////////////////
              //When textinput is not selected
              // onEndEditing={() => {
              //   if (item.activationDays == '') {
              //     setBatchList(batchList.map((batch, i) => (i === index ? {
              //       ...batch,

              //       activationDays: temp,
              //     } : batch)));
              //   }
              // }}
              //////////////////////////////////////////////
              onChangeText={(text) => {
                var value =
                  text.length >= 0
                    ? !isNaN(text)
                      ? text
                      : '0'
                    : '0';

                setBatchList(batchList.map((batch, i) => (i === index ? {
                  ...batch,

                  activationDays: value,
                } : batch)));
              }}
              defaultValue={item.activationDays != 0 ? item.activationDays : ''}
              keyboardType={'decimal-pad'}
            />
          </View>

          <View style={{
            width: '15%',
          }}>
            <TextInput
              style={{
                borderWidth: 1,
                borderColor: '#E5E5E5',
                borderRadius: 5,
                height: switchMerchant ? 35 : 40,
                // width: 80,
                justifyContent: 'center',
                textAlign: 'center',
                marginTop: 5,
                width: '80%',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
                //left: 8
              }}
              placeholder={'0'}
              placeholderTextColor={Platform.select({
                ios: '#a9a9a9',
              })}
              placeholderStyle={{
                justifyContent: 'center',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
              }}
              //iOS
              // clearTextOnFocus
              selectTextOnFocus
              //////////////////////////////////////////////
              //Android
              // onFocus={() => {
              //   setTemp(item.expirationDays)
              //   setBatchList(batchList.map((batch, i) => (i === index ? {
              //     ...batch,

              //     expirationDays: '',
              //   } : batch)));
              // }}
              ///////////////////////////////////////////////
              //When textinput is not selected
              // onEndEditing={() => {
              //   if (item.expirationDays == '') {
              //     setBatchList(batchList.map((batch, i) => (i === index ? {
              //       ...batch,

              //       expirationDays: temp,
              //     } : batch)));
              //   }
              // }}
              //////////////////////////////////////////////
              onChangeText={(text) => {
                var value =
                  text.length >= 0
                    ? !isNaN(text)
                      ? text
                      : '0'
                    : '0';

                setBatchList(batchList.map((batch, i) => (i === index ? {
                  ...batch,

                  expirationDays: value,
                } : batch)));
              }}
              defaultValue={item.expirationDays != 0 ? item.expirationDays : ''}
              keyboardType={'decimal-pad'}
            />
          </View>

          <View style={{
            width: '12%',
          }}>
            <TextInput
              style={{
                borderWidth: 1,
                borderColor: '#E5E5E5',
                borderRadius: 5,
                height: switchMerchant ? 35 : 40,
                // width: 80,
                justifyContent: 'center',
                textAlign: 'center',
                marginTop: 5,
                width: '80%',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
                //left: 8
              }}
              placeholder={'0'}
              placeholderTextColor={Platform.select({
                ios: '#a9a9a9',
              })}
              placeholderStyle={{
                justifyContent: 'center',
                fontFamily: 'NunitoSans-Regular',
                fontSize: switchMerchant ? 10 : 14,
              }}
              //iOS
              // clearTextOnFocus
              selectTextOnFocus
              //////////////////////////////////////////////
              //Android
              // onFocus={() => {
              //   setTemp(item.sendDays)
              //   setBatchList(batchList.map((batch, i) => (i === index ? {
              //     ...batch,

              //     sendDays: '',
              //   } : batch)));
              // }}
              ///////////////////////////////////////////////
              //When textinput is not selected
              // onEndEditing={() => {
              //   if (item.sendDays == '') {
              //     setBatchList(batchList.map((batch, i) => (i === index ? {
              //       ...batch,

              //       sendDays: temp,
              //     } : batch)));
              //   }
              // }}
              //////////////////////////////////////////////
              onChangeText={(text) => {
                var value =
                  text.length >= 0
                    ? !isNaN(text)
                      ? text
                      : '0'
                    : '0';

                setBatchList(batchList.map((batch, i) => (i === index ? {
                  ...batch,

                  sendDays: value,
                } : batch)));
              }}
              defaultValue={item.sendDays != 0 ? item.sendDays : ''}
              keyboardType={'decimal-pad'}
            />
          </View>

          <View style={[{
            width: '15%',
          }]}>
            <TouchableOpacity onPress={() => {
              global.currBatchIndex = index;

              setShowBatchSendTimePicker(true);
            }}
              style={[{
                backgroundColor: Colors.fieldtBgColor,
                width: 115,
                padding: 5,
                height: 40,
                borderRadius: 5,
                marginVertical: 5,
                alignItems: 'center',
                justifyContent: 'center',
                borderWidth: 1,
                borderColor: '#E5E5E5',
                fontFamily: 'NunitoSans-Regular',
                fontSize: 14
              }, switchMerchant ? {
                fontSize: 10,
                width: 93,
                height: 35,
              } : {}]}
            >
              <Text style={[{ fontVariant: ['tabular-nums'], fontFamily: 'NunitoSans-Regular', fontSize: 14 }, switchMerchant ? {
                fontSize: 10,
              } : {}]}>{moment(item.sendTime).format('HH:mm A')}</Text>
            </TouchableOpacity>
          </View>

          <View style={{
            width: '10%',
          }}
          >
            <TouchableOpacity style={{ marginLeft: switchMerchant ? 5 : 10, }}
              onPress={() => {
                setBatchList([
                  ...batchList.slice(0, index),
                  ...batchList.slice(index + 1),
                ]);
              }}>
              <Feather name="trash-2" size={switchMerchant ? 15 : 20} color="#eb3446" />
            </TouchableOpacity>
          </View>
        </View>
      );
    }
  };

  /////////////////////////////////////////////////

  //Render start here

  return (
    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* <View
          style={[
            styles.sidebar,
            !isTablet() ? {} : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={11}
            expandPromotions={true}
          />
        </View> */}
        <ScrollView horizontal={true} scrollEnabled={switchMerchant}>
          <KeyboardAvoidingView>
            <View
              style={
                switchMerchant
                  ? {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    width: windowWidth * 0.8,
                    marginHorizontal: 15,
                    //alignSelf: 'center',
                    margin: 10,
                    paddingHorizontal: 0,
                    paddingTop: 10,
                  }
                  : {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    width: windowWidth * 0.9,
                    marginHorizontal: 15,
                    alignSelf: 'center',
                    margin: 10,
                    paddingHorizontal: 0,
                    paddingTop: 10,
                  }
              }>
              <TouchableOpacity
                style={{ width: 90, height: 35, justifyContent: 'center' }}
                onPress={() => {
                  // props.navigation.navigate('PromotionList');

                  props.navigation.navigate('TopupCreditType');
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    paddingHorizontal: '10%',
                    alignContent: 'center',
                    alignItems: 'center',
                    marginTop: switchMerchant ? 10 : 0,
                  }}>
                  <View style={{ justifyContent: 'center' }}>
                    <Feather
                      name="chevron-left"
                      size={switchMerchant ? 20 : 30}
                      style={{ color: Colors.primaryColor, alignSelf: 'center' }}
                    />
                  </View>
                  <Text
                    style={[
                      {
                        fontSize: 17,
                        color: Colors.primaryColor,
                        fontWeight: '600',
                        marginBottom: Platform.OS === 'ios' ? 0 : 1,
                      },
                      switchMerchant
                        ? {
                          fontSize: 14,
                        }
                        : {},
                    ]}>
                    Back
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* <KeyBoardAwareScrollView style={styles.list}> */}
            <KeyboardAwareScrollView
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
              style={{
                backgroundColor: Colors.whiteColor,
                width: switchMerchant
                  ? windowWidth * 0.87
                  : windowWidth * 0.87,
                height: windowHeight * 0.825,
                marginTop: 0,
                marginHorizontal: switchMerchant ? 30 : 30,
                //alignSelf: 'center',
                borderRadius: 5,
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 1,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,

                marginBottom: switchMerchant ? 25 : 25,
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  width: '100%',
                }}>
                <View
                  style={{
                    //flexDirection: 'row',
                    margin: 20,
                    marginBottom: 10,
                    width: '70%',
                  }}>
                  <View
                    style={{
                      width: '100%',
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                    }}>
                    <Text
                      style={[
                        { fontFamily: 'NunitoSans-Bold', fontSize: 30 },
                        switchMerchant
                          ? {
                            fontSize: 20,
                          }
                          : {},
                      ]}>
                      {creditTypeName.length > 0
                        ? creditTypeName
                        : 'New Credit Type'}
                    </Text>
                  </View>
                </View>
                <View
                  style={{
                    margin: 20,
                    marginBottom: 10,
                  }}>
                  <View style={{}}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 130,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginBottom: 10,
                        },
                        switchMerchant
                          ? {
                            height: 35,
                            width: 120,
                          }
                          : {},
                      ]}
                      disabled={isLoading}
                      onPress={() => { createCreditType() }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {isLoading ? 'LOADING...' : 'SAVE'}
                      </Text>

                      {isLoading ? (
                        <ActivityIndicator
                          color={Colors.whiteColor}
                          size={'small'}
                        />
                      ) : (
                        <></>
                      )}
                    </TouchableOpacity>

                  </View>
                </View>
              </View>

              <View
                style={{
                  flexDirection: 'column',
                  borderWidth: 1,
                  borderColor: '#c4c4c4',
                  width: '97%',
                  alignSelf: 'center',
                  //flex: 1,
                  paddingBottom: 30,
                }}>
                  
                <View
                  style={{
                    flexDirection: 'column',
                    flex: 1,
                    alignSelf: 'center'
                  }}>
                  <View
                    style={{
                      flex: 4,
                      flexDirection: 'row',
                      marginVertical: 20,
                    }}>
                    <View style={{ flexDirection: 'row', marginLeft: 20, width: '80%'}}>
                      <TouchableOpacity
                        onPress={() => {
                          handleChoosePhoto();
                        }}>
                        <View style={{ flexDirection: 'row', zIndex: -2 }}>
                          {image ? (
                            <View
                              style={{
                                backgroundColor: '#F7F7F7',
                                borderRadius: 5,
                                zIndex: 1,
                              }}>
                              <AsyncImage
                                source={{ uri: image }}
                                style={[
                                  { width: 260, height: 200, borderRadius: 5 },
                                  switchMerchant
                                    ? {
                                      width: 200,
                                      height: 160,
                                    }
                                    : {}, {
                                    ...(!switchMerchant && windowWidth < 1000 && {
                                      width: 200, height: 160,
                                    }),
                                  }
                                ]}
                                hideLoading={true}
                              />
                              <View
                                style={{
                                  position: 'absolute',
                                  bottom: 5,
                                  right: 5,
                                  //opacity: 0.5,
                                }}>
                                <FontAwesome5
                                  name="edit"
                                  size={switchMerchant ? 10 : 23}
                                  color={Colors.primaryColor}
                                />
                              </View>
                            </View>
                          ) : (
                            <View
                              style={[
                                {
                                  backgroundColor: '#F7F7F7',
                                  borderRadius: 5,
                                  width: 260,
                                  height: 200,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                },
                                switchMerchant
                                  ? {
                                    width: 200,
                                    height: 160,
                                  }
                                  : {},
                                {
                                  ...(!switchMerchant && windowWidth < 1000 && {
                                    width: 240, height: 180,
                                  }),
                                }
                              ]}>
                              <Icon1
                                name="upload"
                                size={switchMerchant ? 100 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 120 : 150}
                                color="lightgrey"
                                style={{ zIndex: -1 }}
                              />
                              <View
                                style={{
                                  position: 'absolute',
                                  bottom: 5,
                                  right: 5,
                                  //opacity: 0.5,
                                }}>
                                <FontAwesome5
                                  name="edit"
                                  size={switchMerchant ? 10 : 23}
                                  color={Colors.primaryColor}
                                />
                              </View>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>
                      
                      <View style={{ flex: 1, zIndex: -1, marginTop: 1, paddingLeft: 90}}>
                      <View style={{ flexDirection: 'row', flex: 1, zIndex: 1, paddingBottom: 10}}>
                      {/* 1st column */}

                      <View
                        style={{
                          flex: 1,
                          marginRight: switchMerchant
                            ? '5%'
                            : windowWidth <= 1024
                              ? '3%'
                              : '2%',
                        }}>
                        <Text
                          style={[
                            {
                              alignSelf: 'flex-start',
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                              fontWeight: '500',
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          Name
                        </Text>
                        <TextInput
                          placeholder="Name (Ex: RM 10)"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          placeholderStyle={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14,
                          }}
                          style={[
                            {
                              backgroundColor: Colors.fieldtBgColor,
                              width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 220 : 250,
                              height: 40,
                              borderRadius: 5,
                              padding: 5,
                              // marginVertical: 5,
                              marginTop: 5,
                              borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                                width: 200,
                                height: 35,
                              }
                              : {},
                          ]}
                          //iOS
                          // clearTextOnFocus={true}
                          selectTextOnFocus
                          //////////////////////////////////////////////
                          //Android
                          // onFocus={() => {
                          //   setTemp(creditTypeName)
                          //   setCreditTypeName('');
                          // }}
                          ///////////////////////////////////////////////
                          //When textinput is not selected
                          // onEndEditing={() => {
                          //   if (creditTypeName == '') {
                          //     setCreditTypeName(temp);
                          //   }
                          // }}
                          onChangeText={(text) => {
                            setCreditTypeName(text);
                          }}
                          defaultValue={creditTypeName}
                        />
                      </View>

                      {/* 2nd column */}

                      
                    </View>
                        <View style={{ marginTop: '0%', zIndex: -2 }}>
                          <Text
                            style={[
                              {
                                alignSelf: 'flex-start',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 14,
                                fontWeight: '500',
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            Price To Buy (RM)
                          </Text>
                          <TextInput
                            placeholder="Price (Ex: 10)"
                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                            placeholderStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            style={[
                              {
                                backgroundColor: Colors.fieldtBgColor,
                                width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 150 : 180,
                                height: 40,
                                borderRadius: 5,
                                padding: 5,
                                // marginVertical: 5,
                                marginTop: 5,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingLeft: 10,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  // width: 200,
                                  height: 35,
                                }
                                : {},
                            ]}
                            //iOS
                            // clearTextOnFocus={true}
                            selectTextOnFocus
                            //////////////////////////////////////////////
                            //Android
                            // onFocus={() => {
                            //   setTemp(creditTypePrice)
                            //   setCreditTypePrice('');
                            // }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            // onEndEditing={() => {
                            //   if (creditTypePrice == '') {
                            //     setCreditTypePrice(temp);
                            //   }
                            // }}
                            onChangeText={(text) => {
                              setCreditTypePrice(parseValidPriceText(text));
                            }}
                            defaultValue={creditTypePrice}
                            keyboardType={'decimal-pad'}
                          />
                        </View>

                        <View style={{ marginTop: '2%', zIndex: -2 }}>
                          <Text
                            style={[
                              {
                                alignSelf: 'flex-start',
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: 14,
                                fontWeight: '500',
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            Credit Amount (RM)
                          </Text>
                          <TextInput
                            placeholder="Point (Ex: 10)"
                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                            placeholderStyle={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: 14,
                            }}
                            style={[
                              {
                                backgroundColor: Colors.fieldtBgColor,
                                width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 150 : 180,
                                height: 40,
                                borderRadius: 5,
                                padding: 5,
                                // marginVertical: 5,
                                marginTop: 5,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingLeft: 10,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                  // width: 200,
                                  height: 35,
                                }
                                : {},
                            ]}
                            //iOS
                            // clearTextOnFocus={true}
                            //////////////////////////////////////////////
                            //Android
                            // onFocus={() => {
                            //   setTemp(creditTypeValue)
                            //   setCreditTypeValue('');
                            // }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            // onEndEditing={() => {
                            //   if (creditTypeValue == '') {
                            //     setCreditTypeValue(temp);
                            //   }
                            // }}
                            onChangeText={(text) => {
                              setCreditTypeValue(parseValidPriceText(text));
                            }}
                            defaultValue={creditTypeValue}
                            keyboardType={'decimal-pad'}
                          />
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* /////////////////////////////////////// */}

                  <View
                    style={{
                      flex: 7,
                      marginVertical: 20,
                      marginHorizontal: 20,
                      marginLeft: 10,
                    }}>

                    
                  </View>

                  {/* /////////////////////////////////////// */}

                  {/* 2024-10-17 - choose voucher(s) to tag */}

                  <View
                    style={{
                      marginHorizontal: 20,
                      marginTop: 10,
                      marginBottom: 20,
                      zIndex: -1,
                    }}>
                    <View style={{ width: '90%' }}>
                      <View style={{ marginBottom: 5, flexDirection: 'row', alignItems: 'center', }}>
                        <Text
                          style={[
                            {
                              // fontWeight: '500',
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          {/* Criteria */}
                          {
                            'Voucher(s) to tag'
                          }
                        </Text>

                        <TouchableOpacity
                          style={[
                            {
                              marginLeft: 15,
                              alignSelf: 'flex-start',
                            }
                          ]}
                          onPress={() => {
                            setBatchList([
                              ...batchList,
                              {
                                batchId: uuidv4(),
                                orderIndex: 0,
                                batchType: LOYALTY_CAMPAIGN_BATCH_TYPE.SEQUENCE,
                                voucherId: '',
                                voucherName: '',
                                activationDays: '1',
                                expirationDays: '14',

                                sendDays: '1',
                                sendTime: moment().set('hour', 10).valueOf(),
                              }
                            ]);
                          }}>
                          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <FontAwesome name="plus-circle" size={switchMerchant ? 15 : 20} color={Colors.primaryColor} />
                            <Text style={{ marginLeft: switchMerchant ? 5 : 5, color: Colors.primaryColor, marginBottom: Platform.OS === 'ios' ? 0 : 1, fontFamily: 'NunitoSans-SemiBold', fontSize: switchMerchant ? 10 : 12, }}>
                              Add New Batch
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={{
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          borderRadius: 3,
                          padding: 13,
                          paddingTop: 10,
                          zIndex: -1,

                          opacity: 100, // hide first
                        }}>
                        {/* content put here */}

                        <View
                          style={{
                            backgroundColor: '#ffffff',
                            flexDirection: 'row',
                            paddingVertical: 20,
                            paddingHorizontal: 10,
                            paddingRight: 5,
                            // marginTop: 10,
                            borderBottomWidth: StyleSheet.hairlineWidth,
                            width: '100%',
                          }}>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '40%', }}>
                            Voucher
                          </Text>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '15%', }}>
                            Activation Day(s)
                          </Text>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '15%', }}>
                            Expiration Day(s)
                          </Text>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '12%', }}>
                            Sent Day(s)
                          </Text>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '20%', }}>
                            Send Time
                          </Text>
                        </View>

                        <View style={{
                          shadowOpacity: 0,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 1,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 1.22,
                          elevation: 1,
                          //maxHeight: switchMerchant ? Dimensions.get('screen').height * 0.6 : null, 
                        }}>
                          <FlatList
                            nestedScrollEnabled
                            //horizontal={true}
                            data={batchList}
                            extraData={batchList}
                            renderItem={renderBatchList}
                            keyExtractor={(item, index) => String(index)}
                          />
                        </View>
                      </View>
                    </View>
                  </View>

                  <View style={{ paddingTop: 30, zIndex: -3 }}>
                    <Text
                      style={[
                        {
                          fontWeight: '500',
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 14,
                          marginLeft: '2%',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      Blasting Notification
                    </Text>
                    <View
                      style={{
                        marginTop: '1%',
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        width: '80%',
                        // width: 690,
                        marginHorizontal: 20,
                      }}>
                      <View
                        style={{
                          margin: '2%',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Notification/SMS Message
                        </Text>
                      </View>
                      <View style={{ marginTop: 0, }}>
                        <TextInput
                          style={[{
                            // marginTop: '2%',

                            marginLeft: '2%',

                            padding: 5,
                            backgroundColor: Colors.fieldtBgColor,
                            // width: Platform.OS == 'ios' ? '90%' : '85%',
                            // height: Platform.OS == 'ios' ? 100 : 117,
                            width: 650,
                            height: 140,
                            borderRadius: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingTop: Platform.OS == 'ios' ? 10 : 10,
                            paddingLeft: 10,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: 14
                          }, switchMerchant ? {
                            fontSize: 10,
                            width: '92%',
                            height: 97,
                          } : {}]}
                          textAlignVertical={'top'}
                          placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
                          placeholder="%outletName%: Hi %userName%, thank you for the topup of %topupAmount%, visit us to redeem your free voucher(s)!"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          //placeholderStyle={{alignItems: 'flex-start', alignContent: 'flex-start'}}
                          onChangeText={(text) => {
                            // setState({ remark: text });
                            //setRemark(text);
                            text.length <= 150 ? setSmsText(text) : Alert.alert('Error', 'Maximum character 150')
                            // setWordCount(text.length)
                            //setNotificationDescription(text);

                            // logEventAnalytics({
                            //   eventName: ANALYTICS.MODULE_VOUCHER_ADD_VOUCHER_NOTIFICAITON_TB_MESSAGE,
                            //   eventNameParsed: ANALYTICS_PARSED.MODULE_VOUCHER_ADD_VOUCHER_NOTIFICAITON_TB_MESSAGE,
                            // });
                          }}
                          //value={remark}
                          defaultValue={smsText}
                          multiline
                        //maxLength={100}
                        />

                        <View style={{
                          width: 650,
                          marginLeft: '2%',
                          marginBottom: '2%',

                          ...(switchMerchant && windowWidth < 775 && windowHeight < 412 && {
                            width: '90%',
                          })
                        }}>
                          <Text style={{
                            color: Colors.fieldtTxtColor,
                            fontSize: 13,
                            alignSelf: 'flex-end',
                            marginTop: 2,
                          }}>
                            Characters: {`${smsText.length}`}/150
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>

                  {/* //////////////////////////////////////////////////////////// */}

                  {/* /////////////////////////////////////// */}
                </View>
              </View>
            </KeyboardAwareScrollView>
          </KeyboardAvoidingView>
        </ScrollView>
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  list: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.87,
    height: Dimensions.get('window').height * 0.825,
    marginTop: 0,
    marginHorizontal: 20,
    alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 1,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 20,
    width: 300,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },

});

export default NewTopupCreditTypeScreen;
