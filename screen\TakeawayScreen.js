import { Text } from "react-native-fast-text";
import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useRef,
  createRef,
  useCallback,
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  Dimensions,
  TouchableOpacity,
  Switch,
  Modal as ModalComponent,
  ActivityIndicator,
  Platform,
  useWindowDimensions,
  InteractionManager,
  KeyboardAvoidingView,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import QRCode from 'react-native-qrcode-svg';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Feather from 'react-native-vector-icons/Feather';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import DropDownPicker from 'react-native-dropdown-picker';
import Styles from '../constant/Styles';
import moment from 'moment';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  getOrderDiscountInfo,
  getOrderDiscountInfoInclOrderBased,
  isTablet,
  getTransformForScreenInsideNavigation,
  getTransformForModalFullScreen,
  getTransformForModalInsideNavigation,
  getCartItemPriceWithoutAddOn,
  getAddOnChoiceQuantity,
  getAddOnChoicePrice,
  logToFile,
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import {
  ORDER_TYPE,
  USER_ORDER_STATUS,
  LALAMOVE_STATUS_PARSED,
  COURIER_INFO_DICT,
  COURIER_CODE,
  COURIER_DROPDOWN_LIST,
  MRSPEEDY_STATUS_PARSED,
  PAYMENT_CHANNEL_NAME_PARSED,
  EXPAND_TAB_TYPE,
  ORDER_TYPE_SUB,
  KD_PRINT_VARIATION,
  UNIT_TYPE_SHORT,
  PRODUCT_PRICE_TYPE,
  KD_ITEM_STATUS,
  PRIVILEGES_NAME,
  ROLE_TYPE,
  KD_PRINT_EVENT_TYPE,
} from '../constant/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { TextInput, ScrollView, FlatList } from 'react-native-gesture-handler';
import RNPickerSelect from 'react-native-picker-select';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import { color } from 'react-native-reanimated';
import APILocal from '../util/apiLocalReplacers';
import { PRINTER_USAGE_TYPE } from '../constant/printer';
import { calcPrintTotalForKdIndividual, printDocket, printDocketCancelled, printDocketForKD, printDocketForKDCancelled, printKDSummaryCategoryWrapper, printUserOrder } from '../util/printer';
import { useNetInfo } from "@react-native-community/netinfo";
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import {
  // USBPrinter,
  NetPrinter,
  // BLEPrinter,
} from '@conodene/react-native-thermal-receipt-printer-image-qr';
import { FlashList } from "@shopify/flash-list";
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { TableStore } from '../store/tableStore';
import { EI_ID_TYPE } from "../constant/einvoice";
import { eInvoiceSubmitDocumentByUserOrder } from "../util/einvoice";
import CheckBox from "@react-native-community/checkbox";
// import { storageMMKV } from '../util/storageMMKV';
import firestore from '@react-native-firebase/firestore';

import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { Collections } from "../constant/firebase";

const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const TakeawayScreen = React.memo((props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const netInfo = useNetInfo();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [outletId, setOutletId] = useState(User.getOutletId());
  const [table, setTable] = useState([]);
  const [prepareTime, setPrepareTime] = useState([]);
  const [order, setOrder] = useState([]);

  const [sort, setSort] = useState(null);
  const [filter, setFilter] = useState(null);
  const [lastSort, setLastSort] = useState(null);
  const [lastFilter, setLastFilter] = useState(null);

  const [visible, setVisible] = useState(false);
  const [currToPrioritizeOrder, setCurrToPrioritizeOrder] = useState({});
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [modalCancelVisibility, setModalCancelVisibility] = useState(false);
  const [currToCancelOrder, setCurrToCancelOrder] = useState({});
  const [currOrderIndex, setCurrOrderIndex] = useState(0);

  const [modalAuthorizeVisibility, setModalAuthorizeVisibility] =
    useState(false);
  const [currToAuthorizeOrder, setCurrToAuthorizeOrder] = useState({});

  ///////////Sorting////////
  const [sortOrderID, setSortOrderID] = useState();
  const [sortDateTime, setSortDateTime] = useState();
  const [sortCustomerName, setSortCustomerName] = useState();
  const [sortWaitingTime, setSortWaitingTime] = useState();
  const [sortAuthorization, setSortAuthorization] = useState({});
  const [sortSender, setSortSender] = useState();
  const [sortPaymentMethod, setSortPaymentMethod] = useState();
  const [sortTotalPrice, setSortTotalPrice] = useState();

  //////////////////////Manage Sender//////////////////////

  const [manageSenderModal, setManageSenderModal] = useState(false);
  const [currToManageOrder, setCurrToManageOrder] = useState({});

  const [selectedSender, setSelectedSender] = useState(
    COURIER_DROPDOWN_LIST[0].value,
  );

  const [deliveryFeeNew, setDeliveryFeeNew] = useState(0);

  const [deliveryQuotation, setDeliveryQuotation] = useState({
    totalFee: 0,
  });

  ////////////////////////////////////////////////////////
  const [controller, setController] = useState({});
  const [controller1, setController1] = useState({});
  const [refArray, setRefArray] = useState([]);

  const [refreshRate, setRefreshRate] = useState(new Date());

  const [expandOrder, setExpandOrder] = useState(false);
  const [expandViewDict, setExpandViewDict] = useState({});

  const [takeAwayOrders, setTakeAwayOrders] = useState([]);

  const [search, setSearch] = useState('');
  const [filterType, setFilterType] = useState(0)

  const ingreStat = 1;

  const userOrders = OutletStore.useState((s) => s.userOrders);

  const userName = UserStore.useState((s) => s.name);
  const userId = UserStore.useState((s) => s.firebaseUid);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  //////////////////////////////////////////////////////////////

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  /////////////////////////////////////////////////////////////////////////

  // 2025-06-26 - Individual User Managed Category
  const userManagedCategory = UserStore.useState((s) => s.userManagedCategory);

  /////////////////////////////////////////////////////////////////////////

  const [firstLoaded, setFirstLoaded] = useState(true);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const [notify, setNotify] = useState(false);
  const [cancel, setCancel] = useState(false);

  //////////////////////////////////////////////////////////////

  var BUTTON_APPEAR = {
    AUTHORIZED: 'BUTTON_APPEAR.AUTHORIZED',
    UNAUTHORIZED: 'BUTTON_APPEAR.UNAUTHORIZED',
  };

  const [buttonAppear, setButtonAppear] = useState(BUTTON_APPEAR.UNAUTHORIZED);
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////

  // useEffect(() => {
  //   for (const ref of refArray) {
  //     if (refArray.indexOf(ref) === 0 && ref && ref.current) {
  //       ref.current.close();
  //       break;
  //     }
  //   }
  // }, [refArray]);

  // useEffect(() => {
  //   expandOrderFunc(item);
  // });

  const privileges_state = UserStore.useState((s) => s.privileges);

  const [privileges, setPrivileges] = useState([]);
  const role = UserStore.useState((s) => s.role);
  const pinNo = UserStore.useState(s => s.pinNo);

  const selectedOrder = CommonStore.useState(s => s.selectedOrder);
  const [selectedOrders, setSelectedOrders] = useState([]);

  const [remark, setRemark] = useState('');

  const [filteredAndSortedOrders, setFilteredAndSortedOrders] = useState([]);
  const [selectedCartItemDict, setSelectedCartItemDict] = useState({});
  const [expandedOrderId, setExpandedOrderId] = useState(null);

  useEffect(() => {
    const filtered = takeAwayOrders
      .slice(0)
      .sort((a, b) => b.orderDate - a.orderDate)
      .sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder)
      .filter((item) => {
        if (search.trim() !== '') {
          const searchLowerCase = search.toLowerCase();
          const itemMatch = item.cartItems.some(cartItem =>
            cartItem.name.toLowerCase().includes(searchLowerCase) ||
            cartItem.addOns.some(addOn =>
              addOn.name.toLowerCase().includes(searchLowerCase) ||
              addOn.choiceNames.some(choiceName =>
                choiceName.toLowerCase().includes(searchLowerCase)
              )
            )
          );

          const otherFieldsMatch =
            (item.tableCode || '').toLowerCase().includes(searchLowerCase) ||
            item.orderId.toLowerCase().includes(searchLowerCase) ||
            item.waiterName.toLowerCase().includes(searchLowerCase);

          if (!(itemMatch || otherFieldsMatch)) return false;
        }

        // Apply filter type
        if (filterType === 1) {
          return item.paymentDetails === null;
        }
        else if (filterType === 2) {
          return item.paymentDetails !== null;
        }

        return true; // All Orders or no filter
      });

    setFilteredAndSortedOrders(filtered);
  }, [takeAwayOrders, search, filterType]);

  //////////////////////////////////////////////////

  // 2024-06-04 - e-invoice test api

  useEffect(() => {
    // // eInvoiceConnectToken

    // var body = {
    //   tin: 'C26801224050',

    //   outletId: currOutletId,
    // };

    // ApiClient.POST(API.eInvoiceConnectToken, body, false).then(
    //   (result) => {
    //     console.log('result');
    //     console.log(result);

    //     if (result && result.status === 'success') {
    //     } else {
    //     }

    //     CommonStore.update((s) => {
    //       s.isLoading = false;
    //     });
    //   },
    // );

    ///////////////////////////////////////////////

    // eInvoiceValidateTaxpayer

    // var body = {
    //   tin: 'IG26182727080',
    //   idType: EI_ID_TYPE.NRIC,
    //   idValue: '970210145337',

    //   accessToken: currOutlet.eiAccessToken,
    //   expiryDate: currOutlet.eiExpiryDate,

    //   outletId: currOutletId,
    // };

    // ApiClient.POST(API.eInvoiceValidateTaxpayer, body, false).then(
    //   (result) => {
    //     console.log('result');
    //     console.log(result);

    //     if (result && result.status === 'success') {
    //     } else {
    //     }

    //     CommonStore.update((s) => {
    //       s.isLoading = false;
    //     });
    //   },
    // );

    // 2024-06-11 - e-invoice test submit order

    // await eInvoiceSubmitDocumentByUserOrder({
    //   userOrderId: '1fb7b8fb-08aa-4c4d-9ff6-33131f45f751',
    //   currOutlet: currOutlet,
    // });
  }, []);

  //////////////////////////////////////////////////

  useEffect(() => {
    const useEffectCallback = async () => {
      // admin full access

      // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
      // const enteredPinNo = storageMMKV.getString('enteredPinNo');
      const enteredPinNo = await global.watermelonDBDatabase.localStorage.get('enteredPinNo');

      if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
        setPrivileges([
          "EMPLOYEES",
          "OPERATION",
          "PRODUCT",
          "INVENTORY",
          "INVENTORY_COMPOSITE",
          "DOCKET",
          "VOUCHER",
          "PROMOTION",
          "CRM",
          "LOYALTY",
          "TRANSACTIONS",
          "REPORT",
          "RESERVATIONS",

          // for action
          'REFUND_ORDER',

          'SETTINGS',

          'QUEUE',

          'OPEN_CASH_DRAWER',

          'KDS',

          'UPSELLING',

          // for Kitchen

          'REJECT_ITEM',
          'CANCEL_ORDER',
          //'REFUND_tORDER',

          'MANUAL_DISCOUNT',
        ]);
      } else {
        setPrivileges(privileges_state || []);
      }
    };

    useEffectCallback();
  }, [role, privileges_state, pinNo]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (sortOrderID) {
        ////////Sort Order ID
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) => b.orderId.localeCompare(a.orderId)),
        );
      } else {
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) => a.orderId.localeCompare(b.orderId)),
        );
      }
    });
  }, [sortOrderID]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (sortDateTime) {
        ////////Sort Date/Time Order
        setTakeAwayOrders(
          takeAwayOrders.slice(0).sort((a, b) => b.orderDate - a.orderDate),
        );
      } else {
        setTakeAwayOrders(
          takeAwayOrders.slice(0).sort((a, b) => a.orderDate - b.orderDate),
        );
      }
    });
  }, [sortDateTime]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (sortCustomerName) {
        ////////Sort Customer Name
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) => b.userName.localeCompare(a.userName)),
        );
      } else {
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) => a.userName.localeCompare(b.userName)),
        );
      }
    });
  }, [sortCustomerName]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (sortWaitingTime) {
        ///////// Sort Waiting Time
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            // .sort(
            //   (a, b) =>
            //     moment().valueOf() -
            //     b.estimatedPreparedDate -
            //     (moment().valueOf() - a.estimatedPreparedDate),
            // ),
            .sort(
              (a, b) =>
                moment().valueOf() -
                b.createdAt -
                (moment().valueOf() - a.createdAt),
            ),
        );
      } else {
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            // .sort(
            //   (a, b) =>
            //     moment().valueOf() -
            //     a.estimatedPreparedDate -
            //     (moment().valueOf() - b.estimatedPreparedDate),
            // ),
            .sort(
              (a, b) =>
                moment().valueOf() -
                a.createdAt -
                (moment().valueOf() - b.createdAt),
            ),
        );
      }
    });
  }, [sortWaitingTime]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (sortAuthorization) {
        ////////Sort Authorization / Status of the Order
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) => b.orderStatus.localeCompare(a.orderStatus)),
        );
      } else {
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) => a.orderStatus.localeCompare(b.orderStatus)),
        );
      }
    });
  }, [sortAuthorization]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (sortSender) {
        ///////// Sort Sender/ Courier
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) => b.courierCode.localeCompare(a.courierCode)),
        );
      } else {
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) => a.courierCode.localeCompare(b.courierCode)),
        );
      }
    });
  }, [sortSender]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (sortPaymentMethod) {
        ///////// Sort Payment Gateway/ Method
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) =>
              (b.paymentDetails ? b.paymentDetails.channel : 'N/A').localeCompare(
                a.paymentDetails ? a.paymentDetails.channel : 'N/A',
              ),
            ),
        );
      } else {
        setTakeAwayOrders(
          takeAwayOrders
            .slice(0)
            .sort((a, b) =>
              (a.paymentDetails ? a.paymentDetails.channel : 'N/A').localeCompare(
                b.paymentDetails ? b.paymentDetails.channel : 'N/A',
              ),
            ),
        );
      }
    });
  }, [sortPaymentMethod]);

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      if (sortTotalPrice) {
        ////////// Sort Total Price of the Order
        setTakeAwayOrders(
          takeAwayOrders.slice(0).sort((a, b) => b.finalPrice - a.finalPrice),
        );
      } else {
        setTakeAwayOrders(
          takeAwayOrders.slice(0).sort((a, b) => a.finalPrice - b.finalPrice),
        );
      }
    });
  }, [sortTotalPrice]);

  // useEffect(() => {
  //   if (isMounted) {
  //     InteractionManager.runAfterInteractions(() => {
  //       setTakeAwayOrders(
  //         userOrders.filter((order) =>
  //           order.orderType !== ORDER_TYPE.DINEIN &&
  //           order.orderTypeSub !== ORDER_TYPE_SUB.OTHER_DELIVERY &&
  //           order.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT
  //         ),
  //       );

  //       // // console.log('takeAwayOrders');
  //       // // console.log(
  //       //   userOrders.filter((order) => order.orderType !== ORDER_TYPE.DINEIN),
  //       // );
  //     });
  //   }
  // }, [userOrders, isMounted]);

  useEffect(() => {
    setRefArray((ref) =>
      Array(takeAwayOrders.length)
        .fill()
        .map((_, i) => ref[i] || createRef()),
    );
  }, [takeAwayOrders.length]);

  // useEffect(() => {
  //   setTimeout(() => {
  //     setRefreshRate(new Date());

  //     checkOvertimeOrders();
  //   }, 30000);

  //   checkOvertimeOrders();
  // }, [refreshRate]);

  useFocusEffect(
    useCallback(() => {
      // Clear selected orders when the screen is focused
      setSelectedOrders([]);
    }, [])
  );

  useEffect(() => {
    if (manageSenderModal) {
      if (currToManageOrder.crUserAddress && currOutlet) {
        // valid order to proceed

        if (selectedSender === COURIER_CODE.LALAMOVE) {
          var body = {
            outletLat: currToManageOrder.crOutletLat,
            outletLng: currToManageOrder.crOutletLng,
            outletAddress: currToManageOrder.crOutletAddress,

            outletPhone: currToManageOrder.crOutletPhone,
            outletName: currOutlet.name,

            userLat: currToManageOrder.crUserLat,
            userLng: currToManageOrder.crUserLng,
            userAddress: currToManageOrder.crUserAddress,

            userName: currToManageOrder.crUserName,
            userPhone: currToManageOrder.crUserPhone,
            userRemarks: currToManageOrder.crUserRemarks,

            // scheduleAt: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
          };

          // console.log('quotation body');
          // console.log(body);

          ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
            // console.log('lalamove quotation result');
            // console.log(result);

            if (result === undefined) {
              // means lalamove can't deliver to this address

              Alert.alert(
                'Info',
                'Sorry, we are unable to deliver to this address \nPlease try another one',
              );
            } else if (result && result.totalFee) {
              // { totalFee: "0.00", totalFeeCurrency: "MYR" }

              setDeliveryQuotation({
                totalFee: parseFloat(result.totalFee),
                totalFeeCurrency: result.totalFeeCurrency,
                courierCode: COURIER_CODE.LALAMOVE,
              });
            }
          });
        } else if (selectedSender === COURIER_CODE.MRSPEEDY) {
          var body = {
            outletLat: currToManageOrder.crOutletLat,
            outletLng: currToManageOrder.crOutletLng,
            outletAddress: currToManageOrder.crOutletAddress,

            outletPhone: currToManageOrder.crOutletPhone,
            outletName: currOutlet.name,

            userLat: currToManageOrder.crUserLat,
            userLng: currToManageOrder.crUserLng,
            userAddress: currToManageOrder.crUserAddress,

            userName: currToManageOrder.crUserName,
            userPhone: currToManageOrder.crUserPhone,
            userRemarks: currToManageOrder.crUserRemarks,

            totalWeightKg: currToManageOrder.totalWeightKg,
            // outletRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(5, 'minute').utc().toISOString(),
            // outletRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(10, 'minute').utc().toISOString(),
            // userRequiredStartDatetime: moment().add(totalPrepareTime, 'second').add(15, 'minute').utc().toISOString(),
            // userRequiredFinishDatetime: moment().add(totalPrepareTime, 'second').add(30, 'minute').utc().toISOString(),
          };

          // console.log('quotation body');
          // console.log(body);

          ApiClient.POST(API.mrSpeedyCalculateOrder, body).then((result) => {
            // console.log('mr speedy quotation result');
            // console.log(result);

            if (!result || !result.is_successful) {
              // means lalamove can't deliver to this address

              Alert.alert(
                'Info',
                'Sorry, we are unable to deliver to this address \nPlease try another one',
              );
            } else if (
              result.is_successful &&
              result.order &&
              result.order.payment_amount
            ) {
              // { totalFee: "0.00", totalFeeCurrency: "MYR" }

              setDeliveryQuotation({
                totalFee: parseFloat(result.order.payment_amount),
                totalFeeCurrency: 'MYR',
                courierCode: COURIER_CODE.MRSPEEDY,
              });
            }
          });
        }
      } else {
        // do nothing for now
      }
    }
  }, [manageSenderModal, currToManageOrder, selectedSender, currOutlet]);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Transaction
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   getTakeAwayList();
  //   setInterval(() => {
  //     getTakeAwayList();
  //   }, 5000);
  //   //getTakeAwayList();

  // }

  const checkOvertimeOrders = async () => {
    for (var i = 0; i < takeAwayOrders.length; i++) {
      const waitingTime =
        // (moment().valueOf() - takeAwayOrders[i].estimatedPreparedDate) /
        (moment().valueOf() - takeAwayOrders[i].createdAt) /
        (1000 * 60);

      // if (waitingTime >= 300) {
      //   await cancelOrder(takeAwayOrders[i], false);
      // }
    }
  };

  const prioritizeOrder = (param) => {
    var body = {
      orderId: param,
    };

    // Alert.alert(
    //   'Success',
    //   'The order had been prioritized',
    //   [{ text: 'OK', onPress: () => { } }],
    //   { cancelable: false },
    // );
    // // setState({ visible: false });
    // setVisible(false);

    // ApiClient.POST(API.prioritizeOrder, body, false)
    APILocal.prioritizeOrder({ body })
      .then((result) => {
        if (result !== null) {
          Alert.alert(
            'Success',
            'The takeaway order has been prioritized',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          // setState({ visible: false });
          setVisible(false);

          for (const ref of refArray) {
            if (refArray.indexOf(ref) === currOrderIndex && ref && ref.current) {
              ref.current.close();
            }
          }
        } else {
          Alert.alert(
            'Failed',
            'Your request has failed',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          // setState({ visible: false });
          setVisible(false);
        }
      });
  };

  const cancelOrder = async (param, showAlert = true) => {
    var body = {
      orderId: param.uniqueId,
      tableId: param.tableId,

      cancelRemarks: remark || '',

      orderIdHuman: `#${(param.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + param.orderId}`,

      cartItems: param.cartItems,
      cartItemsCancelled: param.cartItemsCancelled ? param.cartItemsCancelled : [],
    };

    if (param.orderType === ORDER_TYPE.DELIVERY) {
      ApiClient.POST(API.cancelUserOrderByMerchant, body, false).then(
        (result) => {
          if (result && result.status === 'success') {
            if (showAlert) {
              Alert.alert(
                'Success',
                'The takeaway order has been cancelled',
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
              );
              // setState({ visible: false });
              // setVisible(false);
              setModalCancelVisibility(false);

              for (const ref of refArray) {
                if (
                  refArray.indexOf(ref) === currOrderIndex &&
                  ref &&
                  ref.current
                ) {
                  ref.current.close();
                }
              }
            }
          } else if (showAlert) {
            Alert.alert(
              'Failed',
              result.message || 'Your request has failed',
              [{ text: 'OK', onPress: () => { } }],
              { cancelable: false },
            );
            // setState({ visible: false });
            // setVisible(false);
            setModalCancelVisibility(false);
          }
        },
      );
    }
    else {
      APILocal.cancelUserOrderByMerchant({ body, uid: userId })
        .then(
          async (result) => {
            if (result && result.status === 'success') {
              if (showAlert) {
                Alert.alert(
                  'Success',
                  'The takeaway order has been cancelled',
                  [{ text: 'OK', onPress: () => { } }],
                  { cancelable: false },
                );
                setModalCancelVisibility(false);

                for (const ref of refArray) {
                  if (
                    refArray.indexOf(ref) === currOrderIndex &&
                    ref &&
                    ref.current
                  ) {
                    ref.current.close();
                  }
                }
              }

              const order = {
                ...param,
                ...result.data,
              };

              if (global.currOutlet.cancelRejectPrintOs) {
                printUserOrder(
                  {
                    orderData: order,
                  },
                  false,
                  [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                  false,
                  false,
                  false,
                  { isInternetReachable: true, isConnected: true },
                  true, // for isPrioritized
                );

                if (order.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
                  printUserOrder(
                    {
                      // orderId: refundOrderId,
                      orderData: order,
                      receiptNote: currOutlet.receiptNote || '',
                    },
                    true,
                    [PRINTER_USAGE_TYPE.RECEIPT],
                    false,
                    false,
                    false,
                    netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                    true,
                    [],
                    [],
                    true,
                  );
                }
              }

              if (global.outletKdEventTypes.includes(KD_PRINT_EVENT_TYPE.CANCEL)) {
                await printUserOrder(
                  {
                    // orderId: orderIdList[i],
                    orderData: order,
                    // receiptNote: currOutlet.receiptNote || '',
                  },
                  false,
                  [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                  false,
                  false,
                  false,
                  netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                  true,
                  [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                  // cancelUser,
                );

                printKDSummaryCategoryWrapper(
                  {
                    // orderId: orderIdList[i],
                    orderData: order,
                  },
                  [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                  // cancelUser,
                );

                if (order && order.cartItemsCancelled && order.cartItemsCancelled.length > 0) {
                  for (let bdIndex = 0; bdIndex < order.cartItemsCancelled.length; bdIndex++) {
                    if (!order.cartItemsCancelled[bdIndex].isDocket) {
                      await printDocketForKDCancelled(
                        {
                          userOrder: order,
                          cartItem: order.cartItemsCancelled[bdIndex],
                        },
                        // true,
                        [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                        // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                        [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                        // cancelUser,
                      );
                    }
                  }

                  for (let index = 0; index < order.cartItemsCancelled.length; index++) {
                    if (order.cartItemsCancelled[index].isDocket) {
                      await printDocketCancelled(
                        {
                          userOrder: order,
                          cartItem: order.cartItemsCancelled[index],
                        },
                        // true,
                        [PRINTER_USAGE_TYPE.RECEIPT],
                        [KD_ITEM_STATUS.PENDING, KD_ITEM_STATUS.DELIVERED],
                        // cancelUser,
                      );
                    }
                  }
                }
              }
            } else if (showAlert) {
              Alert.alert(
                'Failed',
                'Your request has failed',
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
              );
              // setState({ visible: false });
              // setVisible(false);
              setModalCancelVisibility(false);
            }
          },
        );
    }
  };

  const authorizeOrder = async (param, showAlert = true) => {
    var body = {
      orderId: param.uniqueId,
      // tableId: param.tableId,
      waiterId: userId,
      waiterName: userName,
    };

    // ApiClient.POST(API.authorizeUserOrderByMerchant, body, false)
    APILocal.authorizeUserOrderByMerchant({ body })
      .then(
        async (result) => {
          if (result && result.status === 'success') {
            if (showAlert) {
              Alert.alert(
                'Success',
                'The takeaway order has been authorized',
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
              );
              // setState({ visible: false });
              // setVisible(false);
              setModalAuthorizeVisibility(false);
              setButtonAppear(BUTTON_APPEAR.AUTHORIZED);

              for (const ref of refArray) {
                if (
                  refArray.indexOf(ref) === currOrderIndex &&
                  ref &&
                  ref.current
                ) {
                  ref.current.close();
                }
              }
            }

            // const isPrintingReceipt = await AsyncStorage.getItem('isPrintingReceipt');

            // if (isPrintingReceipt === '0' || isPrintingReceipt === null) {
            //   disconnectPrinter(printer);
            // }

            // disconnectPrinter(printer); // no need anymore

            // await printUserOrder(
            //   {
            //     orderId: param.uniqueId,
            //   },
            //   false,
            //   [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //   false,
            //   false,
            //   false,
            //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
            // );

            logToFile('takeaway - printUserOrder - authorize order');

            await printUserOrder(
              {
                orderId: param.uniqueId,
              },
              false,
              [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
              false,
              false,
              false,
              netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
              true, // for isPrioritized
            );

            await printUserOrder(
              {
                orderData: param,
              },
              false,
              [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
              false,
              false,
              false,
              { isInternetReachable: true, isConnected: true },
            );

            printKDSummaryCategoryWrapper(
              {
                orderData: param,
              },
            );

            const printerIpCountDict = await calcPrintTotalForKdIndividual({
              userOrder: param,
            });
            const printerTaskId = uuidv4();
            global.printingTaskIdDict[printerTaskId] = {};

            for (let bdIndex = 0; bdIndex < param.cartItems.length; bdIndex++) {
              if (!param.cartItems[bdIndex].isDocket) {
                await printDocketForKD(
                  {
                    userOrder: param,
                    cartItem: param.cartItems[bdIndex],
                    printerIpCountDict: printerIpCountDict,
                    printerTaskId: printerTaskId,
                  },
                  // true,
                  [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                  // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                );
              }
            }

            // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
            //   await printUserOrder(
            //     {
            //       orderData: param,
            //     },
            //     false,
            //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //     false,
            //     false,
            //     false,
            //     { isInternetReachable: true, isConnected: true },
            //   );
            // }
            // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
            //   printKDSummaryCategoryWrapper(
            //     {
            //       orderData: param,
            //     },
            //   );
            // }
            // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
            //   for (let bdIndex = 0; bdIndex < param.cartItems.length; bdIndex++) {
            //     await printDocketForKD(
            //       {
            //         userOrder: param,
            //         cartItem: param.cartItems[bdIndex],
            //       },
            //       // true,
            //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
            //     );
            //   }
            // }

            if (param && param.cartItems) {
              for (let index = 0; index < param.cartItems.length; index++) {
                if (param.cartItems[index].isDocket) {
                  await printDocket(
                    {
                      userOrder: param,
                      cartItem: param.cartItems[index],
                    },
                    // true,
                    [PRINTER_USAGE_TYPE.RECEIPT],
                  );
                }
              }
            }
          } else if (showAlert) {
            Alert.alert(
              'Failed',
              'Your request has failed',
              [{ text: 'OK', onPress: () => { } }],
              { cancelable: false },
            );
            // setState({ visible: false });
            // setVisible(false);
            setModalAuthorizeVisibility(false);
          }
        },
      );
  };

  const getTakeAwayList = () => {
    ApiClient.GET(API.getCurrentTakeAwayOrder + User.getOutletId()).then(
      (result) => {
        var takeAwayList = result;

        if (unfilteredOrder && unfilteredOrder.length > 0) {
          var diff = false;

          if (takeAwayList.length !== unfilteredOrder.length) {
            diff = true;
          } else {
            for (var i = 0; i < takeAwayList.length; i++) {
              if (takeAwayList[i].id !== unfilteredOrder[i].id) {
                diff = true;
                break;
              }
            }
          }

          diff &&
            setState({
              order: [...takeAwayList],
              unfilteredOrder: [...takeAwayList],
            });
        } else {
          setState({
            order: [...takeAwayList],
            unfilteredOrder: [...takeAwayList],
          });
        }

        // setState({ order: result, unfilteredOrder: result })
      },
    );
  };

  // const sortingOrders = (param) => {
  //   if (param.value == 1) { //orderid
  //     setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.orderId.localeCompare(a.orderId)));
  //   }
  //   if (param.value == 2) { //date time
  //     setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.orderDate - a.orderDate));
  //   }
  //   if (param.value == 3) { //Name
  //     setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.userName.localeCompare(a.userName)));
  //   }
  //   if (param.value == 4) { //Waiting Time
  //     setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => (moment().valueOf() - b.estimatedPreparedDate) - (moment().valueOf() - a.estimatedPreparedDate)));
  //   }
  //   if (param.value == 5) { //Payment Method
  //     setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.orderStatus.localeCompare(a.orderStatus)));
  //   }
  //   if (param.value == 6) { //Total
  //     setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.finalPrice - a.finalPrice));
  //   }
  // }

  const filterOrders = (param) => {
    if (param.value == 0) {
      // All orders
      setTakeAwayOrders(
        userOrders.filter((order) => order.orderType !== ORDER_TYPE.DINEIN),
      );
    }

    if (param.value == 1) {
      //Awaiting Authorizaion
      setTakeAwayOrders(
        userOrders.filter(
          (order) =>
            order.orderType !== ORDER_TYPE.DINEIN &&
            order.paymentDetails === null,
        ),
      );
    }

    if (param.value == 2) {
      //Paid
      setTakeAwayOrders(
        userOrders.filter(
          (order) =>
            order.orderType !== ORDER_TYPE.DINEIN &&
            order.paymentDetails !== null,
        ),
      );
    }
  };

  const expandOrderFunc = (param) => {
    // if (expandOrder == false) {
    //   // return setState({ expandOrder: true }), param.expandOrder = true;
    //   setExpandOrder(true);
    //   setExpandViewDict({
    //     ...expandViewDict,
    //     [param.uniqueId]: true,
    //   });
    //   expandViewDict;
    // } else {
    //   // return setState({ expandOrder: false }), param.expandOrder = false;
    //   setExpandOrder(false);
    //   setExpandViewDict({
    //     ...expandViewDict,
    //     [param.uniqueId]: undefined,
    //   });
    // }

    // if (!expandViewDict[param.uniqueId]) {
    //   // return setState({ expandOrder: true }), param.expandOrder = true;
    //   // setExpandOrder(true);
    //   setExpandViewDict({
    //     ...expandViewDict,
    //     [param.uniqueId]: true,
    //   });
    // } else {
    //   // return setState({ expandOrder: false }), param.expandOrder = false;
    //   // setExpandOrder(false);
    //   setExpandViewDict({
    //     ...expandViewDict,
    //     [param.uniqueId]: false,
    //   });
    // }
    if (expandedOrderId !== param.uniqueId) {
      setSelectedCartItemDict({});
      setExpandedOrderId(param.uniqueId);
    } else {
      setExpandedOrderId(null);
    }
  };

  const bearCost = () => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    var body = {
      orderId: currToManageOrder.uniqueId,
      merchantId,

      deliveryFeeDiff: Math.max(
        deliveryQuotation.totalFee - currToManageOrder.deliveryFee,
        0,
      ),

      deliveryFee: deliveryQuotation.totalFee,
      courierCode: deliveryQuotation.courierCode,
      deliveryCurrency: deliveryQuotation.totalFeeCurrency,
    };

    ApiClient.POST(API.updateUserOrderCourier, body, {
      timeout: 10000,
    }).then(async (result) => {
      // console.log('updateUserOrderCourier');
      // console.log(result);

      if (result) {
        if (result.status === 'success') {
          Alert.alert(
            'Success',
            'Sender for this order has been changed',
            [
              {
                text: 'OK',
                onPress: () => { },
              },
            ],
            { cancelable: false },
          );
        } else {
          Alert.alert(
            'Error',
            result.message,
            [
              {
                text: 'OK',
                onPress: () => { },
              },
            ],
            { cancelable: false },
          );
        }
      } else {
        Alert.alert(
          'Error',
          'Failed to change the sender for this order',
          [
            {
              text: 'OK',
              onPress: () => { },
            },
          ],
          { cancelable: false },
        );
      }

      CommonStore.update((s) => {
        s.isLoading = false;
      });
    });
  };

  const notifyUser = () => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    setNotify(true);

    var body = {
      orderId: currToManageOrder.uniqueId,
    };

    ApiClient.POST(API.notifyUserOrderCourierAction, body, false).then(
      (result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'The user of this order has been notified',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        } else {
          Alert.alert(
            'Failed',
            result.message || 'Your request has failed',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        }

        setManageSenderModal(false);

        CommonStore.update((s) => {
          s.isLoading = false;
        });

        setNotify(false);
      },
    );
  };

  const cancelAndRefund = () => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    setCancel(true);

    var body = {
      orderId: currToManageOrder.uniqueId,
      tableId: currToManageOrder.tableId,
    };

    ApiClient.POST(API.cancelUserOrderByMerchant, body, false).then(
      (result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'The delivery order has been cancelled & the refund process will take several working days to completed',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        } else {
          Alert.alert(
            'Failed',
            result.message || 'Your request has failed',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        }

        setManageSenderModal(false);

        CommonStore.update((s) => {
          s.isLoading = false;
        });

        setCancel(false);
      },
    );
  };

  // const rightAction = (item, index) => {
  //   return (
  //     <View
  //       style={{
  //         justifyContent: 'center',
  //         alignItems: 'center',
  //         flexDirection: 'row',
  //         //width: '32%',
  //       }}>

  //       {(item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
  //         item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
  //         item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
  //         item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) ? (
  //         <TouchableOpacity
  //           onPress={() => {
  //             // setState({
  //             //   currToPrioritizeOrder: item,
  //             //   visible: true,
  //             // });
  //             setCurrOrderIndex(index);
  //             setCurrToPrioritizeOrder(item);
  //             setVisible(true);
  //           }}
  //           style={{
  //             height: '100%',
  //             justifyContent: 'center',
  //             alignItems: 'center',
  //             alignContent: 'center',
  //             alignSelf: 'center',
  //             backgroundColor: Colors.primaryColor,
  //             underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //             paddingBottom: 6,
  //             width: 75,
  //           }}>
  //           {switchMerchant ? (
  //             <MaterialCommunityIcons
  //               name="message-alert-outline"
  //               size={10}
  //               color={Colors.whiteColor}
  //               style={{ marginTop: 10 }}
  //             />
  //           ) : (
  //             <MaterialCommunityIcons
  //               name="message-alert-outline"
  //               size={40}
  //               color={Colors.whiteColor}
  //               style={{ marginTop: 10 }}
  //             />
  //           )}
  //           <Text
  //             style={[
  //               {
  //                 color: Colors.whiteColor,
  //                 fontSize: 12,
  //                 fontFamily: 'NunitoSans-Regular',
  //                 textAlign: 'center',
  //                 width: '80%',
  //               },
  //               switchMerchant
  //                 ? {
  //                   fontSize: 10,
  //                 }
  //                 : {},
  //             ]}>
  //             {`Prioritize\nOrder`}
  //           </Text>
  //         </TouchableOpacity>
  //       ) : null}

  //       {(
  //         (item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
  //           item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
  //           item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
  //           item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED)
  //         &&
  //         (item.orderType === ORDER_TYPE.PICKUP)
  //         // &&
  //         // item.paymentDetails === null
  //       )
  //         ? (
  //           <TouchableOpacity
  //             onPress={() => {
  //               // setState({
  //               //   currToPrioritizeOrder: item,
  //               //   visible: true,
  //               // });

  //               // setCurrOrderIndex(index);
  //               // setCurrToPrioritizeOrder(item);
  //               // setVisible(true);

  //               CommonStore.update(s => {
  //                 s.isCheckingOutTakeaway = true;

  //                 s.checkingOutTakeawayOrder = item;

  //                 s.timestamp = Date.now();

  //                 s.checkingOutTakeawayTimestamp = Date.now();
  //               }, () => {
  //                 navigation.navigate('Table');
  //               });
  //             }}
  //             style={{
  //               height: '100%',
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               alignSelf: 'center',
  //               backgroundColor: Colors.tabCyan,
  //               underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //               paddingBottom: 6,
  //               width: 75,
  //             }}>
  //             {switchMerchant ? (
  //               <MaterialIcons
  //                 name="payment"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             ) : (
  //               <MaterialIcons
  //                 name="payment"
  //                 size={40}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             )}
  //             <Text
  //               style={[
  //                 {
  //                   color: Colors.whiteColor,
  //                   fontSize: 12,
  //                   fontFamily: 'NunitoSans-Regular',
  //                   textAlign: 'center',
  //                   width: '80%',
  //                 },
  //                 switchMerchant
  //                   ? {
  //                     fontSize: 10,
  //                   }
  //                   : {},
  //               ]}>
  //               {`Checkout\nOrder`}
  //             </Text>
  //           </TouchableOpacity>
  //         ) : null}

  //       {
  //         (currOutlet && currOutlet.privileges &&
  //           currOutlet.privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER))
  //           && privileges && privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER) ?
  //           <TouchableOpacity
  //             onPress={() => {
  //               // setState({
  //               //   currToPrioritizeOrder: item,
  //               //   visible: true,
  //               // });
  //               if (item.paymentDetails && (
  //                 item.paymentDetails.txn_ID !== undefined ||
  //                 item.paymentDetails.txnId !== undefined
  //               )) {
  //                 Alert.alert('Info', 'Online paid order is unable to cancel.');
  //               }
  //               else {
  //                 setCurrOrderIndex(index);
  //                 setCurrToCancelOrder(item);
  //                 setModalCancelVisibility(true);
  //               }
  //             }}
  //             style={{
  //               height: '100%',
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               alignSelf: 'center',
  //               // backgroundColor: Colors.primaryColor,
  //               backgroundColor: '#d90000',
  //               underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //               paddingBottom: 6,
  //               width: 75,
  //             }}>
  //             {switchMerchant ? (
  //               <MaterialCommunityIcons
  //                 name="close"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             ) : (
  //               <MaterialCommunityIcons
  //                 name="close"
  //                 size={40}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             )}
  //             <Text
  //               style={[
  //                 {
  //                   color: Colors.whiteColor,
  //                   fontSize: 12,
  //                   fontFamily: 'NunitoSans-Regular',
  //                   textAlign: 'center',
  //                   width: '80%',
  //                 },
  //                 switchMerchant
  //                   ? {
  //                     fontSize: 10,
  //                   }
  //                   : {},
  //               ]}>
  //               {`Cancel\nOrder`}
  //             </Text>
  //           </TouchableOpacity>
  //           : <></>
  //       }

  //       {/* 2023-01-30 - For reprint kd */}

  //       <TouchableOpacity
  //         onPress={async () => {
  //           ///////////////////////////////////////////////////////////////////////////

  //           Alert.alert('Info', 'Kitchen docket has been added to print queue');

  //           var printTimes = 1;

  //           if (global.outletCategoriesDict) {
  //             if (item.cartItems && item.cartItems.length > 0) {
  //               for (var i = 0; i < item.cartItems.length; i++) {
  //                 if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
  //                   global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
  //                   printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
  //                 }
  //               }
  //             }

  //             if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
  //               for (var i = 0; i < item.cartItemsCancelled.length; i++) {
  //                 if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
  //                   global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
  //                   printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
  //                 }
  //               }
  //             }
  //           }

  //           for (var i = 0; i < printTimes; i++) {
  //             await printUserOrder(
  //               {
  //                 orderData: item,
  //               },
  //               false,
  //               [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //               false,
  //               false,
  //               false,
  //               { isInternetReachable: true, isConnected: true },
  //               false,
  //               [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
  //             );

  //             printKDSummaryCategoryWrapper(
  //               {
  //                 orderData: item,
  //               },
  //               [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
  //             );
  //           }

  //           // await printUserOrder(
  //           //   {
  //           //     orderData: item,
  //           //   },
  //           //   false,
  //           //   [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           //   false,
  //           //   false,
  //           //   false,
  //           //   { isInternetReachable: true, isConnected: true },
  //           //   false,
  //           //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
  //           // );

  //           // printKDSummaryCategoryWrapper(
  //           //   {
  //           //     orderData: item,
  //           //   },
  //           //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
  //           // );

  //           // for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
  //           //   await printDocketForKD(
  //           //     {
  //           //       userOrder: item,
  //           //       cartItem: item.cartItems[bdIndex],
  //           //     },
  //           //     // true,
  //           //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           //     // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
  //           //   );
  //           // }

  //           ///////////////////////////////////////////////////////////////////////////

  //           // disconnectPrinter(printer); // no need anymore

  //           // await printUserOrder(
  //           //   {
  //           //     orderId: item.uniqueId,
  //           //     receiptNote: currOutlet.receiptNote || '',
  //           //   },
  //           //   false,
  //           //   [PRINTER_USAGE_TYPE.RECEIPT],
  //           //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           //   false,
  //           //   false,
  //           //   false,
  //           //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
  //           //   // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
  //           //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           // );

  //           // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
  //           //   await printUserOrder(
  //           //     {
  //           //       orderData: item,
  //           //     },
  //           //     false,
  //           //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           //     false,
  //           //     false,
  //           //     false,
  //           //     { isInternetReachable: true, isConnected: true },
  //           //   );
  //           // }
  //           // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
  //           //   printKDSummaryCategoryWrapper(
  //           //     {
  //           //       orderData: item,
  //           //     },
  //           //   );
  //           // }
  //           // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
  //           //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
  //           //     await printDocketForKD(
  //           //       {
  //           //         userOrder: item,
  //           //         cartItem: item.cartItems[bdIndex],
  //           //       },
  //           //       // true,
  //           //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
  //           //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
  //           //     );
  //           //   }
  //           // }
  //         }}
  //         style={{
  //           height: '100%',
  //           justifyContent: 'center',
  //           alignItems: 'center',
  //           alignContent: 'center',
  //           alignSelf: 'center',
  //           backgroundColor: Colors.secondaryColor,
  //           underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //           paddingBottom: 6,
  //           width: 75,
  //         }}>
  //         {switchMerchant ? (
  //           <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
  //         ) : (
  //           <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
  //         )}

  //         <Text
  //           style={[
  //             {
  //               color: Colors.whiteColor,
  //               fontSize: 12,
  //               fontFamily: 'NunitoSans-Regular',
  //               textAlign: 'center',
  //               width: windowWidth <= 1133 ? '85%' : '80%',
  //             },
  //             switchMerchant
  //               ? {
  //                 fontSize: 10,
  //               }
  //               : {},
  //           ]}>
  //           {`Reprint\nKD`}
  //         </Text>
  //       </TouchableOpacity>

  //       {item.orderType === ORDER_TYPE.DELIVERY &&
  //         item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ? (
  //         <TouchableOpacity
  //           onPress={() => {
  //             setCurrToManageOrder(item);
  //             setDeliveryFeeNew(item.deliveryFee);
  //             setDeliveryQuotation({
  //               totalFee: item.deliveryFee,
  //             });

  //             setManageSenderModal(true);
  //           }}
  //           style={{
  //             height: '100%',
  //             justifyContent: 'center',
  //             alignItems: 'center',
  //             alignContent: 'center',
  //             alignSelf: 'center',
  //             //backgroundColor: '#8fbc8f',
  //             backgroundColor: Colors.tabGold,
  //             underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //             paddingBottom: 6,
  //             width: 75,
  //           }}>
  //           {switchMerchant ? (
  //             <MaterialIcons
  //               name="delivery-dining"
  //               size={10}
  //               color={Colors.whiteColor}
  //               style={{ marginTop: 10 }}
  //             />
  //           ) : (
  //             <MaterialIcons
  //               name="delivery-dining"
  //               size={40}
  //               color={Colors.whiteColor}
  //               style={{ marginTop: 10 }}
  //             />
  //           )}
  //           <Text
  //             style={[
  //               {
  //                 color: Colors.whiteColor,
  //                 fontSize: 12,
  //                 fontFamily: 'NunitoSans-Regular',
  //                 textAlign: 'center',
  //                 width: '80%',
  //               },
  //               switchMerchant
  //                 ? {
  //                   fontSize: 10,
  //                 }
  //                 : {},
  //             ]}>
  //             Change Sender
  //           </Text>
  //         </TouchableOpacity>
  //       ) : null}

  //       {item.orderStatus !== USER_ORDER_STATUS.ORDER_AUTHORIZED &&
  //         item.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARING &&
  //         item.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED &&
  //         item.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED
  //         ?
  //         (
  //           <TouchableOpacity
  //             onPress={() => {
  //               //   currToPrioritizeOrder: item,
  //               //   visible: true,
  //               // });
  //               setCurrOrderIndex(index);
  //               setCurrToAuthorizeOrder(item);
  //               setModalAuthorizeVisibility(true);
  //             }}
  //             style={{
  //               height: '100%',
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               alignSelf: 'center',
  //               backgroundColor: '#8fbc8f',
  //               //backgroundColor: Colors.tabCyan,
  //               underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //               paddingBottom: 6,
  //               width: 75,
  //             }}>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="check"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="check"
  //                 size={40}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 10 }}
  //               />
  //             )}

  //             <Text
  //               style={[
  //                 {
  //                   color: Colors.whiteColor,
  //                   fontSize: 12,
  //                   fontFamily: 'NunitoSans-Regular',
  //                   textAlign: 'center',
  //                   width: '80%',
  //                 },
  //                 switchMerchant
  //                   ? {
  //                     fontSize: 10,
  //                   }
  //                   : {},
  //               ]}>
  //               Authorize Order
  //             </Text>
  //           </TouchableOpacity>
  //         ) : null}
  //     </View>
  //   );
  // };

  const renderOrder = ({ item, index }) => {
    const waitingTime =
      // (moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60);
      (moment().valueOf() - item.createdAt) / (1000 * 60);

    var hrs = Math.floor(moment().diff(item.updatedAt, 'minutes') / 60);
    var mins = Math.floor(moment().diff(item.updatedAt, 'minutes') % 60);

    var courierStatusParsed = '';

    if (item.courierCode === COURIER_CODE.LALAMOVE) {
      courierStatusParsed = LALAMOVE_STATUS_PARSED[item.courierStatus];
    } else if (item.courierCode === COURIER_CODE.MRSPEEDY) {
      courierStatusParsed = MRSPEEDY_STATUS_PARSED[item.courierStatus];
    }

    ///////////////////////////

    // console.log('order id');
    // console.log(item.orderId);

    // calculate longest

    var longestStr = 5;

    // for (var i = 0; i < item.cartItems.length; i++) {
    //   const cartItemPriceWIthoutAddOn =
    //     item.cartItems[i].price -
    //     item.cartItems[i].addOns.reduce(
    //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
    //       0,
    //     );

    //   if (cartItemPriceWIthoutAddOn.toFixed(0).length > longestStr) {
    //     longestStr = cartItemPriceWIthoutAddOn.toFixed(0).length;
    //   }

    //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
    //     if (
    //       item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length > longestStr
    //     ) {
    //       longestStr = item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length;
    //     }
    //   }
    // }

    // if (item.totalPrice.toFixed(0).length > longestStr) {
    //   longestStr = item.totalPrice.toFixed(0).length;
    // }

    // if (item.discount.toFixed(0).length > longestStr) {
    //   longestStr = item.discount.toFixed(0).length;
    // }

    // if (item.tax.toFixed(0).length > longestStr) {
    //   longestStr = item.tax.toFixed(0).length;
    // }

    // if (item.finalPrice.toFixed(0).length > longestStr) {
    //   longestStr = item.finalPrice.toFixed(0).length;
    // }

    // // console.log(longestStr);

    ///////////////////////////

    // calculate spacing

    var cartItemPriceWIthoutAddOnSpacingList = [];
    var addOnsSpacingList = [];

    // for (var i = 0; i < item.cartItems.length; i++) {
    //   const cartItemPriceWIthoutAddOn =
    //     item.cartItems[i].price -
    //     item.cartItems[i].addOns.reduce(
    //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
    //       0,
    //     );

    //   cartItemPriceWIthoutAddOnSpacingList.push(
    //     Math.max(longestStr - cartItemPriceWIthoutAddOn.toFixed(0).length, 0) +
    //     1,
    //   );

    //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
    //     addOnsSpacingList.push(
    //       Math.max(
    //         longestStr -
    //         item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length,
    //         0,
    //       ) + 1,
    //     );
    //   }
    // }

    // var totalPriceSpacing =
    //   Math.max(longestStr - item.totalPrice.toFixed(0).length, 0) + 1;
    // var discountSpacing =
    //   Math.max(longestStr - item.discount.toFixed(0).length, 0) + 1;
    // var taxSpacing = Math.max(longestStr - item.tax.toFixed(0).length, 0) + 1;
    // var finalPriceSpacing =
    //   Math.max(longestStr - item.finalPrice.toFixed(0).length, 0) + 1;

    const isChecked = selectedOrders.some(order => order.uniqueId === item.uniqueId);
    ///////////////////////////

      return (
        (<View
          style={{
            paddingVertical: 5,
            shadowOpacity: 0,
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            // elevation: 1,
            elevation: 3,
          }}>
          {/* <Swipeable
            renderRightActions={() => rightAction(item)}
            // ref={ref => {
            //   if (ref && !refArray.includes(ref)) {
            //     refArray[index] = ref;
            //   }
            // }}
            ref={refArray[index]}
            onSwipeableWillOpen={() => {
              // for (const ref of refArray) {
              //   if (refArray.indexOf(ref) != index && ref && ref.current) {
              //     ref.current.close()
              //   }
              // }
            }}> */}
          {((item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
            item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
            item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
            item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) && item.paymentDate === null)
            ?
            <View style={{ position: 'absolute', top: 10, right: 4 }}>
              <TouchableOpacity>
                <CheckBox
                  value={isChecked}  // Set the current checked status
                  onValueChange={(newValue) => {
                    if (newValue) {
                      // Add the selected order to the list
                      setSelectedOrders(prev => [...prev, item]);
                    } else {
                      // Remove the order from the list if unchecked
                      setSelectedOrders(prev => prev.filter(order => order.uniqueId !== item.uniqueId));
                    }
                  }}
                />
              </TouchableOpacity>
            </View>
            : <></>}
          <TouchableOpacity
            style={{ zIndex: -1, }}
            onPress={() => {
              // console.log(windowWidth);
              expandOrderFunc(item);
            }}>
            <View
            style={
              (item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
                item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED)
                ? {
                  elevation: 2,
                  borderRadius: 5,
                  backgroundColor: 'white',
                  borderWidth: 2,
                  borderColor: Colors.primaryColor,
                }
                : {
                  elevation: 2,
                  borderRadius: 5,
                  backgroundColor: 'white',
                  borderWidth: 2,
                  borderColor: Colors.whiteColor,
                }
            }>

            {/* Online / Offline label */}
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 5 }}>
              {item.appType ? (
                <Text style={{ fontSize: 14, fontWeight: 'bold', color: 'green' }}>
                  Online
                </Text>
              ) : (
                <Text style={{ fontSize: 14, fontWeight: 'bold', color: 'red' }}>
                  Offline
                </Text>
              )}
            </View>
              
            <View
              style={{
                opacity: item.isPrioritizedOrder ? 100 : 0,
                position: 'absolute',
                alignItems: 'center',
                width: '8%',
                marginHorizontal: 0.5,
                zIndex: 1,
                flexDirection: 'row',
                paddingTop: 4,
                paddingLeft: 4,
              }}>
              {item.isRefundOrder == true ?
                <FontAwesome name={'star'} size={17} color={'#1260cc'} style={{ paddingRight: 4 }} />
                : <></>}
              <View
                style={[
                  {
                    // width: Platform.OS === 'ios' ? 63 : 60,
                    paddingHorizontal: '2%',
                    paddingRight: '4%',
                    backgroundColor: '#e08300',
                    height: 18,
                    top: Platform.OS === 'ios' ? 3 : 1,
                    borderRadius: 5,
                  },
                  switchMerchant
                    ? {
                      height: windowHeight * 0.03,
                      width: windowWidth * 0.059,
                      // marginTop: '-5%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      // top: -0.5,
                      // top: windowHeight * -0.0003,
                      // left: windowWidth * 0.003,
                    }
                    : {},
                ]}>
                <View
                  style={[
                    {
                      flexDirection: 'row',
                      justifyContent: 'center',
                      width: '80%',
                    },
                    switchMerchant
                      ? {
                        // left: windowWidth * -0.003,
                        marginLeft: windowWidth * -0.006,
                      }
                      : {},
                  ]}>
                  {switchMerchant ? (
                    <MaterialCommunityIcons
                      name="message-alert-outline"
                      size={10}
                      color={Colors.whiteColor}
                      style={{ marginLeft: 15, marginRight: 3 }}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name="message-alert-outline"
                      size={17}
                      color={Colors.whiteColor}
                      style={{ marginLeft: 15, marginRight: 3 }}
                    />
                  )}

                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        fontSize: 8,
                        alignSelf: 'center',
                        bottom: 1,
                      },
                      switchMerchant
                        ? {
                          fontSize: 7,
                          marginLeft: '-1%',
                          width: '100%',
                          // borderWidth: 1,
                          textAlign: 'left',
                        }
                        : {},
                    ]}>
                    Prioritize
                  </Text>
                </View>
              </View>
            </View>
            {/* <View style={{ opacity: item.isPrioritizedOrder ? 100 : 0, position: 'absolute', alignItems: 'center', width: '40%', marginHorizontal: 0.2 }}>
              <FontAwesome name={'star'} size={25} color={Colors.secondaryColor} style={{ padding: 4 }} />
              <Text style={{ color: Colors.secondaryColor, fontSize: 13 }}>{item.isPrioritizedOrder ? "prioritizee" : ""  }</Text>
            </View> */}
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                height:
                  item.appType === 'WEB_ORDER' && windowWidth <= 1133 ? windowHeight * 0.19
                    : windowWidth <= 1133 ? windowHeight * 0.16
                      : item.appType === 'WEB_ORDER' ? windowHeight * 0.16
                        : windowHeight * 0.12,
                alignItems: 'center',
                borderBottomColor: Colors.fieldtT,
                borderBottomWidth:
                  expandViewDict[item.uniqueId] == true
                    ? StyleSheet.hairlineWidth
                    : null,
              }}>
              {/* {item.isRefundOrder == true ? 
                  <View
                    style={[{
                      width: '3.2%',
                      marginHorizontal: 0.5,
                      alignItems: 'flex-start',
                      //paddingLeft: '1%',
                    }, windowWidth === 1280 && windowHeight === 800 ? {
                      right: 1,
                    } : {}]}>
                    <FontAwesome name={'star'} size={25} color={'#1260cc'} style={{ padding: 4 }} />
                  </View>
                  :
                  <View
                    style={[{
                      width: '3.2%',
                      marginHorizontal: 0.5,
                      alignItems: 'flex-start',
                      //paddingLeft: '1%',
                    }, windowWidth === 1280 && windowHeight === 800 ? {
                      right: 1,
                    } : {}]}/>
                } */}

              <View
                style={{
                  width: '8%',
                  marginHorizontal: 0.5,
                  //flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 0,
                  top: 5,
                }}>
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    bottom: 5,
                    // right: 5,
                    // left: Platform.OS == 'ios' ? 0 : 0,
                    // left: Platform.OS == 'android' ? 0 : 0
                  }}>
                  {item.courierId ? (
                    <>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                          //left: 30,
                        }}>
                        <Image
                          style={[
                            {
                              width:
                                windowWidth <= 1133
                                  ? 50
                                  : 60,
                              height:
                                windowWidth <= 1133
                                  ? 50
                                  : 60,
                            },
                            switchMerchant
                              ? {
                                width:
                                  windowWidth * 0.03,
                                height:
                                  windowHeight * 0.05,
                              }
                              : {},
                          ]}
                          source={COURIER_INFO_DICT[item.courierCode].img}
                        />
                        {/* <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', paddingLeft: 5, textAlign: 'center' }}>{COURIER_INFO_DICT[item.courierCode].name}</Text> */}
                      </View>

                      {/* <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                            //left: 30,
                          }}>
                            <Text style={{
                              fontSize: 14, fontFamily: 'NunitoSans-SemiBold',
                              textAlign: 'center', color: Colors.descriptionColor,
                              marginTop: -5,
                              marginLeft: 8,
                            }}>{courierStatusParsed ? courierStatusParsed : 'N/A'}</Text>
                          </View> */}
                    </>
                  ) : (
                    // <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', paddingLeft: 5, textAlign: 'center' }}>N/A</Text>
                    // <Icon name={'shopping-bag'} size={25} color={Colors.fieldtTxtColor} style={{ padding: 4 }} />
                    (<View
                      style={{
                        width: 60,
                        height: 60,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                    {item.appType ? (
                      <QRCode
                        value={item.orderId}
                        size={40}
                      />
                    ) : (
                      <Image
                        style={[
                          {
                            width: 30,
                            height: 30,
                            //marginLeft: 0,
                            //left: 30,
                          },
                          switchMerchant
                            ? {
                              width: windowWidth * 0.03,
                              height:
                                windowHeight * 0.05,
                              top: windowHeight * 0.009,
                            }
                            : {},
                        ]}
                        resizeMode="contain"
                        source={require('../assets/image/store.png')}
                      />
                    )}
                    </View>)
                  )}
                </View>
                {item.appType === 'WEB_ORDER' ?
                  <View
                    style={{
                      width: 40,
                      height: 40,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                    <Ionicons name={'qr-code'} size={switchMerchant ? 20 : 25} />
                  </View>
                  : <></>}
              </View>

              <View
                style={[{
                  width: '9%',
                  marginHorizontal: 0.5,
                  alignItems: 'flex-start',
                  paddingLeft: '1%',
                }, windowWidth === 1280 && windowHeight === 800 ? {
                  right: 1,
                } : {}]}>
                <Text
                  style={[
                    {
                      fontSize: 16,
                      fontFamily: 'NunitoSans-SemiBold',
                      textAlign: 'center',
                      color: Colors.descriptionColor,
                      // marginTop: -5,
                      // marginLeft: 8,
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {courierStatusParsed ? courierStatusParsed : 'N/A'}
                </Text>
              </View>

              <View
                style={[{
                  width: '12%',
                  marginHorizontal: 0.5,
                  alignItems: 'flex-start',
                }, windowWidth === 1280 && windowHeight === 800 ? {
                  right: 1,
                } : {}]}>
                <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                  <Text
                    style={[
                      {
                        color: Colors.fontDark,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    #
                    {item.orderType !== ORDER_TYPE.DINEIN
                      ? item.orderType === ORDER_TYPE.DELIVERY
                        ? 'D'
                        : 'T'
                      : ''}
                    {item.orderId}
                    {/* {item.uniqueId} */}
                  </Text >
                  {item.isLater === 'LATER' ?
                    <EvilIcons
                      name="clock"
                      size={30}
                      color={Colors.primaryColor}
                      style={{ marginRight: 0 }}
                    /> : <></>}
                </View>
                {item.isLater === 'LATER' ?
                  <View style={{ marginTop: 5, flexDirection: 'column', }}>

                    <Text style={[
                      {
                        color: Colors.fontDark,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                      {`${moment(item.scheduleDateTime).format('DD MMM YYYY')}\n${moment(item.scheduleDateTime).format('hh:mm A')}`}
                    </Text>
                  </View>
                  : <></>}
              </View>

              <View
                style={{
                  width: '11.5%',
                  marginHorizontal: 0.5,
                  alignItems: 'flex-start',
                }}>
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {moment(item.orderDate).format('DD MMM YYYY')}
                </Text>
                <Text
                  style={[
                    {
                      color: Colors.fontDark,
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Bold',
                      marginTop: 2,
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {moment(item.orderDate).format('hh:mm A')}
                </Text>
              </View>

              {/* need change to staff name */}
              <View
                style={{
                  width: '10%',
                  marginHorizontal: 0.5,
                  alignItems: 'flex-start',
                }}>
                <View
                  style={{
                    width: '90%',
                    // alignItems: 'center',
                  }}>
                  <Text
                    style={[
                      {
                        color: Colors.fontDark,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}
                    numberOfLines={1}>
                    {item.waiterName ? item.waiterName : 'N/A'}
                  </Text>
                </View>
              </View>

              <View
                style={{
                  width: '16%',
                  marginHorizontal: 0.5,
                  alignItems: 'center',
                }}>
                {item.preorderPackageId ? (
                  <View
                    style={{
                      width: '100%',
                      // alignItems: 'center',
                    }}>
                    <View
                      style={{
                        alignItems: 'center',
                        paddingVertical: 2,
                        borderRadius: 3,
                        backgroundColor: Colors.secondaryColor,
                        width: '65%',
                        paddingBottom: 6,
                      }}>
                      <Text
                        style={[
                          {
                            color: Colors.blackColor,
                            fontSize: Platform.OS == 'ios' ? 16 : 16,
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {'Preorder'}
                      </Text>
                    </View>
                    {switchMerchant ? (
                      <Text
                        style={[
                          {
                            color: Colors.blackColor,
                            fontSize: Platform.OS == 'ios' ? 11 : 11,
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {`${moment(item.preorderCollectionDate).format(
                          'DD MMM YYYY',
                        )}\n${moment(item.preorderCollectionTime).format(
                          'hh:mm A',
                        )}`}
                      </Text>
                    ) : (
                      <Text
                        style={[
                          {
                            color: Colors.blackColor,
                            fontSize: Platform.OS == 'ios' ? 11 : 11,
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {`${moment(item.preorderCollectionDate).format(
                          'DD MMM YYYY',
                        )} ${moment(item.preorderCollectionTime).format(
                          'hh:mm A',
                        )}`}
                      </Text>
                    )}
                  </View>
                ) : (
                  // not sure which one
                  // <View style={{ alignItems: 'center', paddingVertical: 2, borderRadius: 3, backgroundColor: waitingTime < 16 ? '#9e9e9e' : waitingTime < 21 ? Colors.secondaryColor : '#d90000', width: '75%' }}>
                  //   <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{moment().diff(waitingTime, 'minutes') < 60 ? `${mins} mins` : `${hrs}hrs:${mins}mins`}</Text>
                  // <View style={{ alignItems: 'center', paddingVertical: 2, borderRadius: 3, backgroundColor: waitingTime < 16 ? '#9e9e9e' : waitingTime < 21 ? Colors.secondaryColor : '#d90000', width: '65%' }}>
                  //   <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{waitingTime > 60 ? "OverTime" : (waitingTime < 0 ? 0 : waitingTime.toFixed(0)) + ' mins'}</Text>
                  (<View
                    style={{
                      width: '100%',
                      // alignItems: 'center',
                    }}>
                    <View
                      style={{
                        alignItems: 'center',
                        paddingVertical: 2,
                        borderRadius: 3,
                        backgroundColor:
                          waitingTime < 16
                            ? '#9e9e9e'
                            : waitingTime < 21
                              ? Colors.secondaryColor
                              : '#d90000',
                        width: '65%',
                      }}>
                      <Text
                        style={[
                          {
                            color: Colors.whiteColor,
                            fontSize: Platform.OS == 'ios' ? 16 : 16,
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {waitingTime > 60
                          ? 'OverTime'
                          : moment().diff(waitingTime, 'minutes') < 60
                            ? `${mins} mins`
                            : `${hrs}hrs:${mins}mins`}
                      </Text>
                    </View>
                  </View>)
                )}
              </View>
              {/* <View style={{ width: '12%', marginHorizontal: 0.5, alignItems: 'center' }}>
                <View style={{ backgroundColor: item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ? Colors.tabGrey : Colors.primaryColor, paddingVertical: 2, width: item.paymentDate === null ? 100 : 130, alignItems: 'center', borderRadius: 3 }}>
                  <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ? "Pending" : "Authorized"}</Text>
                </View>
              </View> */}
              {/*<View style={{ width: '14%', marginHorizontal: 0.5, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                <View style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  {
                    item.courierId
                      ?
                      <>
                        <View style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                          <Image style={{ width: 25, height: 25, borderRadius: 5 }}
                            source={COURIER_INFO_DICT[item.courierCode].img}
                          />
                          <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', paddingLeft: 5, textAlign: 'center' }}>{COURIER_INFO_DICT[item.courierCode].name}</Text>
                        </View>

                        <View style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                          <Text style={{
                            fontSize: 14, fontFamily: 'NunitoSans-SemiBold',
                            textAlign: 'center', color: Colors.descriptionColor,
                            marginTop: 8,
                            marginLeft: 8,
                          }}>{courierStatusParsed ? courierStatusParsed : 'N/A'}</Text>
                        </View>
                      </>
                      :
                      <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', paddingLeft: 5, textAlign: 'center' }}>N/A</Text>
                  }
                </View>
              </View> */}
              <View
                style={{
                  width: '16%',
                  marginHorizontal: 0.5,
                  alignItems: 'center',
                }}>
                {/* <Text style={{ color: Colors.blackColor, fontFamily: 'NunitoSans-Bold', }}>{item.paymentDetails ? item.paymentDetails.channel : 'N/A'}</Text> */}

                {/* {
                    item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED
                      ?
                      <View style={{
                        width: '100%',
                        alignItems: 'center',
                      }}>
                        <View style={{ alignItems: 'center', paddingVertical: 2, borderRadius: 3, backgroundColor: Colors.secondaryColor, width: '80%' }}>
                          <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{item.paymentDetails ? item.paymentDetails.channel : 'N/A'}</Text>
                        </View>
                      </View>
                      :
                      <View style={{ alignItems: 'center', paddingVertical: 2, borderRadius: 3, backgroundColor: Colors.primaryColor, width: '80%' }}>
                        <Text style={{ color: Colors.whiteColor, fontSize: Platform.OS == 'ios' ? 14 : 16, fontFamily: 'NunitoSans-Regular' }}>{item.paymentDetails ? item.paymentDetails.channel : 'N/A'}</Text>
                      </View>
                  } */}

                <View
                  style={{
                    width: '100%',
                    // alignItems: 'center',
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      paddingVertical: 2,
                      borderRadius: 3,
                      backgroundColor: Colors.secondaryColor,
                      width: '80%',
                    }}>
                    <Text
                      style={[
                        {
                          color: Colors.whiteColor,
                          fontSize: Platform.OS == 'ios' ? 14 : 16,
                          //fontSize: Platform.OS == 'android' ? 14 : 16,
                          fontFamily: 'NunitoSans-Regular',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {(item.paymentDetails && item.paymentDetails.channel)
                        ? PAYMENT_CHANNEL_NAME_PARSED[
                          item.paymentDetails.channel
                        ]
                          ? PAYMENT_CHANNEL_NAME_PARSED[
                          item.paymentDetails.channel
                          ]
                          : item.paymentDetails.channel
                            ? item.paymentDetails.channel
                            : 'N/A'
                        : 'N/A'}
                    </Text>
                  </View>
                </View>
              </View>

              <View
                style={{
                  marginHorizontal: 1,
                  width: '14%',
                  alignItems: 'flex-start',
                  justifyContent: 'space-between',
                  flexDirection: 'row',
                }}>
                <Text
                  style={{
                    fontSize: switchMerchant ? 10 : 16,
                    color: 'black',
                  }}>
                  RM
                </Text>
                <Text
                  style={[
                    Platform.OS === 'android' ? { position: 'relative' } : {},
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : { paddingRight: 25, fontSize: 16 },
                  ]}>
                  {(Math.round(item.finalPrice * 20) / 20)
                    .toFixed(2)
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                </Text>
              </View>
            </View>

            {/* <View
                style={{
                  position: 'absolute',
                  top: Platform.OS == 'ios' ? 3 : 23,
                  right: Platform.OS == 'ios' ? 3 : 10,
                  //top: Platform.OS == 'android' ? 3 : 23, right: Platform.OS == 'android' ? 3 : 10,
                  alignItems: 'center',
                }}> */}
            {/* <TouchableOpacity onPress={() => { expandOrderFunc(item) }}> */}
            {/* <Icon
                  name="chevron-left"
                  size={switchMerchant ? 10 : 30}
                  color={Colors.tabGrey}
                  style={{
                    top: Platform.OS == 'ios' ? 0 : -20,
                    right: Platform.OS == 'ios' ? 0 : -5,
                  }}
                /> */}
            {/* </TouchableOpacity> */}
            {/* </View> */}

            {/* <TouchableOpacity style={{ position: 'absolute', top: Platform.OS == 'ios' ? 3 : 10, right: Platform.OS == 'ios' ? 3 : 10 }} onPress={() => { expandOrderFunc(item) }}>
              <Icon name={expandViewDict[item.uniqueId] == true ? "chevron-up" : "chevron-down"} size={30} color={Colors.tabGrey} />
            </TouchableOpacity> */}

            {expandedOrderId === item.uniqueId ? (
              <View
                style={{
                  minheight: windowHeight * 0.4,
                  paddingTop: '1%',
                  paddingBottom: 20,
                }}>
                {item.cartItems.map((cartItem, index) => {
                  const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItem);

                  return (
                    (<View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        //paddingHorizontal: 10,
                        //backgroundColor: 'blue'
                      }}>
                      <View
                        style={{
                          width: '100%',
                          alignItems: 'flex-start',
                          flexDirection: 'row',
                          //marginTop: '0.5%',
                          //marginVertical: '0.6%',
                          marginBottom: Platform.OS == 'ios' ? 10 : 10,
                          minHeight: 80,
                          //smarginTop: Platform.OS == 'android' ? 10 : 0
                        }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            width: '100%',
                          }}>
                          {index == 0 ? (
                            <View
                              style={{
                                // flex: 0.75,
                                marginHorizontal: 1,
                                width: Platform.OS == 'ios' ? '8%' : '8%',
                                alignItems: 'center',
                                //right: Platform.OS == 'ios' ? 10 : 0,
                                //width: Platform.OS == 'android' ? '8%' : '5%',
                                //justifyContent: 'center',
                                //alignItems: 'center',
                                //paddingRight: '2%',
                                //right: Platform.OS == 'ios' ? 10 : 10,
                                //right: Platform.OS == 'android' ? 10 : 0,
                                //paddingLeft: '2%',
                                //backgroundColor: 'blue',
                                // borderWidth: 1
                              }}>
                              <TouchableOpacity
                                style={{
                                  alignItems: 'center',
                                  // borderWidth:1
                                  // marginTop: '-10%',
                                }}
                                onPress={() => {
                                  var crmUser = null;

                                  if (item.crmUserId !== undefined) {
                                    for (
                                      var i = 0;
                                      i < crmUsers.length;
                                      i++
                                    ) {
                                      if (
                                        item.crmUserId ===
                                        crmUsers[i].uniqueId
                                      ) {
                                        crmUser = crmUsers[i];
                                        break;
                                      }
                                    }
                                  }

                                  if (!crmUser) {
                                    for (
                                      var i = 0;
                                      i < crmUsers.length;
                                      i++
                                    ) {
                                      if (
                                        item.userId ===
                                        crmUsers[i].firebaseUid
                                      ) {
                                        crmUser = crmUsers[i];
                                        break;
                                      }
                                    }
                                  }

                                  if (crmUser) {
                                    CommonStore.update(
                                      (s) => {
                                        s.selectedCustomerEdit = crmUser;
                                        // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                        s.routeParams = {
                                          pageFrom: 'Reservation',
                                        };
                                      },
                                      () => {
                                        navigation.navigate('NewCustomer');
                                      },
                                    );
                                  }
                                }}>
                                <View
                                  style={{
                                    position: 'relative',
                                    //left: Platform.OS === 'ios'? 15: 0,
                                    // borderWidth: 1,
                                    // alignSelf: 'flex-start'
                                  }}>
                                  <Image
                                    style={{
                                      width: switchMerchant ? 30 : 60,
                                      height: switchMerchant ? 30 : 60,
                                    }}
                                    resizeMode="contain"
                                    source={require('../assets/image/profile-pic.jpg')}
                                  />
                                </View>

                                <View
                                  style={{
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    position: 'relative',
                                    //left: Platform.OS === 'ios'? 15: 0
                                  }}>
                                  <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-Bold',
                                        marginTop: 0,
                                        fontSize: 16,
                                        textAlign: 'center',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}
                                    numberOfLines={1}>
                                    {item.userName ? item.userName : 'Guest'}
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                          ) : (
                            <View
                              style={{
                                // flex: 0.75,
                                marginHorizontal: 1,
                                width: Platform.OS == 'ios' ? '8%' : '8%',
                                //width: Platform.OS == 'android' ? '8%' : '5%',
                                justifyContent: 'center',
                                alignItems: 'center',
                                //right: Platform.OS == 'ios' ? 10 : 10,
                                // paddingRight: '2%',
                                // marginTop: '-10%',
                                //backgroundColor: 'blue'
                              }}
                            />
                          )}

                          <View
                            style={{
                              // flex: 0.3,
                              width: '6%',
                              //justifyContent: 'center',
                              alignItems: 'center',
                              // backgroundColor: 'red',
                              //paddingLeft: '1.2%',
                              //right: 10
                              //marginLeft: 10
                            }}>
                            <Text
                              style={[
                                {
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 16,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}>
                              {index + 1}.
                            </Text>
                          </View>

                          <View
                            style={{
                              width: '4%',
                              // height: '100%',
                              //marginRight: 5,
                              paddingLeft: 8,
                              bottom: 4,
                            }}>
                            <CheckBox
                              style={{
                                ...(Platform.OS === 'ios' && {
                                  width: !isTablet() ? 10 : 16,
                                  height: !isTablet() ? 10 : 16,
                                }),
                              }}
                              value={
                                selectedCartItemDict[
                                cartItem.itemId +
                                cartItem.cartItemDate.toString()
                                ] !== false &&
                                selectedCartItemDict[
                                cartItem.itemId +
                                cartItem.cartItemDate.toString()
                                ] !== undefined
                              }
                              onValueChange={(value) => {
                                if (
                                  selectedCartItemDict[
                                  cartItem.itemId +
                                  cartItem.cartItemDate.toString()
                                  ]
                                ) {
                                  // Remove the item from the dictionary when deselected
                                  const newSelectedCartItemDict = { ...selectedCartItemDict };
                                  delete newSelectedCartItemDict[cartItem.itemId + cartItem.cartItemDate.toString()];
                                  setSelectedCartItemDict(newSelectedCartItemDict);
                                } else {
                                  setSelectedCartItemDict({
                                    ...selectedCartItemDict,
                                    [cartItem.itemId + cartItem.cartItemDate.toString()]: {
                                      itemId: cartItem.itemId,
                                      cartItemDate: cartItem.cartItemDate,

                                      actionQuantity: cartItem.quantity,
                                    },
                                  });
                                }
                              }}
                            />
                          </View>

                          <View>
                            {/*start here*/}

                            <View

                              style={{
                                flexDirection: 'row',
                              }}

                            >
                              <View
                                style={{
                                  width: '10%',
                                  //flex: 0.5,
                                  alignItems: 'center',
                                }}>
                                {cartItem.image ? (
                                  <AsyncImage
                                    source={{ uri: cartItem.image }}
                                    // item={cartItem}
                                    style={{
                                      width: switchMerchant ? 30 : 60,
                                      height: switchMerchant ? 30 : 60,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                      borderRadius: 5,
                                    }}
                                  />
                                ) : (
                                  <View
                                    style={{
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      width: switchMerchant ? 30 : 60,
                                      height: switchMerchant ? 30 : 60,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                      borderRadius: 5,
                                    }}>
                                    <Ionicons
                                      name="cart-outline"
                                      size={switchMerchant ? 15 : 35}
                                    />
                                  </View>
                                )}
                              </View>
                              <View
                                style={{
                                  width: '68.5%',
                                  //width: '53%',
                                  marginLeft: 6,
                                  // backgroundColor: 'blue',

                                }}>
                                <View
                                  style={{
                                    alignItems: 'center',
                                    width: '100%',
                                    flexDirection: 'row',
                                    //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                    //marginLeft: Platform.OS == 'android' ? 30 : 0,
                                    marginBottom: 10,
                                    //paddingLeft: '2%',
                                    //backgroundColor: 'blue'

                                  }}>
                                  <View style={{
                                    width: '86.4%',
                                    // backgroundColor: Colors.tabCyan,

                                  }}>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: 16,
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : {},
                                      ]}>
                                      {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ''}
                                    </Text>
                                  </View>
                                  <View
                                    style={{
                                      width: '13%',
                                    }}>
                                    <View
                                      style={{
                                        alignItems: 'center',
                                      }}>
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 16,
                                            //marginRight: 50
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        x{cartItem.quantity}
                                      </Text>
                                    </View>
                                  </View>
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      justifyContent: 'space-between',
                                      width: '25.5%',
                                      marginLeft: '5%',
                                    }}>
                                    <Text
                                      style={
                                        switchMerchant
                                          ? {
                                            fontSize: 10, fontWeight: "bold",
                                          }
                                          : { fontSize: 16, fontWeight: "bold", }
                                      }>
                                      RM
                                    </Text>
                                    <Text
                                      style={
                                        switchMerchant
                                          ? {
                                            fontSize: 10, fontWeight: "bold",
                                          }
                                          : { fontSize: 16, paddingRight: 25, fontWeight: "bold", }
                                      }>
                                      {cartItemPriceWIthoutAddOn
                                        .toFixed(2)
                                        .replace(
                                          /(\d)(?=(\d{3})+(?!\d))/g,
                                          '$1,',
                                        )}
                                    </Text>
                                  </View>
                                </View>
                                {/* <View
                            style={{
                              //backgroundColor: 'purple',
                              // flex: 1.5,
                              width: '21%',
                              flexDirection: 'row',
                            }}> */}
                                {/* <View
                              style={{
                                // flex: 0.5,
                                width: '36%',
                                justifyContent: 'center',
                                alignItems: 'center',
                                //backgroundColor: 'yellow',
                                paddingRight: '25%',
                              }}> */}
                                {/* <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>x{orderItems.quantity}</Text> */}
                                {/* <Text
                                style={[{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 16,
                                  //marginRight: 50
                                },
                                switchMerchant
                                  ? {
                                      fontSize: 8,
                                    }
                                  : {},
                              ]}>
                                x{cartItem.quantity}
                              </Text> */}
                                {/* </View> */}
                                {/* <View
                              style={[{
                                width: '74%',
                                justifyContent: 'center',
                                alignItems: 'flex-start',
                                paddingLeft: Platform.OS == 'ios' ? 0 : '1%',
                                //marginLeft: -10,
                                // marginRight: 23,
                                // backgroundColor: 'green',
                                //left: Platform.OS === 'ios'? 0: -8,
                              }, switchMerchant?{left: windowWidth * 0.01}:{}]}>
                              <Text
                                style={[{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 16,
                                  fontVariant: ['tabular-nums'],
                                },
                                switchMerchant
                                  ? {
                                      fontSize: 8,
                                    }
                                  : {},
                              ]}>
                                <Text>RM</Text>
                                <Text
                                  style={{
                                    opacity: 0,
                                    ...(Platform.OS === 'android' && {
                                      color: 'white',
                                    }),
                                  }}>
                                  {'0'.repeat(
                                    cartItemPriceWIthoutAddOnSpacingList[index],
                                  )}
                                </Text>
                                <Text>
                                  {cartItemPriceWIthoutAddOn
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                              </Text>
                            </View> */}
                                {/* </View> */}

                                {cartItem.remarks &&
                                  cartItem.remarks.length > 0 ? (
                                  <View
                                    style={{
                                      alignItems: 'center',
                                      flexDirection: 'row',

                                      //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                    }}>
                                    <View
                                      style={{
                                        justifyContent: 'center',
                                        //backgroundColor: 'red',
                                      }}>
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-SemiBold',
                                            fontSize: 16,
                                            //height: 20,
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        {cartItem.remarks}
                                      </Text>
                                    </View>
                                  </View>
                                ) : (
                                  <></>
                                )}

                                {cartItem.addOns.map((addOnChoice, i) => {
                                  return (
                                    <View
                                      style={{
                                        flexDirection: 'row',
                                        // marginLeft: -5,
                                        width: '100%',
                                      }}>
                                      {/* <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.fieldtTxtColor }}>Add Ons: RM {addOnChoice.prices.reduce((accum, price) => accum + price, 0)}</Text> */}

                                      <View
                                        style={{
                                          width: '86.4%',
                                          flexDirection: 'row',
                                          // marginLeft:
                                          //   Platform.OS == 'ios' ? 14 : 14,
                                          // backgroundColor: 'red',
                                        }}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-Bold',
                                              fontSize: 16,
                                              color: Colors.fieldtTxtColor,
                                              width: '55%',

                                              // marginLeft: 5,
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}>
                                          {`${addOnChoice.name}:`}
                                        </Text>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-Bold',
                                              fontSize: 16,
                                              color: Colors.fieldtTxtColor,
                                              width: '75%',
                                              // marginLeft: 5,
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}>
                                          {`${addOnChoice.choiceNames[0]}`}
                                        </Text>
                                      </View>

                                      <View
                                        style={{
                                          width: '13%',
                                          flexDirection: 'row',
                                          justifyContent: 'center',
                                          // backgroundColor: 'blue',
                                          //right: '1%'
                                        }}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-Bold',
                                              fontSize: 16,
                                              color: Colors.fieldtTxtColor,
                                              width: '28%',
                                              // right: 38,
                                              //backgroundColor: 'green',
                                              textAlign: 'center',
                                              // alignSelf: 'center',
                                              // paddingRight:
                                              //   Platform.OS == 'ios'
                                              //     ? '20%'
                                              //     : '19%',
                                              //marginRight: 50
                                              //right: '100%'
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                                paddingLeft: '5%',
                                                textAlign: 'left',
                                              }
                                              : {},
                                            !switchMerchant &&
                                              Platform.OS === 'android'
                                              ? {}
                                              : {},
                                          ]}>
                                          {`${addOnChoice.quantities
                                            ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                            : ''
                                            }`}
                                        </Text>
                                      </View>
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                          justifyContent: 'space-between',
                                          width: '25.5%',
                                          marginLeft: '5%',
                                          alignItems: 'center',
                                        }}>
                                        {/* <View style={[switchMerchant?{left: windowWidth * 0.016, width: '80%'}:{}]}> */}
                                        <Text
                                          style={[
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {
                                                color: Colors.descriptionColor,
                                                fontSize: 16,
                                              },
                                          ]}>
                                          RM
                                        </Text>
                                        <Text
                                          style={
                                            switchMerchant
                                              ? { fontSize: 10 }
                                              : {
                                                color: Colors.descriptionColor,
                                                paddingRight: 25,
                                                fontSize: 16,
                                              }
                                          }>
                                          {(getAddOnChoicePrice(addOnChoice, cartItem))
                                            .toFixed(2)
                                            .replace(
                                              /(\d)(?=(\d{3})+(?!\d))/g,
                                              '$1,',
                                            )}
                                        </Text>
                                      </View>
                                    </View>
                                  );
                                })}


                              </View>
                            </View>

                            {/*ingredients area*/}
                            {/* {cartItem.addOns.map((addOnChoice, i) => {
                              const addOnChoices = addOnChoice.choiceNames.join(", ");
                              return (
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    // marginLeft: -5,
                                    width: '100%',
                                    // backgroundColor: Colors.blackColor,
                                    alignItems: 'center',
                                    marginVertical: 5,
                                  }}>
                                  {/* <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.fieldtTxtColor }}>Add Ons: RM {addOnChoice.prices.reduce((accum, price) => accum + price, 0)}</Text> *

                                  <View
                                    style={{
                                      width: '10%',
                                      //flex: 0.5,
                                      alignItems: 'center',


                                    }}>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: 16,
                                          color: Colors.fieldtTxtColor,
                                          width: '25%',
                                          // marginLeft: 5,
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : {},
                                      ]}>
                                      {`${addOnChoice.name}:`}
                                    </Text>
                                    <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: 16,
                                          color: Colors.fieldtTxtColor,
                                          width: '75%',
                                          // marginLeft: 5,
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : {},
                                      ]}>
                                      {/* {`${addOnChoices}`} *
                                    </Text>
                                  </View>


                                  <View
                                    style={{
                                      alignItems: 'center',
                                      width: '68.5%',
                                      flexDirection: 'row',
                                      //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                      //marginLeft: Platform.OS == 'android' ? 30 : 0,
                                      marginBottom: 10,
                                      //paddingLeft: '2%',


                                    }}>
                                    <View
                                      style={{
                                        width: '87.4%',
                                        flexDirection: 'column',
                                        // marginLeft:
                                        //   Platform.OS == 'ios' ? 14 : 14,
                                        // backgroundColor: 'red',
                                      }}>
                                      {/* <Text
                                      style={[
                                        {
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: 16,
                                          color: Colors.fieldtTxtColor,
                                          width: '25%',
                                          // marginLeft: 5,
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : {},
                                      ]}>
                                      {`${addOnChoice.name}:`}
                                    </Text> *
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 16,
                                            color: Colors.fieldtTxtColor,
                                            width: '75%',
                                            // marginLeft: 5,
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        {`${addOnChoice.choiceNames[0]}`}
                                      </Text>

                                      <Text>Ingredient</Text>

                                      <Text

                                        style={[

                                          // { fontSize: 10 }, 
                                          ingreStat === 1 && { color: 'green' },   // If ingreStat is 1, make the color green
                                          ingreStat === 2 && { color: 'yellow' },  // If ingreStat is 2, make the color yellow
                                          ingreStat === 3 && { color: 'red' },


                                        ]}

                                      >
                                        *Status

                                      </Text>
                                    </View>

                                    <View
                                      style={{
                                        width: '13%',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        // backgroundColor: 'blue',
                                        //right: '1%'
                                      }}>
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 16,
                                            color: Colors.fieldtTxtColor,
                                            width: '28%',
                                            // right: 38,
                                            //backgroundColor: 'green',
                                            textAlign: 'center',
                                            // alignSelf: 'center',
                                            // paddingRight:
                                            //   Platform.OS == 'ios'
                                            //     ? '20%'
                                            //     : '19%',
                                            //marginRight: 50
                                            //right: '100%'
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                              paddingLeft: '5%',
                                              textAlign: 'left',
                                            }
                                            : {},
                                          !switchMerchant &&
                                            Platform.OS === 'android'
                                            ? {}
                                            : {},
                                        ]}>
                                        {`${addOnChoice.quantities
                                          ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                          : ''
                                          }`}
                                      </Text>
                                    </View>
                                    <View
                                      style={{
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        width: '25.5%',
                                        marginLeft: '5%',
                                        alignItems: 'center',
                                      }}>
                                      {/* <View style={[switchMerchant?{left: windowWidth * 0.016, width: '80%'}:{}]}> *
                                      <Text
                                        style={[
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {
                                              color: Colors.descriptionColor,
                                              fontSize: 16,
                                            },
                                        ]}>
                                        RM
                                      </Text>
                                      <Text
                                        style={
                                          switchMerchant
                                            ? { fontSize: 10 }
                                            : {
                                              color: Colors.descriptionColor,
                                              paddingRight: 25,
                                              fontSize: 16,
                                            }
                                        }>
                                        {(getAddOnChoicePrice(addOnChoice, cartItem))
                                          .toFixed(2)
                                          .replace(
                                            /(\d)(?=(\d{3})+(?!\d))/g,
                                            '$1,',
                                          )}
                                      </Text>
                                    </View>
                                  </View>
                                </View>
                              );
                            })} */}
                          </View>
                        </View>
                      </View>
                      {((index === item.cartItems.length - 1) && item.cartItemsCancelled) ?
                        <>
                          {item.cartItemsCancelled.map((cartItemCancelled, index) => {
                            const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItemCancelled);

                            return (
                              (<View
                                style={{
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}>
                                <View
                                  style={{
                                    width: '100%',
                                    alignItems: 'flex-start',
                                    flexDirection: 'row',
                                    //marginTop: '0.5%',
                                    //marginVertical: '0.6%',
                                    marginBottom: Platform.OS == 'ios' ? 10 : 10,
                                    minHeight: 80,
                                    //smarginTop: Platform.OS == 'android' ? 10 : 0
                                  }}>
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      width: '100%',
                                    }}>
                                    {index == 0 ? (
                                      <View
                                        style={{
                                          // flex: 0.75,
                                          marginHorizontal: 1,
                                          width: Platform.OS == 'ios' ? '8%' : '8%',
                                          alignItems: 'center',
                                          //right: Platform.OS == 'ios' ? 10 : 0,
                                          //width: Platform.OS == 'android' ? '8%' : '5%',
                                          //justifyContent: 'center',
                                          //alignItems: 'center',
                                          //paddingRight: '2%',
                                          //right: Platform.OS == 'ios' ? 10 : 10,
                                          //right: Platform.OS == 'android' ? 10 : 0,
                                          //paddingLeft: '2%',
                                          //backgroundColor: 'blue',
                                          // borderWidth: 1
                                        }}>
                                        {/* <TouchableOpacity
                                          style={{
                                            alignItems: 'center',
                                            // borderWidth:1
                                            // marginTop: '-10%',
                                          }}
                                          onPress={() => {
                                            var crmUser = null;

                                            if (item.crmUserId !== undefined) {
                                              for (
                                                var i = 0;
                                                i < crmUsers.length;
                                                i++
                                              ) {
                                                if (
                                                  item.crmUserId ===
                                                  crmUsers[i].uniqueId
                                                ) {
                                                  crmUser = crmUsers[i];
                                                  break;
                                                }
                                              }
                                            }

                                            if (!crmUser) {
                                              for (
                                                var i = 0;
                                                i < crmUsers.length;
                                                i++
                                              ) {
                                                if (
                                                  item.userId ===
                                                  crmUsers[i].firebaseUid
                                                ) {
                                                  crmUser = crmUsers[i];
                                                  break;
                                                }
                                              }
                                            }

                                            if (crmUser) {
                                              CommonStore.update(
                                                (s) => {
                                                  s.selectedCustomerEdit = crmUser;
                                                  // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                                  s.routeParams = {
                                                    pageFrom: 'Reservation',
                                                  };
                                                },
                                                () => {
                                                  navigation.navigate('NewCustomer');
                                                },
                                              );
                                            }
                                          }}>
                                          <View
                                            style={{
                                              position: 'relative',
                                              //left: Platform.OS === 'ios'? 15: 0,
                                              // borderWidth: 1,
                                              // alignSelf: 'flex-start'
                                            }}>
                                            <Image
                                              style={{
                                                width: switchMerchant ? 30 : 60,
                                                height: switchMerchant ? 30 : 60,
                                              }}
                                              resizeMode="contain"
                                              source={require('../assets/image/profile-pic.jpg')}
                                            />
                                          </View>

                                          <View
                                            style={{
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              position: 'relative',
                                              //left: Platform.OS === 'ios'? 15: 0
                                            }}>
                                            <Text
                                              style={[
                                                {
                                                  fontFamily: 'NunitoSans-Bold',
                                                  marginTop: 0,
                                                  fontSize: 16,
                                                  textAlign: 'center',
                                                },
                                                switchMerchant
                                                  ? {
                                                    fontSize: 10,
                                                  }
                                                  : {},
                                              ]}
                                              numberOfLines={1}>
                                              {item.userName ? item.userName : 'Guest'}
                                            </Text>
                                          </View>
                                        </TouchableOpacity> */}
                                      </View>
                                    ) : (
                                      <View
                                        style={{
                                          // flex: 0.75,
                                          marginHorizontal: 1,
                                          width: Platform.OS == 'ios' ? '8%' : '8%',
                                          //width: Platform.OS == 'android' ? '8%' : '5%',
                                          justifyContent: 'center',
                                          alignItems: 'center',
                                          //right: Platform.OS == 'ios' ? 10 : 10,
                                          // paddingRight: '2%',
                                          // marginTop: '-10%',
                                          //backgroundColor: 'blue'
                                        }}
                                      />
                                    )}

                                    <View
                                      style={{
                                        // flex: 0.3,
                                        width: '6%',
                                        //justifyContent: 'center',
                                        alignItems: 'center',
                                        // backgroundColor: 'red',
                                        //paddingLeft: '1.2%',
                                        //right: 10
                                        //marginLeft: 10
                                      }}>
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 16,
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        {/* {index + 1}. */}
                                      </Text>
                                    </View>

                                    <View
                                      style={{
                                        width: '4%',
                                        // height: '100%',
                                        //marginRight: 5,
                                        paddingLeft: 8,
                                        bottom: 4,
                                      }}>
                                      {/* <CheckBox
                                        style={{
                                          ...(Platform.OS === 'ios' && {
                                            width: !isTablet() ? 10 : 16,
                                            height: !isTablet() ? 10 : 16,
                                          }),
                                        }}
                                        value={
                                          selectedCartItemDict[
                                          cartItem.itemId +
                                          cartItem.cartItemDate.toString()
                                          ] !== false &&
                                          selectedCartItemDict[
                                          cartItem.itemId +
                                          cartItem.cartItemDate.toString()
                                          ] !== undefined
                                        }
                                        onValueChange={(value) => {
                                          if (
                                            selectedCartItemDict[
                                            cartItem.itemId +
                                            cartItem.cartItemDate.toString()
                                            ]
                                          ) {
                                            // Remove the item from the dictionary when deselected
                                            const newSelectedCartItemDict = { ...selectedCartItemDict };
                                            delete newSelectedCartItemDict[cartItem.itemId + cartItem.cartItemDate.toString()];
                                            setSelectedCartItemDict(newSelectedCartItemDict);
                                          } else {
                                            setSelectedCartItemDict({
                                              ...selectedCartItemDict,
                                              [cartItem.itemId + cartItem.cartItemDate.toString()]: {
                                                itemId: cartItem.itemId,
                                                cartItemDate: cartItem.cartItemDate,

                                                actionQuantity: cartItem.quantity,
                                              },
                                            });
                                          }
                                        }}
                                      /> */}
                                    </View>

                                    <View>
                                      {/*start here*/}

                                      <View

                                        style={{
                                          flexDirection: 'row',
                                        }}

                                      >
                                        <View
                                          style={{
                                            width: '10%',
                                            //flex: 0.5,
                                            alignItems: 'center',
                                          }}>
                                          {cartItemCancelled.image ? (
                                            <AsyncImage
                                              source={{ uri: cartItemCancelled.image }}
                                              // item={cartItem}
                                              style={{
                                                width: switchMerchant ? 30 : 60,
                                                height: switchMerchant ? 30 : 60,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                                borderRadius: 5,
                                              }}
                                            />
                                          ) : (
                                            <View
                                              style={{
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                width: switchMerchant ? 30 : 60,
                                                height: switchMerchant ? 30 : 60,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                                borderRadius: 5,
                                              }}>
                                              <Ionicons
                                                name="cart-outline"
                                                size={switchMerchant ? 15 : 35}
                                              />
                                            </View>
                                          )}
                                        </View>
                                        <View
                                          style={{
                                            width: '68.5%',
                                            //width: '53%',
                                            marginLeft: 6,
                                            // backgroundColor: 'blue',

                                          }}>
                                          <View
                                            style={{
                                              alignItems: 'center',
                                              width: '100%',
                                              flexDirection: 'row',
                                              //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                              //marginLeft: Platform.OS == 'android' ? 30 : 0,
                                              marginBottom: 10,
                                              //paddingLeft: '2%',
                                              //backgroundColor: 'blue'

                                            }}>
                                            <View style={{
                                              width: '86.4%',
                                              // backgroundColor: Colors.tabCyan,

                                            }}>
                                              <Text
                                                style={[
                                                  {
                                                    fontFamily: 'NunitoSans-Bold',
                                                    fontSize: 16,
                                                    color: 'red',
                                                    textDecorationLine: 'line-through',
                                                  },
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10,
                                                    }
                                                    : {},
                                                ]}>
                                                {cartItemCancelled.name}{cartItemCancelled.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItemCancelled.unitType]})` : ''}
                                              </Text>
                                            </View>
                                            <View
                                              style={{
                                                width: '13%',
                                              }}>
                                              <View
                                                style={{
                                                  alignItems: 'center',
                                                }}>
                                                <Text
                                                  style={[
                                                    {
                                                      fontFamily: 'NunitoSans-Bold',
                                                      fontSize: 16,
                                                      //marginRight: 50
                                                      color: 'red',
                                                      textDecorationLine: 'line-through',
                                                    },
                                                    switchMerchant
                                                      ? {
                                                        fontSize: 10,
                                                      }
                                                      : {},
                                                  ]}>
                                                  x{cartItemCancelled.quantity}
                                                </Text>
                                              </View>
                                            </View>
                                            <View
                                              style={{
                                                flexDirection: 'row',
                                                justifyContent: 'space-between',
                                                width: '25.5%',
                                                marginLeft: '5%',
                                              }}>
                                              {/* <Text
                                                style={
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10, fontWeight: "bold",
                                                    }
                                                    : { fontSize: 16, fontWeight: "bold", }
                                                }>
                                                RM
                                              </Text>
                                              <Text
                                                style={
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10, fontWeight: "bold",
                                                    }
                                                    : { fontSize: 16, paddingRight: 25, fontWeight: "bold", }
                                                }>
                                                {cartItemPriceWIthoutAddOn
                                                  .toFixed(2)
                                                  .replace(
                                                    /(\d)(?=(\d{3})+(?!\d))/g,
                                                    '$1,',
                                                  )}
                                              </Text> */}
                                            </View>
                                          </View>
                                          {/* <View
                            style={{
                              //backgroundColor: 'purple',
                              // flex: 1.5,
                              width: '21%',
                              flexDirection: 'row',
                            }}> */}
                                          {/* <View
                              style={{
                                // flex: 0.5,
                                width: '36%',
                                justifyContent: 'center',
                                alignItems: 'center',
                                //backgroundColor: 'yellow',
                                paddingRight: '25%',
                              }}> */}
                                          {/* <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>x{orderItems.quantity}</Text> */}
                                          {/* <Text
                                style={[{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 16,
                                  //marginRight: 50
                                },
                                switchMerchant
                                  ? {
                                      fontSize: 8,
                                    }
                                  : {},
                              ]}>
                                x{cartItem.quantity}
                              </Text> */}
                                          {/* </View> */}
                                          {/* <View
                              style={[{
                                width: '74%',
                                justifyContent: 'center',
                                alignItems: 'flex-start',
                                paddingLeft: Platform.OS == 'ios' ? 0 : '1%',
                                //marginLeft: -10,
                                // marginRight: 23,
                                // backgroundColor: 'green',
                                //left: Platform.OS === 'ios'? 0: -8,
                              }, switchMerchant?{left: windowWidth * 0.01}:{}]}>
                              <Text
                                style={[{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 16,
                                  fontVariant: ['tabular-nums'],
                                },
                                switchMerchant
                                  ? {
                                      fontSize: 8,
                                    }
                                  : {},
                              ]}>
                                <Text>RM</Text>
                                <Text
                                  style={{
                                    opacity: 0,
                                    ...(Platform.OS === 'android' && {
                                      color: 'white',
                                    }),
                                  }}>
                                  {'0'.repeat(
                                    cartItemPriceWIthoutAddOnSpacingList[index],
                                  )}
                                </Text>
                                <Text>
                                  {cartItemPriceWIthoutAddOn
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                              </Text>
                            </View> */}
                                          {/* </View> */}

                                          {cartItemCancelled.remarks &&
                                            cartItemCancelled.remarks.length > 0 ? (
                                            <View
                                              style={{
                                                alignItems: 'center',
                                                flexDirection: 'row',

                                                //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                              }}>
                                              <View
                                                style={{
                                                  justifyContent: 'center',
                                                  //backgroundColor: 'red',
                                                }}>
                                                <Text
                                                  style={[
                                                    {
                                                      fontFamily: 'NunitoSans-SemiBold',
                                                      fontSize: 16,
                                                      //height: 20,
                                                      color: 'red',
                                                      textDecorationLine: 'line-through',
                                                    },
                                                    switchMerchant
                                                      ? {
                                                        fontSize: 10,
                                                      }
                                                      : {},
                                                  ]}>
                                                  {cartItemCancelled.remarks}
                                                </Text>
                                              </View>
                                            </View>
                                          ) : (
                                            <></>
                                          )}

                                          {cartItemCancelled.rejectRemarks &&
                                            cartItemCancelled.rejectRemarks.length > 0 ? (
                                            <View
                                              style={{
                                                alignItems: 'center',
                                                flexDirection: 'row',

                                                //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                              }}>
                                              <View
                                                style={{
                                                  justifyContent: 'center',
                                                  //backgroundColor: 'red',
                                                }}>
                                                <Text
                                                  style={[
                                                    {
                                                      fontFamily: 'NunitoSans-SemiBold',
                                                      fontSize: 16,
                                                      //height: 20,
                                                      color: 'red',
                                                      textDecorationLine: 'line-through',
                                                    },
                                                    switchMerchant
                                                      ? {
                                                        fontSize: 10,
                                                      }
                                                      : {},
                                                  ]}>
                                                  {`(${cartItemCancelled.rejectRemarks})`}
                                                </Text>
                                              </View>
                                            </View>
                                          ) : (
                                            <></>
                                          )}

                                          {cartItemCancelled.addOns.map((addOnChoice, i) => {
                                            return (
                                              <View
                                                style={{
                                                  flexDirection: 'row',
                                                  // marginLeft: -5,
                                                  width: '100%',
                                                }}>
                                                {/* <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.fieldtTxtColor }}>Add Ons: RM {addOnChoice.prices.reduce((accum, price) => accum + price, 0)}</Text> */}

                                                <View
                                                  style={{
                                                    width: '86.4%',
                                                    flexDirection: 'row',
                                                    // marginLeft:
                                                    //   Platform.OS == 'ios' ? 14 : 14,
                                                    // backgroundColor: 'red',
                                                  }}>
                                                  <Text
                                                    style={[
                                                      {
                                                        fontFamily: 'NunitoSans-Bold',
                                                        fontSize: 16,
                                                        color: Colors.fieldtTxtColor,
                                                        width: '55%',
                                                        color: 'red',
                                                        textDecorationLine: 'line-through',
                                                        // marginLeft: 5,
                                                      },
                                                      switchMerchant
                                                        ? {
                                                          fontSize: 10,
                                                        }
                                                        : {},
                                                    ]}>
                                                    {`${addOnChoice.name}:`}
                                                  </Text>
                                                  <Text
                                                    style={[
                                                      {
                                                        fontFamily: 'NunitoSans-Bold',
                                                        fontSize: 16,
                                                        color: Colors.fieldtTxtColor,
                                                        width: '75%',
                                                        // marginLeft: 5,
                                                        color: 'red',
                                                        textDecorationLine: 'line-through',
                                                      },
                                                      switchMerchant
                                                        ? {
                                                          fontSize: 10,
                                                        }
                                                        : {},
                                                    ]}>
                                                    {`${addOnChoice.choiceNames[0]}`}
                                                  </Text>
                                                </View>

                                                <View
                                                  style={{
                                                    width: '13%',
                                                    flexDirection: 'row',
                                                    justifyContent: 'center',
                                                    // backgroundColor: 'blue',
                                                    //right: '1%'
                                                  }}>
                                                  <Text
                                                    style={[
                                                      {
                                                        fontFamily: 'NunitoSans-Bold',
                                                        fontSize: 16,
                                                        color: Colors.fieldtTxtColor,
                                                        width: '28%',
                                                        // right: 38,
                                                        //backgroundColor: 'green',
                                                        textAlign: 'center',
                                                        // alignSelf: 'center',
                                                        // paddingRight:
                                                        //   Platform.OS == 'ios'
                                                        //     ? '20%'
                                                        //     : '19%',
                                                        //marginRight: 50
                                                        //right: '100%'
                                                        color: 'red',
                                                        textDecorationLine: 'line-through',
                                                      },
                                                      switchMerchant
                                                        ? {
                                                          fontSize: 10,
                                                          paddingLeft: '5%',
                                                          textAlign: 'left',
                                                        }
                                                        : {},
                                                      !switchMerchant &&
                                                        Platform.OS === 'android'
                                                        ? {}
                                                        : {},
                                                    ]}>
                                                    {`${addOnChoice.quantities
                                                      ? `x${getAddOnChoiceQuantity(addOnChoice, cartItemCancelled)}`
                                                      : ''
                                                      }`}
                                                  </Text>
                                                </View>
                                                <View
                                                  style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'space-between',
                                                    width: '25.5%',
                                                    marginLeft: '5%',
                                                    alignItems: 'center',
                                                  }}>
                                                  {/* <View style={[switchMerchant?{left: windowWidth * 0.016, width: '80%'}:{}]}> */}
                                                  {/* <Text
                                                    style={[
                                                      switchMerchant
                                                        ? {
                                                          fontSize: 10,
                                                        }
                                                        : {
                                                          color: Colors.descriptionColor,
                                                          fontSize: 16,
                                                        },
                                                    ]}>
                                                    RM
                                                  </Text>
                                                  <Text
                                                    style={
                                                      switchMerchant
                                                        ? { fontSize: 10 }
                                                        : {
                                                          color: Colors.descriptionColor,
                                                          paddingRight: 25,
                                                          fontSize: 16,
                                                        }
                                                    }>
                                                    {(getAddOnChoicePrice(addOnChoice, cartItem))
                                                      .toFixed(2)
                                                      .replace(
                                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                                        '$1,',
                                                      )}
                                                  </Text> */}
                                                </View>
                                              </View>
                                            );
                                          })}


                                        </View>
                                      </View>
                                    </View>
                                  </View>
                                </View>
                              </View>)
                            );
                          })}
                        </>
                        :
                        <></>}
                      <View
                        style={{
                          flexDirection: 'row',
                          width: '100%',
                          marginBottom: 0,
                        }}>
                        <View style={{ width: '68.39%' }} >
                          {/* Moved Buttons */}
                          {index === item.cartItems.length - 1 ? (
                            <>
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  alignItems: 'center',
                                  flexDirection: 'row',
                                  height: 100,
                                  marginHorizontal: 10,
                                  marginTop: 20,
                                }}>

                                {(item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                                  item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                                  item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
                                  item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) ? (
                                  <TouchableOpacity
                                    onPress={() => {
                                      // setState({
                                      //   currToPrioritizeOrder: item,
                                      //   visible: true,
                                      // });
                                      setCurrOrderIndex(index);
                                      setCurrToPrioritizeOrder(item);
                                      setVisible(true);
                                    }}
                                    style={[
                                      {
                                        height: '100%',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        backgroundColor: Colors.primaryColor,
                                        underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                        width: 75,
                                        marginLeft: 25,
                                        borderRadius: 10,
                                      },
                                      switchMerchant
                                        ? {
                                          width: windowWidth * 0.08,
                                        }
                                        : {},
                                    ]}>
                                    {switchMerchant ? (
                                      <MaterialCommunityIcons
                                        name="message-alert-outline"
                                        size={10}
                                        color={Colors.whiteColor}
                                        style={{ marginTop: 10 }}
                                      />
                                    ) : (
                                      <MaterialCommunityIcons
                                        name="message-alert-outline"
                                        size={40}
                                        color={Colors.whiteColor}
                                        style={{ marginTop: 10 }}
                                      />
                                    )}
                                    <Text
                                      style={[
                                        {
                                          color: Colors.whiteColor,
                                          fontSize: 12,
                                          fontFamily: 'NunitoSans-Regular',
                                          textAlign: 'center',
                                          width: '80%',
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : {},
                                      ]}>
                                      {`Prioritize\nOrder`}
                                    </Text>
                                  </TouchableOpacity>
                                ) : null}

                                {(
                                  ((item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
                                    item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                                    item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED || item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED) && (item.orderType === ORDER_TYPE.PICKUP))
                                  // &&
                                  // item.paymentDetails === null
                                )
                                  ? (
                                    <TouchableOpacity
                                      onPress={() => {
                                        // setState({
                                        //   currToPrioritizeOrder: item,
                                        //   visible: true,
                                        // });

                                        // setCurrOrderIndex(index);
                                        // setCurrToPrioritizeOrder(item);
                                        // setVisible(true);

                                        // global.viewTableOrderModalPrev = false;

                                        // TableStore.update(s => {
                                        //   s.selectedOrderToPayUserList = [];
                                        //   s.selectedOrderToPayUserIdDict = [];
                                        //   s.viewTableOrderModal = true;
                                        // });

                                        TableStore.update(s => {
                                          s.orderDisplayIndividual = false;
                                          s.orderDisplayProduct = false;
                                          s.orderDisplaySummary = true;

                                          s.viewTableOrderModal = false;
                                          s.renderPaymentSummary = false;
                                          s.renderReceipt = false;

                                          s.displayQrModal = false;
                                          s.displayQModal = false;
                                          s.deleteTableModal = false;
                                          s.updateTableModal = false;
                                          s.joinTableModal = false;
                                          s.moveOrderModal = false;
                                          s.addSectionAreaModel = false;
                                          s.addTableModal = false;
                                          s.preventDeleteTableModal = false;
                                          s.seatingModal = false;
                                          s.showLoyaltyModal = false;
                                          s.showAddLoyaltyModal = false;
                                          s.cashbackModal = false;
                                        });

                                        // CommonStore.update(s => {
                                        //   s.isCheckingOutTakeaway = true;

                                        //   s.checkingOutTakeawayOrder = item;

                                        //   s.timestamp = Date.now();

                                        //   s.checkingOutTakeawayTimestamp = Date.now();
                                        // }, () => {
                                        //   navigation.navigate('Table');
                                        // });

                                        setTimeout(() => {
                                          CommonStore.update(s => {
                                            s.isCheckingOutTakeaway = true;

                                            s.checkingOutTakeawayOrder = item;

                                            s.timestamp = Date.now();

                                            s.checkingOutTakeawayTimestamp = Date.now();

                                            s.currPage = 'Table';
                                          }, () => {
                                            setTimeout(() => {
                                              // takeaway navigate table (2) off
                                              if (global.currOutlet.taNT2Off) {
                                                // do nothing
                                              }
                                              else {
                                                navigation.navigate('Table');
                                              }
                                            }, global.currOutlet.taNT6Timer ? global.currOutlet.taNT6Timer : 100);
                                          });
                                        }, global.currOutlet.taNT5Timer ? global.currOutlet.taNT5Timer : 100);
                                      }}
                                      style={[
                                        {
                                          height: '100%',
                                          justifyContent: 'center',
                                          alignItems: 'center',
                                          backgroundColor: Colors.tabCyan,
                                          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                          width: 75,
                                          marginLeft: 25,
                                          borderRadius: 10,
                                        },
                                        switchMerchant
                                          ? {
                                            width: windowWidth * 0.08,
                                          }
                                          : {},
                                      ]}>
                                      {switchMerchant ? (
                                        <MaterialIcons
                                          name="payment"
                                          size={10}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      ) : (
                                        <MaterialIcons
                                          name="payment"
                                          size={40}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      )}
                                      <Text
                                        style={[
                                          {
                                            color: Colors.whiteColor,
                                            fontSize: 12,
                                            fontFamily: 'NunitoSans-Regular',
                                            textAlign: 'center',
                                            width: '80%',
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        {`Checkout\nOrder`}
                                      </Text>
                                    </TouchableOpacity>
                                  ) : null}

                                {
                                  true ?
                                    <TouchableOpacity
                                      onPress={() => {
                                        // setState({
                                        //   currToPrioritizeOrder: item,
                                        //   visible: true,
                                        // });

                                        global.pinUnlockCallback = async () => {
                                          if (item.paymentDetails !== null /* && (
                                          item.paymentDetails.txn_ID !== undefined ||
                                          item.paymentDetails.txnId !== undefined
                                        ) */
                                            || (item.combinedOrderList && item.combinedOrderList.length > 0)
                                          ) {
                                            Alert.alert('Info', 'Paid order is unable to cancel.');
                                          }
                                          else {
                                            setCurrOrderIndex(index);
                                            setCurrToCancelOrder(item);
                                            setModalCancelVisibility(true);
                                          }
                                        };

                                        if ((currOutlet && currOutlet.privileges &&
                                          currOutlet.privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER))
                                          && privileges && privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER)) {
                                          global.pinUnlockCallback();
                                        }
                                        else {
                                          CommonStore.update(s => {
                                            s.pinUnlockType = PRIVILEGES_NAME.CANCEL_ORDER;
                                            s.showPinUnlockModal = true;
                                          });
                                        }
                                      }}
                                      style={[
                                        {
                                          height: '100%',
                                          justifyContent: 'center',
                                          alignItems: 'center',
                                          // backgroundColor: Colors.primaryColor,
                                          backgroundColor: '#d90000',
                                          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                          width: 75,
                                          marginLeft: 25,
                                          borderRadius: 10,
                                        },
                                        switchMerchant
                                          ? {
                                            width: windowWidth * 0.08,
                                          }
                                          : {},
                                      ]}>
                                      {switchMerchant ? (
                                        <MaterialCommunityIcons
                                          name="close"
                                          size={10}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      ) : (
                                        <MaterialCommunityIcons
                                          name="close"
                                          size={40}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      )}
                                      <Text
                                        style={[
                                          {
                                            color: Colors.whiteColor,
                                            fontSize: 12,
                                            fontFamily: 'NunitoSans-Regular',
                                            textAlign: 'center',
                                            width: '80%',
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        {`Cancel\nOrder`}
                                      </Text>
                                    </TouchableOpacity>
                                    : <></>
                                }

                                {/* 2023-01-30 - For reprint kd */}

                                <TouchableOpacity
                                  onPress={async () => {
                                    ///////////////////////////////////////////////////////////////////////////

                                    Alert.alert('Info', 'Kitchen docket has been added to print queue');

                                    var printTimes = 1;

                                    if (global.outletCategoriesDict) {
                                      if (item.cartItems && item.cartItems.length > 0) {
                                        for (var i = 0; i < item.cartItems.length; i++) {
                                          if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
                                            global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
                                            printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                          }
                                        }
                                      }

                                      if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
                                        for (var i = 0; i < item.cartItemsCancelled.length; i++) {
                                          if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
                                            global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
                                            printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                          }
                                        }
                                      }
                                    }

                                    for (var i = 0; i < printTimes; i++) {
                                      logToFile('takeaway - printUserOrder - KITCHEN_DOCKET');

                                      // Check if there are selected items in selectedCartItemDict
                                      const selectedCartItemKeys = Object.keys(selectedCartItemDict || {});
                                      const hasSelectedItems = selectedCartItemKeys.length > 0;
                                      let printOrderData = item;
                                      if (hasSelectedItems) {
                                        // Only include selected cart items
                                        const selectedCartItems = item.cartItems.filter(cartItem =>
                                          selectedCartItemKeys.includes(cartItem.itemId + cartItem.cartItemDate.toString())
                                        );
                                        printOrderData = {
                                          ...item,
                                          cartItems: selectedCartItems,
                                        };
                                      }

                                      await printUserOrder(
                                        {
                                          orderData: printOrderData,
                                        },
                                        false,
                                        [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                        false,
                                        false,
                                        false,
                                        { isInternetReachable: true, isConnected: true },
                                        false,
                                        [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                      );

                                      printKDSummaryCategoryWrapper(
                                        {
                                          orderData: printOrderData,
                                        },
                                        [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                      );

                                      const printerIpCountDict = await calcPrintTotalForKdIndividual({
                                        userOrder: printOrderData,
                                      });
                                      const printerTaskId = uuidv4();
                                      global.printingTaskIdDict[printerTaskId] = {};

                                      if (printOrderData && printOrderData.cartItems && printOrderData.cartItems.length > 0) {
                                        for (let bdIndex = 0; bdIndex < printOrderData.cartItems.length; bdIndex++) {
                                          if (!printOrderData.cartItems[bdIndex].isDocket) {
                                            await printDocketForKD(
                                              {
                                                userOrder: printOrderData,
                                                cartItem: printOrderData.cartItems[bdIndex],
                                                printerIpCountDict: printerIpCountDict,
                                                printerTaskId: printerTaskId,
                                              },
                                              // true,
                                              [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                              // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                              [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                              // deliveredUser,
                                            );
                                          }
                                        }

                                        for (let index = 0; index < printOrderData.cartItems.length; index++) {
                                          if (printOrderData.cartItems[index].isDocket) {
                                            await printDocket(
                                              {
                                                userOrder: printOrderData,
                                                cartItem: printOrderData.cartItems[index],
                                              },
                                              // true,
                                              [PRINTER_USAGE_TYPE.RECEIPT],
                                              [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                              // deliveredUser,
                                            );
                                          }
                                        }
                                      }
                                    }

                                    // await printUserOrder(
                                    //   {
                                    //     orderData: item,
                                    //   },
                                    //   false,
                                    //   [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    //   false,
                                    //   false,
                                    //   false,
                                    //   { isInternetReachable: true, isConnected: true },
                                    //   false,
                                    //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                    // );

                                    // printKDSummaryCategoryWrapper(
                                    //   {
                                    //     orderData: item,
                                    //   },
                                    //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                    // );

                                    // for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                    //   await printDocketForKD(
                                    //     {
                                    //       userOrder: item,
                                    //       cartItem: item.cartItems[bdIndex],
                                    //     },
                                    //     // true,
                                    //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    //     // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                    //   );
                                    // }

                                    ///////////////////////////////////////////////////////////////////////////

                                    // disconnectPrinter(printer); // no need anymore

                                    // await printUserOrder(
                                    //   {
                                    //     orderId: item.uniqueId,
                                    //     receiptNote: currOutlet.receiptNote || '',
                                    //   },
                                    //   false,
                                    //   [PRINTER_USAGE_TYPE.RECEIPT],
                                    //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    //   false,
                                    //   false,
                                    //   false,
                                    //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                    //   // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
                                    //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    // );

                                    // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
                                    //   await printUserOrder(
                                    //     {
                                    //       orderData: item,
                                    //     },
                                    //     false,
                                    //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    //     false,
                                    //     false,
                                    //     false,
                                    //     { isInternetReachable: true, isConnected: true },
                                    //   );
                                    // }
                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
                                    //   printKDSummaryCategoryWrapper(
                                    //     {
                                    //       orderData: item,
                                    //     },
                                    //   );
                                    // }
                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
                                    //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                    //     await printDocketForKD(
                                    //       {
                                    //         userOrder: item,
                                    //         cartItem: item.cartItems[bdIndex],
                                    //       },
                                    //       // true,
                                    //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                    //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                    //     );
                                    //   }
                                    // }
                                  }}
                                  style={[
                                    {
                                      height: '100%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      backgroundColor: Colors.secondaryColor,
                                      underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                      width: 75,
                                      marginLeft: 25,
                                      borderRadius: 10,
                                    },
                                    switchMerchant
                                      ? {
                                        width: windowWidth * 0.08,
                                      }
                                      : {},
                                  ]}>
                                  {switchMerchant ? (
                                    <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                  ) : (
                                    <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                  )}

                                  <Text
                                    style={[
                                      {
                                        color: Colors.whiteColor,
                                        fontSize: 12,
                                        fontFamily: 'NunitoSans-Regular',
                                        textAlign: 'center',
                                        width: windowWidth <= 1133 ? '85%' : '80%',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {`Reprint\nKD`}
                                  </Text>
                                </TouchableOpacity>

                                {/* 2023-05-08 - For reprint os */}

                                <TouchableOpacity
                                  onPress={async () => {
                                    ///////////////////////////////////////////////////////////////////////////

                                    Alert.alert('Info', 'Order summary has been added to print queue');

                                    var printTimes = 1;

                                    // if (global.outletCategoriesDict) {
                                    //   if (item.cartItems && item.cartItems.length > 0) {
                                    //     for (var i = 0; i < item.cartItems.length; i++) {
                                    //       if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
                                    //         global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
                                    //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                    //       }
                                    //     }
                                    //   }

                                    //   if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
                                    //     for (var i = 0; i < item.cartItemsCancelled.length; i++) {
                                    //       if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
                                    //         global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
                                    //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                    //       }
                                    //     }
                                    //   }
                                    // }

                                    for (var i = 0; i < printTimes; i++) {
                                      logToFile('takeaway - printUserOrder - ORDER_SUMMARY');

                                      await printUserOrder(
                                        {
                                          orderData: item,
                                        },
                                        false,
                                        [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                                        false,
                                        false,
                                        false,
                                        { isInternetReachable: true, isConnected: true },
                                        true, // for isPrioritized
                                        [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                      );

                                      // printKDSummaryCategoryWrapper(
                                      //   {
                                      //     orderData: item,
                                      //   },
                                      //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                      // );

                                      // if (item && item.cartItems && item.cartItems.length > 0) {
                                      //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                      //     if (!item.cartItems[bdIndex].isDocket) {
                                      //       await printDocketForKD(
                                      //         {
                                      //           userOrder: item,
                                      //           cartItem: item.cartItems[bdIndex],
                                      //         },
                                      //         // true,
                                      //         [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                      //         // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                      //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                      //         // deliveredUser,
                                      //       );
                                      //     }
                                      //   }

                                      //   for (let index = 0; index < item.cartItems.length; index++) {
                                      //     if (item.cartItems[index].isDocket) {
                                      //       await printDocket(
                                      //         {
                                      //           userOrder: item,
                                      //           cartItem: item.cartItems[index],
                                      //         },
                                      //         // true,
                                      //         [PRINTER_USAGE_TYPE.RECEIPT],
                                      //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                      //         // deliveredUser,
                                      //       );
                                      //     }
                                      //   }
                                      // }
                                    }
                                  }}
                                  style={[
                                    {
                                      height: '100%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      backgroundColor: Colors.secondaryColor,
                                      underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                      width: 75,
                                      marginLeft: 25,
                                      borderRadius: 10,
                                    },
                                    switchMerchant
                                      ? {
                                        width: windowWidth * 0.08,
                                      }
                                      : {},
                                  ]}>
                                  {switchMerchant ? (
                                    <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                  ) : (
                                    <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                  )}

                                  <Text
                                    style={[
                                      {
                                        color: Colors.whiteColor,
                                        fontSize: 12,
                                        fontFamily: 'NunitoSans-Regular',
                                        textAlign: 'center',
                                        width: windowWidth <= 1133 ? '85%' : '80%',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {`Reprint\nOS`}
                                  </Text>
                                </TouchableOpacity>

                                {item.orderType === ORDER_TYPE.DELIVERY &&
                                  item.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ? (
                                  <TouchableOpacity
                                    onPress={() => {
                                      setCurrToManageOrder(item);
                                      setDeliveryFeeNew(item.deliveryFee);
                                      setDeliveryQuotation({
                                        totalFee: item.deliveryFee,
                                      });

                                      setManageSenderModal(true);
                                    }}
                                    style={[
                                      {
                                        height: '100%',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        backgroundColor: Colors.tabGold,
                                        underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                        width: 75,
                                        marginLeft: 25,
                                        borderRadius: 10,
                                      },
                                      switchMerchant
                                        ? {
                                          width: windowWidth * 0.08,
                                        }
                                        : {},
                                    ]}>
                                    {switchMerchant ? (
                                      <MaterialIcons
                                        name="delivery-dining"
                                        size={10}
                                        color={Colors.whiteColor}
                                        style={{ marginTop: 10 }}
                                      />
                                    ) : (
                                      <MaterialIcons
                                        name="delivery-dining"
                                        size={40}
                                        color={Colors.whiteColor}
                                        style={{ marginTop: 10 }}
                                      />
                                    )}
                                    <Text
                                      style={[
                                        {
                                          color: Colors.whiteColor,
                                          fontSize: 12,
                                          fontFamily: 'NunitoSans-Regular',
                                          textAlign: 'center',
                                          width: '80%',
                                        },
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : {},
                                      ]}>
                                      Change Sender
                                    </Text>
                                  </TouchableOpacity>
                                ) : null}

                                {item.orderStatus !== USER_ORDER_STATUS.ORDER_AUTHORIZED &&
                                  item.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARING &&
                                  item.orderStatus !== USER_ORDER_STATUS.ORDER_PREPARED &&
                                  item.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED
                                  ?
                                  (
                                    <TouchableOpacity
                                      onPress={() => {
                                        //   currToPrioritizeOrder: item,
                                        //   visible: true,
                                        // });
                                        setCurrOrderIndex(index);
                                        setCurrToAuthorizeOrder(item);
                                        setModalAuthorizeVisibility(true);
                                      }}
                                      style={[
                                        {
                                          height: '100%',
                                          justifyContent: 'center',
                                          alignItems: 'center',
                                          backgroundColor: '#8fbc8f',
                                          //backgroundColor: Colors.tabCyan,
                                          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                          width: 75,
                                          marginLeft: 25,
                                          borderRadius: 10,
                                        },
                                        switchMerchant
                                          ? {
                                            width: windowWidth * 0.08,
                                          }
                                          : {},
                                      ]}>
                                      {switchMerchant ? (
                                        <Feather
                                          name="check"
                                          size={10}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      ) : (
                                        <Feather
                                          name="check"
                                          size={40}
                                          color={Colors.whiteColor}
                                          style={{ marginTop: 10 }}
                                        />
                                      )}

                                      <Text
                                        style={[
                                          {
                                            color: Colors.whiteColor,
                                            fontSize: 12,
                                            fontFamily: 'NunitoSans-Regular',
                                            textAlign: 'center',
                                            width: '80%',
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        Authorize Order
                                      </Text>
                                    </TouchableOpacity>
                                  ) : null}
                              </View>
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  alignItems: 'center',
                                  flexDirection: 'row',
                                  height: 100,
                                  marginHorizontal: 10,
                                  marginTop: 20,
                                }}>
                                <TouchableOpacity
                                  onPress={async () => {
                                    try {
                                      const orderSnapshot = await firestore()
                                        .collection(Collections.UserOrder)
                                        .where('uniqueId', '==', item.uniqueId)
                                        .limit(1)
                                        .get();

                                      if (orderSnapshot && !orderSnapshot.empty) {
                                        const orderDoc = orderSnapshot.docs[0];
                                        const queryOrder = orderDoc.data();

                                        if (queryOrder.orderStatus !== USER_ORDER_STATUS.ORDER_DELIVERED) {
                                          const undeliveredCartItems = (queryOrder.cartItems || []).filter(
                                            cartItem => !cartItem.deliveredAt && (
                                              userManagedCategory && userManagedCategory.includes(cartItem.categoryId)
                                            )
                                          );

                                          if (undeliveredCartItems.length > 0) {
                                            const body = {
                                              orderItemList: undeliveredCartItems.map(cartItem => ({
                                                ...cartItem,
                                                userOrderId: queryOrder.uniqueId,
                                                userOrder: queryOrder,
                                              })),
                                              orderIdList: [queryOrder.uniqueId],
                                              outlet: currOutlet,
                                            };

                                            const result = await APILocal.orderDeliverMultipleSummary({ body, uid: firebaseUid });

                                            if (result && result.status === 'success') {
                                              await firestore()
                                                .collection(Collections.UserOrder)
                                                .doc(item.uniqueId)
                                                .update({
                                                  notifyAt: Date.now(),
                                                  categoryNamePrepared: undeliveredCartItems
                                                    .map(cartItem => {
                                                      const category = outletCategoriesDict[cartItem.categoryId];
                                                      return category ? category.name : cartItem.categoryName;
                                                    })
                                                    .join(', '),
                                                })

                                              Alert.alert('Success', `Order #${item.orderId} has been delivered and notified`,
                                                [{ text: 'OK' }],
                                                { cancelable: false }
                                              );
                                            }
                                          }
                                          else {
                                            await firestore()
                                              .collection(Collections.UserOrder)
                                              .doc(item.uniqueId)
                                              .update({
                                                notifyAt: Date.now(),
                                              });

                                            Alert.alert('Success', `Order #${item.orderId} has been notified.`,
                                              [{ text: 'OK' }],
                                              { cancelable: false }
                                            );
                                          }
                                        }
                                        else {
                                          await firestore()
                                            .collection(Collections.UserOrder)
                                            .doc(item.uniqueId)
                                            .update({
                                              notifyAt: Date.now(),
                                            });

                                          Alert.alert('Success', `Order #${item.orderId} has been notified.`,
                                            [{ text: 'OK' }],
                                            { cancelable: false }
                                          );
                                        }
                                      }
                                      else {
                                        Alert.alert('Error', 'Failed to send notification');
                                        console.log('No matching orders found');
                                      }
                                    }
                                    catch (error) {
                                      console.error('Failed to update timestamp:', error);
                                      Alert.alert(
                                        'Error',
                                        'Failed to send notification',
                                        [{ text: 'OK' }],
                                        { cancelable: false }
                                      );
                                    }
                                  }}
                                  style={[
                                    {
                                      height: '100%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      backgroundColor: Colors.primaryColor,
                                      underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                      width: 75,
                                      marginLeft: 25,
                                      borderRadius: 10,
                                    },
                                    switchMerchant
                                      ? {
                                        width: windowWidth * 0.08,
                                      }
                                      : {},
                                  ]}>
                                  {switchMerchant ? (
                                    <Ionicons
                                      name="notifications"
                                      size={10}
                                      color={Colors.whiteColor}
                                      style={{ marginTop: 10 }}
                                    />
                                  ) : (
                                    <Ionicons
                                      name="notifications"
                                      size={40}
                                      color={Colors.whiteColor}
                                      style={{ marginTop: 10 }}
                                    />
                                  )}

                                  <Text
                                    style={[
                                      {
                                        color: Colors.whiteColor,
                                        fontSize: 12,
                                        fontFamily: 'NunitoSans-Regular',
                                        textAlign: 'center',
                                        width: windowWidth <= 1133 ? '85%' : '80%',
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {`Notify\nOrder`}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </>
                          ) : (<></>)}
                        </View>
                        <View style={{ width: 8 }} />
                        {index === item.cartItems.length - 1 ? (
                          <View
                            style={{
                              flexDirection: 'row',
                              //backgroundColor: 'yellow',
                              width: '28%',
                            }}>
                            <View
                              style={{
                                justifyContent: 'center',
                                width: '100%',
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Subtotal:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: '50%',
                                    marginHorizontal: 1,
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? { fontSize: 10 }
                                        : { fontSize: 16 }
                                    }>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {
                                          fontSize: 16,
                                          paddingRight: 25
                                        }
                                    }>
                                    {((item.isRefundOrder && item.finalPrice <= 0) ? 0 : (
                                      item.totalPrice +
                                      getOrderDiscountInfo(item)
                                    ))
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>
                              {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                                <View
                                  style={{
                                    flexDirection: 'row',
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                          width: '50%',
                                        }
                                        : {
                                          fontSize: 16,
                                          width: '50%',
                                          fontFamily: 'Nunitosans-Bold',
                                        }
                                    }>
                                    Delivery Fee:
                                  </Text>
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      justifyContent: 'space-between',
                                      width: '50%',
                                      marginHorizontal: 1,
                                    }}>
                                    <Text
                                      style={
                                        switchMerchant
                                          ? { fontSize: 10 }
                                          : { fontSize: 16 }
                                      }>
                                      RM
                                    </Text>
                                    <Text
                                      style={
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                          }
                                          : { fontSize: 16, paddingRight: 25 }
                                      }>
                                      {item.deliveryFee
                                        .toFixed(2)
                                        .replace(
                                          /(\d)(?=(\d{3})+(?!\d))/g,
                                          '$1,',
                                        )}
                                    </Text>
                                  </View>
                                </View>
                              ) : (
                                <></>
                              )}
                              {
                                (currOutlet.pickupPackagingFee && cartItem.orderType === ORDER_TYPE.PICKUP)
                                  ?
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                    }}>
                                    <Text
                                      style={
                                        switchMerchant
                                          ? {
                                            fontSize: 10,
                                            width: '50%',
                                          }
                                          : {
                                            fontSize: 16,
                                            width: '50%',
                                            fontFamily: 'Nunitosans-Bold',
                                          }
                                      }>
                                      {/* Takeaway  */}
                                      {`Packaging Fee`}
                                    </Text>
                                    <View
                                      style={{
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        width: '50%',
                                        marginHorizontal: 1,
                                      }}>
                                      <Text
                                        style={
                                          switchMerchant
                                            ? { fontSize: 10 }
                                            : { fontSize: 16 }
                                        }>
                                        RM
                                      </Text>
                                      <Text
                                        style={
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : { fontSize: 16, paddingRight: 25 }
                                        }>
                                        {item.pickupPackagingFee
                                          .toFixed(2)
                                          .replace(
                                            /(\d)(?=(\d{3})+(?!\d))/g,
                                            '$1,',
                                          )}
                                      </Text>
                                    </View>
                                  </View>
                                  :
                                  <></>
                              }

                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Discount:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: '50%',
                                    marginHorizontal: 1,
                                  }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 16,
                                    }}>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {' '}
                                    {((item.isRefundOrder && item.finalPrice <= 0) ? 0 : (
                                      // item.discount +
                                      (getOrderDiscountInfoInclOrderBased(item))
                                    ))
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>

                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Tax:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: '50%',
                                    marginHorizontal: 1,
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16 }
                                    }>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {item.tax
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Service Charge:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: '50%',
                                    marginHorizontal: 1,
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16 }
                                    }>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {(item.sc || 0)
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Rounding:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: '50%',
                                    marginHorizontal: 1,
                                  }}>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16 }
                                    }>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {(item.finalPrice ? (item.finalPrice - item.finalPriceBefore) : 0)
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>

                              <View
                                style={{
                                  flexDirection: 'row',
                                }}>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        width: '50%',
                                      }
                                      : {
                                        fontSize: 16,
                                        width: '50%',
                                        fontFamily: 'Nunitosans-Bold',
                                      }
                                  }>
                                  Total:
                                </Text>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    width: '50%',
                                    marginHorizontal: 1,
                                  }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 10 : 16,
                                    }}>
                                    RM
                                  </Text>
                                  <Text
                                    style={
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : { fontSize: 16, paddingRight: 25 }
                                    }>
                                    {item.finalPrice
                                      .toFixed(2)
                                      .replace(
                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                        '$1,',
                                      )}
                                  </Text>
                                </View>
                              </View>
                            </View>
                          </View>
                        ) : (
                          <></>
                        )}
                      </View>

                      {/* <View style={{alignItems:'flex-end'}}>
                          <View style={{ flexDirection: 'row' }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>Subtotal: {(Math.ceil(cartItem.price * 20-0.05) /20).toFixed(2)}</Text>
                          </View>
                        </View> */}
                      {/* {(cartItem.remarks && cartItem.remarks.length > 0) ?
                        <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                          
                          <View style={{ flex: 1, justifyContent: 'center', }}>
                            <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 15 }}>{cartItem.remarks}</Text>
                          </View>
                          
                        </View>
                        : <></>
                      } */}
                    </View>)
                  );
                })}
                {(item.cartItems && item.cartItems.length === 0 && item.cartItemsCancelled) ?
                  <>
                    {
                      item.cartItemsCancelled && item.cartItemsCancelled.map((cartItem, index) => {
                        return (
                          (<View
                            style={{
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}>
                            <View
                              style={{
                                width: '100%',
                                alignItems: 'flex-start',
                                flexDirection: 'row',
                                //marginTop: '0.5%',
                                //marginVertical: '0.6%',
                                marginBottom: Platform.OS == 'ios' ? 10 : 10,
                                minHeight: 80,
                                //smarginTop: Platform.OS == 'android' ? 10 : 0
                              }}>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  width: '100%',
                                }}>
                                {index == 0 ? (
                                  <View
                                    style={{
                                      // flex: 0.75,
                                      marginHorizontal: 1,
                                      width: Platform.OS == 'ios' ? '8%' : '8%',
                                      alignItems: 'center',
                                      //right: Platform.OS == 'ios' ? 10 : 0,
                                      //width: Platform.OS == 'android' ? '8%' : '5%',
                                      //justifyContent: 'center',
                                      //alignItems: 'center',
                                      //paddingRight: '2%',
                                      //right: Platform.OS == 'ios' ? 10 : 10,
                                      //right: Platform.OS == 'android' ? 10 : 0,
                                      //paddingLeft: '2%',
                                      //backgroundColor: 'blue',
                                      // borderWidth: 1
                                    }}>
                                    {/* <TouchableOpacity
                                      style={{
                                        alignItems: 'center',
                                        // borderWidth:1
                                        // marginTop: '-10%',
                                      }}
                                      onPress={() => {
                                        var crmUser = null;

                                        if (item.crmUserId !== undefined) {
                                          for (
                                            var i = 0;
                                            i < crmUsers.length;
                                            i++
                                          ) {
                                            if (
                                              item.crmUserId ===
                                              crmUsers[i].uniqueId
                                            ) {
                                              crmUser = crmUsers[i];
                                              break;
                                            }
                                          }
                                        }

                                        if (!crmUser) {
                                          for (
                                            var i = 0;
                                            i < crmUsers.length;
                                            i++
                                          ) {
                                            if (
                                              item.userId ===
                                              crmUsers[i].firebaseUid
                                            ) {
                                              crmUser = crmUsers[i];
                                              break;
                                            }
                                          }
                                        }

                                        if (crmUser) {
                                          CommonStore.update(
                                            (s) => {
                                              s.selectedCustomerEdit = crmUser;
                                              // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                              s.routeParams = {
                                                pageFrom: 'Reservation',
                                              };
                                            },
                                            () => {
                                              navigation.navigate('NewCustomer');
                                            },
                                          );
                                        }
                                      }}>
                                      <View
                                        style={{
                                          position: 'relative',
                                          //left: Platform.OS === 'ios'? 15: 0,
                                          // borderWidth: 1,
                                          // alignSelf: 'flex-start'
                                        }}>
                                        <Image
                                          style={{
                                            width: switchMerchant ? 30 : 60,
                                            height: switchMerchant ? 30 : 60,
                                          }}
                                          resizeMode="contain"
                                          source={require('../assets/image/profile-pic.jpg')}
                                        />
                                      </View>

                                      <View
                                        style={{
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          position: 'relative',
                                          //left: Platform.OS === 'ios'? 15: 0
                                        }}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-Bold',
                                              marginTop: 0,
                                              fontSize: 16,
                                              textAlign: 'center',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}
                                          numberOfLines={1}>
                                          {item.userName ? item.userName : 'Guest'}
                                        </Text>
                                      </View>
                                    </TouchableOpacity> */}
                                  </View>
                                ) : (
                                  <View
                                    style={{
                                      // flex: 0.75,
                                      marginHorizontal: 1,
                                      width: Platform.OS == 'ios' ? '8%' : '8%',
                                      //width: Platform.OS == 'android' ? '8%' : '5%',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      //right: Platform.OS == 'ios' ? 10 : 10,
                                      // paddingRight: '2%',
                                      // marginTop: '-10%',
                                      //backgroundColor: 'blue'
                                    }}
                                  />
                                )}

                                <View
                                  style={{
                                    // flex: 0.3,
                                    width: '10%',
                                    //justifyContent: 'center',
                                    alignItems: 'center',
                                    // backgroundColor: 'red',
                                    //paddingLeft: '1.2%',
                                    //right: 10
                                    //marginLeft: 10
                                  }}>
                                  {/* <Text
                                    style={[
                                      {
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: 16,
                                      },
                                      switchMerchant
                                        ? {
                                          fontSize: 10,
                                        }
                                        : {},
                                    ]}>
                                    {index + 1}.
                                  </Text> */}
                                </View>

                                <View
                                  style={{
                                    width: '10%',
                                    //flex: 0.5,
                                    alignItems: 'center',
                                  }}>
                                  {cartItem.image ? (
                                    <AsyncImage
                                      source={{ uri: cartItem.image }}
                                      // item={cartItem}
                                      style={{
                                        width: switchMerchant ? 30 : 60,
                                        height: switchMerchant ? 30 : 60,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        borderRadius: 5,
                                      }}
                                    />
                                  ) : (
                                    <View
                                      style={{
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        width: switchMerchant ? 30 : 60,
                                        height: switchMerchant ? 30 : 60,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        borderRadius: 5,
                                      }}>
                                      <Ionicons
                                        name="cart-outline"
                                        size={switchMerchant ? 15 : 35}
                                      />
                                    </View>
                                  )}
                                </View>
                                <View
                                  style={{
                                    width: '68.5%',
                                    //width: '53%',
                                    marginLeft: 6,
                                  }}>
                                  <View
                                    style={{
                                      alignItems: 'center',
                                      width: '100%',
                                      flexDirection: 'row',
                                      //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                      //marginLeft: Platform.OS == 'android' ? 30 : 0,
                                      marginBottom: 10,
                                      //paddingLeft: '2%',
                                      //backgroundColor: 'blue'
                                    }}>
                                    <View style={{ width: '66.4%' }}>
                                      <Text
                                        style={[
                                          {
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: 16,
                                            color: 'red',
                                            textDecorationLine: 'line-through',
                                          },
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : {},
                                        ]}>
                                        {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ''}
                                      </Text>
                                    </View>
                                    <View
                                      style={{
                                        width: '13%',
                                      }}>
                                      <View
                                        style={{
                                          alignItems: 'center',
                                        }}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-Bold',
                                              fontSize: 16,
                                              //marginRight: 50
                                              color: 'red',
                                              textDecorationLine: 'line-through',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}>
                                          x{cartItem.quantity}
                                        </Text>
                                      </View>
                                    </View>
                                    <View
                                      style={{
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        width: '20.45%',
                                        marginHorizontal: 1,
                                      }}>
                                      {/* <Text
                                        style={
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : { fontSize: 16 }
                                        }>
                                        RM
                                      </Text>
                                      <Text
                                        style={
                                          switchMerchant
                                            ? {
                                              fontSize: 10,
                                            }
                                            : { fontSize: 16, paddingRight: 25 }
                                        }>
                                        {cartItemPriceWIthoutAddOn
                                          .toFixed(2)
                                          .replace(
                                            /(\d)(?=(\d{3})+(?!\d))/g,
                                            '$1,',
                                          )}
                                      </Text> */}
                                    </View>
                                  </View>
                                  {/* <View
                            style={{
                              //backgroundColor: 'purple',
                              // flex: 1.5,
                              width: '21%',
                              flexDirection: 'row',
                            }}> */}
                                  {/* <View
                              style={{
                                // flex: 0.5,
                                width: '36%',
                                justifyContent: 'center',
                                alignItems: 'center',
                                //backgroundColor: 'yellow',
                                paddingRight: '25%',
                              }}> */}
                                  {/* <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>x{orderItems.quantity}</Text> */}
                                  {/* <Text
                                style={[{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 16,
                                  //marginRight: 50
                                },
                                switchMerchant
                                  ? {
                                      fontSize: 8,
                                    }
                                  : {},
                              ]}>
                                x{cartItem.quantity}
                              </Text> */}
                                  {/* </View> */}
                                  {/* <View
                              style={[{
                                width: '74%',
                                justifyContent: 'center',
                                alignItems: 'flex-start',
                                paddingLeft: Platform.OS == 'ios' ? 0 : '1%',
                                //marginLeft: -10,
                                // marginRight: 23,
                                // backgroundColor: 'green',
                                //left: Platform.OS === 'ios'? 0: -8,
                              }, switchMerchant?{left: windowWidth * 0.01}:{}]}>
                              <Text
                                style={[{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 16,
                                  fontVariant: ['tabular-nums'],
                                },
                                switchMerchant
                                  ? {
                                      fontSize: 8,
                                    }
                                  : {},
                              ]}>
                                <Text>RM</Text>
                                <Text
                                  style={{
                                    opacity: 0,
                                    ...(Platform.OS === 'android' && {
                                      color: 'white',
                                    }),
                                  }}>
                                  {'0'.repeat(
                                    cartItemPriceWIthoutAddOnSpacingList[index],
                                  )}
                                </Text>
                                <Text>
                                  {cartItemPriceWIthoutAddOn
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                              </Text>
                            </View> */}
                                  {/* </View> */}

                                  {cartItem.remarks &&
                                    cartItem.remarks.length > 0 ? (
                                    <View
                                      style={{
                                        alignItems: 'center',
                                        flexDirection: 'row',
                                        //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                      }}>
                                      <View
                                        style={{
                                          justifyContent: 'center',
                                          //backgroundColor: 'red',
                                        }}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-SemiBold',
                                              fontSize: 16,
                                              //height: 20,
                                              color: 'red',
                                              textDecorationLine: 'line-through',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}>
                                          {cartItem.remarks}
                                        </Text>
                                      </View>
                                    </View>
                                  ) : (
                                    <></>
                                  )}
                                  {cartItem.rejectRemarks &&
                                    cartItem.rejectRemarks.length > 0 ? (
                                    <View
                                      style={{
                                        alignItems: 'center',
                                        flexDirection: 'row',
                                        //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                      }}>
                                      <View
                                        style={{
                                          justifyContent: 'center',
                                          //backgroundColor: 'red',
                                        }}>
                                        <Text
                                          style={[
                                            {
                                              fontFamily: 'NunitoSans-SemiBold',
                                              fontSize: 16,
                                              //height: 20,
                                              color: 'red',
                                              textDecorationLine: 'line-through',
                                            },
                                            switchMerchant
                                              ? {
                                                fontSize: 10,
                                              }
                                              : {},
                                          ]}>
                                          {`(${cartItem.rejectRemarks})`}
                                        </Text>
                                      </View>
                                    </View>
                                  ) : (
                                    <></>
                                  )}

                                  {cartItem.addOns.map((addOnChoice, i) => {
                                    const addOnChoices = addOnChoice.choiceNames.join(", ");
                                    return (
                                      <View
                                        style={{
                                          flexDirection: 'row',
                                          // marginLeft: -5,
                                          width: '100%',
                                        }}>
                                        {/* <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.fieldtTxtColor }}>Add Ons: RM {addOnChoice.prices.reduce((accum, price) => accum + price, 0)}</Text> */}

                                        <View
                                          style={{
                                            width: '66.4%',
                                            flexDirection: 'row',
                                            // marginLeft:
                                            //   Platform.OS == 'ios' ? 14 : 14,
                                            // backgroundColor: 'red',
                                          }}>
                                          <Text
                                            style={[
                                              {
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: 16,
                                                color: Colors.fieldtTxtColor,
                                                width: '25%',
                                                // marginLeft: 5,
                                                color: 'red',
                                                textDecorationLine: 'line-through',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`${addOnChoice.name}:`}
                                          </Text>
                                          <Text
                                            style={[
                                              {
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: 16,
                                                color: Colors.fieldtTxtColor,
                                                width: '75%',
                                                // marginLeft: 5,
                                                color: 'red',
                                                textDecorationLine: 'line-through',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`${addOnChoices}`}
                                          </Text>
                                        </View>

                                        <View
                                          style={{
                                            width: '13%',
                                            flexDirection: 'row',
                                            justifyContent: 'center',
                                            // backgroundColor: 'blue',
                                            //right: '1%'
                                          }}>
                                          <Text
                                            style={[
                                              {
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: 16,
                                                color: Colors.fieldtTxtColor,
                                                width: '28%',
                                                // right: 38,
                                                //backgroundColor: 'green',
                                                textAlign: 'center',
                                                // alignSelf: 'center',
                                                // paddingRight:
                                                //   Platform.OS == 'ios'
                                                //     ? '20%'
                                                //     : '19%',
                                                //marginRight: 50
                                                //right: '100%'
                                                color: 'red',
                                                textDecorationLine: 'line-through',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                  paddingLeft: '5%',
                                                  textAlign: 'left',
                                                }
                                                : {},
                                              !switchMerchant &&
                                                Platform.OS === 'android'
                                                ? {}
                                                : {},
                                            ]}>
                                            {`${addOnChoice.quantities
                                              ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                              : ''
                                              }`}
                                          </Text>
                                        </View>
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            width: '20.45%',
                                            marginHorizontal: 1,
                                            alignItems: 'center',
                                          }}>
                                          {/* <View style={[switchMerchant?{left: windowWidth * 0.016, width: '80%'}:{}]}> */}
                                          {/* <Text
                                            style={[
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {
                                                  color: Colors.descriptionColor,
                                                  fontSize: 16,
                                                },
                                            ]}>
                                            RM
                                          </Text>
                                          <Text
                                            style={
                                              switchMerchant
                                                ? { fontSize: 10 }
                                                : {
                                                  color: Colors.descriptionColor,
                                                  paddingRight: 25,
                                                  fontSize: 16,
                                                }
                                            }>
                                            {(getAddOnChoicePrice(addOnChoice, cartItem))
                                              .toFixed(2)
                                              .replace(
                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                '$1,',
                                              )}
                                          </Text> */}
                                        </View>
                                      </View>
                                    );
                                  })}
                                </View>
                              </View>
                            </View>
                            <View style={{ flexDirection: 'row', width: '100%' }}>
                              <View style={{ width: '70%' }} >
                                {/* Moved Buttons */}
                                {
                                  false
                                    // index === item.cartItemsCancelled.length - 1
                                    ? (
                                      <View
                                        style={{
                                          justifyContent: 'flex-start',
                                          alignItems: 'center',
                                          flexDirection: 'row',
                                          height: 100,
                                          marginHorizontal: 10,
                                          marginTop: 20,
                                        }}>
                                        <TouchableOpacity
                                          onPress={() => {
                                            // setState({
                                            //   currToPrioritizeOrder: item,
                                            //   visible: true,
                                            // });
                                            setCurrOrderIndex(index);
                                            setCurrToPrioritizeOrder(item);
                                            setVisible(true);
                                          }}
                                          style={[
                                            {
                                              height: '100%',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              backgroundColor: Colors.primaryColor,
                                              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                              width: 75,
                                              marginLeft: 25,
                                              borderRadius: 10,
                                            },
                                            switchMerchant
                                              ? {
                                                width: windowWidth * 0.08,
                                              }
                                              : {},
                                          ]}>
                                          {switchMerchant ? (
                                            <MaterialCommunityIcons
                                              name="message-alert-outline"
                                              size={10}
                                              color={Colors.whiteColor}
                                              style={{ marginTop: 10 }}
                                            />
                                          ) : (
                                            <MaterialCommunityIcons
                                              name="message-alert-outline"
                                              size={40}
                                              color={Colors.whiteColor}
                                              style={{ marginTop: 10 }}
                                            />
                                          )}

                                          <Text
                                            style={[
                                              {
                                                color: Colors.whiteColor,
                                                fontSize: 12,
                                                fontFamily: 'NunitoSans-Regular',
                                                textAlign: 'center',
                                                width: windowWidth <= 1133 ? '85%' : '80%',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`Prioritize\nOrder`}
                                          </Text>
                                        </TouchableOpacity>

                                        {(
                                          (item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
                                            item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING ||
                                            item.orderStatus === USER_ORDER_STATUS.ORDER_PREPARED ||
                                            item.orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED)
                                          &&
                                          (item.tableId === '')
                                          // &&
                                          // item.paymentDetails === null
                                        )
                                          ? (
                                            <TouchableOpacity
                                              onPress={() => {
                                                // setState({
                                                //   currToPrioritizeOrder: item,
                                                //   visible: true,
                                                // });

                                                // setCurrOrderIndex(index);
                                                // setCurrToPrioritizeOrder(item);
                                                // setVisible(true);

                                                // global.viewTableOrderModalPrev = false;

                                                // TableStore.update(s => {
                                                //   s.selectedOrderToPayUserList = [];
                                                //   s.selectedOrderToPayUserIdDict = [];
                                                //   s.viewTableOrderModal = true;
                                                // });

                                                TableStore.update(s => {
                                                  s.orderDisplayIndividual = false;
                                                  s.orderDisplayProduct = false;
                                                  s.orderDisplaySummary = true;

                                                  s.viewTableOrderModal = false;
                                                  s.renderPaymentSummary = false;
                                                  s.renderReceipt = false;

                                                  s.displayQrModal = false;
                                                  s.displayQModal = false;
                                                  s.deleteTableModal = false;
                                                  s.updateTableModal = false;
                                                  s.joinTableModal = false;
                                                  s.moveOrderModal = false;
                                                  s.addSectionAreaModel = false;
                                                  s.addTableModal = false;
                                                  s.preventDeleteTableModal = false;
                                                  s.seatingModal = false;
                                                  s.showLoyaltyModal = false;
                                                  s.showAddLoyaltyModal = false;
                                                  s.cashbackModal = false;
                                                });

                                                // CommonStore.update(s => {
                                                //   s.isCheckingOutTakeaway = true;

                                                //   s.checkingOutTakeawayOrder = item;

                                                //   s.timestamp = Date.now();
                                                // }, () => {
                                                //   navigation.navigate('Table');
                                                // });

                                                setTimeout(() => {
                                                  CommonStore.update(s => {
                                                    s.isCheckingOutTakeaway = true;

                                                    s.checkingOutTakeawayOrder = item;

                                                    s.timestamp = Date.now();

                                                    s.checkingOutTakeawayTimestamp = Date.now();

                                                    s.currPage = 'Table';
                                                  }, () => {
                                                    setTimeout(() => {
                                                      // takeaway navigate table (2) off
                                                      if (global.currOutlet.taNT2Off) {
                                                        // do nothing
                                                      }
                                                      else {
                                                        navigation.navigate('Table');
                                                      }
                                                    }, global.currOutlet.taNT6Timer ? global.currOutlet.taNT6Timer : 100);
                                                  });
                                                }, global.currOutlet.taNT5Timer ? global.currOutlet.taNT5Timer : 100);
                                              }}
                                              style={[
                                                {
                                                  height: '100%',
                                                  justifyContent: 'center',
                                                  alignItems: 'center',
                                                  backgroundColor: Colors.tabCyan,
                                                  underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                  width: 75,
                                                  marginLeft: 25,
                                                  borderRadius: 10,
                                                },
                                                switchMerchant
                                                  ? {
                                                    width: windowWidth * 0.08,
                                                  }
                                                  : {},
                                              ]}>
                                              {switchMerchant ? (
                                                <MaterialIcons
                                                  name="payment"
                                                  size={10}
                                                  color={Colors.whiteColor}
                                                  style={{ marginTop: 10 }}
                                                />
                                              ) : (
                                                <MaterialIcons
                                                  name="payment"
                                                  size={40}
                                                  color={Colors.whiteColor}
                                                  style={{ marginTop: 10 }}
                                                />
                                              )}
                                              <Text
                                                style={[
                                                  {
                                                    color: Colors.whiteColor,
                                                    fontSize: 12,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    textAlign: 'center',
                                                    width: '80%',
                                                  },
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10,
                                                    }
                                                    : {},
                                                ]}>
                                                Checkout Order
                                              </Text>
                                            </TouchableOpacity>
                                          ) : null}
                                        {
                                          true ?
                                            <TouchableOpacity
                                              onPress={() => {
                                                // setState({
                                                //   currToPrioritizeOrder: item,
                                                //   visible: true,
                                                // });

                                                // setCurrOrderIndex(index);
                                                // setCurrToCancelOrder(item);
                                                // setModalCancelVisibility(true);

                                                global.pinUnlockCallback = async () => {
                                                  if (item.paymentDetails !== null /* && (
                                                item.paymentDetails.txn_ID !== undefined ||
                                                item.paymentDetails.txnId !== undefined
                                              ) */
                                                    || (item.combinedOrderList && item.combinedOrderList.length > 0)
                                                  ) {
                                                    Alert.alert('Info', 'Paid order is unable to cancel.');
                                                  }
                                                  else {
                                                    setCurrOrderIndex(index);
                                                    setCurrToCancelOrder(item);
                                                    setModalCancelVisibility(true);
                                                  }
                                                };

                                                if ((currOutlet && currOutlet.privileges &&
                                                  currOutlet.privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER))
                                                  && privileges && privileges.includes(PRIVILEGES_NAME.CANCEL_ORDER)) {
                                                  global.pinUnlockCallback();
                                                }
                                                else {
                                                  CommonStore.update(s => {
                                                    s.pinUnlockType = PRIVILEGES_NAME.CANCEL_ORDER;
                                                    s.showPinUnlockModal = true;
                                                  });
                                                }
                                              }}
                                              style={[
                                                {
                                                  height: '100%',
                                                  justifyContent: 'center',
                                                  alignItems: 'center',
                                                  // backgroundColor: Colors.primaryColor,
                                                  backgroundColor: '#d90000',
                                                  underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                  width: 75,
                                                  marginLeft: 25,
                                                  borderRadius: 10,
                                                },
                                                switchMerchant
                                                  ? {
                                                    width: windowWidth * 0.08,
                                                  }
                                                  : {},
                                              ]}>
                                              {switchMerchant ? (
                                                <MaterialCommunityIcons
                                                  name="close"
                                                  size={10}
                                                  color={Colors.whiteColor}
                                                  style={{ marginTop: 10 }}
                                                />
                                              ) : (
                                                <MaterialCommunityIcons
                                                  name="close"
                                                  size={40}
                                                  color={Colors.whiteColor}
                                                  style={{ marginTop: 10 }}
                                                />
                                              )}

                                              <Text
                                                style={[
                                                  {
                                                    color: Colors.whiteColor,
                                                    fontSize: 12,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    textAlign: 'center',
                                                    width: windowWidth <= 1133 ? '85%' : '80%',
                                                  },
                                                  switchMerchant
                                                    ? {
                                                      fontSize: 10,
                                                    }
                                                    : {},
                                                ]}>
                                                {`Cancel\nOrder`}
                                              </Text>
                                            </TouchableOpacity>
                                            : <></>
                                        }

                                        {/* 2023-01-30 - For reprint kd */}

                                        <TouchableOpacity
                                          onPress={async () => {
                                            ///////////////////////////////////////////////////////////////////////////

                                            Alert.alert('Info', 'Kitchen docket has been added to print queue');

                                            var printTimes = 1;

                                            if (global.outletCategoriesDict) {
                                              if (item.cartItems && item.cartItems.length > 0) {
                                                for (var i = 0; i < item.cartItems.length; i++) {
                                                  if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
                                                    global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
                                                    printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                                  }
                                                }
                                              }

                                              if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
                                                for (var i = 0; i < item.cartItemsCancelled.length; i++) {
                                                  if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
                                                    global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
                                                    printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                                  }
                                                }
                                              }
                                            }

                                            for (var i = 0; i < printTimes; i++) {
                                              logToFile('takeaway - printUserOrder - KITCHEN_DOCKET cancelled');

                                              await printUserOrder(
                                                {
                                                  orderData: item,
                                                },
                                                false,
                                                [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                false,
                                                false,
                                                false,
                                                { isInternetReachable: true, isConnected: true },
                                                false,
                                                [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                              );

                                              printKDSummaryCategoryWrapper(
                                                {
                                                  orderData: item,
                                                },
                                                [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                              );

                                              const printerIpCountDict = await calcPrintTotalForKdIndividual({
                                                userOrder: item,
                                              });
                                              const printerTaskId = uuidv4();
                                              global.printingTaskIdDict[printerTaskId] = {};

                                              if (item && item.cartItems && item.cartItems.length > 0) {
                                                for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                                  if (!item.cartItems[bdIndex].isDocket) {
                                                    await printDocketForKD(
                                                      {
                                                        userOrder: item,
                                                        cartItem: item.cartItems[bdIndex],
                                                        printerIpCountDict: printerIpCountDict,
                                                        printerTaskId: printerTaskId,
                                                      },
                                                      // true,
                                                      [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                      // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                      [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                      // deliveredUser,
                                                    );
                                                  }
                                                }

                                                for (let index = 0; index < item.cartItems.length; index++) {
                                                  if (item.cartItems[index].isDocket) {
                                                    await printDocket(
                                                      {
                                                        userOrder: item,
                                                        cartItem: item.cartItems[index],
                                                      },
                                                      // true,
                                                      [PRINTER_USAGE_TYPE.RECEIPT],
                                                      [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                                      // deliveredUser,
                                                    );
                                                  }
                                                }
                                              }
                                            }

                                            // Clear selectedCartItemDict after printing
                                            setSelectedCartItemDict({});

                                            // for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                            //   await printDocketForKD(
                                            //     {
                                            //       userOrder: item,
                                            //       cartItem: item.cartItems[bdIndex],
                                            //     },
                                            //     // true,
                                            //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            //     // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                            //   );
                                            // }

                                            ///////////////////////////////////////////////////////////////////////////

                                            // disconnectPrinter(printer); // no need anymore

                                            // await printUserOrder(
                                            //   {
                                            //     orderId: item.uniqueId,
                                            //     receiptNote: currOutlet.receiptNote || '',
                                            //   },
                                            //   false,
                                            //   [PRINTER_USAGE_TYPE.RECEIPT],
                                            //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            //   false,
                                            //   false,
                                            //   false,
                                            //   netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                            //   // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
                                            //   // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            // );

                                            // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
                                            //   await printUserOrder(
                                            //     {
                                            //       orderData: item,
                                            //     },
                                            //     false,
                                            //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            //     false,
                                            //     false,
                                            //     false,
                                            //     { isInternetReachable: true, isConnected: true },
                                            //   );
                                            // }
                                            // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
                                            //   printKDSummaryCategoryWrapper(
                                            //     {
                                            //       orderData: item,
                                            //     },
                                            //   );
                                            // }
                                            // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
                                            //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                            //     await printDocketForKD(
                                            //       {
                                            //         userOrder: item,
                                            //         cartItem: item.cartItems[bdIndex],
                                            //       },
                                            //       // true,
                                            //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                            //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                            //     );
                                            //   }
                                            // }
                                          }}
                                          style={[
                                            {
                                              height: '100%',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              backgroundColor: Colors.secondaryColor,
                                              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                              width: 75,
                                              marginLeft: 25,
                                              borderRadius: 10,
                                            },
                                            switchMerchant
                                              ? {
                                                width: windowWidth * 0.08,
                                              }
                                              : {},
                                          ]}>
                                          {switchMerchant ? (
                                            <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                          ) : (
                                            <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                          )}

                                          <Text
                                            style={[
                                              {
                                                color: Colors.whiteColor,
                                                fontSize: 12,
                                                fontFamily: 'NunitoSans-Regular',
                                                textAlign: 'center',
                                                width: windowWidth <= 1133 ? '85%' : '80%',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`Reprint\nKD`}
                                          </Text>
                                        </TouchableOpacity>

                                        {/* 2023-05-08 - For reprint os */}

                                        <TouchableOpacity
                                          onPress={async () => {
                                            ///////////////////////////////////////////////////////////////////////////

                                            Alert.alert('Info', 'Order summary has been added to print queue');

                                            var printTimes = 1;

                                            // if (global.outletCategoriesDict) {
                                            //   if (item.cartItems && item.cartItems.length > 0) {
                                            //     for (var i = 0; i < item.cartItems.length; i++) {
                                            //       if (global.outletCategoriesDict[item.cartItems[i].categoryId] &&
                                            //         global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum) {
                                            //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                            //       }
                                            //     }
                                            //   }

                                            //   if (item.cartItemsCancelled && item.cartItemsCancelled.length > 0) {
                                            //     for (var i = 0; i < item.cartItemsCancelled.length; i++) {
                                            //       if (global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId] &&
                                            //         global.outletCategoriesDict[item.cartItemsCancelled[i].categoryId].printKDNum) {
                                            //         printTimes = global.outletCategoriesDict[item.cartItems[i].categoryId].printKDNum;
                                            //       }
                                            //     }
                                            //   }
                                            // }

                                            for (var i = 0; i < printTimes; i++) {
                                              logToFile('takeaway - printUserOrder - ORDER_SUMMARY cancelled');

                                              await printUserOrder(
                                                {
                                                  orderData: item,
                                                },
                                                false,
                                                [PRINTER_USAGE_TYPE.ORDER_SUMMARY],
                                                false,
                                                false,
                                                false,
                                                { isInternetReachable: true, isConnected: true },
                                                true, // for isPrioritized
                                                [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                              );

                                              // printKDSummaryCategoryWrapper(
                                              //   {
                                              //     orderData: item,
                                              //   },
                                              //   [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
                                              // );

                                              // if (item && item.cartItems && item.cartItems.length > 0) {
                                              //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                              //     if (!item.cartItems[bdIndex].isDocket) {
                                              //       await printDocketForKD(
                                              //         {
                                              //           userOrder: item,
                                              //           cartItem: item.cartItems[bdIndex],
                                              //         },
                                              //         // true,
                                              //         [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                              //         // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                              //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                              //         // deliveredUser,
                                              //       );
                                              //     }
                                              //   }

                                              //   for (let index = 0; index < item.cartItems.length; index++) {
                                              //     if (item.cartItems[index].isDocket) {
                                              //       await printDocket(
                                              //         {
                                              //           userOrder: item,
                                              //           cartItem: item.cartItems[index],
                                              //         },
                                              //         // true,
                                              //         [PRINTER_USAGE_TYPE.RECEIPT],
                                              //         [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
                                              //         // deliveredUser,
                                              //       );
                                              //     }
                                              //   }
                                              // }
                                            }
                                          }}
                                          style={[
                                            {
                                              height: '100%',
                                              justifyContent: 'center',
                                              alignItems: 'center',
                                              backgroundColor: Colors.secondaryColor,
                                              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                              width: 75,
                                              marginLeft: 25,
                                              borderRadius: 10,
                                            },
                                            switchMerchant
                                              ? {
                                                width: windowWidth * 0.08,
                                              }
                                              : {},
                                          ]}>
                                          {switchMerchant ? (
                                            <Ionicons name="receipt-outline" size={10} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                          ) : (
                                            <Ionicons name="receipt-outline" size={40} color={Colors.whiteColor} style={{ marginTop: 10 }} />
                                          )}

                                          <Text
                                            style={[
                                              {
                                                color: Colors.whiteColor,
                                                fontSize: 12,
                                                fontFamily: 'NunitoSans-Regular',
                                                textAlign: 'center',
                                                width: windowWidth <= 1133 ? '85%' : '80%',
                                              },
                                              switchMerchant
                                                ? {
                                                  fontSize: 10,
                                                }
                                                : {},
                                            ]}>
                                            {`Reprint\nOS`}
                                          </Text>
                                        </TouchableOpacity>
                                      </View>
                                    ) : (
                                      <></>
                                    )}
                              </View>

                            </View>
                          </View>)
                        );
                      })
                    }
                  </>
                  : <></>}
              </View>
            ) : null}
          </View>
        </TouchableOpacity>
        {/* </Swipeable> */}
      </View >)
    );
  };

  const renderModal = (item) => {
    return (
      <View>
        {item && item.uniqueId && (
          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            style={{
              flex: 1,
              backgroundColor: 'rgba(0,0,0,0.5)',
            }}
            visible={visible}
            transparent
            animationType="slide">
            <View
              style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                // flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                // minHeight: windowHeight,

                width: windowWidth,
                height: windowHeight,

                ...getTransformForModalFullScreen(),
              }}>
              <View
                style={[
                  styles.confirmBox,
                  switchMerchant
                    ? {
                      height: windowHeight * 0.35,
                      width: windowWidth * 0.4,
                    }
                    : {},
                ]}>
                <View style={{ marginTop: switchMerchant ? '4%' : 40 }}>
                  <Text
                    style={[
                      {
                        textAlign: 'center',
                        fontWeight: '700',
                        fontSize: 24,
                      },
                      switchMerchant
                        ? {
                          fontSize: 16,
                        }
                        : {},
                    ]}>
                    Prioritize Takeaway
                  </Text>
                </View>
                <View style={{ marginTop: switchMerchant ? '2%' : 20 }}>
                  <Text
                    style={[
                      {
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 18,
                        width: '80%',
                        alignSelf: 'center',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {/* Priotize Takeaway for {item.userName}, Order#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId} */}
                    Order #{item.orderId}
                  </Text>
                </View>
                <View style={{ height: windowHeight * 0.033 }} />
                <View
                  style={{
                    flexDirection: 'row',
                    alignSelf: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '50%',
                    alignContent: 'center',
                    zIndex: 6000,
                  }} />
                <View
                  style={[
                    {
                      alignSelf: 'center',
                      marginTop: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: switchMerchant ? '71.5%' : 250,
                      height: switchMerchant ? 35 : 40,
                      alignContent: 'center',
                      flexDirection: 'row',
                      marginTop: switchMerchant ? '-5%' : windowHeight === 800 && windowWidth === 1280 ? 32 : 40,
                    },
                    switchMerchant
                      ? {
                        marginTop: '13%',
                        // borderWidth: 1,
                        position: 'absolute',
                        bottom: 0,
                        alignItems: 'flex-end',
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    onPress={() => {
                      prioritizeOrder(item.uniqueId);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '70%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 35 : 80,
                      borderBottomLeftRadius: 10,
                      borderRightWidth: StyleSheet.hairlineWidth,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={[
                        { fontSize: 22, color: Colors.primaryColor },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            width: '100%',
                            textAlign: 'center',
                          }
                          : {},
                      ]}>
                      Confirm
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => {
                      // setState({ visible: false });
                      setVisible(false);
                    }}
                    style={[
                      {
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: switchMerchant ? 35 : 80,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      },
                      switchMerchant ? {} : {},
                    ]}>
                    <Text
                      style={[
                        { fontSize: 22, color: Colors.descriptionColor },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            // borderWidth: 1,
                            width: '100%',
                            textAlign: 'center',
                          }
                          : {},
                      ]}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </ModalView>
        )}
      </View>
    );
  };

  const renderModalCancel = (item) => {
    return (
      <View>
        {item && item.uniqueId && (
          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            style={{
              flex: 1,
              backgroundColor: 'rgba(0,0,0,0.5)',
            }}
            visible={modalCancelVisibility}
            transparent
            animationType="slide">
            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              style={[styles.modalContainer, {

              }]}>
              <View
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  // flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  // minHeight: windowHeight,

                  width: windowWidth,
                  height: windowHeight,

                  ...getTransformForModalFullScreen(),
                }}>
                <View
                  style={[
                    styles.confirmBox,
                    switchMerchant
                      ? {
                        height: windowHeight * 0.35,
                        width: windowWidth * 0.4,
                      }
                      : {
                        height: 290,
                      },
                  ]}>
                  <View style={{ marginTop: switchMerchant ? '4%' : 40 }}>
                    <Text
                      style={[
                        {
                          textAlign: 'center',
                          fontWeight: '700',
                          fontSize: 24,
                        },
                        switchMerchant
                          ? {
                            fontSize: 16,
                          }
                          : {},
                      ]}>
                      Cancel Takeaway
                    </Text>
                  </View>
                  <View style={{ marginTop: switchMerchant ? '2%' : 20 }}>
                    <Text
                      style={[
                        {
                          textAlign: 'center',
                          color: Colors.descriptionColor,
                          fontSize: 18,
                          width: '80%',
                          alignSelf: 'center',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {/* Cancel Takeaway for {item.userName}, Order#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId} */}
                      Order #{item.orderId}
                    </Text>
                  </View>
                  <View style={{ marginTop: 20, }}>
                    <TextInput style={{
                      alignSelf: 'center',
                      padding: 5,
                      backgroundColor: Colors.fieldtBgColor,
                      width: '80%',
                      height: 45,
                      borderRadius: 5,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      paddingLeft: 10,
                      fontFamily: 'NunitoSans-Regular',
                      fontSize: 14
                    }}
                      placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
                      placeholder='Remarks...'
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      defaultValue={remark}
                      onChangeText={text => {
                        setRemark(text);
                      }}
                    />
                  </View>
                  <View style={{ height: windowHeight * 0.033 }} />
                  <View
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'center',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '50%',
                      alignContent: 'center',
                      zIndex: 6000,
                    }} />
                  <View
                    style={[
                      {
                        alignSelf: 'center',
                        marginTop: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: switchMerchant ? '71.5%' : 250,
                        height: switchMerchant ? 35 : 40,
                        alignContent: 'center',
                        flexDirection: 'row',
                        marginTop: switchMerchant ? '-5%' : windowHeight === 800 && windowWidth === 1280 ? 32 : 40,
                      },
                      switchMerchant
                        ? {
                          marginTop: '13%',
                          position: 'absolute',
                          bottom: 0,
                          alignItems: 'flex-end',
                        }
                        : {},
                    ]}>
                    <TouchableOpacity
                      onPress={() => {
                        cancelOrder(item);
                        setRemark('');
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: switchMerchant ? 35 : 80,
                        borderBottomLeftRadius: 10,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text
                        style={[
                          { fontSize: 22, color: '#d90000' },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: '100%',
                              textAlign: 'center',
                            }
                            : {},
                        ]}>
                        Confirm
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        // setState({ visible: false });
                        // setVisible(false);
                        setModalCancelVisibility(false);
                        setRemark('');
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: switchMerchant ? 35 : 80,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      }}>
                      <Text
                        style={[
                          { fontSize: 22, color: Colors.descriptionColor },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: '100%',
                              textAlign: 'center',
                            }
                            : {},
                        ]}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </KeyboardAvoidingView>
          </ModalView>
        )}
      </View>
    );
  };

  const renderModalAuthorize = (item) => {
    return (
      <View>
        {item && item.uniqueId && (
          <ModalView
            supportedOrientations={['landscape', 'portrait']}
            style={{
              flex: 1,
              backgroundColor: 'rgba(0,0,0,0.5)',
            }}
            visible={modalAuthorizeVisibility}
            transparent
            animationType="slide">
            <View
              style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                // flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                // minHeight: windowHeight,

                width: windowWidth,
                height: windowHeight,

                ...getTransformForModalFullScreen(),
              }}>
              <View
                style={[
                  styles.confirmBox,
                  switchMerchant
                    ? {
                      height: windowHeight * 0.35,
                      width: windowWidth * 0.4,
                    }
                    : {},
                ]}>
                <View style={{ marginTop: switchMerchant ? '4%' : 40 }}>
                  <Text
                    style={[
                      {
                        textAlign: 'center',
                        fontWeight: '700',
                        fontSize: 24,
                      },
                      switchMerchant
                        ? {
                          fontSize: 16,
                        }
                        : {},
                    ]}>
                    Authorize Takeaway
                  </Text>
                </View>
                <View style={{ marginTop: switchMerchant ? '2%' : 20 }}>
                  <Text
                    style={[
                      {
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 18,
                        width: '80%',
                        alignSelf: 'center',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {/* Authorize Takeaway for {item.userName}, Order#{item.orderType !== ORDER_TYPE.DINEIN ? "T" : ""}{item.orderId} */}
                    Order #{item.orderId}
                  </Text>
                </View>
                <View style={{ height: windowHeight * 0.033 }} />
                <View
                  style={{
                    flexDirection: 'row',
                    alignSelf: 'center',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '50%',
                    alignContent: 'center',
                    zIndex: 6000,
                  }} />
                <View
                  style={[
                    {
                      alignSelf: 'center',
                      marginTop: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: switchMerchant ? '71.5%' : 250,
                      height: switchMerchant ? 35 : 40,
                      alignContent: 'center',
                      flexDirection: 'row',
                      marginTop: switchMerchant ? '-5%' : 40,
                    },
                    switchMerchant
                      ? {
                        marginTop: '13%',
                        position: 'absolute',
                        bottom: 0,
                        alignItems: 'flex-end',
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    onPress={() => {
                      authorizeOrder(item);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '70%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 35 : 80,
                      borderBottomLeftRadius: 10,
                      borderRightWidth: StyleSheet.hairlineWidth,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={[
                        { fontSize: 22, color: Colors.tabCyan },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            width: '100%',
                            textAlign: 'center',
                          }
                          : {},
                      ]}>
                      Confirm
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => {
                      // setState({ visible: false });
                      // setVisible(false);
                      setModalAuthorizeVisibility(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '70%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 35 : 80,
                      borderBottomRightRadius: 10,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={[
                        { fontSize: 22, color: Colors.descriptionColor },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            width: '100%',
                            textAlign: 'center',
                          }
                          : {},
                      ]}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </ModalView>
        )}
      </View>
    );
  };

  // 28 May - Component Listener
  // Kd Tech Team Whatsapp, 28 May, 3.46PM: Merchant will easily hit few thousand orders
  const MAX_REALTIME_DINE_IN_ORDERS = 100;
  const unsubscribeRef = useRef(null);

  useEffect(() => {
    if (!isMounted) {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }

      setTakeAwayOrders([]);

      return;
    }

    const query = firestore()
      .collection(Collections.UserOrder)
      .where('outletId', '==', outletId)
      // .where('isReservationOrder', '==', false)
      .where('orderType', '==', ORDER_TYPE.PICKUP)
      .where('orderTypeSub', '==', ORDER_TYPE_SUB.NORMAL)
      //.where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT)
      .where('orderStatus', 'in', [
        USER_ORDER_STATUS.ORDER_RECEIVED,
        USER_ORDER_STATUS.ORDER_AUTHORIZED,
        USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT,
        USER_ORDER_STATUS.ORDER_PREPARING,
        USER_ORDER_STATUS.ORDER_PREPARED,
        USER_ORDER_STATUS.ORDER_DELIVERED,
      ])
      .where(
        'createdAt',
        '>=',
        !currOutlet.toggleOpenOrder
          ? moment().subtract(1, 'day').startOf('day').valueOf()
          : moment().subtract(currOutlet.openOrderDays ? currOutlet.openOrderDays : 30, 'day').startOf('day').valueOf(),
      )
      .orderBy('createdAt', 'desc')
      // .orderBy('updatedAt', 'desc')
      .limit(currOutlet.oLimitT ? currOutlet.oLimitT : 1000);

    // Store unsubscribe function for cleanup
    const unsubscribe = query.onSnapshot((snapshot) => {
      if (snapshot) {
        console.log('snapshot listened', snapshot.docs.length);

        const orders = [];
        snapshot.forEach(doc => {
          const data = doc.data();

          if (data.orderStatus !== USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT) {
            orders.push(data);
          }
        });

        setTakeAwayOrders(orders);
      }
    });

    unsubscribeRef.current = unsubscribe;

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [isMounted]);

  return (
    // <View style={styles.container}>
    //   {renderModal(currToPrioritizeOrder)}

    //   <View style={styles.sidebar}>
    (<UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet()
            ? {
              transform: [{ scaleX: 1 }, { scaleY: 1 }],
            }
            : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {renderModal(currToPrioritizeOrder)}
        {renderModalCancel(currToCancelOrder)}
        {renderModalAuthorize(currToAuthorizeOrder)}
        {/* <View
          style={[
            styles.sidebar,
            !isTablet()
              ? {
                width: windowWidth * 0.08,
              }
              : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}
        <View style={{ flex: 1, paddingHorizontal: 25, paddingVertical: 30 }}>
          <View
            style={[
              {
                flexDirection: 'row',
                alignItems: 'center',
                padding: 2,
                width: '100%',
                justifyContent: 'space-between',
              },
              switchMerchant
                ? {
                  // borderWidth: 1,
                  height: 35,
                  marginTop: '-3%',
                  // width: windowWidth * 0.809,
                  // backgroundColor: 'blue'
                }
                : {},
            ]}>
            <View style={{ flexDirection: 'row' }}>
              <Text
                style={[
                  {
                    fontSize: 26,
                    marginRight: 30,
                    fontFamily: 'NunitoSans-Bold',
                  },
                  switchMerchant
                    ? {
                      // fontSize: 15,
                      // follow  dashboard
                      fontSize: windowWidth / 35,
                      // borderWidth: 1,
                      // top: windowHeight * -0.095,
                    }
                    : {},
                ]}>
                {takeAwayOrders.length}{' '}
                {takeAwayOrders.length > 1 ? 'Orders' : 'Order'}
              </Text>
            </View>

            <View
              style={[
                {
                  height: 40,
                  flexDirection: 'row',
                },
                !isTablet()
                  ? {
                    marginLeft: 0,
                  }
                  : {},
              ]}>
              {selectedOrders.length > 0 ?
                <TouchableOpacity
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: Colors.primaryColor,
                    borderRadius: 5,
                    // width: 120,
                    paddingHorizontal: 15,
                    height: switchMerchant ? 35 : 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    marginRight: 15,
                    // display: 'none',
                  }}
                  onPress={() => {
                    CommonStore.update(s => {
                      s.isCheckingOutTakeaway = true;

                      s.checkingOutTakeawayOrderList = selectedOrders;

                      s.timestamp = Date.now();
                      s.checkingOutTakeawayTimestamp = Date.now();
                    }, () => {
                      navigation.navigate('Table');
                    });
                  }}>
                  <Text
                    style={{
                      marginLeft: 5,
                      fontSize: switchMerchant ? 10 : 16,
                      color: '#FFFFFF',
                      fontFamily: 'NunitoSans-Bold',
                    }}>
                    {'CHECKOUT'}
                  </Text>
                </TouchableOpacity>
                : <></>}
              <View
                style={[
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                    paddingLeft: 10,
                    height: 40,
                    borderWidth: 1,
                    borderRightWidth: 0,
                    borderBottomLeftRadius: 5,
                    borderTopLeftRadius: 5,
                    borderColor: '#E5E5E5',
                    backgroundColor: 'white',
                    // marginRight: 15,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  },
                  switchMerchant
                    ? {
                      height: 35,
                      // width: windowWidth * 0.18,
                      paddingLeft: windowWidth * 0.002,
                      // marginRight: '10%',
                      // top: windowHeight * -0.075,
                      // right: windowHeight * 0.08,
                    }
                    : {},
                ]}>
                <Text
                  style={[
                    {
                      fontSize: 16,
                      paddingRight: Platform.OS == 'ios' ? 20 : 20,
                      borderColor: Colors.fieldtTxtColor,
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                        // paddingLeft: '7%',
                        paddingLeft: '1%',
                      }
                      : {},
                  ]}>
                  {' '}
                  Filter
                </Text>
              </View>
              <DropDownPicker
                arrowColor={Colors.primaryColor}
                arrowSize={switchMerchant ? 13 : 23}
                arrowStyle={[
                  { fontWeight: 'bold' },
                  switchMerchant
                    ? {
                      // top: windowHeight * -0.005,
                      height: '180%',
                      // borderWidth: 1
                    }
                    : {},
                ]}
                labelStyle={[
                  { fontFamily: 'NunitoSans-Regular' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                itemStyle={[
                  { justifyContent: 'flex-start', marginLeft: 5 },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                placeholderStyle={[
                  { color: 'black' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                style={[
                  {
                    width: 140,
                    borderWidth: 0,
                    height: 40,
                    paddingHorizontal: 5,
                    paddingVertical: 0,
                    borderBottomRightRadius: 5,
                    borderTopRightRadius: 5,
                    borderTopLeftRadius: 0,
                    borderBottomLeftRadius: 0,
                    borderColor: '#E5E5E5',
                    borderWidth: 1,
                    borderLeftWidth: 0,
                    paddingLeft: 2,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    marginRight: 15,
                    right: 1,
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                      height: 35,
                      flex: 0,
                      width: windowWidth * 0.113,
                    }
                    : {},
                ]}
                dropDownStyle={{
                  paddingLeft: 2,
                  right: 15,
                  width: switchMerchant
                    ? windowWidth * 0.113
                    : 140,
                }}
                items={[
                  { label: 'All Orders', value: 0 },
                  { label: 'Pending', value: 1 },
                  { label: 'Paid', value: 2 },
                ]} //Pending Approval
                placeholder={'All Orders'}
                onChangeItem={(item) => {
                  setFilterType(item.value)
                }}
                defaultValue={filterType}
              />
              {/* </View> */}
              <View
                style={[
                  {
                    width: 250,
                    height: 40,
                    backgroundColor: 'white',
                    borderRadius: 5,
                    flexDirection: 'row',
                    alignContent: 'center',
                    alignItems: 'center',
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                  },
                  switchMerchant
                    ? {
                      height: 35,
                      width: windowWidth * 0.2,
                      // marginRight: windowWidth * -0.04,
                      // top: windowHeight * -0.075,
                    }
                    : {},
                ]}>
                {switchMerchant ? (
                  <Icon
                    name="search"
                    size={13}
                    color={Colors.primaryColor}
                    style={{ marginLeft: 15 }}
                  />
                ) : (
                  <Icon
                    name="search"
                    size={18}
                    color={Colors.primaryColor}
                    style={{ marginLeft: 15 }}
                  />
                )}
                {switchMerchant ? (
                  <TextInput
                    // editable={!loading}
                    // underlineColorAndroid={Colors.whiteColor}
                    style={[
                      {
                        width: 220,
                        fontSize: 15,
                        fontFamily: 'NunitoSans-Regular',
                        paddingLeft: 5,
                        height: 45,
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                          // borderWidth:1,
                          width: 180,
                          height: windowHeight * 0.18,
                        }
                        : {},
                    ]}
                    clearButtonMode="while-editing"
                    placeholder=" Search"
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    onChangeText={(text) => {
                      // setSearch(text.trim());
                      setSearch(text);
                    }}
                    value={search}
                  />
                ) : (
                  <TextInput
                    // editable={!loading}
                    underlineColorAndroid={Colors.whiteColor}
                    style={{
                      width: 220,
                      fontSize: 15,
                      fontFamily: 'NunitoSans-Regular',
                      paddingLeft: 5,
                      height: 45,
                    }}
                    clearButtonMode="while-editing"
                    placeholder=" Search"
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    onChangeText={(text) => {
                      // setSearch(text.trim());
                      setSearch(text);
                    }}
                    value={search}
                  />
                )}
              </View>
            </View>
          </View>
          <View style={{ flexDirection: 'row', marginTop: 20, alignItems: 'center', justifyContent: 'flex-end', zIndex: -1, }}>
            {/* <TouchableOpacity
              onPress={() => {
                props.navigation.navigate('Order');
              }}
              style={{
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: Colors.primaryColor,
                width: 100,
                marginRight: 10,
                borderRadius: 10,
              }}>
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: 'NunitoSans-SemiBold',
                textAlign: 'center',
              }}>
                Dine In
              </Text>
            </TouchableOpacity> */}

            <TouchableOpacity
              style={{
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: Colors.secondaryColor,
                width: 100,
                marginRight: 10,
                borderRadius: 10,
              }}>
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: 'NunitoSans-SemiBold',
                textAlign: 'center',
              }}>
                Transaction
              </Text>
            </TouchableOpacity>

            {/* <TouchableOpacity
              onPress={() => {
                props.navigation.navigate('OtherDelivery');
              }}
              style={{
                height: 40,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: Colors.tabCyan,
                width: 100,
                borderRadius: 10,
              }}>
              <Text style={{
                color: Colors.whiteColor,
                fontSize: 14,
                fontFamily: 'NunitoSans-SemiBold',
                textAlign: 'center',
              }}>
                Home Delivery
              </Text>
            </TouchableOpacity> */}
          </View>
          <View
            style={[
              { marginTop: 30, marginBottom: 100, zIndex: -1 },
              switchMerchant
                ? {
                  marginTop: '3%',
                  // marginBottom: windowHeight * 0.2,
                  marginBottom: windowHeight * 0.1,
                }
                : {},
            ]}>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                paddingBottom: 10,
              }}>
              {/* <View style={{ flex: Platform.OS == 'ios' ? 0.7 : 0.5, paddingHorizontal: Platform.OS == 'ios' ? 5 : 20, alignItems: 'center' }}>
              <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Order ID</Text>
            </View>
            <View style={{ flex: 1, paddingHorizontal: 10, alignItems: 'center' }}>
              <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Date/Time</Text>
            </View>
            <View style={{ flex: 1, paddingHorizontal: 10, alignItems: 'center' }}>
              <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Name</Text>
            </View>
            <View style={{ flex: 1, paddingRight: 5, alignItems: 'center' }}>
              <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Waiting Time</Text>
            </View>
            <View style={{ flex: 1.5, paddingHorizontal: 10, alignItems: 'center' }}>
              <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Payment Status</Text>
            </View>
            <View style={{ flex: 0.8, paddingHorizontal: 5, alignItems: 'center' }}>
              <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Sender</Text>
            </View>
            <View style={{ flex: 1, paddingHorizontal: 10, alignItems: 'center', flexDirection: 'row' }}>
              <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Total</Text>
              <Text style={{ color: Colors.primaryColor, fontSize: 9 }}> *include tax</Text>
            </View> */}
              {/* <View
              style={[{
                width: '3.2%',
                marginHorizontal: 0.5,
                alignItems: 'flex-start',
                //paddingLeft: '1%',
              }, windowWidth === 1280 && windowHeight === 800 ? {
                right: 1,
              } : {}]}/> */}
              <View
                style={{
                  alignItems: 'flex-start',
                  width: '8%',
                  marginHorizontal: 0.5,
                  paddingLeft: '1%',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setSortSender(!sortSender);
                  }}>
                  <Text
                    style={[
                      {
                        color: 'black',
                        fontFamily: 'NunitoSans-Regular',
                        position: 'relative',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {' '}
                    Type
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  alignItems: 'flex-start',
                  width: '9%',
                  marginHorizontal: 0.5,
                  paddingLeft: '1%',
                }}>
                {/* <TouchableOpacity onPress={()=>{setTakeAwayOrders(takeAwayOrders.slice(0).sort((a, b) => b.orderId.localeCompare(a.orderId)))}}> */}
                <TouchableOpacity
                  onPress={() => {
                    setSortOrderID(!sortOrderID);
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Status
                  </Text>
                </TouchableOpacity>
              </View>

              <View
                style={{
                  alignItems: 'flex-start',
                  width: '12%',
                  marginHorizontal: 0.5,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setSortDateTime(!sortDateTime);
                  }}>
                  {/* <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Date/Time</Text> */}
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Order ID
                  </Text>
                </TouchableOpacity>
              </View>
              {/* <View style={{ alignItems: 'center', width: '13%', marginHorizontal: 0.5 }}>
              <TouchableOpacity onPress={() => { setSortCustomerName(!sortCustomerName) }}>
                <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Customer</Text>
              </TouchableOpacity>
            </View> */}
              <View
                style={{
                  alignItems: 'flex-start',
                  width: '11.5%',
                  marginHorizontal: 0.5,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setSortCustomerName(!sortCustomerName);
                  }}>
                  <Text
                    style={[
                      {
                        color: 'black',
                        fontFamily: 'NunitoSans-Regular',
                        position: 'relative',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Date/Time
                  </Text>
                </TouchableOpacity>
              </View>

              <View
                style={{
                  alignItems: 'flex-start',
                  width: '10%',
                  marginHorizontal: 0.5,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setSortWaitingTime(!sortWaitingTime);
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Staff
                  </Text>
                </TouchableOpacity>
              </View>
              {/* <View style={{ alignItems: 'center', width: '12%', marginHorizontal: 0.5 }}>
              <TouchableOpacity onPress={() => { setSortAuthorization(!sortAuthorization) }}>
                <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Authorization</Text>
              </TouchableOpacity>
            </View> */}
              <View
                style={{
                  alignItems: 'flex-start',
                  width: '16%',
                  marginHorizontal: 0.5,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setSortSender(!sortSender);
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Waiting Time
                  </Text>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  alignItems: 'flex-start',
                  width: '16%',
                  marginHorizontal: 0.5,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setSortPaymentMethod(!sortPaymentMethod);
                  }}>
                  {/* <Text style={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular' }}>Payment Method</Text> */}
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Payment Status
                  </Text>
                </TouchableOpacity>
              </View>

              <View
                style={{
                  alignItems: 'flex-start',
                  flexDirection: 'row',
                  width: '15%',
                  marginHorizontal: 0.5,
                }}>
                <TouchableOpacity
                  style={{ flexDirection: 'row', alignItems: 'center' }}
                  onPress={() => {
                    setSortTotalPrice(!sortTotalPrice);
                  }}>
                  <Text
                    style={[
                      { color: 'black', fontFamily: 'NunitoSans-Regular' },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Total
                  </Text>
                  <Text
                    style={[
                      { color: Colors.primaryColor, fontSize: 9 },
                      switchMerchant
                        ? {
                          fontSize: 8,
                        }
                        : {},
                    ]}>
                    {' '}
                    *incl tax
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            <FlatList
              data={filteredAndSortedOrders}
              renderItem={renderOrder}
              keyExtractor={(item, index) => String(index)}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                paddingBottom: 80,
              }}
              maxToRenderPerBatch={5}
              windowSize={5}
              removeClippedSubviews={true}
              initialNumToRender={5}
              updateCellsBatchingPeriod={50}
            />
          </View>
        </View>

        {/* ManageSenderModal Start */}
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={manageSenderModal}
          transparent
          animationType="slide">
          <View style={styles.modalContainer}>
            <View
              style={[
                styles.ManageFilterBox,
                {
                  height: 230,
                  width: 200,
                  borderRadius: 12,
                  padding: 30,
                  paddingHorizontal: 30,
                  paddingTop: 20,

                  ...getTransformForModalInsideNavigation(),
                },
              ]}>
              <View
                style={{
                  justifyContent: 'flex-end',
                  alignItems: 'flex-end',
                  marginTop: -10,
                  marginRight: -20,
                }}>
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    setManageSenderModal(false);
                  }}>
                  <AntDesign
                    name={'closecircle'}
                    size={switchMerchant ? 15 : 25}
                    color={'#858C94'}
                  />
                </TouchableOpacity>
              </View>

              <View>
                <Text
                  style={[
                    { fontFamily: 'Nunitosans-Bold', fontSize: 19 },
                    switchMerchant
                      ? {
                        fontSize: 16,
                      }
                      : {},
                  ]}>
                  Change Sender
                </Text>
              </View>

              <View
                style={{
                  borderColor: '#E5E5E5',
                  borderWidth: 1,
                  marginVertical: 5,
                  marginBottom: 10,
                  marginHorizontal: -5,
                }}
              />

              <View style={{ width: '60%', right: '7%' }}>
                <TouchableOpacity
                  disabled={isLoading}
                  style={{
                    width: 160,
                    height: switchMerchant ? 35 : 40,
                    borderRadius: 10,
                    borderWidth: 1,
                    justifyContent: 'center',
                    backgroundColor: Colors.primaryColor,
                    borderColor: Colors.primaryColor,
                    marginTop: 10,
                  }}
                  onPress={() => {
                    Alert.alert(
                      'Info',
                      'Are you sure you want to notify your customer on the sender\'s current status?',
                      [
                        {
                          text: 'YES',
                          onPress: () => {
                            notifyUser();
                          },
                        },
                        {
                          text: 'NO',
                          onPress: () => { },
                        },
                      ],
                    );
                  }}>
                  {isLoading && notify ? (
                    <ActivityIndicator
                      style={{}}
                      color={Colors.whiteColor}
                      size={'small'}
                    />
                  ) : (
                    <Text
                      style={[
                        {
                          textAlign: 'center',
                          fontWeight: '700',
                          color: Colors.whiteColor,
                          fontSize: 14,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      NOTIFY USER
                    </Text>
                  )}
                </TouchableOpacity>
              </View>
              <View style={{ height: 5 }} />
              <View style={{ width: '60%', right: '7%' }}>
                <TouchableOpacity
                  disabled={isLoading}
                  style={{
                    width: 160,
                    height: switchMerchant ? 35 : 40,
                    borderRadius: 10,
                    borderWidth: 1,
                    borderColor: 'red',
                    backgroundColor: Colors.tabRed,
                    justifyContent: 'center',
                    marginTop: 10,
                  }}
                  onPress={() => {
                    Alert.alert(
                      'Cancel & Refund',
                      'Are you sure you want to cancel and refund this order?',
                      [
                        {
                          text: 'YES',
                          onPress: () => {
                            cancelAndRefund();
                          },
                        },
                        {
                          text: 'NO',
                          onPress: () => { },
                        },
                      ],
                    );
                  }}>
                  {isLoading && cancel ? (
                    <ActivityIndicator
                      style={{}}
                      color={Colors.whiteColor}
                      size={'small'}
                    />
                  ) : (
                    <Text
                      style={[
                        {
                          textAlign: 'center',
                          fontWeight: '700',
                          color: Colors.whiteColor,
                          fontSize: 14,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      CANCEL & REFUND
                    </Text>
                  )}
                </TouchableOpacity>
              </View>
              <View style={{ height: 5 }} />

              {/* <View style={{ marginTop: 20, zIndex: -1, alignItems: 'center' }}>
              <TouchableOpacity
                disabled={isLoading}
                style={{ width: 100, height: 40, borderRadius: 10, borderWidth: 1, justifyContent: 'center', backgroundColor: Colors.secondaryColor, borderColor: Colors.secondaryColor }}
                onPress={() => {
                  Alert.alert(
                    'Info',
                    'Are you sure you want to notify your customer about current sender status?\nLet them decided further actions upon current order.',
                    [
                      {
                        text: 'OK',
                        onPress: () => {
                          notifyUser();
                        },
                      },
                      {
                        text: 'No',
                        onPress: () => { },
                      }
                    ]
                  )
                }}
              >
                {
                  isLoading
                    ?
                    <ActivityIndicator style={{
                    }} color={Colors.whiteColor} size={'small'} />
                    :
                    <Text style={{ textAlign: 'center', fontWeight: '700', color: Colors.whiteColor, fontSize: 16 }}>
                      Notify User
                    </Text>
                }
              </TouchableOpacity>
            </View>

            <View style={{ marginTop: 20, zIndex: -1, alignItems: 'center' }}>
              <TouchableOpacity
                disabled={isLoading}
                style={{ width: 140, height: 40, borderRadius: 10, borderWidth: 1, borderColor: 'red', backgroundColor: Colors.tabRed, justifyContent: 'center' }}
                onPress={() => {
                  Alert.alert(
                    'Cancel & Refund',
                    'Do you sure you want to cancel and refund order?',
                    [
                      {
                        text: 'Yes',
                        onPress: () => {
                          cancelAndRefund();
                        },
                      },
                      {
                        text: 'No',
                        onPress: () => { },
                      }
                    ]
                  )
                }}
              >
                {
                  isLoading
                    ?
                    <ActivityIndicator style={{
                    }} color={Colors.whiteColor} size={'small'} />
                    :
                    <Text style={{ textAlign: 'center', fontWeight: '700', color: Colors.whiteColor, fontSize: 16 }}>
                      Cancel & Refund
                    </Text>
                }
              </TouchableOpacity>
            </View> */}
            </View>
          </View>
        </ModalView>
        {/* ManageSenderModal End */}
      </View>
    </UserIdleWrapper>)
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  titles: {
    flex: 1,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },
  ManageFilterBox: {
    //width: windowWidth * 0.4,
    //height: windowHeight * 0.7,
    //borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  confirmBox: {
    width: 350,
    height: 237,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default TakeawayScreen;
