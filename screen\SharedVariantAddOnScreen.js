import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  TextInput,
  Modal as ModalComponent,
  PermissionsAndroid,
  Platform,
  ActivityIndicator,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import moment from 'moment';
import SideBar from './SideBar';
import { Text } from "react-native-fast-text";
import Colors from '../constant/Colors';
import Feather from 'react-native-vector-icons/Feather';
import Ionicon from 'react-native-vector-icons/Ionicons';
import Entypo from 'react-native-vector-icons/Entypo';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Styles from '../constant/Styles';
import {
  EMAIL_REPORT_TYPE,
  PRODUCT_SORT,
  PRODUCT_SORT_VALUE,
  PRODUCT_SORT_COMPARE_OPERATOR,
  PRODUCT_SORT_FIELD_TYPE_COMPARE,
  EXPAND_TAB_TYPE,
  ROLE_TYPE,
} from '../constant/common';
import {
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet
} from '../util/common';
import { CommonStore } from '../store/commonStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import Switch from 'react-native-switch-pro';
import { KeyboardAvoidingView } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import APILocal from '../util/apiLocalReplacers';
import Ionicons from 'react-native-vector-icons/Ionicons';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const SharedVariantAddOnScreen = (props) => {
  const { navigation } = props;
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  ////////////////////////////////////////////////////////////////////////////////////

  const ItemVariation = {
    VARIANT: 'VARIANT',
    ADD_ON: 'ADD-ON'
  };

  const commonChoicesBody = {
    name: null,
    price: null,
    choiceStockCount: null,
    orderIndex: 1,
  }

  const newVariantChoice = {
    ...commonChoicesBody,
  };

  const newAddOnChoice = {
    ...commonChoicesBody,
    minSelect: null,
    maxSelect: null,
  };

  const newSortingOption = {
    filter: '',
    displayDeleted: false,
    titleAscending: true,
    categoryAscending: null,
    numOfChoicesAscending: null,
    createdOnAscending: null,
    updatedOnAscending: null,
  }

  const updateSortingOption = (key) => {
    setSortingOption(prevOptions => ({
      ...newSortingOption,
      filter: prevOptions.filter,
      displayDeleted: prevOptions.displayDeleted,
      [key]: (prevOptions[key] != null) ? prevOptions[key] : true,
    }));
  };

  ////////////////////////////////////////////////////////////////////////////////////
  // Shared State Store
  const userName = UserStore.useState((s) => s.name);
  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);
  const outletSelectDropdownView = CommonStore.useState((s) => s.outletSelectDropdownView);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);
  const outletCategories = OutletStore.useState((s) => s.outletCategories);

  const allOutletsItemAddOn = CommonStore.useState((s) => s.allOutletsItemAddOn);
  const allOutletsItemAddOnChoiceDict = CommonStore.useState((s) => s.allOutletsItemAddOnChoiceDict);

  ////////////////////////////////////////////////////////////////////////////////////

  // [OutletItemCategoryId]: [OutletItemId, ...]
  const outletItemCategorizedByCategory = outletCategories.reduce((acc, category) => {
    acc[category.uniqueId] = outletItemsUnsorted.filter(item => {
      return item.categoryId === category.uniqueId;
    });

    return acc;
  }, {});

  const choicesLengthCount = () => {
    switch (selectedCreationOption) {
      case ItemVariation.VARIANT:
        return variantChoices.length;
      case ItemVariation.ADD_ON:
        return addOnChoices.length;
      default:
        return '-';
    }
  };

  ////////////////////////////////////////////////////////////////////////////////////
  // Local States

  const [temp, setTemp] = useState('');
  const [loading, setLoading] = useState(false);

  const [sharedVariantAddOnList, setSharedVariantAddOnList] = useState([]);
  const [sortedList, setSortedList] = useState([]);

  // CreationOption: ItemVariation.VARIANT / ItemVariation.ADD_ON
  const [selectedCreationOption, setSelectedCreationOption] = useState(ItemVariation.VARIANT);
  const [disabledCreationOption, setDisabledCreationOption] = useState(null);
  const [showVariantAddOnModal, setShowVariantAddOnModal] = useState(false);
  const [showBindingToOutletItemModal, setShowBindingToOutletItemModal] = useState(false);
  const [editingVariantAddOn, setEditingVariantAddOn] = useState(null);

  // States for Data Input Field
  // Variants
  const [name, setName] = useState('');             // String
  const [minSelect, setMinSelect] = useState('');   // String
  const [maxSelect, setMaxSelect] = useState('');   // String
  const [skipSc, setSkipSc] = useState(false);

  const [min, setMin] = useState('');   // String
  const [max, setMax] = useState('');   // String

  const [variantChoices, setVariantChoices] = useState([newVariantChoice]);   // Object {newVariantChoice}
  const [addOnChoices, setAddOnChoices] = useState([newAddOnChoice]);         // Object {newAddOnChoice}

  const [outletItemIdList, setOutletItemIdList] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(Object.keys(outletItemCategorizedByCategory)[0]);

  const [sortingOption, setSortingOption] = useState(newSortingOption);

  const [hiddenVariantAddOnIdDict, setHiddenVariantAddOnIdDict] = useState({});
  const [originalHqrState, setOriginalHqrState] = useState({});

  const ORDER_TYPES = [
    { label: 'Default', value: 'DEFAULT' },
    { label: 'Dine In', value: 'DINEIN' },
    { label: 'Takeaway', value: 'PICKUP' },
    { label: 'Other D', value: 'DELIVERY' },
  ];
  const [ots, setOts] = useState([]);

  const toggleOrderType = (value) => {
    if (ots.includes(value)) {
      setOts(prev => prev.filter(v => v !== value));
    } else {
      setOts(prev => [...prev, value]);
    }
  };

  const isSelected = (value) => ots.includes(value);
  // 7-May-2024 - Auto Select Feature
  const [groupAutoSelect, setGroupAutoSelect] = useState(false);

  ////////////////////////////////////////////////////////////////////////////////////
  // Local Methods
  // method for close button (JJ's comment)
  const handleCloseBindingModalAction = () => {
    setShowBindingToOutletItemModal(false);
    setEditingVariantAddOn(null);
    setLoading(false);
  };

  const handleCloseEditModalAction = () => {
    // Create Process
    if (!editingVariantAddOn) {
      setShowVariantAddOnModal(false)
    }
    // Edit Process
    else {
      setEditingVariantAddOn(null);
      setDisabledCreationOption(null);
      setShowVariantAddOnModal(false);
    };
  };

  // Create Shared Variant / AddOn
  const handleSaveButtonAction = () => {
    let validationErrors = validateVariantChoices();

    if (validationErrors.length > 0) {
      Alert.alert(
        'Error',
        validationErrors.join('\n'),
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false }
      );
    }
    // Is Editing Variant / AddOn
    else if (editingVariantAddOn) {
      actionEditVariantAddOn();
    }
    // Is Creating Variant / AddOn
    else {
      actionCreateVariantAddOn();
    }
  };

  const actionCreateVariantAddOn = async () => {
    setLoading(true);

    let commonBody = {
      merchantId,
      outletId: currOutletId,
      name,
      isGroupAutoSelect: groupAutoSelect,
    }

    switch (selectedCreationOption) {
      case ItemVariation.VARIANT: {
        let body = {
          ...commonBody,
          minSelect,
          maxSelect,
          variantChoices,
          skipSc,
          ots,
          // ...skipSc && {
          //   skipSc: skipSc,
          // },
        };

        APILocal.createSharedAddOn({ body })
          .then((result) => {
            if (result && result.status === 'success') {
              Alert.alert('Success', 'Shared Variant Created.');
            } else {
              Alert.alert('Error', 'Failed to create Shared Variant.');
            }

            setLoading(false);
            setShowVariantAddOnModal(false);
          });

        break;
      }
      case ItemVariation.ADD_ON: {
        let body = {
          ...commonBody,
          min,
          max,
          addOnChoices,
          skipSc,
          ots,

          // ...skipSc && {
          //   skipSc: skipSc,
          // },
        };

        APILocal.createSharedAddOn({ body })
          .then((result) => {
            if (result && result.status === 'success') {
              Alert.alert('Success', 'Shared AddOn Created.');
            } else {
              Alert.alert('Error', 'Failed to create Shared AddOn.');
            }

            setLoading(false);
            setShowVariantAddOnModal(false);
          });

        break;
      }
      default: {
        break;
      }
    }
  };

  const handleSwitchChange = (value, sharedVariant) => {
    console.log('switch status', value);
    setOriginalHqrState((prev) => ({
      ...prev,
      [sharedVariant.uniqueId]: hiddenVariantAddOnIdDict[sharedVariant.uniqueId] !== undefined ? hiddenVariantAddOnIdDict[sharedVariant.uniqueId] : sharedVariant.hqr,
    }));

    const updatedDict = {
      ...hiddenVariantAddOnIdDict,
      [sharedVariant.uniqueId]: value,
    };
    setHiddenVariantAddOnIdDict(updatedDict);

    const isVariant = sharedVariant.minSelect !== undefined && sharedVariant.maxSelect !== undefined;
    const body = {
      merchantId: sharedVariant.merchantId,
      outletId: sharedVariant.outletId,
      outletItemIdList: sharedVariant.outletItemIdList,
      name: sharedVariant.name,
      uniqueId: sharedVariant.uniqueId,
      ...(isVariant
        ? {
          minSelect: sharedVariant.minSelect,
          maxSelect: sharedVariant.maxSelect,
          variantChoices: sharedVariant.choices || [],
          ...(value !== undefined && { hqr: value }),
        }
        : {
          addOnChoices: sharedVariant.choices || [],
          ...(value !== undefined && { hqr: value }),
        }),
      ...(sharedVariant.skipSc !== undefined && { skipSc: sharedVariant.skipSc }),
      ...(sharedVariant.prevChoicesUniqueIdList && { prevChoicesUniqueIdList: sharedVariant.prevChoicesUniqueIdList }),
      ...(sharedVariant.ots !== undefined && { ots: sharedVariant.ots }),
    };

    console.log('Request body:', body);
    APILocal.updateSharedAddOn({ body })
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert('Success', 'Updated the shared add-on successfully.');
        } else {
          throw new Error('Update failed');
        }
      })
      .catch((error) => {
        console.error('Error updating shared add-on:', error);
        Alert.alert('Error', 'An error occurred while updating the shared add-on.');

        setHiddenVariantAddOnIdDict((prev) => ({
          ...prev,
          [sharedVariant.uniqueId]: originalHqrState[sharedVariant.uniqueId],
        }));
      });
  };

  ///////////////

  // Edit Shared Variant / AddOn
  const handleEditButtonAction = (item) => {
    setEditingVariantAddOn(item);
    setShowVariantAddOnModal(true);
  };

  const actionEditVariantAddOn = async () => {
    setLoading(true);

    let prevChoicesUniqueIdList = editingVariantAddOn.choices.map(item => item.uniqueId);

    let commonBody = {
      merchantId,
      outletId: currOutletId,
      outletItemId: editingVariantAddOn.outletItemId,
      outletItemIdList: editingVariantAddOn.outletItemIdList,
      uniqueId: editingVariantAddOn.uniqueId,
      hqr: editingVariantAddOn.hqr ? editingVariantAddOn.hqr : false,
      name,
      prevChoicesUniqueIdList,
      isGroupAutoSelect: groupAutoSelect || false,
    }

    switch (editingVariantAddOn.type) {
      case (ItemVariation.VARIANT): {
        let body = {
          ...commonBody,
          minSelect,
          maxSelect,
          variantChoices,
          skipSc,
          ots,

          // ...skipSc && {
          //   skipSc: skipSc
          // },
        }

        APILocal.updateSharedAddOn({ body })
          .then((result) => {
            if (result && result.status === 'success') {
              Alert.alert('Success', 'Shared Variant Updated.');
            } else {
              Alert.alert('Error', 'Failed to update Shared Variant.');
            }

            setLoading(false);
            setEditingVariantAddOn(false);
            setShowVariantAddOnModal(false);
          });

        break;
      }
      case (ItemVariation.ADD_ON): {
        let body = {
          ...commonBody,
          min,
          max,
          addOnChoices,
          skipSc,
          ots,

          // ...skipSc && {
          //   skipSc: skipSc,
          // },
        };

        APILocal.updateSharedAddOn({ body })
          .then((result) => {
            if (result && result.status === 'success') {
              Alert.alert('Success', 'Shared AddOn Updated');
            } else {
              Alert.alert('Error', 'Failed to update Shared AddOn.');
            }

            setLoading(false);
            setEditingVariantAddOn(false);
            setShowVariantAddOnModal(false);
          });

        break;
      }
      default: {
        break;
      }
    }
  };

  ///////////////

  // Delete Shared Variant / AddOn
  const handleDeleteButtonAction = (item) => {
    Alert.alert(
      'Confirm Delete',
      `Are you sure you want to delete item ['${item.name}'] ?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => actionDeleteVariantAddOn(item),
        },
      ]
    );
  };

  const actionDeleteVariantAddOn = async (item) => {
    setLoading(true);

    let body = {
      addOnItem: item,
      addOnUniqueId: item.uniqueId
    };

    APILocal.deleteSharedAddOn({ body })
      .then((result) => {
        if (result && result.status === 'success') {
          Alert.alert('Success', 'Item deleted.');
        } else {
          Alert.alert('Error', 'Failed to delete Shared Variant.');
        }
      });

    setLoading(false);
  };

  ///////////////

  // Update Outlet Items
  const handleUpdateOutletItemButtonAction = (item) => {
    setEditingVariantAddOn(item)
    setOutletItemIdList(item.outletItemIdList);
    setShowBindingToOutletItemModal(true);
  };

  const actionUpdateOutletItemIdList = async () => {
    setLoading(true);

    const isListUnchanged = JSON.stringify(editingVariantAddOn.outletItemIdList) === JSON.stringify(outletItemIdList);

    // No Changes
    if (isListUnchanged) {
      Alert.alert(
        'Success',
        'Outlet Item Id List Updated. *(Unchanged)',
        [
          {
            text: 'OK',
            onPress: () => {
              setShowBindingToOutletItemModal(false);
              setEditingVariantAddOn(null);
              setLoading(false);
            }
          }
        ]
      );
    }

    // Changes
    else {
      let body = {
        selectedAddOn: editingVariantAddOn,
        outletItemIds: outletItemIdList,
        prevOutletItemIds: editingVariantAddOn.outletItemIdList,
      };

      try {
        const result = await APILocal.updateSharedAddOnOutletItemList2(body);

        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Outlet Item Id List Updated.',
            [
              {
                text: 'OK',
                onPress: () => {
                  setShowBindingToOutletItemModal(false);
                  setEditingVariantAddOn(null);
                  setLoading(false);
                }
              }
            ]
          );
        } else {
          Alert.alert(
            'Error',
            'Failed to update OutletItemIdList.',
            [
              {
                text: 'OK',
                onPress: () => {
                  setShowBindingToOutletItemModal(false);
                  setEditingVariantAddOn(null);
                  setLoading(false);
                }
              }
            ]
          );
        }
      } catch (error) {
        Alert.alert(
          'Error',
          'Failed to update OutletItemIdList.',
          [
            {
              text: 'OK',
              onPress: () => {
                setShowBindingToOutletItemModal(false);
                setEditingVariantAddOn(null);
                setLoading(false);
              }
            }
          ]
        );
      }
    }
  };

  ///////////////

  // Return value : ItemVariation.VARIANT , ItemVariation.ADD_ON
  const isVariantOrAddOn = useCallback((item) => {
    if (item.minSelect !== undefined && item.maxSelect !== undefined) {
      return ItemVariation.VARIANT;
    } else {
      return ItemVariation.ADD_ON;
    }
  }, []);

  // Sorting Logic
  const sortItems = useMemo(() => {
    return (originalItems, options) => {
      let filteredItems = [...originalItems];

      // Filtering (displayDeleted && Search)
      filteredItems = filteredItems.filter((item) => {
        if (options.displayDeleted) {
          return true;
        } else {
          return !item.deletedAt;
        }
      });

      const searchText = options.filter.toLowerCase().trim();
      if (searchText !== '') {
        filteredItems = filteredItems.filter((item) =>
          item.name.toLowerCase().includes(searchText)
        );
      }

      filteredItems.sort((a, b) => {
        if (options.titleAscending === true) {
          return a.name.localeCompare(b.name);
        } else if (options.titleAscending === false) {
          return b.name.localeCompare(a.name);
        }
        return 0;
      });

      filteredItems.sort((a, b) => {
        if (options.categoryAscending === true) {
          return a.type.localeCompare(b.type);
        } else if (options.titleAscending === false) {
          return b.type.localeCompare(a.type);
        }
        return 0;
      });

      filteredItems.sort((a, b) => {
        if (options.numOfChoicesAscending === true) {
          return a.choices.length - b.choices.length;
        } else if (options.numOfChoicesAscending === false) {
          return b.choices.length - a.choices.length;
        }
        return 0;
      });

      filteredItems.sort((a, b) => {
        if (options.createdOnAscending === true) {
          return a.createdAt - b.createdAt;
        } else if (options.createdOnAscending === false) {
          return b.createdAt - a.createdAt;
        }
        return 0;
      });

      filteredItems.sort((a, b) => {
        if (options.updatedOnAscending === true) {
          return a.updatedAt - b.updatedAt;
        } else if (options.updatedOnAscending === false) {
          return b.updatedAt - a.updatedAt;
        }
        return 0;
      });

      return filteredItems;
    };
  }, []);

  // Checking if all OutletItem is selected under certain category
  const allItemSelected = useMemo(() => {
    const selectedCategoryItems = outletItemCategorizedByCategory[selectedCategory] || [];
    return selectedCategoryItems.length > 0 && selectedCategoryItems.every(item => outletItemIdList.includes(item.uniqueId));
  }, [outletItemIdList, outletItemCategorizedByCategory, selectedCategory]);

  ////////////////////////////////////////////////////////////////////////////////////
  // Validation methods

  const validateVariantChoices = () => {
    const errorMessages = [];

    if (!selectedCreationOption) {
      errorMessages.push('Invalid selectedCreationOption');
      return errorMessages;
    }

    const invalidName = !name || name.trim() === '';
    const invalidMinSelectVariant = !minSelect || Number.isNaN(parseInt(minSelect, 10)) || (parseInt(minSelect, 10)) < 0;
    const invalidMaxSelectVariant = !maxSelect || Number.isNaN((parseInt(maxSelect, 10))) || (parseInt(minSelect, 10)) > (parseInt(maxSelect, 10));

    const variantConditions = [invalidName, invalidMinSelectVariant, invalidMaxSelectVariant];
    const variantErrorMessage = ['Invalid name', 'Invalid minSelect', 'Invalid maxSelect'];

    const validateChoices = (item, index) => {
      const invalidOption = !item.name || item.name.trim() === '';
      const invalidMinSelectChoices = !item.minSelect || Number.isNaN(item.minSelect) || item.minSelect < 0;
      const invalidMaxChoiceChoices = !item.maxSelect || Number.isNaN(item.maxSelect) || item.minSelect > item.maxSelect;
      const invalidPrice = !item.price || Number.isNaN(parseFloat(item.price)) || item.price < 0;
      const invalidChoiceStockCount = !item.choiceStockCount || Number.isNaN(parseInt(item.choiceStockCount, 10)) || parseInt(item.choiceStockCount, 10) < 0;

      const choicesVariantConditions = [invalidOption, invalidPrice, invalidChoiceStockCount];
      const choicesVariantErrorMessages = [
        `[Option ${index + 1}] Invalid option name.`,
        `[Option ${index + 1}] Invalid price.`,
        `[Option ${index + 1}] Invalid stock count.`,
      ];

      switch (selectedCreationOption) {
        case ItemVariation.VARIANT: {
          choicesVariantConditions.forEach((condition, i) => {
            if (condition) {
              errorMessages.push(choicesVariantErrorMessages[i]);
            }
          });
          break;
        }
        case ItemVariation.ADD_ON: {
          let choicesAddOnConditions = [...choicesVariantConditions, invalidMinSelectChoices, invalidMaxChoiceChoices];
          let choicesAddOnErrorMessages = [
            ...choicesVariantErrorMessages,
            `[Option ${index + 1}] Invalid minimum selection number.`,
            `[Option ${index + 1}] Invalid maximum selection number.`,
          ];
          choicesAddOnConditions.forEach((condition, i) => {
            if (condition) {
              errorMessages.push(choicesAddOnErrorMessages[i]);
            }
          });
          break;
        }
        default: {
          break;
        }
      }
    }

    switch (selectedCreationOption) {
      case ItemVariation.VARIANT: {
        variantConditions.forEach((condition, i) => {
          if (condition) {
            errorMessages.push(variantErrorMessage[i]);
          }
        });

        variantChoices.forEach((item, index) => {
          validateChoices(item, index);
        });
        break;
      }
      case ItemVariation.ADD_ON: {
        variantConditions.forEach((condition, i) => {
          if (i === 1 || i === 2) { // Skip Condition index 1, 2
            return;
          }

          if (condition) {
            errorMessages.push(variantErrorMessage[i]);
          }
        });

        addOnChoices.forEach((item, index) => {
          validateChoices(item, index);
        });
        break;
      }
      default: {
        break;
      }
    }

    return errorMessages;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  // Local Methods for Choices

  const addChoices = () => {
    switch (selectedCreationOption) {
      case ItemVariation.VARIANT:
        setVariantChoices([...variantChoices, { ...newVariantChoice, orderIndex: variantChoices.length + 1 }]);
        break;
      case ItemVariation.ADD_ON:
        setAddOnChoices([...addOnChoices, { ...newAddOnChoice, orderIndex: addOnChoices.length + 1 }]);
        break;
      default:
        break;
    }
  };

  const removeChoices = (index) => {
    switch (selectedCreationOption) {
      case ItemVariation.VARIANT: {
        let newVariantChoices = [...variantChoices];
        newVariantChoices.splice(index, 1);
        setVariantChoices(newVariantChoices);
        break;
      }
      case ItemVariation.ADD_ON: {
        let newAddOnChoices = [...addOnChoices];
        newAddOnChoices.splice(index, 1);
        setAddOnChoices(newAddOnChoices);
        break;
      }
      default:
        break;
    }
  };

  // 6 Aug 2024 - These are used for Updating Choices only
  // UI rely on States, so require this methods to update
  const updateChoices = (index, field, updater) => {
    if (selectedCreationOption === ItemVariation.VARIANT) {
      setVariantChoices((prevChoices) => {
        const updatedChoices = [...prevChoices];
        updatedChoices[index] = { ...updatedChoices[index], [field]: updater(updatedChoices[index][field]) };
        return updatedChoices;
      });
    } else {
      setAddOnChoices((prevChoices) => {
        const updatedChoices = [...prevChoices];
        updatedChoices[index] = { ...updatedChoices[index], [field]: updater(updatedChoices[index][field]) };
        return updatedChoices;
      });
    }
  };

  const handleFocus = (index, field) => {
    setTemp((selectedCreationOption === ItemVariation.VARIANT ? variantChoices : addOnChoices)[index][field]);
    updateChoices(index, field, () => '');
  };

  const handleEndEditing = (index, field) => {
    updateChoices(index, field, (currentValue) => (currentValue === '' ? temp : currentValue));
  };

  const handleChangeText = (index, field, text) => {
    updateChoices(index, field, () => text);
  };

  ////////////////////////////////////////////////////////////////////////////////////
  // Auto Clear State when Editing or Updating VariantAddOn

  useEffect(() => {
    if (!editingVariantAddOn) {
      setName('');
      setMinSelect('');
      setMaxSelect('');
      setVariantChoices([newVariantChoice]);
      setAddOnChoices([newAddOnChoice]);
      setSelectedCreationOption(ItemVariation.VARIANT);
      setSkipSc(false);
      setOts([]);
      setGroupAutoSelect(false);
    }
    else {
      let item = editingVariantAddOn;

      setName(item.name);
      setMin('');
      setMax('');
      setSelectedCreationOption(item.type);
      setDisabledCreationOption(item.type === ItemVariation.VARIANT ? ItemVariation.ADD_ON : ItemVariation.VARIANT)
      setSkipSc(false);
      setOts(item.ots || []);
      setGroupAutoSelect(item.isGroupAutoSelect);

      switch (item.type) {
        case ItemVariation.VARIANT: {
          setMinSelect(item.minSelect.toString());
          setMaxSelect(item.maxSelect.toString());
          setSkipSc(item.skipSc ? item.skipSc : false);
          setOts(item.ots || []);

          let choices = item.choices.map(choice => ({
            ...choice,
            price: choice.price.toString(),
            choiceStockCount: choice.choiceStockCount.toString()
          }));

          choices.sort((a, b) => {
            const aOrderIndex = a.orderIndex !== undefined ? a.orderIndex : Infinity;
            const bOrderIndex = b.orderIndex !== undefined ? b.orderIndex : Infinity;

            if (aOrderIndex === Infinity && bOrderIndex === Infinity) {
              return 0;
            }

            return aOrderIndex - bOrderIndex;
          });

          setVariantChoices(choices)
          break;
        }
        case ItemVariation.ADD_ON: {
          setMin((item.min ? item.min : 0).toString());
          setMax((item.max ? item.max : 0).toString());
          setSkipSc(item.skipSc ? item.skipSc : false);
          setOts(item.ots || []);

          let choices = item.choices.map(choice => ({
            ...choice,
            minSelect: choice.minSelect.toString(),
            maxSelect: choice.maxSelect.toString(),
            price: choice.price.toString(),
            choiceStockCount: choice.choiceStockCount.toString()
          }));

          choices.sort((a, b) => {
            const aOrderIndex = a.orderIndex !== undefined ? a.orderIndex : Infinity;
            const bOrderIndex = b.orderIndex !== undefined ? b.orderIndex : Infinity;

            if (aOrderIndex === Infinity && bOrderIndex === Infinity) {
              return 0;
            }

            return aOrderIndex - bOrderIndex;
          });

          setAddOnChoices(choices)
          break;
        }
        default: {
          break;
        }
      };
    }
  }, [editingVariantAddOn])

  ////////////////////////////////////////////////////////////////////////////////////
  // For Sorting

  // Filtering Shared Variant & Add Ons
  useEffect(() => {
    let sharedAddOnList = allOutletsItemAddOn.filter(obj => obj.isShared);
    const combinedAddOnList = sharedAddOnList.map(item => ({
      ...item,
      type: isVariantOrAddOn(item),
      choices: allOutletsItemAddOnChoiceDict[item.uniqueId],
    }))

    setSharedVariantAddOnList(combinedAddOnList);
  }, [allOutletsItemAddOn, allOutletsItemAddOnChoiceDict, isVariantOrAddOn])

  // Update Sorted Result
  useEffect(() => {
    const newFilteredItems = sortItems(sharedVariantAddOnList, sortingOption);

    // Update only if results are not same
    if (JSON.stringify(newFilteredItems) !== JSON.stringify(sortedList)) {
      setSortedList(newFilteredItems);
    }
  }, [sharedVariantAddOnList, sortingOption, sortItems, sortedList]);

  ////////////////////////////////////////////////////////////////////////////////////
  // Display Method

  const renderInputForm = () => {
    return (
      <View style={{
        flex: 1,
        width: '100%',
        flexDirection: 'column',
        alignItems: 'center',
        marginVertical: '2%'
      }}>

        {/* Modal Title */}
        <Text style={{
          fontSize: switchMerchant ? 15 : 20,
          fontFamily: 'NunitoSans-Bold',
          marginVertical: '1%'
        }}>
          Option ({selectedCreationOption === ItemVariation.VARIANT ? "Single" : "Multiple"})
        </Text>

        {/* FlexRowContainer - Name, (MinSelect, MaxSelect) */}
        <View style={{
          ...styles.flexRowContainer,
          width: '100%',
          height: '12.5%'
        }}>

          {/* name Field */}
          <View style={{
            width: selectedCreationOption === ItemVariation.VARIANT ? '35%' : '35%',
            height: '100%',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            marginHorizontal: 10,
          }}>
            <Text style={{
              fontSize: switchMerchant ? 10 : 18,
              fontFamily: 'NunitoSans-SemiBold',
              marginHorizontal: 10
            }}>
              Title
            </Text>

            <TextInput
              underlineColorAndroid={Colors.fieldtBgColor}
              style={{
                flex: 1,
                height: '100%',
                paddingLeft: 10,
                backgroundColor: Colors.fieldtBgColor,
                borderRadius: 5,
                borderWidth: 1,
                borderColor: '#E5E5E5',
                fontSize: switchMerchant ? 10 : 14,
              }}
              placeholder="Category"
              placeholderTextColor={Platform.select({
                ios: '#a9a9a9',
              })}
              clearTextOnFocus
              onChangeText={(text) => {
                setName(text);
              }}
              value={name}
            />
          </View>

          {/* Display only selectedCreationOption is VARIANT */}
          {selectedCreationOption === ItemVariation.VARIANT ? (
            <>
              {/* minSelect Field */}
              <View style={{
                flex: 1,
                height: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                marginHorizontal: 10,
              }}>

                <Text style={{
                  width: '70%',
                  fontSize: switchMerchant ? 10 : 18,
                  fontFamily: 'NunitoSans-SemiBold',
                  textAlign: 'right',
                  marginRight: 5,
                }}>
                  Min Choice
                </Text>

                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    flex: 1,
                    height: '100%',
                    backgroundColor: Colors.fieldtBgColor,
                    borderRadius: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  textAlign='center'
                  placeholder="1"
                  placeholderTextColor={Platform.select({
                    ios: '#a9a9a9',
                  })}
                  selectTextOnFocus
                  // clearTextOnFocus

                  // onFocus={() => {
                  //   setTemp(minSelect);
                  //   setMinSelect('');
                  // }}
                  // onEndEditing={() => {
                  //   if (minSelect.trim() === '') {
                  //     setMinSelect(temp);
                  //   };
                  // }}
                  onChangeText={(text) => {
                    setMinSelect(text);
                  }}
                  value={minSelect}
                />
              </View>

              {/* maxSelect Field */}
              <View style={{
                flex: 1,
                height: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                marginHorizontal: 10,
              }}>

                <Text style={{
                  width: '70%',
                  fontSize: switchMerchant ? 10 : 18,
                  fontFamily: 'NunitoSans-SemiBold',
                  textAlign: 'right',
                  marginRight: 5,
                }}>
                  Max Choice
                </Text>

                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    flex: 1,
                    height: '100%',
                    backgroundColor: Colors.fieldtBgColor,
                    borderRadius: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    fontSize: switchMerchant ? 10 : 14,
                  }}
                  textAlign='center'
                  placeholder="1"
                  placeholderTextColor={Platform.select({
                    ios: '#a9a9a9',
                  })}
                  selectTextOnFocus
                  // clearTextOnFocus

                  // onFocus={() => {
                  //   setTemp(maxSelect);
                  //   setMaxSelect('');
                  // }}
                  // onEndEditing={() => {
                  //   if (maxSelect.trim() === '') {
                  //     setMaxSelect(temp);
                  //   };
                  // }}
                  onChangeText={(text) => {
                    setMaxSelect(text);
                  }}
                  value={maxSelect}
                />

              </View>

              {/* skipSc Field */}
              <View style={{
                flex: 1,
                height: '100%',
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                marginHorizontal: 10,
              }}>

                <Text style={{
                  width: '70%',
                  fontSize: switchMerchant ? 10 : 18,
                  fontFamily: 'NunitoSans-SemiBold',
                  textAlign: 'right',
                  marginRight: 5,
                }}>
                  Skip SC
                </Text>

                <View style={{ marginLeft: switchMerchant ? 0 : 0 }}>
                  <Switch
                    width={42}
                    style={
                      {
                        top: 1,
                      }
                    }
                    // disabled={true}
                    value={skipSc}
                    circleColorActive={Colors.primaryColor}
                    circleColorInactive={Colors.fieldtTxtColor}
                    backgroundActive="#dddddd"
                    onSyncPress={(value) => {
                      setSkipSc(value);
                    }}
                  />
                </View>
              </View>
            </>
          ) : <></>}

          {/* Display only selectedCreationOption is ADD_ON */}
          {
            selectedCreationOption === ItemVariation.ADD_ON ? (
              <>
                {/* minSelect Field */}
                <View style={{
                  flex: 1,
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginHorizontal: 10,
                }}>

                  <Text style={{
                    width: '70%',
                    fontSize: switchMerchant ? 10 : 18,
                    fontFamily: 'NunitoSans-SemiBold',
                    textAlign: 'right',
                    marginRight: 5,
                  }}>
                    Min Choice
                  </Text>

                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{
                      flex: 1,
                      height: '100%',
                      backgroundColor: Colors.fieldtBgColor,
                      borderRadius: 5,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                    textAlign='center'
                    placeholder="0"
                    placeholderTextColor={Platform.select({
                      ios: '#a9a9a9',
                    })}
                    clearTextOnFocus

                    onFocus={() => {
                      setTemp(min);
                      setMin('');
                    }}
                    onEndEditing={() => {
                      if (min.trim() === '') {
                        setMin(temp);
                      };
                    }}
                    onChangeText={(text) => {
                      setMin(text);
                    }}
                    value={min}
                  />
                </View>

                {/* maxSelect Field */}
                <View style={{
                  flex: 1,
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginHorizontal: 10,
                }}>

                  <Text style={{
                    width: '70%',
                    fontSize: switchMerchant ? 10 : 18,
                    fontFamily: 'NunitoSans-SemiBold',
                    textAlign: 'right',
                    marginRight: 5,
                  }}>
                    Max Choice
                  </Text>

                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{
                      flex: 1,
                      height: '100%',
                      backgroundColor: Colors.fieldtBgColor,
                      borderRadius: 5,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      fontSize: switchMerchant ? 10 : 14,
                    }}
                    textAlign='center'
                    placeholder="0"
                    placeholderTextColor={Platform.select({
                      ios: '#a9a9a9',
                    })}
                    clearTextOnFocus

                    onFocus={() => {
                      setTemp(max);
                      setMax('');
                    }}
                    onEndEditing={() => {
                      if (max.trim() === '') {
                        setMax(temp);
                      };
                    }}
                    onChangeText={(text) => {
                      setMax(text);
                    }}
                    value={max}
                  />

                </View>

                {/* skipSc Field */}
                <View style={{
                  flex: 1,
                  height: '100%',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginHorizontal: 10,
                }}>

                  <Text style={{
                    width: '70%',
                    fontSize: switchMerchant ? 10 : 18,
                    fontFamily: 'NunitoSans-SemiBold',
                    textAlign: 'right',
                    marginRight: 5,
                  }}>
                    Skip SC
                  </Text>

                  <View style={{ marginLeft: switchMerchant ? 0 : 0 }}>
                    <Switch
                      width={42}
                      style={
                        {
                          top: 1,
                        }
                      }
                      // disabled={true}
                      value={skipSc}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                      onSyncPress={(value) => {
                        setSkipSc(value);
                      }}
                    />
                  </View>
                </View>
              </>
            ) : <></>
          }
        </View >

        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', width: '90%', marginTop: 15, }}>
          <View>
            <Text style={{
              fontSize: switchMerchant ? 10 : 18,
              fontFamily: 'NunitoSans-SemiBold',
              textAlign: 'right',
              marginRight: 10,
            }}>
              Order Types
            </Text>
          </View>
          <View style={{ alignItems: 'center', flexDirection: "row", marginLeft: 10, padding: 10 }}>
            {ORDER_TYPES.map(({ label, value }, index) => (
              <TouchableOpacity
                key={label + index}
                onPress={() => toggleOrderType(value)}
                style={{
                  padding: 10,
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
              >
                <View
                  style={{
                    width: 22,
                    height: 22,
                    borderWidth: 1.5,
                    borderColor: isSelected(value) ? Colors.primaryColor : '#333',
                    backgroundColor: isSelected(value) ? Colors.primaryColor : '#fff',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: 8,
                  }}
                >
                  {isSelected(value) && <Icon name="check" size={16} color="#fff" />}
                </View>
                <Text>{label}</Text>
              </TouchableOpacity>
            ))}
          </View>
          <View style={{
            flex: 1,
            height: '100%',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            marginHorizontal: 10,
          }}>

            <Text style={{
              width: '70%',
              fontSize: switchMerchant ? 10 : 18,
              fontFamily: 'NunitoSans-SemiBold',
              textAlign: 'right',
              marginRight: 5,
            }}>
              AutoSelect
            </Text>

            <View style={{ marginLeft: switchMerchant ? 0 : 0 }}>
              <Switch
                width={42}
                style={
                  {
                    top: 1,
                  }
                }
                // disabled={true}
                value={groupAutoSelect}
                circleColorActive={Colors.primaryColor}
                circleColorInactive={Colors.fieldtTxtColor}
                backgroundActive="#dddddd"
                onSyncPress={(value) => {
                  setGroupAutoSelect(value);
                }}
              />
            </View>
          </View>
        </View>

        {/* Divider */}
        < View style={{
          width: '100%',
          borderWidth: 0.5,
          borderColor: 'black',
          marginVertical: '2%',
          opacity: 0.1
        }} />

        {/* Display Variant/AddOn Choices */}
        <View style={{
          flex: 1,
          width: '99%',
        }}>

          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>

            <Text style={{
              fontSize: switchMerchant ? 14 : 17,
              fontFamily: 'Nunitosans-Bold',
            }}>
              Options ({choicesLengthCount()}):
            </Text>

            <TouchableOpacity style={{ marginLeft: 10 }} onPress={() => (addChoices())}>
              <Feather
                name={"plus-square"}
                size={switchMerchant ? 17 : 20}
                color={Colors.primaryColor}
              />
            </TouchableOpacity>

          </View>

          {/* <KeyboardAwareScrollView> */}
          <ScrollView>
            {selectedCreationOption === ItemVariation.VARIANT ? (
              variantChoices.map((item, index) => (
                renderChoices(item, index, variantChoices.length)
              ))
            ) : (
              addOnChoices.map((item, index) => (
                renderChoices(item, index, addOnChoices.length)
              ))
            )}
          </ScrollView>
          {/* </KeyboardAwareScrollView> */}

        </View>
      </View >
    );
  };

  const renderChoices = (item, index, length) => {
    const isAddOn = selectedCreationOption === ItemVariation.ADD_ON;
    const lastItem = length === 1;

    return (
      <View style={{
        flexDirection: 'row',
        height: windowHeight * 0.06,
        width: '100%',
        alignItems: 'center',
        marginTop: (index === 0) ? 0 : 10,
      }}>

        {/* Remove Choice Button */}
        <View style={{
          marginHorizontal: 10
        }}>
          <TouchableOpacity onPress={() => { removeChoices(index) }} disabled={lastItem}>
            <Feather
              name={lastItem ? "meh" : "minus-circle"}
              size={switchMerchant ? 17 : 20}
              color={lastItem ? Colors.descriptionColor : "#eb3446"}
            />
          </TouchableOpacity>
        </View>

        {/* Name */}
        <View style={{
          width: isAddOn ? '20%' : '30%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
          marginHorizontal: '1%',
        }}>
          <TextInput
            key={index}
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 5,
              borderWidth: 1,
              borderColor: '#E5E5E5',
              paddingLeft: 10,
              fontSize: switchMerchant ? 10 : 14,
            }}
            placeholder="Option Name"
            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            clearTextOnFocus
            onChangeText={(text) => handleChangeText(index, 'name', text)}
            value={item.name}
          />
        </View>

        {/* MinChoice & MaxChoice */}
        {isAddOn && (
          <View style={{
            ...styles.flexRowContainer,
            width: isAddOn ? '14.5%' : '17.5%',
            marginHorizontal: 10,
          }}>
            {/* MinSelect */}
            <View style={{
              flexDirection: 'column',
              width: '40%'
            }}>
              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                style={{
                  height: '100%',
                  backgroundColor: Colors.fieldtBgColor,
                  borderRadius: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  fontSize: switchMerchant ? 10 : 14,
                }}
                textAlign='center'
                placeholder="MIN"
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                keyboardType="decimal-pad"
                clearTextOnFocus

                onFocus={() => handleFocus(index, 'minSelect')}
                onEndEditing={() => handleEndEditing(index, 'minSelect')}
                onChangeText={(text) => handleChangeText(index, 'minSelect', text)}
                value={item.minSelect}
              />
            </View>

            <Text style={{
              flex: 1,
              fontSize: switchMerchant ? 10 : 20,
              fontFamily: 'NunitoSans-Bold',
              textAlign: 'center'
            }}>
              -
            </Text>

            {/* MaxSelect */}
            <View style={{
              flexDirection: 'column',
              width: '40%'
            }}>

              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                style={{
                  height: '100%',
                  backgroundColor: Colors.fieldtBgColor,
                  borderRadius: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  fontSize: switchMerchant ? 10 : 14,
                }}
                textAlign='center'
                placeholder="MAX"
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                keyboardType="decimal-pad"
                clearTextOnFocus

                onFocus={() => handleFocus(index, 'maxSelect')}
                onEndEditing={() => handleEndEditing(index, 'maxSelect')}
                onChangeText={(text) => handleChangeText(index, 'maxSelect', text)}
                value={item.maxSelect}
              />
            </View>
          </View>
        )}

        {/* Price */}
        <View style={{
          width: isAddOn ? '12.5%' : '15%',
          height: '100%',
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          marginHorizontal: '1%',
        }}>
          <Text style={{
            fontSize: switchMerchant ? 10 : 20,
            fontFamily: 'NunitoSans-Bold',
            marginRight: 10,
          }}>
            RM
          </Text>

          <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              flex: 1,
              height: '100%',
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 5,
              borderWidth: 1,
              borderColor: '#E5E5E5',
              fontSize: switchMerchant ? 10 : 14,
            }}
            textAlign={'center'}
            placeholder="0.00"
            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType="decimal-pad"
            clearTextOnFocus

            onFocus={() => handleFocus(index, 'price')}
            onEndEditing={() => handleEndEditing(index, 'price')}
            onChangeText={(text) => handleChangeText(index, 'price', text)}
            value={item.price}
          />
        </View>

        {/* Stock */}
        <View style={{
          width: isAddOn ? '6.5%' : '10%',
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
          marginHorizontal: '1%',
        }}>
          <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: Colors.fieldtBgColor,
              borderWidth: 1,
              borderRadius: 5,
              borderColor: '#E5E5E5',
              fontSize: switchMerchant ? 10 : 14,
            }}
            textAlign={'center'}
            placeholder="0"
            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType="decimal-pad"
            clearTextOnFocus

            onFocus={() => handleFocus(index, 'choiceStockCount')}
            onEndEditing={() => handleEndEditing(index, 'choiceStockCount')}
            onChangeText={(text) => handleChangeText(index, 'choiceStockCount', text)}
            value={item.choiceStockCount}
          />
        </View>

        <View style={{
          marginHorizontal: 10,
          width: isAddOn ? '13%' : '15%',
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
          <TouchableOpacity onPress={() => (moveChoicesToTop(item))}>
            <Entypo name='align-top' size={25} color={Colors.primaryColor} />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => (moveChoicesBackward(item))}>
            <Entypo name='chevron-down' size={25} color={Colors.primaryColor} />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => (moveChoicesForward(item))}>
            <Entypo name='chevron-up' size={25} color={Colors.primaryColor} />
          </TouchableOpacity>
        </View>

        {groupAutoSelect && (
          <View style={{
            width: '12%',
            height: '100%',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            marginHorizontal: 10,
          }}>

            <Text style={{
              // width: '70%',
              fontSize: switchMerchant ? 10 : 18,
              fontFamily: 'NunitoSans-SemiBold',
              textAlign: 'right',
              marginRight: 5,
            }}>
              AutoSelect
            </Text>

            <View style={{ marginLeft: switchMerchant ? 0 : 0 }}>
              <TouchableOpacity
                onPress={() => {
                  let autoSelectCount = 0;
                  let maxLimit = 0;

                  if (selectedCreationOption === ItemVariation.VARIANT) {
                    variantChoices.forEach(choice => {
                      if (choice.isAutoSelect && choice !== item) {
                        autoSelectCount++;
                      }
                    });
                    if (!item.isAutoSelect) autoSelectCount++;

                    maxLimit = parseInt(maxSelect, 10) || 0;
                  }
                  else {
                    addOnChoices.forEach(choice => {
                      if (choice.isAutoSelect && choice !== item) {
                        autoSelectCount++;
                      }
                    });
                    if (!item.isAutoSelect) autoSelectCount++;

                    maxLimit = parseInt(max, 10) || 0;
                  }

                  if (maxLimit > 0 && autoSelectCount > maxLimit) {
                    Alert.alert(
                      'Auto-Select Limit Exceeded',
                      `You can only auto-select up to ${maxLimit} choices.`,
                      [{ text: 'OK' }]
                    );
                  }
                  else {
                    handleChangeText(index, 'isAutoSelect', !item.isAutoSelect);
                  }
                }}
              >
                <View style={{
                  height: windowWidth * 0.02,
                  width: windowWidth * 0.02,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: item?.isAutoSelect ? Colors.primaryColor : Colors.fieldtTxtColor,
                }}>
                  <Ionicons name="checkmark-sharp" size={13} color={'#FFFFFF'} style={{ margin: 2 }} />
                </View>
              </TouchableOpacity>

              {/* <Switch
                width={42}
                style={{ top: 1 }}
                value={item.isAutoSelect || false}
                circleColorActive={Colors.primaryColor}
                circleColorInactive={Colors.fieldtTxtColor}
                backgroundActive="#dddddd"
                onSyncPress={(value) => {
                  // If Toggle On, Checking
                  if (value) {
                    let autoSelectCount = 0;
                    let maxLimit = 0;
                    
                    if (selectedCreationOption === ItemVariation.VARIANT) {
                      variantChoices.forEach(choice => {
                        if (choice.isAutoSelect && choice !== item) {
                          autoSelectCount++;
                        }
                      });
                      if (value) autoSelectCount++;
                      
                      maxLimit = parseInt(maxSelect, 10) || 0;
                    } 
                    else {
                      addOnChoices.forEach(choice => {
                        if (choice.isAutoSelect && choice !== item) {
                          autoSelectCount++;
                        }
                      });
                      if (value) autoSelectCount++;
                      
                      maxLimit = parseInt(max, 10) || 0;
                    }
                    
                    // Limit Check
                    if (maxLimit > 0 && autoSelectCount > maxLimit) {
                      Alert.alert(
                        'Auto-Select Limit Exceeded',
                        `You can only auto-select up to ${maxLimit} choices.`,
                        [{ text: 'OK' }]
                      );
                    } 
                    else {
                      handleChangeText(index, 'isAutoSelect', value);
                    }
                  } 
                  else {
                    handleChangeText(index, 'isAutoSelect', value);
                  }
                }}
              /> */}
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderVariantListHeader = () => {
    return (
      <View style={{
        width: '100%',
        borderRadius: 5,
        borderBottomWidth: 1,
        borderColor: Colors.blackColor,
        backgroundColor: Colors.whiteColor,
      }}>
        <View style={{ flexDirection: 'row', marginVertical: windowHeight * 0.015, marginHorizontal: '1%' }}>
          <View style={{ ...styles.flexRowContainer, width: '20%', justifyContent: 'flex-start' }}>
            <TouchableOpacity
              style={{ ...styles.flexRowContainer }}
              onPress={() => { updateSortingOption('titleAscending') }}
            >
              <Text style={styles.fontStyle}>Title</Text>
              <View>
                <Entypo
                  name="triangle-up"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.titleAscending === true ? Colors.secondaryColor : Colors.descriptionColor}
                />

                <Entypo
                  name="triangle-down"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.titleAscending === false ? Colors.secondaryColor : Colors.descriptionColor}
                />
              </View>
            </TouchableOpacity>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '8%', justifyContent: 'flex-start' }}>
            <TouchableOpacity
              style={{ ...styles.flexRowContainer }}
              onPress={() => { updateSortingOption('categoryAscending') }}
            >
              <Text style={styles.fontStyle}>Type</Text>
              <View>
                <Entypo
                  name="triangle-up"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.categoryAscending === true ? Colors.secondaryColor : Colors.descriptionColor}
                />

                <Entypo
                  name="triangle-down"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.categoryAscending === false ? Colors.secondaryColor : Colors.descriptionColor}
                />
              </View>
            </TouchableOpacity>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '8%', justifyContent: 'flex-start' }}>
            <Text style={styles.fontStyle}>Min - Max</Text>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '8%', justifyContent: 'flex-start' }}>
            <TouchableOpacity
              style={{ ...styles.flexRowContainer }}
              onPress={() => { updateSortingOption('numOfChoicesAscending') }}
            >
              <Text style={styles.fontStyle}>Choices Available</Text>
              <View>
                <Entypo
                  name="triangle-up"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.numOfChoicesAscending === true ? Colors.secondaryColor : Colors.descriptionColor}
                />

                <Entypo
                  name="triangle-down"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.numOfChoicesAscending === false ? Colors.secondaryColor : Colors.descriptionColor}
                />
              </View>
            </TouchableOpacity>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '18%', justifyContent: 'flex-start' }}>
            <Text style={styles.fontStyle}>Attached Items</Text>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '9%' }}>
            <TouchableOpacity
              style={{ ...styles.flexRowContainer }}
              onPress={() => { updateSortingOption('createdOnAscending') }}
            >
              <Text style={styles.fontStyle}>Create On</Text>
              <View>
                <Entypo
                  name="triangle-up"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.createdOnAscending === true ? Colors.secondaryColor : Colors.descriptionColor}
                />

                <Entypo
                  name="triangle-down"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.createdOnAscending === false ? Colors.secondaryColor : Colors.descriptionColor}
                />
              </View>
            </TouchableOpacity>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '9%' }}>
            <TouchableOpacity
              style={{ ...styles.flexRowContainer }}
              onPress={() => { updateSortingOption('updatedOnAscending') }}
            >
              <Text style={styles.fontStyle}>Last Update</Text>
              <View>
                <Entypo
                  name="triangle-up"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.updatedOnAscending === true ? Colors.secondaryColor : Colors.descriptionColor}
                />

                <Entypo
                  name="triangle-down"
                  size={switchMerchant ? 7 : 14}
                  color={sortingOption.updatedOnAscending === false ? Colors.secondaryColor : Colors.descriptionColor}
                />
              </View>
            </TouchableOpacity>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '9%' }}>
            <Text style={styles.fontStyle}>{'Hide from\nQR'}</Text>
          </View>


          <View style={{ ...styles.flexRowContainer, width: '10%' }} />
        </View >
      </View >
    );
  };

  const renderVariantListItems = ({ item, index }) => {
    const sharedVariant = item;

    const outletItemNameList = outletItemsUnsorted
      .filter(outletItem =>
        sharedVariant.outletItemIdList.includes(outletItem.uniqueId)
      )
      .map(outletItem => outletItem.name);

    return (
      <View style={{
        width: '100%',
        height: 'auto',
        marginVertical: windowHeight * 0.015,
      }}>
        <TouchableOpacity style={{ flexDirection: 'row', marginVertical: windowHeight * 0.005, marginHorizontal: '1%' }}>
          <View style={{ ...styles.flexRowContainer, width: '20%', justifyContent: 'flex-start' }}>
            <Text>{sharedVariant.name}</Text>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '8%', justifyContent: 'flex-start' }}>
            <Text>{sharedVariant.type}</Text>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '8%', justifyContent: 'flex-start' }}>
            <Text>{`${(sharedVariant && sharedVariant.minSelect != null ? sharedVariant.minSelect :
              (
                (sharedVariant.min && sharedVariant.max) ? sharedVariant.min : ''
              )
            )} - ${(sharedVariant && sharedVariant.maxSelect != null ? sharedVariant.maxSelect :
              (
                (sharedVariant.min && sharedVariant.max) ? sharedVariant.max : ''
              )
            )}`}</Text>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '8%', justifyContent: 'flex-start' }}>
            <Text>{Array.isArray(sharedVariant.choices) ? sharedVariant.choices.length : '-'}</Text>
          </View>

          <View style={{ ...styles.flexRowContainer, width: '18%', justifyContent: 'flex-start' }}>
            <Text
              style={{
                maxWidth: '95%',
              }}
              numberOfLines={2}
            >
              {sharedVariant.outletItemIdList.length === 0 ? 'No Item Attached' : `${sharedVariant.outletItemIdList.length} [${outletItemNameList}]`}
            </Text>
          </View>

          <View style={{ ...styles.flexRowContainer, flexDirection: 'column', width: '9%' }}>
            <Text style={styles.fontStyle}>{moment(sharedVariant.createdAt).format('D MMM YYYY')}</Text>
            <Text style={styles.fontStyle}>{moment(sharedVariant.createdAt).format('h:mm:ss a')}</Text>
          </View>

          <View style={{ ...styles.flexRowContainer, flexDirection: 'column', width: '9%' }}>
            <Text style={styles.fontStyle}>{moment(sharedVariant.updatedAt).format('D MMM YYYY')}</Text>
            <Text style={styles.fontStyle}>{moment(sharedVariant.updatedAt).format('h:mm:ss a')}</Text>
          </View>

          <View style={{ ...styles.flexRowContainer, flexDirection: 'column', width: '9%' }}>
            <Switch
              value={hiddenVariantAddOnIdDict[sharedVariant.uniqueId] !== undefined ? hiddenVariantAddOnIdDict[sharedVariant.uniqueId] : sharedVariant.hqr}
              onSyncPress={(value) => handleSwitchChange(value, sharedVariant)}
              width={switchMerchant ? 34 : 36}
              circleColorActive={Colors.primaryColor}
              circleColorInactive={Colors.fieldtTxtColor}
              backgroundActive="#dddddd"
            />
          </View>

          <View style={{ ...styles.flexRowContainer, justifyContent: 'space-evenly', width: '10%' }}>
            {/* Edit Button */}
            <TouchableOpacity
              disabled={loading}
              onPress={() => { handleEditButtonAction(sharedVariant) }}
            >
              <AntDesign name="edit" size={20} color={Colors.primaryColor} />
            </TouchableOpacity>

            {/* Update Item Button */}
            <TouchableOpacity
              disabled={loading}
              onPress={() => { handleUpdateOutletItemButtonAction(sharedVariant) }}
            >
              <AntDesign name="addfolder" size={20} color={Colors.primaryColor} />
            </TouchableOpacity>

            {/*Delete Button */}
            <TouchableOpacity
              disabled={loading}
              onPress={() => { handleDeleteButtonAction(sharedVariant) }}
            >
              <AntDesign name="delete" size={20} color={Colors.tabRed} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity >
      </View >
    )
  };

  const renderListEmpty = () => {
    return (
      <View style={{ ...styles.flexRowContainer, flex: 1, height: '100%' }}>
        <Text style={{
          fontFamily: 'NunitoSans-Regular',
          fontSize: switchMerchant ? 10 : 16,
          color: Colors.blackColor,
          height: '100%',
          marginTop: '30%'
        }}>
          No shared variants & add-ons yet
        </Text>
      </View>
    )
  };

  // left list (JJ's comment)
  const renderOutletCategoriesItems = ({ item }) => {
    let isSelectedCategory = item.uniqueId === selectedCategory;

    return (
      <TouchableOpacity
        onPress={() => { setSelectedCategory(item.uniqueId) }}
        style={{
          width: '100%',
          paddingVertical: '10%',
          backgroundColor: isSelectedCategory ? Colors.primaryColor : Colors.lightGrey,
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <Text style={{ color: isSelectedCategory ? Colors.whiteColor : Colors.blackColor, fontSize: 14 }}>{item.name}</Text>
      </TouchableOpacity>
    );
  };

  // right side (JJ's comment)
  const renderSelectedOutletCategoriesItems = ({ item, index }) => {
    let isSelectedItem = outletItemIdList.includes(item.uniqueId);

    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        maxWidth: '33.3%',
        height: windowHeight * 0.2,
      }}>
        <TouchableOpacity
          onPress={() => {
            setOutletItemIdList(prevList => {
              const isItemExist = prevList.includes(item.uniqueId);

              if (isItemExist) {
                return prevList.filter(id => id !== item.uniqueId);
              } else {
                return [...prevList, item.uniqueId];
              }
            });
          }}
          style={{
            width: '90%',
            height: '90%',
            justifyContent: 'center',
            alignItems: 'center',
            borderWidth: 1,
            borderRadius: 10,
            borderColor: isSelectedItem ? Colors.whiteColor : Colors.blackColor,
            backgroundColor: isSelectedItem ? Colors.primaryColor : Colors.whiteColor,
          }}
        >
          <View style={{
            height: '90%',
            width: '90%',
            justifyContent: 'center',
            alignItems: 'center'
          }}>

            <Text style={{ color: isSelectedItem ? Colors.whiteColor : Colors.blackColor }}>{item.name}</Text>

          </View>

        </TouchableOpacity>

      </View>
    );
  };

  ////////////////////////////////////////////////////////////////////////////////////
  // 30-Aug-2024 - Variant/AddOn Choices Order Index Support

  useEffect(() => {
    let tempVariantChoices = [...variantChoices];

    tempVariantChoices.forEach((item, idx) => {
      item.orderIndex = idx + 1;
    });

    if (JSON.stringify(tempVariantChoices) !== JSON.stringify(variantChoices)) {
      setVariantChoices(tempVariantChoices)
    }

  }, [variantChoices])

  useEffect(() => {
    let tempAddOnChoices = [...addOnChoices];

    tempAddOnChoices.forEach((item, idx) => {
      item.orderIndex = idx + 1;
    });

    if (JSON.stringify(tempAddOnChoices) !== JSON.stringify(addOnChoices)) {
      setAddOnChoices(tempAddOnChoices)
    }

  }, [addOnChoices])

  const findArrayContainingChoice = (targetChoice) => {
    const arrays = [variantChoices, addOnChoices];
    return arrays.find(array =>
      array.some(item => item === targetChoice)
    );
  };

  const moveChoicesForward = (targetChoice) => {
    const resultArray = findArrayContainingChoice(targetChoice);

    if (resultArray) {
      let updatedChoicesList = [...resultArray];

      const choiceIndex = updatedChoicesList.findIndex(item => item === targetChoice);

      if (choiceIndex > 0) {
        const tempChoice = updatedChoicesList[choiceIndex - 1];
        updatedChoicesList[choiceIndex - 1] = updatedChoicesList[choiceIndex];
        updatedChoicesList[choiceIndex] = tempChoice;

        updateChoicesArray(resultArray, updatedChoicesList);
      }
    }
  };

  const moveChoicesBackward = (targetChoice) => {
    const resultArray = findArrayContainingChoice(targetChoice);

    if (resultArray) {
      let updatedChoicesList = [...resultArray];

      const choiceIndex = updatedChoicesList.findIndex(item => item === targetChoice);

      if (choiceIndex < updatedChoicesList.length - 1) {
        const tempChoice = updatedChoicesList[choiceIndex + 1];
        updatedChoicesList[choiceIndex + 1] = updatedChoicesList[choiceIndex];
        updatedChoicesList[choiceIndex] = tempChoice;

        updateChoicesArray(resultArray, updatedChoicesList);
      }
    }
  };

  const moveChoicesToTop = (targetChoice) => {
    const resultArray = findArrayContainingChoice(targetChoice);

    if (resultArray) {
      let updatedChoicesList = [...resultArray];

      const choiceIndex = updatedChoicesList.findIndex(item => item === targetChoice);

      if (choiceIndex > -1) {
        const [itemToMove] = updatedChoicesList.splice(choiceIndex, 1);
        updatedChoicesList.unshift(itemToMove);

        updateChoicesArray(resultArray, updatedChoicesList);
      }
    }
  };

  const updateChoicesArray = (oriArray, setOriArrayValue) => {
    if (oriArray === variantChoices) {
      setVariantChoices(setOriArrayValue);
    }
    else if (oriArray === addOnChoices) {
      setAddOnChoices(setOriArrayValue);
    }
  };

  ////////////////////////////////////////////////////////////////////////////////////
  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            // width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Variant & Add-On
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  return (
    <View style={[
      styles.container,
      !isTablet() ? { transform: [{ scaleX: 1 }, { scaleY: 1 }] } : {},
      {
        ...getTransformForScreenInsideNavigation(),
      }
    ]}>
      {/* Create / Edit Shared Variant/AddOn Modal */}
      <ModalView
        visible={showVariantAddOnModal}
        onRequestClose={() => { setEditingVariantAddOn(null) }}
        animationType={'slide'}
        transparent
      >
        <KeyboardAvoidingView
          style={{ ...styles.modalContainer }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
        >
          <View style={{
            ...styles.modalView,
            ...getTransformForModalInsideNavigation(),
          }}>

            {/* Modal Close Button */}
            <View style={{ ...styles.flexRowContainer, width: '100%', justifyContent: 'flex-end', marginVertical: '1%' }}>
              <TouchableOpacity onPress={() => { handleCloseEditModalAction() }}>
                <AntDesign name='close' size={30} color={Colors.blackColor} />
              </TouchableOpacity>
            </View>

            {/* Modal Contents */}
            <View style={{ flex: 1, width: '100%' }}>
              {/* Toggle Selection for Variant / Add-On */}
              <View style={{ flexDirection: 'row', width: '100%', height: '10%' }}>
                <TouchableOpacity
                  style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: disabledCreationOption === ItemVariation.VARIANT ? Colors.fieldtTxtColor : (selectedCreationOption === ItemVariation.VARIANT ? Colors.primaryColor : Colors.lightGrey),
                  }}
                  onPress={() => { setSelectedCreationOption(ItemVariation.VARIANT) }}
                  disabled={editingVariantAddOn !== null}
                >
                  <Text style={[{
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 16,
                    color: selectedCreationOption === ItemVariation.VARIANT ? Colors.whiteColor : Colors.blackColor,
                  }]}>
                    {ItemVariation.VARIANT}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: disabledCreationOption === ItemVariation.ADD_ON ? Colors.fieldtTxtColor : (selectedCreationOption === ItemVariation.ADD_ON ? Colors.primaryColor : Colors.lightGrey),
                  }}
                  onPress={() => { setSelectedCreationOption(ItemVariation.ADD_ON) }}
                  disabled={editingVariantAddOn !== null}
                >
                  <Text style={{
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: switchMerchant ? 10 : 16,
                    color: selectedCreationOption === ItemVariation.ADD_ON ? Colors.whiteColor : Colors.blackColor,
                  }}>
                    {ItemVariation.ADD_ON}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Input Form */}
              {/* <KeyboardAvoidingView style={{flex:1}}> */}
              {renderInputForm()}
              {/* </KeyboardAvoidingView> */}
            </View >

            {/* Modal Action Button */}
            < View style={{ ...styles.flexRowContainer, width: '100%', height: '10%' }}>
              <TouchableOpacity
                style={{
                  width: '20%',
                  height: '80%',
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: Colors.primaryColor,
                  borderWidth: 1,
                  borderRadius: 5,
                  borderColor: Colors.primaryColor,
                  paddingHorizontal: 10,
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  zIndex: -1,
                }}
                onPress={() => { handleSaveButtonAction() }}
                disabled={loading}
              >
                {!loading ? (
                  <Text style={{
                    fontSize: switchMerchant ? 10 : 16,
                    fontFamily: 'NunitoSans-Bold',
                    color: Colors.whiteColor,
                  }}>
                    {editingVariantAddOn === null ? "CREATE" : "SAVE"}
                  </Text>
                ) : (
                  <ActivityIndicator
                    color={Colors.whiteColor}
                    size={'small'}
                  />
                )}
              </TouchableOpacity>
            </View >
          </View >
        </KeyboardAvoidingView >
      </ModalView >

      {/* Bind OutletItem to Shared Variant/AddOn Modal */}
      < ModalView
        visible={showBindingToOutletItemModal}
        onRequestClose={() => { setEditingVariantAddOn(null) }}
        animationType={'slide'}
        transparent
      >
        <View style={{ ...styles.modalContainer }}>
          <View style={{
            ...styles.modalView,
            ...getTransformForModalInsideNavigation(),
            // height: '75%'
          }}>

            {/* Main Content */}
            <View style={{ flex: 1, flexDirection: 'column', width: '100%' }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  width: '100%',
                  paddingHorizontal: 10,
                  marginVertical: 10
                }}>
                <Text style={{
                  fontSize: 20,
                  fontFamily: 'NunitoSans-Bold',
                  color: Colors.blackColor,
                  marginVertical: 10
                }}>
                  Choose the outlet item(s) to apply
                </Text>

                {/* Close Button */}
                <TouchableOpacity onPress={() => handleCloseBindingModalAction()}>
                  <AntDesign name='close' size={30} color={Colors.blackColor} />
                </TouchableOpacity>
              </View>

              <View style={{
                flex: 1,
                flexDirection: 'row',
                borderWidth: 1,
                borderRadius: 5,
                borderColor: Colors.tabGrey
              }}>

                <FlatList
                  data={outletCategories}
                  renderItem={renderOutletCategoriesItems}
                  ItemSeparatorComponent={() => <View style={{ ...styles.divider, alignSelf: 'center', width: '80%' }} />}
                  style={{
                    width: '20%',
                    borderWidth: 1,
                  }}
                />

                <View style={{ width: '80%' }}>
                  <View style={{
                    width: '100%',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    marginVertical: '1.5%'
                  }}>
                    <TouchableOpacity
                      onPress={() => {
                        setOutletItemIdList(prevArray => {
                          const selectedItems = outletItemCategorizedByCategory[selectedCategory];

                          if (allItemSelected) {
                            return prevArray.filter(item => !selectedItems.some(selectedItem => selectedItem.uniqueId === item));
                          } else {
                            const newItems = selectedItems
                              .filter(selectedItem => !prevArray.includes(selectedItem.uniqueId))
                              .map(item => item.uniqueId);
                            return [...prevArray, ...newItems];
                          }
                        });
                      }}

                      style={{
                        height: windowWidth * 0.02,
                        width: windowWidth * 0.02,
                        marginRight: 10,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: allItemSelected ? Colors.primaryColor : Colors.fieldtTxtColor,
                      }}
                    >
                      <Ionicon name="checkmark-sharp" size={14} color={'#FFFFFF'} style={{ margin: 2 }} />
                    </TouchableOpacity>

                    <Text style={{ minWidth: '20%' }}>{allItemSelected ? 'All items selected' : 'Select all items'}</Text>
                  </View>

                  <FlatList
                    data={outletItemCategorizedByCategory[selectedCategory]}
                    renderItem={renderSelectedOutletCategoriesItems}
                    ListEmptyComponent={renderListEmpty}
                    extraData={selectedCategory}
                    numColumns={3}
                  />

                </View>

              </View>
            </View>

            {/* Action Button */}
            <View style={{ ...styles.flexRowContainer, width: '85%', height: '10%', marginTop: '2%' }}>
              {/* Save Button */}
              <View style={{ width: '20%' }}>
                <TouchableOpacity
                  style={{
                    width: '100%',
                    height: '80%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: Colors.primaryColor,
                    borderWidth: 1,
                    borderRadius: 5,
                    borderColor: Colors.primaryColor,
                    paddingHorizontal: 10,
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                  }}
                  onPress={() => { actionUpdateOutletItemIdList() }}
                  disabled={loading}
                >
                  {!loading ? (
                    <Text style={{
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Bold',
                      color: Colors.whiteColor,
                    }}>
                      SAVE
                    </Text>
                  ) : (
                    <ActivityIndicator
                      color={Colors.whiteColor}
                      size={'small'}
                    />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </ModalView >

      {/* Side Bar */}
      {/* <View style={{ width: switchMerchant ? windowWidth * Styles.sideBarWidth : windowWidth * 0.08 }}>
        <SideBar navigation={props.navigation} selectedTab={2} expandProduct />
      </View> */}

      {/* Main View */}
      < View style={{
        width: windowWidth * (1 - Styles.sideBarWidth),
        marginVertical: windowHeight * 0.02,
        paddingHorizontal: windowWidth * 0.02,
      }}>

        {/* Row Container - Action Button */}
        < View style={{ ...styles.flexRowContainer, justifyContent: 'flex-end', marginVertical: 10 }}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
              borderWidth: 1,
              borderColor: Colors.primaryColor,
              backgroundColor: '#0F1A3C',
              borderRadius: 5,
              paddingHorizontal: 10,
              height: switchMerchant ? 35 : 40,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
              zIndex: -1,
            }}
            onPress={() => {
              setShowVariantAddOnModal(true);
            }}
            disabled={loading}
          >
            <View style={{ ...styles.flexRowContainer }}>
              <AntDesign name="pluscircle" size={switchMerchant ? 10 : 20} color={Colors.whiteColor} />

              <Text style={{
                color: Colors.whiteColor,
                marginLeft: 5,
                fontSize: switchMerchant ? 10 : 16,
                fontFamily: 'NunitoSans-Bold',
              }}>
                SHARED VARIANT & ADD-ON
              </Text>
            </View>
          </TouchableOpacity>
        </View >

        {/* Row Container - Back Button & Search Bar */}
        < View style={{
          ...styles.flexRowContainer,
          width: '100%',
          justifyContent: 'space-between'
        }}>

          {/* Back Button */}
          < TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}
            onPress={() => {
              navigation.navigate('VariantAddOn');
            }}>

            <Feather
              name="chevron-left"
              size={switchMerchant ? 20 : 30}
              style={{ color: Colors.primaryColor, alignSelf: 'center' }}
            />

            <Text style={{
              fontFamily: 'Nunitosans-Bold',
              color: Colors.primaryColor,
              fontSize: switchMerchant ? 14 : 17,
              marginBottom: Platform.OS === 'ios' ? 0 : 1,
            }}>
              Back
            </Text>

          </TouchableOpacity >

          {/* Search Box */}
          < View style={{
            width: switchMerchant ? 200 : 250,
            height: switchMerchant ? 35 : 40,
            backgroundColor: 'white',
            borderRadius: 5,
            flexDirection: 'row',
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.22,
            shadowRadius: 3.22,
            elevation: 3,
          }}>
            <Feather name="search" size={switchMerchant ? 13 : 18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />

            <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              style={{
                width: switchMerchant ? 180 : 220,
                fontSize: switchMerchant ? 10 : 15,
                fontFamily: 'NunitoSans-Regular',
                paddingLeft: 5
              }}
              clearButtonMode="while-editing"
              placeholder=" Search"
              placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
              onChangeText={(text) => {
                setSortingOption({
                  ...sortingOption,
                  filter: text
                });
              }}
              value={sortingOption.filter}
            />

          </View >
        </View >

        {/* Shared Variant & Add-On List */}
        < View style={{
          flex: 1,
          marginVertical: windowHeight * 0.02,
          backgroundColor: Colors.whiteColor,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 3,
        }}>
          <FlatList
            data={sortedList}
            renderItem={renderVariantListItems}
            keyExtractor={(item, index) => String(index)}
            ItemSeparatorComponent={() => <View style={{ ...styles.divider, alignSelf: 'center', width: '95%' }} />}
            ListHeaderComponent={renderVariantListHeader}
            ListEmptyComponent={renderListEmpty}
            stickyHeaderIndices={[0]}
            style={{
              height: 'auto'
            }}
          />
        </View >

      </View >
    </View >
  );
};

const styles = StyleSheet.create({
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: Colors.highlightColor,
  },
  flexRowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').height * 0.8,
    width: Dimensions.get('window').width * 0.85,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    paddingHorizontal: '2%',
    paddingVertical: '1%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  fontStyle: {
    textAlign: 'center',
    fontFamily: 'NunitoSans-Regular',
    fontSize: 14,
    color: Colors.blackColor,
  },
  divider: {
    borderWidth: 1,
    borderColor: Colors.blackColor,
    opacity: 0.2,
  },
});

export default SharedVariantAddOnScreen;
