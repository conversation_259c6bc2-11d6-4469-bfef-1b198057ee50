import React, { useState, useEffect, useCallback } from 'react';
import { Text } from "react-native-fast-text";
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
  Platform,
  Modal,
  ActivityIndicator
} from 'react-native';
import { 
  getTiktokShopHealth,
} from '../util/tiktok';
import { useFocusEffect } from '@react-navigation/native';
import { isTablet } from '../util/common';
import { ScrollView } from 'react-native-gesture-handler';
import { EXPAND_TAB_TYPE } from '../constant/common';
import { UserStore } from '../store/userStore';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { apiUrl } from '../constant/env';
import AntDesign from 'react-native-vector-icons/AntDesign';
import UserIdleWrapper from '../components/userIdleWrapper';
import TikTokShopSvg from '../assets/svg/TikTok_Shop.svg';
import Colors from '../constant/Colors';
import WebView from 'react-native-webview';
import moment from 'moment';

const SettingTikTokScreen = props => {
  const { navigation } = props;
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [isLoading, setIsLoading] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  //////////////////////////////////////////////////////////////////////////
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const userName = UserStore.useState(s => s.name);
  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////

  // TikTok linking states
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authorizationUrl, setAuthorizationUrl] = useState('');

  const isAuthorized = OutletStore.useState((s) => s.isTiktokShopAuthorized);
  const [linkedShopName, setLinkedShopName] = useState(null);
  const [lastAuthorizedTime, setLastAuthorizedTime] = useState(null);

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      setLinkedShopName(currOutlet.tiktokShopName || '');
      setLastAuthorizedTime(currOutlet.tiktokTokenUpdatedAt || null);
      setAuthorizationUrl(`${apiUrl}tiktok/oauth/init/${currOutlet.uniqueId}`);
    }
  }, [currOutlet]);

  const handleCloseAuthModal = () => {
    setShowAuthModal(false);

    setTimeout(() => {
      setIsLoading(false);
    }, 5000);
  }

  //////////////////////////////////////////////////////////////////////

  // Diagnosis states
  const [showDiagnosisModal, setShowDiagnosisModal] = useState(false);
  const [diagnosisLoading, setDiagnosisLoading] = useState(false);
  const [diagnosisResult, setDiagnosisResult] = useState(null);

  const runTiktokDiagnosis = async () => {
    if (!currOutlet || !currOutlet.uniqueId) return;

    try {
      setDiagnosisResult(null);
      setDiagnosisLoading(true);
      setShowDiagnosisModal(true);

      const result = await getTiktokShopHealth(currOutlet.uniqueId);

      console.log('tiktok result', result);

      if (result && result.status === 'ok') {
        setDiagnosisResult(result.data);
      }
      else {
        setDiagnosisResult({
          status: result?.status || 'error',
          message: result?.message || 'Unknown error',
        });
      }
    } 
    catch (e) {
      setDiagnosisResult({ status: 'error', message: 'Request failed' });
    }
    finally {
      setDiagnosisLoading(false);
    }
  }

  //////////////////////////////////////////////////////////////////////

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            ...global.getHeaderTitleStyle(),
          },
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          TikTok Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  return (
    <UserIdleWrapper disabled={!isMounted}>
      {/* OAuth Modal */}
      <Modal
        visible={showAuthModal}
        animationType={'slide'}
        transparent={true}
        onRequestClose={handleCloseAuthModal}
      >
        <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', padding: 20 }}>
          <View style={{
            backgroundColor: Colors.whiteColor,
            borderRadius: 8,
            overflow: 'hidden',
            height: '100%',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingHorizontal: 12,
              paddingVertical: 10,
              borderBottomWidth: 1,
              borderBottomColor: '#eee',
            }}>
              <Text style={{ fontFamily: 'NunitoSans-Bold' }}>Authorize TikTok Shop</Text>
              <TouchableOpacity onPress={handleCloseAuthModal}>
                <AntDesign name="close" size={24} color={Colors.primaryColor} />
              </TouchableOpacity>
            </View>
            <WebView
              source={authorizationUrl ? { uri: authorizationUrl } : { html: '<html><body></body></html>' }}
              startInLoadingState
              renderError={() => (
                <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
                  <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>
                    Unable to load authorization page. Please try again later.
                  </Text>
                </View>
              )}
              style={{ flex: 1 }}
            />
          </View>
        </View>
      </Modal>

      {/* Diagnosis Modal */}
      <Modal
        visible={showDiagnosisModal}
        animationType={'slide'}
        transparent={true}
        onRequestClose={() => setShowDiagnosisModal(false)}
      >
        <View style={{ flex:1, backgroundColor: 'rgba(0,0,0,0.5)', justifyContent: 'center', padding: 20 }}>
          <View style={{
            backgroundColor: Colors.whiteColor,
            borderRadius: 8,
            overflow: 'hidden',
            width: '50%',
            height: 'auto',
            maxHeight: '85%',
            alignSelf: 'center',
          }}>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingHorizontal: 12,
              paddingVertical: 10,
              borderBottomWidth: 1,
              borderBottomColor: '#eee',
            }}>
              <Text style={{ fontFamily: 'NunitoSans-Bold' }}>TikTok Shop Diagnosis</Text>
              <TouchableOpacity onPress={() => setShowDiagnosisModal(false)}>
                <AntDesign name="close" size={24} color={Colors.primaryColor} />
              </TouchableOpacity>
            </View>

            <ScrollView style={{ padding: 16 }}>
              {diagnosisLoading && (
                <View style={{ paddingVertical: 16, alignItems: 'center' }}>
                  <ActivityIndicator size="small" color={Colors.primaryColor} />
                </View>
              )}

              {!diagnosisLoading && !diagnosisResult && (
                <Text style={{ fontFamily: 'NunitoSans-Regular', color: '#666' }}>No result.</Text>
              )}

              {!diagnosisLoading && diagnosisResult && (
                <View style={{ gap: 8, paddingBottom: 40 }}>
                  {diagnosisResult.map((item, index) => {
                    const isPassed = !item.is_failed;

                    const badgeColors = isPassed
                      ? { bg: '#e6f7ea', border: '#52c41a', text: '#389e0d' }
                      : { bg: '#fdecea', border: '#f5222d', text: '#a8071a' };

                    return (
                      <View key={index} style={{ marginBottom: 12 }}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                          <View style={{ flex: 1 }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', marginBottom: 4 }}>{item.check_item}</Text>

                            {!isPassed && item.fail_reasons && item.fail_reasons.length > 0 && (
                              <View style={{ marginLeft: 8, paddingLeft: 8, borderLeftWidth: 2, borderLeftColor: '#f5222d' }}>
                                {item.fail_reasons.map((reason, reasonIndex) => (
                                  <Text key={reasonIndex} style={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: 12,
                                    color: '#a8071a',
                                    marginBottom: 2
                                  }}>
                                    • {reason}
                                  </Text>
                                ))}
                              </View>
                            )}
                          </View>
                          
                          <View style={{
                            backgroundColor: badgeColors.bg,
                            borderColor: badgeColors.border,
                            borderWidth: 1,
                            paddingVertical: 4,
                            paddingHorizontal: 8,
                            borderRadius: 6,
                            alignSelf: 'center'
                          }}>
                            <Text style={{ color: badgeColors.text, fontFamily: 'NunitoSans-Bold' }}>
                              {isPassed ? 'PASS' : 'FAIL'}
                            </Text>
                          </View>
                        </View>
                      </View>
                    );
                  })}
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Loading State */}
      {isLoading && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000,
          pointerEvents: 'auto',
        }}>
          <ActivityIndicator size="large" color={Colors.whiteColor} />
        </View>
      )}

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          padding: 20,
          backgroundColor: Colors.highlightColor,
          flexGrow: 1,
        }}
      >
        {/* Main Container */}
        <View style={{
          width: '100%',
          backgroundColor: Colors.whiteColor,
          alignSelf: 'center',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 2,
          },
          shadowOpacity: 0.22,
          shadowRadius: 3.22,
          elevation: 3,
          borderRadius: 5,
          padding: 20,
          paddingVertical: 40,
        }}>
          {/* TikTok Shop Logo */}
          <View style={{
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <TikTokShopSvg width={200} height={30} />
          </View>

          {/* Auth Status & Authorize Button */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={{ fontFamily: 'NunitoSans-Bold', marginRight: 10 }}>Auth Status</Text>
              <View style={{
                backgroundColor: isAuthorized ? '#e6f7ea' : '#fdecea',
                borderColor: isAuthorized ? '#52c41a' : '#f5222d',
                borderWidth: 1,
                paddingVertical: 4,
                paddingHorizontal: 8,
                borderRadius: 6,
              }}>
                <Text style={{ color: isAuthorized ? '#389e0d' : '#a8071a', fontFamily: 'NunitoSans-Bold' }}>
                  {isAuthorized ? 'Authorized' : (lastAuthorizedTime ? 'Expired' : 'Not authorized')}
                </Text>
              </View>
            </View>

            {!isAuthorized && (
              <TouchableOpacity
                onPress={() => {
                  setShowAuthModal(true)
                  setIsLoading(true)
                }}
                style={{
                  justifyContent: 'center',
                  flexDirection: 'row',
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  backgroundColor: '#0F1A3C',
                  borderRadius: 5,
                  paddingHorizontal: 12,
                  height: 35,
                  alignItems: 'center',
                }}
              >
                <Text style={{
                  color: Colors.whiteColor,
                  fontSize: switchMerchant ? 10 : 14,
                  fontFamily: 'NunitoSans-Bold',
                }}>AUTHORIZE</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Authorized Shop Info */}
          <View style={{
            flexDirection: 'column',
            gap: 8,
            marginTop: 20,
          }}>
            <View style={{ flexDirection: 'row' }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: 16,
                width: 160,
              }}>Linked Shop Name</Text>

              <Text style={{
                flexShrink: 1,
                fontSize: 16,
              }}>{linkedShopName || 'N/A'}</Text>
            </View>

            <View style={{ flexDirection: 'row' }}>
              <Text style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: 16,
                width: 160,
              }}>Last Authorized</Text>
              <Text style={{
                flexShrink: 1,
                fontSize: 16,
              }}>{(lastAuthorizedTime && moment(lastAuthorizedTime).format('h:mm A, DD MMMM YYYY')) || 'N/A'}</Text>
            </View>
          </View>

          {/* Diagnosis trigger (only when authorized) */}
          {isAuthorized && (
            <View style={{
              marginTop: 28,
              borderTopWidth: 1,
              borderTopColor: '#f0f0f0',
              paddingTop: 16,
            }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>TikTok Shop Diagnosis</Text>
                <TouchableOpacity
                  onPress={runTiktokDiagnosis}
                  disabled={diagnosisLoading || !currOutlet?.uniqueId}
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#0F1A3C',
                    borderRadius: 5,
                    paddingHorizontal: 12,
                    height: 35,
                    alignItems: 'center',
                    opacity: diagnosisLoading ? 0.7 : 1,
                  }}
                >
                  <Text style={{
                    color: Colors.whiteColor,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Bold',
                  }}>{diagnosisLoading ? 'CHECKING...' : 'RUN'}</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </UserIdleWrapper>
  );
}

const styles = StyleSheet.create({
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SettingTikTokScreen;