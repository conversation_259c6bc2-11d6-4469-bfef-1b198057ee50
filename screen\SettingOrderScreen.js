import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { TextInput, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Styles from '../constant/Styles';
import Switch from 'react-native-switch-pro';
import {
  getTransformForScreenInsideNavigation,
  isTablet
} from '../util/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import { EXPAND_TAB_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { parseValidPriceText } from '../util/common';
import APILocal from '../util/apiLocalReplacers';

const SettingScreen = props => {
  const {
    navigation,
  } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [loading, setLoading] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [order, setOrder] = useState(true);

  //////////////////////////////////////////////////////////////////////////

  const [temp, setTemp] = useState('');

  const [deliveryDistance, setDeliveryDistance] = useState('1');
  const [freeDeliveyAboveAmountValue, setFreeDeliveyAboveAmountValue] = useState('');
  const [freeDeliveyAboveAmountFlag, setFreeDeliveyAboveAmountFlag] = useState(false);
  const [discountOrderAboveAmountValue, setDiscountOrderAboveAmountValue] = useState('');
  const [discountOrderAboveAmountThreshold, setDiscountOrderAboveAmountThreshold] = useState('');

  const [deliveryPrice, setDeliveryPrice] = useState('0.00');
  const [pickUpPrice, setPickUpPrice] = useState('0.00');
  const [deliveryPackagingFee, setDeliveryPackagingFee] = useState('0.00');
  const [pickupPackagingFee, setPickupPackagingFee] = useState('0.00');

  const [takeawayAutoAuthorizationMinute, setTakeawayAutoAuthorizationMinute] = useState('0');
  const [takeawayAutoAuthorizationActive, setTakeawayAutoAuthorizationActive] = useState(false);

  const [takeawayAutoApproveOrdersActive, setTakeawayAutoApproveOrdersActive] = useState(false);

  //////////////////////////////////////////////////////////////////////

  // 2024-07-30 - to authorize dine-in orders

  const [dineInRequiredAuthorization, setDineInRequiredAuthorization] = useState(false);

  const [dineInAutoAuthorizationMinute, setDineInAutoAuthorizationMinute] = useState('0');
  const [dineInAutoAuthorizationActive, setDineInAutoAuthorizationActive] = useState(false);

  //////////////////////////////////////////////////////////////////////

  const [takeawayRequiredAuthorizationBeforePayment, setTakeawayRequiredAuthorizationBeforePayment] = useState(false);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);

  const userName = UserStore.useState(s => s.name);
  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      setDeliveryDistance(currOutlet.deliveryDistance ? currOutlet.deliveryDistance.toFixed(0) : '1');
      setFreeDeliveyAboveAmountValue(currOutlet.freeDeliveyAboveAmountValue ? currOutlet.freeDeliveyAboveAmountValue.toFixed(2) : '0');
      setFreeDeliveyAboveAmountFlag(currOutlet.freeDeliveyAboveAmountFlag ? currOutlet.freeDeliveyAboveAmountFlag : false);
      setDiscountOrderAboveAmountValue(currOutlet.discountOrderAboveAmountValue ? currOutlet.discountOrderAboveAmountValue.toFixed(2) : '0');
      setDiscountOrderAboveAmountThreshold(currOutlet.discountOrderAboveAmountThreshold ? currOutlet.discountOrderAboveAmountThreshold.toFixed(2) : '0');

      setDeliveryPrice(currOutlet.deliveryPrice ? parseFloat(currOutlet.deliveryPrice).toFixed(2) : '0.00');
      setPickUpPrice(currOutlet.pickUpPrice ? parseFloat(currOutlet.pickUpPrice).toFixed(2) : '0.00');
      setDeliveryPackagingFee(currOutlet.deliveryPackagingFee ? parseFloat(currOutlet.deliveryPackagingFee).toFixed(2) : '0.00');
      setPickupPackagingFee(currOutlet.pickupPackagingFee ? parseFloat(currOutlet.pickupPackagingFee).toFixed(2) : '0.00');

      setTakeawayAutoAuthorizationMinute(currOutlet.takeawayAutoAuthorizationMinute ? parseFloat(currOutlet.takeawayAutoAuthorizationMinute).toFixed(0) : '0');
      setTakeawayAutoAuthorizationActive(currOutlet.takeawayAutoAuthorizationActive ? currOutlet.takeawayAutoAuthorizationActive : false);

      setTakeawayAutoApproveOrdersActive(currOutlet.takeawayAutoApproveOrdersActive ? currOutlet.takeawayAutoApproveOrdersActive : false);

      setDineInRequiredAuthorization(currOutlet.dineInRequiredAuthorization ? currOutlet.dineInRequiredAuthorization : false);
      setDineInAutoAuthorizationMinute(currOutlet.dineInAutoAuthorizationMinute ? parseFloat(currOutlet.dineInAutoAuthorizationMinute).toFixed(0) : '0');
      setDineInAutoAuthorizationActive(currOutlet.dineInAutoAuthorizationActive ? currOutlet.dineInAutoAuthorizationActive : false);

      setTakeawayRequiredAuthorizationBeforePayment(currOutlet.takeawayRequiredAuthorizationBeforePayment ? currOutlet.takeawayRequiredAuthorizationBeforePayment : false);
    }
  }, [currOutlet]);

  // useEffect(() => {
  //   const selectedTargetOutlet = allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId);

  //   if (selectedTargetOutlet) {
  //     setDeliveryDistance(selectedTargetOutlet.deliveryDistance ? selectedTargetOutlet.deliveryDistance.toFixed(0) : '1');
  //     setFreeDeliveyAboveAmountValue(selectedTargetOutlet.freeDeliveyAboveAmountValue ? selectedTargetOutlet.freeDeliveyAboveAmountValue.toFixed(2) : '0');
  //     setFreeDeliveyAboveAmountFlag(selectedTargetOutlet.freeDeliveyAboveAmountFlag ? selectedTargetOutlet.freeDeliveyAboveAmountFlag : false);
  //     setDiscountOrderAboveAmountValue(selectedTargetOutlet.discountOrderAboveAmountValue ? selectedTargetOutlet.discountOrderAboveAmountValue.toFixed(2) : '0');
  //     setDiscountOrderAboveAmountThreshold(selectedTargetOutlet.discountOrderAboveAmountThreshold ? selectedTargetOutlet.discountOrderAboveAmountThreshold.toFixed(2) : '0');

  //     setDeliveryPrice(selectedTargetOutlet.deliveryPrice ? parseFloat(selectedTargetOutlet.deliveryPrice).toFixed(2) : '0');
  //     setPickUpPrice(selectedTargetOutlet.pickUpPrice ? parseFloat(selectedTargetOutlet.pickUpPrice).toFixed(2) : '0');
  //     setDeliveryPackagingFee(selectedTargetOutlet.deliveryPackagingFee ? parseFloat(selectedTargetOutlet.deliveryPackagingFee).toFixed(2) : '0');
  //     setPickupPackagingFee(selectedTargetOutlet.pickupPackagingFee ? parseFloat(selectedTargetOutlet.pickupPackagingFee).toFixed(2) : '0');
  //   }
  // }, [selectedTargetOutletId]);

  useEffect(() => {
    setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  //////////////////////////////////////////////////////////////////////////

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Order Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {

  //   ApiClient.GET(API.getOutletByMerchant + User.getMerchantId()).then((result) => {
  //     setState({ outletInfo: result });
  //     result.map((element) => {
  //       setState({
  //         outletId: element.id,
  //         outletName: element.name,
  //         merchantName: element.merchant.name
  //       });
  //     });
  //   });

  //   ApiClient.GET(API.getAllSettingRedemption + User.getMerchantId()).then((result) => {
  //     setState({ redemptionInfo: result });
  //   });

  //   outlet()
  //   ApiClient.GET(API.getOutletCategory + User.getOutletId()).then((result) => {
  //     if (result !== undefined) {
  //       setState({ categoryOutlet: result });
  //     }

  //   });
  // }

  const orderFunc = () => {
    // var body = {
    //   merchantId: User.getMerchantId(),
    //   isAllOutlet: isChecked6 == true ? '1' : null,
    //   outletId: outletId,
    //   deliveryDistance: showDistance,
    //   deliveryFee: isChecked6 == true ? amount : amount1,
    //   deliveryHourStart: hourStart,
    //   deliveryHourEnd: hourEnd,
    //   deliveryPrice: isChecked8 == true ? value1 : 0,
    //   pickUpPrice: isChecked9 == true ? value2 : 0,
    //   fireorder: status1,
    //   category: category,
    // };

    var body = {
      merchantId: merchantId,
      // outletId: selectedTargetOutletId,
      outletId: currOutletId,
      deliveryDistance: +(parseFloat(deliveryDistance).toFixed(2)),
      freeDeliveyAboveAmountValue: +(parseFloat(freeDeliveyAboveAmountValue).toFixed(2)),
      freeDeliveyAboveAmountFlag: freeDeliveyAboveAmountFlag,
      discountOrderAboveAmountValue: +(parseFloat(discountOrderAboveAmountValue).toFixed(2)),
      discountOrderAboveAmountThreshold: +(parseFloat(discountOrderAboveAmountThreshold).toFixed(2)),
      deliveryPrice: +(parseFloat(deliveryPrice).toFixed(2)),
      pickUpPrice: +(parseFloat(pickUpPrice).toFixed(2)),
      deliveryPackagingFee: +(parseFloat(deliveryPackagingFee).toFixed(2)),
      pickupPackagingFee: +(parseFloat(pickupPackagingFee).toFixed(2)),

      takeawayAutoAuthorizationMinute: +(parseFloat(takeawayAutoAuthorizationMinute).toFixed(0)),
      takeawayAutoAuthorizationActive: takeawayAutoAuthorizationActive,
      takeawayAutoApproveOrdersActive: takeawayAutoApproveOrdersActive ? takeawayAutoApproveOrdersActive : false,

      dineInRequiredAuthorization: dineInRequiredAuthorization,
      dineInAutoAuthorizationMinute: +(parseFloat(dineInAutoAuthorizationMinute).toFixed(0)),
      dineInAutoAuthorizationActive: dineInAutoAuthorizationActive,

      takeawayRequiredAuthorizationBeforePayment: takeawayRequiredAuthorizationBeforePayment,
    
      
    };

    // console.log(body);

    // ApiClient.POST(API.updateOutletOrderDetails, body, false)
    APILocal.updateOutletOrderDetails({ body: body })
      .then((result) => {
        setLoading(true)
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Order settings has been updated.',
            [
              {
                text: 'OK',
                onPress: () => {
                  setLoading(false)
                },
              },
            ],
            { cancelable: false },
          );
        }
      });
  }

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>    
    (<UserIdleWrapper disabled={!isMounted}>
      <View style={[styles.container, !isTablet() ? {
        transform: [
          { scaleX: 1 },
          { scaleY: 1 },
        ],
      } : {}, {
        ...getTransformForScreenInsideNavigation(),
      }]}>
        {/* <View style={[styles.sidebar, !isTablet() ? {
          width: windowWidth * 0.08,
        } : {}, switchMerchant ? {
          // width: '10%'
        } : {}, {
          width: windowWidth * 0.08,
        }]}>
          <SideBar navigation={props.navigation} selectedTab={10} expandSettings={true} />
        </View> */}

        <ScrollView
          showsVerticalScrollIndicator={false}
          // scrollEnabled={switchMerchant}
          style={{ backgroundColor: Colors.highlightColor }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}>
          <ScrollView horizontal={true}>

            <View style={[styles.content, {
              width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
            }]}>
              {/* <View style={{flexDirection: 'row', marginBottom: 10}}>
            <View style={{flexDirection: 'row', flex: 1}}></View>
            <TextInput
              editable={!loading}
              underlineColorAndroid={Colors.whiteColor}
              clearButtonMode="while-editing"
              style={styles.textInput}
              placeholder="search"
              onChangeText={(text) => {
                setState({search: text.trim()});
              }}
              value={email}
            />
          </View> */}
              {/* <View
            style={{
              flexDirection: 'row',
              backgroundColor: Colors.highlightColor,
              padding: 12,
            }}>
            <TouchableOpacity
              onPress={() => {
                setState({
                  merchantDisplay: true,
                  shift: false,
                  tax: false,
                  sample: false,
                  redemption: false,
                  order: false,
                });
              }}>
              <Text>General</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  shift: true,
                  merchantDisplay: false,
                  tax: false,
                  sample: false,
                  redemption: false,
                  order: false,
                });
                getCurrentShift(User.getOutletId());
              }}>
              <Text style={{ marginLeft: 30 }}>Shift</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  tax: true,
                  merchantDisplay: false,
                  shift: false,
                  sample: false,
                  redemption: false,
                  order: false,
                });
                getTax(User.getOutletId())
              }}>
              <Text style={{ marginLeft: 30 }}>Tax</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  sample: true,
                  tax: false,
                  merchantDisplay: false,
                  shift: false,
                  redemption: false,
                  order: false,
                });
                setState({
                  showNote: false,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Sample Receipt</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  merchantDisplay: false,
                  shift: false,
                  tax: false,
                  sample: false,
                  redemption: false,
                  order: true,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Order</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                setState({
                  merchantDisplay: false,
                  shift: false,
                  tax: false,
                  sample: false,
                  redemption: true,
                  order: false,
                  redemptionList: true,
                });
              }}>
              <Text style={{ marginLeft: 30 }}>Redemption</Text>
            </TouchableOpacity>
          </View> */}

              {order ? (
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    // height: windowHeight - 120,
                    height: '100%',
                    width: windowWidth * 0.87,
                    alignSelf: 'center',

                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    // elevation: 1,
                    elevation: 3,
                    borderRadius: 5,

                    // borderRadius: 8,
                  }}>
                  <KeyboardAwareScrollView
                    contentContainerStyle={{
                      // top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                    }}>
                    <View
                      style={{
                      }}>
                      <View style={{ flexDirection: 'row', padding: 30, zIndex: -3 }}>
                        <View style={{ flex: 3 }}>
                          {/* <View style={{ flexDirection: 'row', flex: 1, width: '100%', }}>

                          <View style={{ justifyContent: 'center', flex: 1 }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>Choose Outlet </Text>
                          </View>
                          <View style={{ flex: 1 }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                                marginLeft: '5.3%',

                              }}>
                              <DropDownPicker
                                labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                containerStyle={{ height: 40 }}
                                items={targetOutletDropdownList}
                                defaultValue={selectedTargetOutletId}
                                placeholderStyle={{ color: 'black' }}
                                style={{ backgroundColor: '#fafafa' }}
                                itemStyle={{
                                  justifyContent: 'flex-start', marginLeft: 5,
                                }}
                                dropDownStyle={{ backgroundColor: '#fafafa' }}
                                onChangeItem={(item) => {
                                  setSelectedTargetOutletId(item.value)
                                }}
                              />
                            </View>
                          </View>

                          <View
                            style={{
                              justifyContent: 'center',
                              flex: 1,
                              marginLeft: '5%',
                            }}>
                          </View>
                        </View> */}

                          {/* //////////////////////////////////////////////// */}

                          {/* 2024-07-30 - to enable authorize dine-in orders */}

                          {/*
                          <View style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>

                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Dine-In To Require Authorization{' '}
                                </Text>
                              </View>
                            </View>

                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 40,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                    // bottom: -2,
                                  }}
                                  value={dineInRequiredAuthorization}
                                  onSyncPress={(statusTemp) =>
                                    setDineInRequiredAuthorization(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1, justifyContent: 'center' }}>
                            </View>

                          </View>
                          */}


                          {/* <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            
                            <View style={{
                              justifyContent: 'center',
                              // flex: 1,
                              width: '33.33%',
                            }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Dine-In Auto-Authorization Time (minute){' '}
                                </Text>
                              </View>
                            </View>

                            <View style={{
                              // flex: 1,
                              flexDirection: 'row',
                              // backgroundColor: 'red',
                              width: '40.33%',
                            }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                  placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  keyboardType={'decimal-pad'}
                                  placeholder="60"
                                  containerStyle={{ height: 40 }}
                                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                  itemStyle={{
                                    justifyContent: 'flex-start', marginLeft: 5,
                                  }}
                                  defaultValue={dineInAutoAuthorizationMinute}
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(dineInAutoAuthorizationMinute)
                                    setDineInAutoAuthorizationMinute('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (dineInAutoAuthorizationMinute == '') {
                                      setDineInAutoAuthorizationMinute(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={text => {
                                    setDineInAutoAuthorizationMinute(text);
                                  }}
                                />
                              </View>

                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',

                                  // backgroundColor: 'blue',

                                  // marginRight: 20,
                                }}>
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 40,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                    // bottom: -2,
                                  }}
                                  value={dineInAutoAuthorizationActive}
                                  onSyncPress={(statusTemp) =>
                                    // setState({ status: status })
                                    setDineInAutoAuthorizationActive(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>
                            </View>

                            <View style={{
                              // flex: 1,
                              justifyContent: 'center',
                              // backgroundColor: 'green',
                              width: '26.33%',
                            }}>

                            </View>
                          </View> */}

                          {/* //////////////////////////////////////////////// */}

                          {/*
                          <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}
                          >

                            <View
                              style={{
                                justifyContent: 'center',
                                // flex: 1,
                                width: '33.33%',
                              }}
                            >
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  {`Dine-In / Takeaway \nAuto-Authorization Time (minute) `}
                                </Text>
                              </View>
                            </View>

                            <View
                              style={{
                                // flex: 1,
                                flexDirection: 'row',
                                // backgroundColor: 'red',
                                width: '40.33%',
                              }}
                            >
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}
                              >
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 },
                                    { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }
                                  ]}
                                  placeholderStyle={{
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 10,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14
                                  }}
                                  keyboardType={'decimal-pad'}
                                  placeholder="60"
                                  containerStyle={{ height: 40 }}
                                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    marginLeft: 5,
                                  }}
                                  defaultValue={takeawayAutoAuthorizationMinute}
                                  selectTextOnFocus
                                  onChangeText={text => {
                                    setTakeawayAutoAuthorizationMinute(text);
                                  }}
                                />
                              </View>

                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                  // backgroundColor: 'blue',
                                  // marginRight: 20,
                                }}
                              >
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 40,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios'
                                    //  ? [{ scaleX: .6 }, { scaleY: .6 }]
                                    //  : [{ scaleX: .8 }, { scaleY: .8 }],
                                    // bottom: -2,
                                  }}
                                  value={takeawayAutoAuthorizationActive}
                                  onSyncPress={(statusTemp) =>
                                    setTakeawayAutoAuthorizationActive(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>
                            </View>

                            <View
                              style={{
                                // flex: 1,
                                justifyContent: 'center',
                                // backgroundColor: 'green',
                                width: '26.33%',
                              }}
                            >
                            </View>

                          </View>
                          */}


                        {/*
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}
                        >
                          <View style={{ justifyContent: 'center', flex: 1 }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                Dine-In Auto-Authorization{' '}
                              </Text>
                            </View>
                          </View>

                          <View style={{ flex: 1, flexDirection: 'row' }}>
                            <View
                              style={{
                                marginLeft: switchMerchant ? 5 : 5,
                                justifyContent: 'center',
                              }}
                            >
                              <Switch
                                width={42}
                                style={{
                                  //flexDirection: 'row',
                                  //width: '15%',
                                  // marginRight: 40,
                                  // marginLeft: 20,
                                  //transform: Platform.OS == 'ios'
                                  //  ? [{ scaleX: .6 }, { scaleY: .6 }]
                                  //  : [{ scaleX: .8 }, { scaleY: .8 }],
                                  // bottom: -2,
                                }}
                                value={dineInAutoAuthorizationActive}
                                onSyncPress={(statusTemp) =>
                                  setDineInAutoAuthorizationActive(statusTemp)
                                }
                                circleColorActive={Colors.primaryColor}
                                circleColorInactive={Colors.fieldtTxtColor}
                                backgroundActive="#dddddd"
                              />
                            </View>
                          </View>

                          <View style={{ flex: 1, justifyContent: 'center' }}>
                          </View>
                        </View>
                        */}


                          <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Takeaway Auto-Authorization {' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 40,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                    // bottom: -2,
                                  }}
                                  value={takeawayAutoAuthorizationActive}
                                  onSyncPress={(statusTemp) =>
                                    // setState({ status: status })
                                    setTakeawayAutoAuthorizationActive(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1, justifyContent: 'center' }}>

                            </View>
                          </View>

                          <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  {`Takeaway To Require Authorization\nBefore Online Payment`}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 40,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                    // bottom: -2,
                                  }}
                                  value={takeawayRequiredAuthorizationBeforePayment}
                                  onSyncPress={(statusTemp) =>
                                    // setState({ status: status })
                                    setTakeawayRequiredAuthorizationBeforePayment(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1, justifyContent: 'center' }}>

                            </View>
                          </View>

                          <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Scheduled Takeaway Auto-Authorization {' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Switch
                                  width={42}
                                  style={{
                                    //flexDirection: 'row',
                                    //width: '15%',
                                    // marginRight: 40,
                                    // marginLeft: 20,
                                    //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                    // bottom: -2,
                                  }}
                                  value={takeawayAutoApproveOrdersActive}
                                  onSyncPress={(statusTemp) =>
                                    // setState({ status: status })
                                    setTakeawayAutoApproveOrdersActive(statusTemp)
                                  }
                                  circleColorActive={Colors.primaryColor}
                                  circleColorInactive={Colors.fieldtTxtColor}
                                  backgroundActive="#dddddd"
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1, justifyContent: 'center' }}>

                            </View>
                          </View>

                          <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Delivery Max Distance{' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[styles.textInputTakeawayCharges, { backgroundColor: Colors.fieldtBgColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                  placeholderStyle={{ color: 'black', height: 40, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  itemStyle={{ justifyContent: 'flex-start', marginLeft: 5 }}
                                  keyboardType={'decimal-pad'}
                                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                  placeholder="0"
                                  defaultValue={deliveryDistance}
                                  //iOS
                                  // clearTextOnFocus={true}
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(deliveryDistance)
                                  //   setDeliveryDistance('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (deliveryDistance == '') {
                                  //     setDeliveryDistance(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={text => {
                                    setDeliveryDistance(text);
                                  }}
                                />
                              </View>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                                    color: 'black',
                                    opacity: 1,
                                    justifyContent: 'center'
                                  }}>
                                  KM
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1 }}></View>
                          </View>

                          <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Takeaway Charges Per Product (RM){' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                  placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  keyboardType={'decimal-pad'}
                                  placeholder="0"
                                  containerStyle={{ height: 40 }}
                                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                  itemStyle={{
                                    justifyContent: 'flex-start', marginLeft: 5,
                                  }}
                                  defaultValue={pickUpPrice}
                                  //iOS
                                  // clearTextOnFocus={true}
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(pickUpPrice)
                                  //   setPickUpPrice('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (pickUpPrice == '') {
                                  //     setPickUpPrice(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={text => {
                                    setPickUpPrice(parseValidPriceText(text));
                                  }}
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1 }}></View>
                          </View>

                          <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Delivery Charges Per Product (RM){' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                  placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  keyboardType={'decimal-pad'}
                                  placeholder="0"
                                  containerStyle={{ height: 40 }}
                                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                  itemStyle={{
                                    justifyContent: 'flex-start', marginLeft: 5,
                                  }}
                                  defaultValue={deliveryPrice}
                                  //iOS
                                  // clearTextOnFocus={true}
                                  selectTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  // onFocus={() => {
                                  //   setTemp(deliveryPrice)
                                  //   setDeliveryPrice('');
                                  // }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  // onEndEditing={() => {
                                  //   if (deliveryPrice == '') {
                                  //     setDeliveryPrice(temp);
                                  //   }
                                  // }}
                                  //////////////////////////////////////////////
                                  onChangeText={text => {
                                    setDeliveryPrice(parseValidPriceText(text));
                                  }}
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1 }}></View>
                          </View>

                          {/* <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Takeaway Packaging Fee Per Order (RM){' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                  placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  keyboardType={'decimal-pad'}
                                  placeholder="0"
                                  containerStyle={{ height: 40 }}
                                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                  itemStyle={{
                                    justifyContent: 'flex-start', marginLeft: 5,
                                  }}
                                  defaultValue={pickupPackagingFee}
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(pickupPackagingFee)
                                    setPickupPackagingFee('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (pickupPackagingFee == '') {
                                      setPickupPackagingFee(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={text => {
                                    setPickupPackagingFee(parseValidPriceText(text));
                                  }}
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1 }}></View>
                          </View> */}

                          {/* <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Delivery Packaging Fee Per Order (RM){' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                  placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                  keyboardType={'decimal-pad'}
                                  placeholder="0"
                                  containerStyle={{ height: 40 }}
                                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                  itemStyle={{
                                    justifyContent: 'flex-start', marginLeft: 5,
                                  }}
                                  defaultValue={deliveryPackagingFee}
                                  //iOS
                                  clearTextOnFocus={true}
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(deliveryPackagingFee)
                                    setDeliveryPackagingFee('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (deliveryPackagingFee == '') {
                                      setDeliveryPackagingFee(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={text => {
                                    setDeliveryPackagingFee(parseValidPriceText(text));
                                  }}
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1 }}></View>
                          </View> */}

                          {/* <View
                      style={{ flexDirection: 'row', flex: 1, marginTop: 30, zIndex: -2 }}>
                      <View style={{ justifyContent: 'center', flex: 1 }}>
                        <Text style={styles.textSize}>Delivery Fee: </Text>
                      </View>
                      <View style={{ flex: 1, flexDirection: 'row' }}>
                        <View style={{ flex: 2 }}>
                          <CheckBox
                            style={{
                              paddingVertical: 10,
                            }}
                            onClick={() => {
                              // setState({
                              //   isChecked7: !isChecked7,
                              // });
                              // setIsChecked7(isChecked7);
                              setFreeDeliveyAboveAmountFlag(!freeDeliveyAboveAmountFlag);
                            }}
                            checkBoxColor={Colors.fieldtBgColor}
                            uncheckedCheckBoxColor={Colors.tabGrey}
                            checkedCheckBoxColor={Colors.primaryColor}
                            isChecked={freeDeliveyAboveAmountFlag}
                            rightText={'Free Delivery Above RM:'}
                            rightTextStyle={{
                              fontSize: 13,
                              fontFamily: 'NunitoSans-SemiBold',
                              color: Colors.descriptionColor,
                            }}
                          />
                        </View>
                        <View style={{ flex: 1, height: 35, alignSelf: 'center' }}>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[styles.textInput8, { textAlign: 'center' }]}
                            placeholder="RM"
                            onChangeText={(text) => {
                              // setState({ amount: text });
                              // setAmount(text);
                              setFreeDeliveyAboveAmountValue(text);
                            }}
                            value={freeDeliveyAboveAmountValue}
                            ref={myTextInput}
                            keyboardType={'decimal-pad'}
                          />
                        </View>
                      </View>
                      <View style={{ flex: 1 }}></View>
                    </View> */}
                          {/* <View
                      style={{ flexDirection: 'row', flex: 1, marginTop: 10, alignItems: 'center', zIndex: -2 }}>
                      <View style={{ flex: 1 }}>
                      </View>
                      <View style={{ flex: 1, flexDirection: 'row', alignItems: 'center' }}>
                        <View style={{}}>
                          <Text style={{ color: Colors.descriptionColor, fontSize: 13 }}>
                            Discount (RM):
                          </Text>
                        </View>
                        <View style={{ height: 35, paddingHorizontal: 5 }}>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[styles.textInput8, { textAlign: 'center' }]}
                            placeholder="Amount"
                            onChangeText={(text) => {
                              // setState({ discount: text });
                              // setDiscount(text);
                              setDiscountOrderAboveAmountValue(text);
                            }}
                            value={discountOrderAboveAmountValue}
                            ref={myTextInput}
                            keyboardType={'decimal-pad'}
                          />
                        </View>

                        <View style={{ marginLeft: 15, }}>
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              fontSize: 13,
                              fontFamily: 'NunitoSans-Regular'
                            }}>
                            For Order Above (RM):
                          </Text>
                        </View>

                        <View style={{ height: 35, paddingHorizontal: 5 }}>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[styles.textInput8, { textAlign: 'center', }]}
                            placeholder="Amount"
                            onChangeText={(text) => {
                              // setState({ amount1: text });
                              // setAmount1(text);
                              setDiscountOrderAboveAmountThreshold(text);
                            }}
                            value={discountOrderAboveAmountThreshold}
                            ref={myTextInput}
                            keyboardType={'decimal-pad'}
                          />
                        </View>
                      </View>
                      <View style={{ flex: 1 }}></View>
                    </View> */}

                          {/* <View
                      style={{ flexDirection: 'row', flex: 1, marginTop: 10 }}>
                      <View
                        style={{ justifyContent: 'center', flex: 1 }}></View>
                      <View style={{ flex: 1, flexDirection: 'row' }}>
                        <View style={{ flex: 2 }}>
                          <TouchableOpacity style={styles.addNewView}>
                            <View style={styles.addButtonView1}>
                              <View style={{ marginLeft: '15%' }}>
                                <AntDesign
                                  name="pluscircle"
                                  size={20}
                                  color={Colors.primaryColor}
                                />
                              </View>
                              <Text
                                style={{
                                  marginLeft: 10,
                                  color: Colors.primaryColor,
                                }}>
                                Add Other
                                </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        <View style={{ flex: 1 }}></View>
                      </View>
                      <View style={{ flex: 1 }}></View>
                    </View> */}

                          {/* <View
                      style={{ flexDirection: 'row', flex: 1, marginTop: 30 }}>
                      <View style={{ justifyContent: 'center', flex: 1 }}>
                        <Text style={styles.textSize}>Delivery Hours: </Text>
                      </View>
                      <View style={{ flex: 1, flexDirection: 'row' }}>
                        <View style={{ flex: 3 }}>
                          <View>
                            <View style={{ marginLeft: 25 }}>
                              <Text style={{ color: Colors.descriptionColor, fontSize: 13 }}>
                                Session 1
                              </Text>
                            </View>
                            <View style={styles.textInput9}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholder="FROM"
                                onChangeText={(text) => {
                                  // setState({ hourStart: text });
                                  setHourStart(text);
                                }}
                                value={hourStart}
                                ref={myTextInput}
                              />

                            </View>
                          </View>
                        </View>
                        <View style={{ flex: 1, justifyContent: 'center', marginTop: 5 }}>
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              fontSize: 15,
                            }}>
                            TO
                            </Text>
                        </View>
                        <View style={{ flex: 2 }}>
                          <View>
                            <View style={{ marginLeft: 25 }}>
                              <Text style={{ color: Colors.descriptionColor, fontSize: 13 }}>
                                Session 2
                              </Text>
                            </View>
                            <View style={styles.textInput9}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholder="END"
                                onChangeText={(text) => {
                                  // setState({ hourEnd: text });
                                  setHourEnd(text);
                                }}
                                value={hourEnd}
                                ref={myTextInput}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                      <View style={{ flex: 1 }}></View>
                    </View> */}

                          {/* <View style={{ flex: 1, flexDirection: 'row', alignSelf: 'center' }}>

                      <TouchableOpacity style={styles.addNewView}>
                        <AntDesign
                          name="pluscircle"
                          size={20}
                          color={Colors.primaryColor}
                        />
                        <Text
                          style={{
                            color: Colors.primaryColor,
                            paddingHorizontal: 5
                          }}>
                          Add Sessions
                                </Text>
                      </TouchableOpacity>

                    </View> */}


                        </View>

                      </View>
                    </View>
                    <View
                      style={{
                        // marginBottom: 15,
                        // borderBottomWidth: StyleSheet.hairlineWidth,
                      }}>
                      {/* <View style={{ flexDirection: 'row', padding: 25 }}> */}
                      {/* <View style={{ flex: 3 }}> */}
                      {/* <View> */}
                      {/* <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                        }}>
                        <View style={{ justifyContent: 'center' }}>
                          <Text style={styles.textSize}>
                            Delivery Price:{'    '}
                          </Text>
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                          <View style={{ flex: 12 }}>
                            <CheckBox
                              style={{
                                padding: 10,
                              }}
                              onClick={() => {
                                // setState({
                                //   isChecked8: !isChecked8,
                                // });
                                setIsChecked8(!isChecked8);
                              }}
                              checkBoxColor={Colors.fieldtBgColor}
                              uncheckedCheckBoxColor={Colors.tabGrey}
                              checkedCheckBoxColor={Colors.primaryColor}
                              isChecked={isChecked8}
                              rightText={'Same as Dine-in Price'}
                              rightTextStyle={{
                                fontSize: 15,
                                fontWeight: 'bold',
                                color: Colors.descriptionColor,
                              }}
                            />
                          </View>
                          <View style={{ flex: 1 }}>
                            <NumericInput
                              value={deliveryPrice}
                              onChange={(value) =>
                                // setState({ value1: value })
                                // setValue1(value)
                                setDeliveryPrice(value),
                                // console.log('pressed')
                              }
                              minValue={0}
                              maxValue={50}
                              onLimitReached={(isMax, msg) =>
                                console.log(isMax, msg)
                              }
                              totalWidth={180}
                              totalHeight={40}
                              iconSize={25}
                              step={1}
                              valueType="real"
                              rounded
                              textColor={Colors.primaryColor}
                              iconStyle={{ color: 'white' }}
                              // rightButtonBackgroundColor={'red'}
                              rightButtonBackgroundColor={Colors.primaryColor}
                              leftButtonBackgroundColor={'grey'}
                            />
                          </View>
                        </View>
                         <View style={{ flex: 1 }}></View>
                      </View> */}

                      {/* <View
                        style={{
                          flexDirection: 'row',
                          //flex: 1,
                          //marginTop: 15,
                          justifyContent: 'flex-start',
                          marginLeft: 5
                        }}> */}
                      {/* <View style={{ justifyContent: 'center' }}>
                          <Text style={styles.textSize}>
                            Takeaway Charges:{' '}
                          </Text>
                        </View> */}
                      {/* <View style={{ flexDirection: 'row' }}> */}
                      {/* <View style={{ flex: 12 }}>
                            <CheckBox
                              style={{
                                padding: 10,
                              }}
                              onClick={() => {
                                // setState({
                                //   isChecked9: !isChecked9,
                                // });
                                setIsChecked9(!isChecked9);
                              }}
                              checkBoxColor={Colors.fieldtBgColor}
                              uncheckedCheckBoxColor={Colors.tabGrey}
                              checkedCheckBoxColor={Colors.primaryColor}
                              isChecked={isChecked9}
                              rightText={'Same as Dine-in Price'}
                              rightTextStyle={{
                                fontSize: 15,
                                fontWeight: 'bold',
                                color: Colors.descriptionColor,
                              }}
                            />
                          </View> */}
                      {/* <View style={{ flex: 1 }}> */}
                      {/* <TextInput
                              underlineColorAndroid={Colors.fieldtBgColor}
                              style={styles.textInputTakeawayCharges}
                              placeholderStyle={{ color: 'black' }}
                              itemStyle={{ justifyContent: 'flex-start', marginLeft: 10 }}
                              placeholder="0"
                              onChangeText={(text) => {

                              }}
                            /> */}
                      {/* <NumericInput
                              value={pickUpPrice}
                              onChange={(value) =>
                                // setState({ value2: value })
                                // setValue2(value)
                                setPickUpPrice(value)
                              }
                              minValue={0}
                              onLimitReached={(isMax, msg) =>
                                console.log(isMax, msg)
                              }
                              totalWidth={180}
                              totalHeight={40}
                              iconSize={25}
                              step={1}
                              valueType="real"
                              rounded
                              textColor={Colors.primaryColor}
                              iconStyle={{ color: 'white' }}
                              rightButtonBackgroundColor={Colors.primaryColor}
                              leftButtonBackgroundColor={'grey'}
                            /> */}
                      {/* </View> */}
                      {/* </View> */}
                      {/* <View style={{ flex: 1 }}></View> */}
                      {/* </View> */}
                      {/* </View> */}
                      {/* </View> */}
                      {/* right */}
                      {/* <View style={{ flex: 2 }}></View>
                </View> */}
                    </View>

                    {/* <View
                style={{
                  marginBottom: 15,
                }}>
                <View style={{ flexDirection: 'row', padding: 30 }}>
                  <View style={{ flex: 3 }}>
                    <View>
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                        }}>
                        <View style={{ justifyContent: 'center', flex: 1 }}>
                          <Text style={styles.textSize}>Fire Order: </Text>
                        </View>
                        <View style={{ flex: 1, flexDirection: 'row' }}>
                          <Switch
                            style={{
                              //flexDirection: 'row',
                              width: 50,
                              marginRight: 20,
                              transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                            }}
                            value={status1}
                            onSyncPress={(value) =>
                              // setState({ status1: status1 })
                              setStatus1(value)
                            }
                            circleColorActive={Colors.primaryColor}
                            circleColorInactive={Colors.primaryColor}
                            backgroundActive={Colors.lightPrimary}
                          />
                          <Text
                            style={{
                              fontSize: 20,
                              color: Colors.primaryColor,
                            }}>
                            {status1 ? 'Yes' : 'No'}
                          </Text>
                        </View>
                        <View style={{ flex: 1 }}></View>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                          marginTop: 5,
                        }}>
                        <View style={{ justifyContent: 'center', flex: 1 }}>
                          <Text style={styles.textSize}>Categories: </Text>
                        </View>
                        <View style={{ flex: 1, flexDirection: 'row' }}>
                          <View style={{ flex: 2 }}>
                            <View
                              style={{
                                width: 200,
                                height: 50,
                                justifyContent: 'center',
                                marginLeft: '5%',
                              }}>
                              <DropDownPicker
                                items={categoryOutlet.map((item) => ({
                                  label: item.categoryName, //<= after hayden change you need to change it to item.name
                                  value: item.categoryId,
                                }))}
                                containerStyle={{ height: 40 }}
                                placeholder="Choose Categories"
                                placeholderStyle={{ color: 'black' }}
                                style={{ backgroundColor: '#fafafa' }}
                                itemStyle={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row-reverse'
                                }}
                                defaultValue={'24'}
                                multiple={true}
                                customTickIcon={(press) => <Ionicon name={"checkbox-outline"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                                dropDownStyle={{ backgroundColor: '#fafafa' }}
                                onChangeItem={(item) => {
                                  // setState({
                                  //   category: item.value
                                  // });
                                  setCategory(item.value);
                                }}
                              />
                            </View>
                          </View>
                        </View>
                        <View style={{ flex: 1 }}></View>
                      </View>
                    </View>
                  </View>
                  <View style={{ flex: 2 }}></View>
                </View>
              </View> */}
                    <View style={{ alignItems: 'center' }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#0F1A3C',
                          borderRadius: 5,
                          width: 120,
                          paddingHorizontal: 10,
                          height: 35,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        disabled={loading}
                        onPress={() => { orderFunc() }}>
                        <Text style={{
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                          {loading ? 'LOADING...' : 'SAVE'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginTop: 20 }}></View>
                  </KeyboardAwareScrollView>
                </View>
              ) : null}
            </View>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );

}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: '100%',
    height: '100%',
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInputTakeawayCharges: {
    backgroundColor: Colors.fieldtBgColor,
    width: 200,
    height: 40,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
    //marginLeft: 190,
  },

  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 60,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 5,
    borderColor: '#E5E5E5',
    borderWidth: 1,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 110,
    height: Platform.OS == 'ios' ? 30 : 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput10: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput1: {
    fontFamily: 'NunitoSans-Regular',
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: 'NunitoSans-Regular',
    width: '85%',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 10
  },
  textInput4: {
    width: '85%',
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: 'NunitoSans-Regular',
    width: '80 %',
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: 'center'
  },
  textInput7: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button1: {
    width: '15%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: '60%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: '2%',
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: '30%',
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 30,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold'
  },
  viewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0,
    width: '100%',
    marginBottom: 15,
  },
  openHourContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
    marginBottom: 15,
    width: '100%',
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
  },
  addButtonView1: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 65,
    marginTop: 7,
    width: '83%',
    alignSelf: 'flex-end',
  },
  addNewView1: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    alignItems: 'center',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%'
  },
  shiftView: {
    flexDirection: 'row',
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  closeView: {
    flexDirection: 'row',
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 200,
    height: 40,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  taxView: {
    flexDirection: 'row',
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'flex-end',
  },
  sectionView: {
    flexDirection: 'row',
    borderRadius: 5,
    padding: 16,
    alignItems: 'center',
  },
  receiptView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default SettingScreen;