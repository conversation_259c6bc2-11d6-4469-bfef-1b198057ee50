import ApiClient from './ApiClient';

const API_ROUTE = {
  GET_SHOP_HEALTH: 'tiktok/checkListingPrerequisites',
  GET_CATEGORIES: 'tiktok/getCategories',
  GET_WAREHOUSES: 'tiktok/getWarehouses',

  SYNC_PRODUCT: 'tiktok/syncOutletItem',
  DELETE_PRODUCT: 'tiktok/deleteOutletItem',
  TOGGLE_PRODUCT_ACTIVATION: 'tiktok/toggleProductActivation',
}

export const getTiktokShopHealth = async (outletId) => { 
  const response = await ApiClient.POST(API_ROUTE.GET_SHOP_HEALTH, {
    outletId,
  });

  return response;
}

export const getTiktokCategories = async (outletId) => {
  const response = await ApiClient.POST(API_ROUTE.GET_CATEGORIES, {
    outletId,
  });

  return response;
}

export const getTiktokShopWarehouses = async (outletId) => {
  const response = await ApiClient.POST(API_ROUTE.GET_WAREHOUSES, {
    outletId,
  });

  return response;
}

//////////////////////////////////////////////////

/**
 * Sync products to TikTok Shop
 * @param { Array } items - Array of objects containing:
 *   - item_id: outletItemId (unique identifier for the outlet item)
 *   - category_id: TikTok category ID (must be obtained from getTiktokCategories endpoint)
 */
export const syncTiktokProduct = async (items) => {
  // Validate payload before sending
  if (!items || !Array.isArray(items) || items.length === 0) {
    throw new Error('Items array is required and must not be empty');
  }

  // Validate each item's required attributes
  items.forEach((item, i) => {
    if (!item?.item_id || !item?.category_id) {
      throw new Error(`Item at index ${i} is missing required attributes`);
    }
  });

  try {
    const payload = {
      items,
    };

    const response = await ApiClient.POST(API_ROUTE.SYNC_PRODUCT, payload);

    return response;
  } 
  catch (error) {
    console.error(error);
    throw new Error(error.message);
  }
}

/**
 * Delete a product from TikTok Shop
 * @param { String | String[] } item_id - Outlet item ID
 * Prerequisites: - The outletItemId must have tiktokItemId
 */
export const deleteTiktokProduct = async (item_id) => {
  try {
    const payload = {
      item_id,
    };

    const response = await ApiClient.POST(API_ROUTE.DELETE_PRODUCT, payload);

    return response;
  }
  catch (error) {
    console.error(error);
    throw new Error(error.message);
  }
}

export const toggleTiktokProductActivation = async (item_id, is_active) => {
  const response = await ApiClient.POST(API_ROUTE.TOGGLE_PRODUCT_ACTIVATION, {
    item_id,
    is_active,
  });

  return response;
}