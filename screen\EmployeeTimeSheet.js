import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useRef, useCallback } from 'react';
import {
    StyleSheet,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Dimensions,
    Platform,
    Switch,
    Modal as ModalComponent,
    KeyboardAvoidingView,
    TextInput,
    ActivityIndicator,
    useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import GCalendar from '../assets/svg/GCalendar';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { <PERSON>List, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Styles from '../constant/Styles';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CheckBox from 'react-native-check-box';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {
    isTablet,
    getTransformForModalInsideNavigation,
    getTransformForScreenInsideNavigation,
    logEventAnalytics,
} from '../util/common';
import { CommonStore } from '../store/commonStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import Upload from '../assets/svg/Upload';
import Download from '../assets/svg/Download';
import {
    convertArrayToCSV,
    generateEmailReport,
    sortReportDataList,
} from '../util/common';
import {
    EMAIL_REPORT_TYPE,
    REPORT_SORT_FIELD_TYPE,
    TABLE_PAGE_SIZE_DROPDOWN_LIST,
    ORDER_TYPE,
    USER_ORDER_ACTION_PARSED,
    EXPAND_TAB_TYPE,
} from '../constant/common';
import RNFetchBlob from 'rn-fetch-blob';
import { useKeyboard } from '../hooks';
import XLSX from 'xlsx';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import RNPickerSelect from 'react-native-picker-select';
import AsyncImage from '../components/asyncImage';
import Feather from 'react-native-vector-icons/Feather';
import Tooltip from 'react-native-walkthrough-tooltip';

import firestore from '@react-native-firebase/firestore';
import { Collections } from '../constant/firebase';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';

const { nanoid } = require('nanoid');
const RNFS = require('@dr.pogodin/react-native-fs');
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const EmployeeTimeSheet = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [keyboardHeight] = useKeyboard();
    const [visible, setVisible] = useState(false);
    const [perPage, setPerPage] = useState(10);
    const [pageCount, setPageCount] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    const [pageReturn, setPageReturn] = useState(1);
    const [search, setSearch] = useState('');

    const [loading, setLoading] = useState(false);

    const [pushPagingToTop, setPushPagingToTop] = useState(false);

    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
    const [rev_date, setRev_date] = useState(
        moment().subtract(6, 'days').startOf('day'),
    );
    const [rev_date1, setRev_date1] = useState(
        moment().endOf(Date.now()).endOf('day'),
    );

    const userName = UserStore.useState((s) => s.name);

    const [exportEmail, setExportEmail] = useState('');

    const [showDetails, setShowDetails] = useState(false);

    const [exportModalVisibility, setExportModalVisibility] = useState(false);


    const merchantId = UserStore.useState((s) => s.merchantId);
    const isLoading = CommonStore.useState((s) => s.isLoading);
    const [isCsv, setIsCsv] = useState(false);
    const [isExcel, setIsExcel] = useState(false);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );


    const allOutletsEmployees = OutletStore.useState(s => s.allOutletsEmployees);
    const employeeClockDict = OutletStore.useState(s => s.employeeClockDict);

    const [allOutletsEmployeesClock, setAllOutletsEmployeesClock] = useState([]);
    const [allOutletsEmployeesDetails, setAllOutletsEmployeesDetails] = useState([]);

    const [employeeClockUserIdDict, setEmployeeClockUserIdDict] = useState({});

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);
    const allOutlets = MerchantStore.useState((s) => s.allOutlets);
    const currOutletId = MerchantStore.useState((s) => s.currOutletId);

    // const isMasterAccount = UserStore.useState((s) => s.isMasterAccount);

    // useEffect(() => {
    //     if (!isMounted) {
    //         if (unsubscribeRef.current) {
    //             unsubscribeRef.current();
    //             unsubscribeRef.current = null;
    //         }

    //         OutletStore.update((s) => {
    //             s.allOutletsEmployeesUserActionsDict =
    //                 {};
    //         });

    //         return;
    //     }

    //     let query = null;

    //     if (isMasterAccount) {
    //         query = firestore()
    //             .collection(Collections.UserAction)
    //             .where('merchantId', '==', merchantId);
    //     }
    //     else {
    //         query = firestore()
    //             .collection(Collections.UserAction)
    //             .where('outletId', '==', currOutletId);
    //     }

    //     // Store unsubscribe function for cleanup
    //     const unsubscribe = query.onSnapshot((snapshot) => {
    //         global.emitter.emit(`${Collections.UserAction}-snapshot`, { snapshot });
    //     });

    //     unsubscribeRef.current = unsubscribe;

    //     return () => {
    //         if (unsubscribeRef.current) {
    //             unsubscribeRef.current();
    //             unsubscribeRef.current = null;
    //         }
    //     };
    // }, [isMounted, isMasterAccount, currOutletId]);

    useEffect(() => {
        const useEffectCallback = async () => {
            if (showDetails && allOutletsEmployeesDetails) {
                setPageReturn(currentPage);
                // console.log('currentPage value is');
                // console.log(currentPage);
                setCurrentDetailsPage(1);
                setPageCount(Math.ceil(allOutletsEmployeesDetails.length / perPage));
            }
        };

        useEffectCallback();
    }, [showDetails, allOutletsEmployeesDetails, perPage]);

    useEffect(() => {
        const useEffectCallback = async () => {
            // const userActionSnapshot = await firestore()
            //   .collection(Collections.UserAction)
            //   .get();

            // const userAction = userActionSnapshot.docs.map(doc => doc.data());

            const tempAllOutletsEmployeesClock = allOutletsEmployees.map(
                (employee) => {
                    // const employeeAction = employeeClockDict.reduce(
                    //   (prev, curr) => {
                    //     if (curr.userId == employee.firebaseUid) {
                    //       return curr.actions;
                    //     }
                    //     return [];
                    //   },
                    //   []
                    // );

                    // console.log('==========================');
                    // console.log(employee.name);
                    // console.log(employee.firebaseUid);
                    // console.log(employeeClockDict[employee.firebaseUid]);

                    var employeeClock = [];

                    if (employeeClockDict[employee.firebaseUid]) {
                        var filteredClockRecords = [];

                        if (employeeClockDict[employee.firebaseUid].clockRecords &&
                            employeeClockDict[employee.firebaseUid].clockRecords.length > 0) {
                            filteredClockRecords = employeeClockDict[employee.firebaseUid].clockRecords.filter(record => moment(record.clockInTime, 'x').isBetween(rev_date, rev_date1) || moment(record.clockOutTime, 'x').isBetween(rev_date, rev_date1));
                        }

                        var additionalActions = [];
                        if (employeeClockUserIdDict[employee.firebaseUid] &&
                            employeeClockUserIdDict[employee.firebaseUid].length > 0) {
                            additionalActions = employeeClockUserIdDict[employee.firebaseUid].filter(action => moment(action.clockInTime, 'x').isBetween(rev_date, rev_date1) || moment(action.clockOutTime, 'x').isBetween(rev_date, rev_date1));
                        }

                        employeeClock = [
                            ...filteredClockRecords,
                            ...additionalActions,
                        ];
                    }

                    console.log('employeeClock');
                    console.log(employeeClock);

                    return {
                        ...employee,
                        detailsList: employeeClock.map(clock => ({
                            ...clock,
                            day: clock.clockInTime
                                ?
                                (moment(clock.clockInTime).format('dddd'))
                                :
                                'N/A',
                            duration: clock.clockInTime && clock.clockOutTime
                                ?
                                Number((moment(clock.clockOutTime).diff(clock.clockInTime, 'minute') / 60).toFixed(2))
                                :
                                0.00,
                            name: employee.name,
                            role: employee.role,
                            outletId: employee.outletId,
                        })).reverse(),
                    };
                }
            );

            // console.log('allOutletsEmployeesClock', tempAllOutletsEmployeesClock);
            setAllOutletsEmployeesClock(tempAllOutletsEmployeesClock);

            setPageCount(Math.ceil(tempAllOutletsEmployeesClock.length / perPage));
        };

        useEffectCallback();
    }, [allOutletsEmployees, employeeClockDict, rev_date, rev_date1, employeeClockUserIdDict]);

    useEffect(() => {
        const useEffectCallback = async () => {
            var userActionSliceList = [];

            const userActionSliceStartSnapshot = await firestore()
                .collection(Collections.EmployeeClockSlice)
                .where('outletId', '==', currOutletId)
                .where('startDate', '>=', moment(rev_date).valueOf())
                .where('startDate', '<=', moment(rev_date1).valueOf())
                .orderBy('startDate', 'desc') // get the latest data first
                // .where('createdAt', '>=', moment(rev_date).valueOf())
                // .where('createdAt', '<=', moment(rev_date1).valueOf())
                // .orderBy('createdAt', 'desc') // get the latest data first
                .limit(500)
                .get();

            if (userActionSliceStartSnapshot) {
                for (var i = 0; i < userActionSliceStartSnapshot.size; i++) {
                    userActionSliceList.push(userActionSliceStartSnapshot.docs[i].data());
                }
            }

            const userActionSliceEndSnapshot = await firestore()
                .collection(Collections.EmployeeClockSlice)
                .where('outletId', '==', currOutletId)
                .where('endDate', '>=', moment(rev_date).valueOf())
                .where('endDate', '<=', moment(rev_date1).valueOf())
                .orderBy('endDate', 'desc') // get the latest data first
                .limit(500)
                .get();

            if (userActionSliceEndSnapshot) {
                for (var i = 0; i < userActionSliceEndSnapshot.size; i++) {
                    const record = userActionSliceEndSnapshot.docs[i].data();

                    if (userActionSliceList.find(findItem => findItem.uniqueId === record.uniqueId)) {
                        // existed, no need add
                    }
                    else {
                        userActionSliceList.push(record);
                    }
                }
            }

            var userActionSliceEmployeeUserIdDictTemp = {};

            for (var i = 0; i < userActionSliceList.length; i++) {
                const userActionSlice = userActionSliceList[i];

                if (userActionSliceEmployeeUserIdDictTemp[userActionSlice.firebaseUid]) {
                    userActionSliceEmployeeUserIdDictTemp[userActionSlice.firebaseUid] = [
                        ...userActionSliceEmployeeUserIdDictTemp[userActionSlice.firebaseUid],

                        userActionSlice.clockRecords,
                    ];
                }
                else {
                    userActionSliceEmployeeUserIdDictTemp[userActionSlice.firebaseUid] = userActionSlice.clockRecords;
                }
            }

            setEmployeeClockUserIdDict(userActionSliceEmployeeUserIdDictTemp);
        };

        useEffectCallback();
    }, [rev_date, rev_date1, currOutletId]);

    const setState = () => { };

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('MenuOrderingScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'MenuOrderingScreen';
                            s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                        });
                    }

                    logEventAnalytics({
                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_C_LOGO,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_C_LOGO
                    })
                }}
                style={styles.headerLeftStyle}>
                <Image
                    style={[{
                        width: 124,
                        height: 26,
                    }, switchMerchant ? {
                        transform: [
                            { scaleX: 0.7 },
                            { scaleY: 0.7 }
                        ],
                    } : {}]}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        ...global.getHeaderTitleStyle(),
                    },
                    // windowWidth >= 768 && switchMerchant
                    //     ? { right: windowWidth * 0.1 }
                    //     : {},
                    // windowWidth <= 768
                    //     ? { right: 20 }
                    //     : {},

                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Employee Timesheet
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }

                        logEventAnalytics({
                            eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_C_PROFILE,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_C_PROFILE
                        })
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });


    const nextPage = () => {
        setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
    };

    const prevPage = () => {
        setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
    };

    const nextDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage + 1 > pageCount
                ? currentDetailsPage
                : currentDetailsPage + 1,
        );
    };

    const prevDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage - 1 < 1 ? currentDetailsPage : currentDetailsPage - 1,
        );
    };

    const renderItem = ({ item, index }) => {

        return (
            <TouchableOpacity
                onPress={() => {
                    setShowDetails(true);
                    setAllOutletsEmployeesDetails(item.detailsList);

                    logEventAnalytics({
                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_TABLE_C_DATA,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_TABLE_C_DATA
                    })
                }}
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    paddingHorizontal: 3,
                    paddingLeft: 1,
                    borderColor: '#BDBDBD',
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                }}>
                <View style={{ flexDirection: 'row' }}>
                    <Text
                        style={{
                            width: '5%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {index + 1}
                    </Text>
                    <Text
                        style={{
                            width: '25%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.name}
                    </Text>
                    <Text
                        style={{
                            width: '25%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.detailsList.length}
                    </Text>
                </View>
            </TouchableOpacity>
        );
    };

    const renderItemDetails = ({ item, index }) => {

        return (
            <View
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    borderColor: '#BDBDBD',
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                }}>
                <View style={{ flexDirection: 'row' }}>
                    <Text
                        style={{
                            width: '6%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: 'NunitoSans-Regular',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {index + 1}
                    </Text>
                    <Text
                        style={{
                            width: '23.5%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontWeight: '500',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.day}
                    </Text>
                    <Text
                        style={{
                            width: '23.5%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontWeight: '500',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.clockInTime ? moment(item.clockInTime, 'x').format('DD MMM YY hh:mm A') : 'N/A'}
                    </Text>
                    <Text
                        style={{
                            width: '23.5%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontWeight: '500',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.clockOutTime ? moment(item.clockOutTime, 'x').format('DD MMM YY hh:mm A') : 'N/A'}
                    </Text>
                    <Text
                        style={{
                            width: '23.5%',
                            fontSize: switchMerchant ? 10 : 13,
                            fontWeight: '500',
                            textAlign: 'left',
                            paddingLeft: 10,
                        }}>
                        {item.duration}
                    </Text>
                </View>
            </View>
        );
    };

    // Support function for generate Excel/CSV
    const getOutletName = (outletId, allOutletsList) => {
        if (!allOutletsList) return '-';
        const outlet = allOutletsList.find(o => o.uniqueId === outletId);
        return outlet ? outlet.name : '-';
    };

    const convertDataToExcelFormat = () => {
        const excelData = [];
        const allEvents = [];

        // Collect Data
        if (!showDetails) {
            if (allOutletsEmployeesClock) {
                allOutletsEmployeesClock.forEach(employee => {
                    if (employee.detailsList && employee.detailsList.length > 0) {
                        employee.detailsList.forEach(detail => {
                            allEvents.push({
                                employeeName: employee.name || '-',
                                role: employee.role || '-',
                                eventDateTimestamp: detail.clockInTime,
                                clockInTime: detail.clockInTime,
                                clockOutTime: detail.clockOutTime,
                                duration: detail.duration,
                                outletId: employee.outletId,
                            });
                        });
                    }
                });
            }
        } 
        else {
            if (allOutletsEmployeesDetails) {
                allOutletsEmployeesDetails.forEach(detail => {
                    allEvents.push({
                        employeeName: detail.name || '-',
                        role: detail.role || '-',
                        eventDateTimestamp: detail.clockInTime,
                        clockInTime: detail.clockInTime,
                        clockOutTime: detail.clockOutTime,
                        duration: detail.duration,
                        outletId: detail.outletId,
                    });
                });
            }
        }

        const groupedByDateAndEmployee = allEvents.reduce((acc, event) => {
            if (!event.eventDateTimestamp) return acc;
            const eventMoment = moment(event.eventDateTimestamp, 'x');
            const dateKey = eventMoment.format('YYYY-MM-DD');

            acc[dateKey] = acc[dateKey] || {};
            
            if (!acc[dateKey][event.employeeName]) {
                acc[dateKey][event.employeeName] = {
                    role: event.role,
                    firstOutletIdForDay: event.outletId, 
                    events: []
                };
            }

            acc[dateKey][event.employeeName].events.push({
                clockIn: event.clockInTime ? moment(event.clockInTime, 'x').format('DD MMM YY hh:mm A') : '-',
                clockOut: event.clockOutTime ? moment(event.clockOutTime, 'x').format('DD MMM YY hh:mm A') : '-',
                duration: (typeof event.duration === 'number' && !isNaN(event.duration)) 
                            ? parseFloat(event.duration).toFixed(2) 
                            : (event.duration === 0 ? '0.00' : (event.duration || '-')),
            });
            return acc;
        }, {});

        // Determine maximum clock events per day
        let maxClockEventsPerDay = 0;
        Object.values(groupedByDateAndEmployee).forEach(employeesOnDate => {
            Object.values(employeesOnDate).forEach(employeeData => {
                maxClockEventsPerDay = Math.max(maxClockEventsPerDay, employeeData.events.length);
            });
        });

        // Output
        const sortedDates = Object.keys(groupedByDateAndEmployee).sort((a, b) => moment(a, 'YYYY-MM-DD').valueOf() - moment(b, 'YYYY-MM-DD').valueOf());

        for (const dateKey of sortedDates) {
            const employeesOnThisDate = groupedByDateAndEmployee[dateKey];
            const sortedEmployeeNames = Object.keys(employeesOnThisDate).sort();

            for (const empName of sortedEmployeeNames) {
                const employeeData = employeesOnThisDate[empName];
                const row = {
                    'Date': moment(dateKey, 'YYYY-MM-DD').format('DD MMM YYYY'),
                    'Day': moment(dateKey, 'YYYY-MM-DD').format('dddd'),
                    'Employee Name': empName,
                    'Role': employeeData.role,
                    'Outlet': getOutletName(employeeData.firstOutletIdForDay, allOutlets),
                };

                for (let i = 0; i < maxClockEventsPerDay; i++) {
                    const eventDetail = employeeData.events[i];
                    if (eventDetail) {
                        row[`Clock In ${i + 1}`] = eventDetail.clockIn;
                        row[`Clock Out ${i + 1}`] = eventDetail.clockOut;
                        row[`Duration ${i + 1}`] = eventDetail.duration;
                    } else {
                        row[`Clock In ${i + 1}`] = '-';
                        row[`Clock Out ${i + 1}`] = '-';
                        row[`Duration ${i + 1}`] = '-';
                    }
                }
                excelData.push(row);
            }
        }
        return excelData;
    };

    const convertDataToCSVFormat = () => {
        const csvBuildArray = [];
        const allEvents = [];

        // Collect Data
        if (!showDetails) {
            if (allOutletsEmployeesClock) {
                allOutletsEmployeesClock.forEach(employee => {
                    if (employee.detailsList && employee.detailsList.length > 0) {
                        employee.detailsList.forEach(detail => {
                            allEvents.push({
                                employeeName: employee.name || '-',
                                role: employee.role || '-',
                                eventDateTimestamp: detail.clockInTime,
                                clockInTime: detail.clockInTime,
                                clockOutTime: detail.clockOutTime,
                                duration: detail.duration,
                                outletId: employee.outletId,
                            });
                        });
                    }
                });
            }
        } 
        else {
            if (allOutletsEmployeesDetails) {
                allOutletsEmployeesDetails.forEach(detail => {
                    allEvents.push({
                        employeeName: detail.name || '-',
                        role: detail.role || '-',
                        eventDateTimestamp: detail.clockInTime,
                        clockInTime: detail.clockInTime,
                        clockOutTime: detail.clockOutTime,
                        duration: detail.duration,
                        outletId: detail.outletId,
                    });
                });
            }
        }

        const groupedByDateAndEmployee = allEvents.reduce((acc, event) => {
            if (!event.eventDateTimestamp) return acc;
            const eventMoment = moment(event.eventDateTimestamp, 'x');
            const dateKey = eventMoment.format('YYYY-MM-DD');

            acc[dateKey] = acc[dateKey] || {};
            
            if (!acc[dateKey][event.employeeName]) {
                acc[dateKey][event.employeeName] = {
                    role: event.role,
                    firstOutletIdForDay: event.outletId,
                    events: []
                };
            }

            acc[dateKey][event.employeeName].events.push({
                clockIn: event.clockInTime ? moment(event.clockInTime, 'x').format('DD MMM YY hh:mm A') : '-',
                clockOut: event.clockOutTime ? moment(event.clockOutTime, 'x').format('DD MMM YY hh:mm A') : '-',
                duration: (typeof event.duration === 'number' && !isNaN(event.duration)) 
                            ? parseFloat(event.duration).toFixed(2) 
                            : (event.duration === 0 ? '0.00' : (event.duration || '-')),
            });
            return acc;
        }, {});

        let maxClockEventsPerDay = 0;
        Object.values(groupedByDateAndEmployee).forEach(employeesOnDate => {
            Object.values(employeesOnDate).forEach(employeeData => {
                maxClockEventsPerDay = Math.max(maxClockEventsPerDay, employeeData.events.length);
            });
        });

        // Construct Headers
        const headers = ['Date', 'Day', 'Employee Name', 'Role', 'Outlet'];
        for (let i = 0; i < maxClockEventsPerDay; i++) {
            headers.push(`Clock In ${i + 1}`);
            headers.push(`Clock Out ${i + 1}`);
            headers.push(`Duration ${i + 1}`);
        }
        csvBuildArray.push(headers.join(','));

        // Construct Rows Data
        const sortedDates = Object.keys(groupedByDateAndEmployee).sort((a, b) => moment(a, 'YYYY-MM-DD').valueOf() - moment(b, 'YYYY-MM-DD').valueOf());

        for (const dateKey of sortedDates) {
            const employeesOnThisDate = groupedByDateAndEmployee[dateKey];
            const sortedEmployeeNames = Object.keys(employeesOnThisDate).sort();

            for (const empName of sortedEmployeeNames) {
                const employeeData = employeesOnThisDate[empName];
                const tempRowObject = {
                    'Date': moment(dateKey, 'YYYY-MM-DD').format('DD MMM YYYY'),
                    'Day': moment(dateKey, 'YYYY-MM-DD').format('dddd'),
                    'Employee Name': empName,
                    'Role': employeeData.role,
                    'Outlet': getOutletName(employeeData.firstOutletIdForDay, allOutlets),
                };

                for (let i = 0; i < maxClockEventsPerDay; i++) {
                    const eventDetail = employeeData.events[i];
                    if (eventDetail) {
                        tempRowObject[`Clock In ${i + 1}`] = eventDetail.clockIn;
                        tempRowObject[`Clock Out ${i + 1}`] = eventDetail.clockOut;
                        tempRowObject[`Duration ${i + 1}`] = eventDetail.duration;
                        // tempRowObject[`Outlet ${i + 1}`] = getOutletName(eventDetail.outletId, allOutlets);
                    } else {
                        tempRowObject[`Clock In ${i + 1}`] = '-';
                        tempRowObject[`Clock Out ${i + 1}`] = '-';
                        tempRowObject[`Duration ${i + 1}`] = '-';
                        // tempRowObject[`Outlet ${i + 1}`] = '-';
                    }
                }
                
                const csvRowValues = headers.map(header => {
                    let value = tempRowObject[header];
                    if (value === null || typeof value === 'undefined') {
                        value = '-'; // 确保未定义的值也用 '-'
                    }
                    let stringValue = String(value);
                    if (stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('"')) {
                        stringValue = `"${stringValue.replace(/"/g, '""')}"`;
                    }
                    return stringValue;
                });
                csvBuildArray.push(csvRowValues.join(','));
            }
        }
        
        if (csvBuildArray.length === 1 && allEvents.length === 0) {
            // return ''
        }

        return csvBuildArray.join('\r\n');
    };

    const emailVariant = () => {
        const excelData = convertDataToExcelFormat();

        var body = {
            // data: CsvData,
            //data: convertArrayToCSV(todaySalesChart.dataSource.data),
            data: JSON.stringify(excelData),
            //data: convertDataToExcelFormat(),
            email: exportEmail,
        };

        ApiClient.POST(API.emailDashboard, body, false).then((result) => {
            if (result !== null) {
                Alert.alert(
                    'Success',
                    'Email has been sent',
                    [{ text: 'OK', onPress: () => { } }],
                    { cancelable: false },
                );
            }
        });

        setVisible(false);
    };

    var leftSpacing = '0%';

    if (windowWidth >= 1280) {
        leftSpacing = '10%';
    }

    const flatListRef = useRef();

    const filterItem = (item) => {
        if (search !== '') {
            if (
                item.name
                    .toLowerCase()
                    .includes(search.toLowerCase())
            ) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    const filterItemDetails = (item) => {
        if (search !== '') {
            if (
                item.clockInTime &&
                moment(item.clockInTime, 'x')
                    .format('DD MMM YYY hh:mma')
                    .toLowerCase()
                    .includes(search.toLowerCase())
            ) {
                return true;
            } else if (
                item.clockOutTime &&
                moment(item.clockOutTime, 'x')
                    .format('DD MMM YYY hh:mma')
                    .toLowerCase()
                    .includes(search.toLowerCase())
            ) {
                return true;
            } else if (
                item.day &&
                item.day.toLowerCase().includes(search.toLowerCase())
            ) {
                return true;
            } else if (
                item.duration &&
                item.duration.toLowerCase().includes(search.toLowerCase())
            ) {
                return true;
            } else {
                return false;
            }
        } else {
            // check if there is data between the dates
            // return moment(item.clockInTime, 'x').isBetween(rev_date, rev_date1);

            return true;
        }
    }

    return (
        <UserIdleWrapper disabled={!isMounted}>
            <View
                style={[
                    styles.container,
                    !isTablet()
                        ? {
                            transform: [{ scaleX: 1 }, { scaleY: 1 }],
                        }
                        : {},
                    {
                        ...getTransformForScreenInsideNavigation(),
                    }
                ]}>
                {/* <View
                    style={[
                        styles.sidebar,
                        !isTablet()
                            ? {
                                width: windowWidth * 0.08,
                            }
                            : {},
                        switchMerchant
                            ? {
                                // width: '10%'
                            }
                            : {},

                        {
                            width: windowWidth * 0.08,
                        }
                    ]}>
                    <SideBar
                        navigation={props.navigation}
                        selectedTab={9}
                        expandEmployees
                    />
                </View> */}
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{}}
                    contentContainerStyle={{
                        paddingBottom: windowHeight * 0.1,
                        backgroundColor: Colors.highlightColor,
                    }}>
                    <ScrollView horizontal>
                        <ModalView
                            style={{}}
                            visible={exportModalVisibility}
                            supportedOrientations={['portrait', 'landscape']}
                            transparent
                            animationType={'fade'}>
                            <View
                                style={{
                                    flex: 1,
                                    backgroundColor: Colors.modalBgColor,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    top:
                                        Platform.OS === 'android'
                                            ? 0
                                            : keyboardHeight > 0
                                                ? -keyboardHeight * 0.45
                                                : 0,
                                }}>
                                <View
                                    style={{
                                        height: windowWidth * 0.3,
                                        width: windowWidth * 0.4,
                                        backgroundColor: Colors.whiteColor,
                                        borderRadius: 12,
                                        padding: windowWidth * 0.03,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        ...getTransformForModalInsideNavigation(),
                                    }}>
                                    <TouchableOpacity
                                        disabled={isLoading}
                                        style={{
                                            position: 'absolute',
                                            right: windowWidth * 0.02,
                                            top: windowWidth * 0.02,

                                            elevation: 1000,
                                            zIndex: 1000,
                                        }}
                                        onPress={() => {
                                            setExportModalVisibility(false);
                                        }}>
                                        <AntDesign
                                            name="closecircle"
                                            size={switchMerchant ? 15 : 25}
                                            color={Colors.fieldtTxtColor}
                                        />
                                    </TouchableOpacity>
                                    <View
                                        style={{
                                            alignItems: 'center',
                                            top: '20%',
                                            position: 'absolute',
                                        }}>
                                        <Text
                                            style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                textAlign: 'center',
                                                fontSize: switchMerchant ? 16 : 24,
                                            }}>
                                            Download Report
                                        </Text>
                                    </View>
                                    <View style={{ top: switchMerchant ? '14%' : '10%' }}>
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 10 : 20,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            Email Address:
                                        </Text>
                                        <TextInput
                                            underlineColorAndroid={Colors.fieldtBgColor}
                                            style={{
                                                backgroundColor: Colors.fieldtBgColor,
                                                width: switchMerchant ? 240 : 370,
                                                height: switchMerchant ? 35 : 50,
                                                borderRadius: 5,
                                                padding: 5,
                                                marginVertical: 5,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                                paddingLeft: 10,
                                                fontSize: switchMerchant ? 10 : 14,
                                            }}
                                            autoCapitalize='none'
                                            placeholderStyle={{ padding: 5 }}
                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                            placeholder="Enter your email"
                                            onChangeText={(text) => {
                                                setExportEmail(text);
                                                logEventAnalytics({
                                                    eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_TB_EMAIL,
                                                    eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_TB_EMAIL
                                                })
                                            }}
                                            value={exportEmail}
                                        />
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 10 : 20,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginTop: 15,
                                            }}>
                                            Send As:
                                        </Text>

                                        <View
                                            style={{
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                marginTop: 10,
                                            }}>
                                            <TouchableOpacity
                                                disabled={isLoading}
                                                style={{
                                                    justifyContent: 'center',
                                                    flexDirection: 'row',
                                                    borderWidth: 1,
                                                    borderColor: Colors.primaryColor,
                                                    backgroundColor: '#0F1A3C',
                                                    borderRadius: 5,
                                                    width: switchMerchant ? 100 : 100,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: 'center',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -1,
                                                    marginRight: 15,
                                                }}
                                                onPress={() => {
                                                    if (exportEmail.length > 0) {
                                                        CommonStore.update((s) => {
                                                            s.isLoading = true;
                                                        });

                                                        setIsExcel(true);

                                                        const excelData = convertDataToExcelFormat();

                                                        generateEmailReport(
                                                            EMAIL_REPORT_TYPE.EXCEL,
                                                            excelData,
                                                            'KooDoo Employee Timesheet',
                                                            'KooDoo Employee Timesheet.xlsx',
                                                            `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                                            exportEmail,
                                                            'KooDoo Employee Timesheet',
                                                            'KooDoo Employee Timesheet',
                                                            () => {
                                                                CommonStore.update((s) => {
                                                                    s.isLoading = false;
                                                                });

                                                                setIsExcel(false);

                                                                Alert.alert(
                                                                    'Success',
                                                                    'Report will be sent to the email address shortly',
                                                                );

                                                                setExportModalVisibility(false);
                                                            },
                                                            [],
                                                            false
                                                        );
                                                    } else {
                                                        Alert.alert('Info', 'Invalid email address');
                                                    }

                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_C_REP_EXCEL,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_C_REP_EXCEL
                                                    })
                                                }}>
                                                {isLoading && isExcel ? (
                                                    <ActivityIndicator
                                                        size={'small'}
                                                        color={Colors.whiteColor}
                                                    />
                                                ) : (
                                                    <Text
                                                        style={{
                                                            color: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        EXCEL
                                                    </Text>
                                                )}
                                            </TouchableOpacity>

                                            <TouchableOpacity
                                                disabled={isLoading}
                                                style={{
                                                    justifyContent: 'center',
                                                    flexDirection: 'row',
                                                    borderWidth: 1,
                                                    borderColor: Colors.primaryColor,
                                                    backgroundColor: '#0F1A3C',
                                                    borderRadius: 5,
                                                    width: switchMerchant ? 100 : 100,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: 'center',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -1,
                                                }}
                                                onPress={() => {
                                                    if (exportEmail.length > 0) {
                                                        CommonStore.update((s) => {
                                                            s.isLoading = true;
                                                        });

                                                        setIsCsv(true);

                                                        const csvData = convertDataToCSVFormat();

                                                        generateEmailReport(
                                                            EMAIL_REPORT_TYPE.CSV,
                                                            csvData,
                                                            'KooDoo Employee Timesheet Report',
                                                            'KooDoo Employee Timesheet Report.csv',
                                                            `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                                            exportEmail,
                                                            'KooDoo Employee Timesheet Report',
                                                            'KooDoo Employee Timesheet Report',
                                                            () => {
                                                                CommonStore.update((s) => {
                                                                    s.isLoading = false;
                                                                });

                                                                setIsCsv(false);

                                                                Alert.alert(
                                                                    'Success',
                                                                    'Report will be sent to the email address shortly',
                                                                );

                                                                setExportModalVisibility(false);
                                                            },
                                                            [],
                                                            false
                                                        );
                                                    } else {
                                                        Alert.alert('Info', 'Invalid email address');
                                                    }

                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_C_REP_CSV,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_DL_BTN_C_REP_CSV
                                                    })
                                                }}>
                                                {isLoading && isCsv ? (
                                                    <ActivityIndicator
                                                        size={'small'}
                                                        color={Colors.whiteColor}
                                                    />
                                                ) : (
                                                    <Text
                                                        style={{
                                                            color: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        CSV
                                                    </Text>
                                                )}
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </ModalView>

                        <DateTimePickerModal
                            isVisible={showDateTimePicker}
                            mode={'date'}
                            onConfirm={(text) => {
                                setRev_date(moment(text).startOf('day'));
                                setShowDateTimePicker(false);
                            }}
                            onCancel={() => {
                                setShowDateTimePicker(false);
                            }}
                            maximumDate={moment(rev_date1).toDate()}
                            date={moment(rev_date).toDate()}
                        />

                        <DateTimePickerModal
                            isVisible={showDateTimePicker1}
                            mode={'date'}
                            onConfirm={(text) => {
                                setRev_date1(moment(text).endOf('day'));
                                setShowDateTimePicker1(false);
                            }}
                            onCancel={() => {
                                setShowDateTimePicker1(false);
                            }}
                            minimumDate={moment(rev_date).toDate()}
                            date={moment(rev_date1).toDate()}
                        />

                        <ModalView
                            supportedOrientations={['landscape', 'portrait']}
                            style={{ flex: 1 }}
                            visible={visible}
                            transparent
                            animationType="slide">
                            <KeyboardAvoidingView
                                style={{
                                    backgroundColor: 'rgba(0,0,0,0.5)',
                                    flex: 1,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    minHeight: windowHeight,
                                    top:
                                        Platform.OS === 'ios' && keyboardHeight > 0
                                            ? -keyboardHeight * 0.5
                                            : 0,
                                }}>
                                <View style={[styles.confirmBox1, { ...getTransformForModalInsideNavigation(), }]}>
                                    <Text
                                        style={{
                                            fontSize: 24,
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            marginTop: 40,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        Enter your email
                                    </Text>
                                    <View
                                        style={{
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            alignContent: 'center',
                                            marginTop: 20,
                                            flexDirection: 'row',
                                            width: '80%',
                                        }}>
                                        <View style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                                            <Text
                                                style={{ color: Colors.descriptionColor, fontSize: 20 }}>
                                                email:
                                            </Text>
                                        </View>
                                        <TextInput
                                            underlineColorAndroid={Colors.fieldtBgColor}
                                            style={[styles.textInput8, { paddingLeft: 5 }]}
                                            placeholder="Enter your email"
                                            // style={{
                                            //     // paddingLeft: 1,
                                            // }}
                                            //defaultValue={extentionCharges}
                                            onChangeText={(text) => {
                                                // setState({ exportEmail: text });
                                                setExportEmail(text);
                                            }}
                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                            value={exportEmail}
                                        />
                                    </View>
                                    <Text
                                        style={{
                                            fontSize: 20,
                                            fontFamily: 'NunitoSans-Bold',
                                            marginTop: 25,
                                            justifyContent: 'center',
                                            alignSelf: 'center',
                                            alignContent: 'center',
                                        }}>
                                        Share As:
                                    </Text>

                                    {/* Share file using email */}
                                    <View
                                        style={{
                                            justifyContent: 'space-between',
                                            alignSelf: 'center',
                                            marginTop: 10,
                                            flexDirection: 'row',
                                            width: '80%',
                                        }}>
                                        <TouchableOpacity
                                            style={[
                                                styles.modalSaveButton1,
                                                {
                                                    zIndex: -1,
                                                },
                                            ]}
                                            onPress={() => { }}>
                                            <Text
                                                style={[
                                                    styles.modalDescText,
                                                    { color: Colors.primaryColor },
                                                ]}>
                                                Excel
                                            </Text>
                                        </TouchableOpacity>

                                        <TouchableOpacity
                                            style={[
                                                styles.modalSaveButton1,
                                                {
                                                    zIndex: -1,
                                                },
                                            ]}
                                            onPress={() => { }}>
                                            <Text
                                                style={[
                                                    styles.modalDescText,
                                                    { color: Colors.primaryColor },
                                                ]}>
                                                CSV
                                            </Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            style={[
                                                styles.modalSaveButton1,
                                                {
                                                    zIndex: -1,
                                                },
                                            ]}
                                            onPress={() => { }}>
                                            <Text
                                                style={[
                                                    styles.modalDescText,
                                                    { color: Colors.primaryColor },
                                                ]}>
                                                PDF
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                    <View
                                        style={{
                                            alignSelf: 'center',
                                            marginTop: 20,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            // width: 260,
                                            width: windowWidth * 0.2,
                                            height: 60,
                                            alignContent: 'center',
                                            flexDirection: 'row',
                                            marginTop: 40,
                                        }}>
                                        <TouchableOpacity
                                            onPress={emailVariant}
                                            style={{
                                                backgroundColor: Colors.fieldtBgColor,
                                                width: '100%',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                alignContent: 'center',
                                                height: 60,
                                                borderBottomLeftRadius: 10,
                                                borderRightWidth: StyleSheet.hairlineWidth,
                                                borderTopWidth: StyleSheet.hairlineWidth,
                                            }}>
                                            <Text
                                                style={{
                                                    fontSize: 22,
                                                    color: Colors.primaryColor,
                                                    fontFamily: 'NunitoSans-SemiBold',
                                                }}>
                                                Email
                                            </Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            onPress={() => {
                                                // setState({ visible: false });
                                                setVisible(false);
                                            }}
                                            style={{
                                                backgroundColor: Colors.fieldtBgColor,
                                                width: '100%',
                                                justifyContent: 'center',
                                                alignItems: 'center',
                                                alignContent: 'center',
                                                height: 60,
                                                borderBottomRightRadius: 10,
                                                borderTopWidth: StyleSheet.hairlineWidth,
                                            }}>
                                            <Text
                                                style={{
                                                    fontSize: 22,
                                                    color: Colors.descriptionColor,
                                                    fontFamily: 'NunitoSans-SemiBold',
                                                }}>
                                                Cancel
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </KeyboardAvoidingView>
                        </ModalView>

                        <View
                            style={[
                                styles.content,
                                {
                                    padding: 16,
                                    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
                                },
                            ]}>
                            <View style={{ flex: 1 }}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        alignSelf: 'center',
                                        justifyContent: 'space-between',
                                        //padding: 18,
                                        marginTop: 5,
                                        width: windowWidth * 0.87,
                                    }}>
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 20 : 26,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        Employee Timesheet
                                    </Text>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                        }}>
                                        <TouchableOpacity
                                            style={{
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#0F1A3C',
                                                borderRadius: 5,
                                                //width: 140,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                                marginRight: 10,
                                            }}
                                            onPress={() => {
                                                setExportModalVisibility(true);
                                                logEventAnalytics({
                                                    eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_C_DOWNLOAD_BTN,
                                                    eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_C_DOWNLOAD_BTN
                                                })
                                            }}>
                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                <Icon
                                                    name="download"
                                                    size={switchMerchant ? 10 : 20}
                                                    color={Colors.whiteColor}
                                                />
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Bold',
                                                    }}>
                                                    DOWNLOAD
                                                </Text>
                                            </View>
                                        </TouchableOpacity>

                                        <View
                                            style={[
                                                {
                                                    height: switchMerchant ? 35 : 40,
                                                },
                                                !isTablet()
                                                    ? {
                                                        marginLeft: 0,
                                                    }
                                                    : {},
                                            ]}>
                                            <View
                                                style={{
                                                    width: switchMerchant ? 200 : 250,
                                                    height: switchMerchant ? 35 : 40,
                                                    backgroundColor: 'white',
                                                    borderRadius: 5,
                                                    flexDirection: 'row',
                                                    alignContent: 'center',
                                                    alignItems: 'center',

                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 3,
                                                    borderWidth: 1,
                                                    borderColor: '#E5E5E5',
                                                }}>
                                                <Icon
                                                    name="search"
                                                    size={switchMerchant ? 13 : 18}
                                                    color={Colors.primaryColor}
                                                    style={{ marginLeft: 15 }}
                                                />
                                                <TextInput
                                                    editable={!loading}
                                                    underlineColorAndroid={Colors.whiteColor}
                                                    style={{
                                                        width: switchMerchant ? 180 : 220,
                                                        fontSize: switchMerchant ? 10 : 15,
                                                        fontFamily: 'NunitoSans-Regular',
                                                        paddingLeft: 5,
                                                        height: 45,
                                                    }}
                                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                    clearButtonMode="while-editing"
                                                    placeholder=" Search"
                                                    onChangeText={(text) => {
                                                        setSearch(text);
                                                        logEventAnalytics({
                                                            eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_TB_SEARCH,
                                                            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_TB_SEARCH
                                                        })
                                                    }}
                                                    value={search}
                                                />
                                            </View>
                                        </View>
                                    </View>
                                </View>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        //backgroundColor: '#ffffff',
                                        justifyContent: 'space-between',
                                        //padding: 18,
                                        marginTop: 20,
                                        width: '100%',
                                        paddingLeft: switchMerchant
                                            ? 0
                                            : windowWidth <= 1823 &&
                                                windowWidth >= 1820
                                                ? '1.5%'
                                                : '1%',
                                        paddingRight: switchMerchant
                                            ? 0
                                            : windowWidth <= 1823 &&
                                                windowWidth >= 1820
                                                ? '1.5%'
                                                : '1%',
                                    }}>
                                    <TouchableOpacity
                                        style={[
                                            {
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#0F1A3C',
                                                borderRadius: 5,
                                                //width: 160,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,

                                                opacity: !showDetails ? 0 : 100,
                                            },
                                        ]}
                                        onPress={() => {
                                            setShowDetails(false);
                                            setPageCount(Math.ceil(allOutletsEmployeesClock.length / perPage));
                                            setCurrentPage(pageReturn);
                                            // console.log('Returning to page');
                                            // console.log(pageReturn);
                                            logEventAnalytics({
                                                eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_TABLE_C_SUMMARY,
                                                eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_TABLE_C_SUMMARY
                                            })
                                        }}
                                        disabled={!showDetails}>
                                        <AntDesign
                                            name="arrowleft"
                                            size={switchMerchant ? 10 : 20}
                                            color={Colors.whiteColor}
                                            style={
                                                {
                                                }
                                            }
                                        />
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            Summary
                                        </Text>
                                    </TouchableOpacity>

                                    <View style={{ flexDirection: 'row' }}>
                                        <View
                                            style={[
                                                {
                                                    paddingHorizontal: 15,
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    borderRadius: 10,
                                                    paddingVertical: 10,
                                                    justifyContent: 'center',
                                                    backgroundColor: Colors.whiteColor,
                                                    shadowOpacity: 0,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                },
                                            ]}>
                                            <View
                                                style={{ alignSelf: 'center', marginRight: 5 }}
                                                onPress={() => {
                                                    setState({
                                                        pickerMode: 'date',
                                                        showDateTimePicker: true,
                                                    });
                                                }}>
                                                <GCalendar
                                                    width={switchMerchant ? 15 : 20}
                                                    height={switchMerchant ? 15 : 20}
                                                />
                                            </View>

                                            <TouchableOpacity
                                                onPress={() => {
                                                    setShowDateTimePicker(true);
                                                    setShowDateTimePicker1(false);

                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_C_CAL_START,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_C_CAL_START
                                                    })
                                                }}
                                                style={{
                                                    marginHorizontal: 4,
                                                }}>
                                                <Text
                                                    style={
                                                        switchMerchant
                                                            ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                                            : { fontFamily: 'NunitoSans-Regular' }
                                                    }>
                                                    {moment(rev_date).format('DD MMM yyyy')}
                                                </Text>
                                            </TouchableOpacity>

                                            <Text
                                                style={
                                                    switchMerchant
                                                        ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                                        : { fontFamily: 'NunitoSans-Regular' }
                                                }>
                                                -
                                            </Text>

                                            <TouchableOpacity
                                                onPress={() => {
                                                    setShowDateTimePicker(false);
                                                    setShowDateTimePicker1(true);

                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_C_CAL_END,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_C_CAL_END
                                                    });
                                                }}
                                                style={{
                                                    marginHorizontal: 4,
                                                }}>
                                                <Text
                                                    style={
                                                        switchMerchant
                                                            ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                                            : { fontFamily: 'NunitoSans-Regular' }
                                                    }>
                                                    {moment(rev_date1).format('DD MMM yyyy')}
                                                </Text>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </View>

                                <View style={{ width: '100%', marginTop: 20 }}>
                                    <View
                                        style={{
                                            backgroundColor: Colors.whiteColor,
                                            width: windowWidth * 0.87,
                                            height:
                                                Platform.OS == 'android'
                                                    ? windowHeight * 0.6
                                                    : windowHeight * 0.66,
                                            marginTop: 0,
                                            marginHorizontal: 30,
                                            marginBottom: 10,
                                            alignSelf: 'center',
                                            borderRadius: 5,
                                            shadowOpacity: 0,
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 3,
                                        }}>
                                        {!showDetails ? (
                                            <View style={{ marginTop: 10, flexDirection: 'row' }}>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '5%',
                                                        borderRightWidth: 1,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        paddingLeft: 10,
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    textAlign: 'left',
                                                                }}>
                                                                {'No.\n'}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </View>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '25%',
                                                        borderRightWidth: 1,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        padding: 10,
                                                    }}>
                                                    <TouchableOpacity
                                                        onPress={() => {
                                                            // if (
                                                            //   currReportSummarySort ===
                                                            //   REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC
                                                            // ) {
                                                            //   setCurrReportSummarySort(
                                                            //     REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_DESC,
                                                            //   );
                                                            // } else {
                                                            //   setCurrReportSummarySort(
                                                            //     REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC,
                                                            //   );
                                                            // }
                                                        }}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text
                                                                    numberOfLines={2}
                                                                    style={{
                                                                        fontSize: switchMerchant ? 10 : 13,
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                        textAlign: 'left',
                                                                    }}>
                                                                    {'Employee Name\n'}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '25%',
                                                        borderRightWidth: 1,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        padding: 10,
                                                    }}>
                                                    <TouchableOpacity
                                                        onPress={() => {

                                                        }}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text
                                                                    numberOfLines={2}
                                                                    style={{
                                                                        fontSize: switchMerchant ? 10 : 13,
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                    }}>
                                                                    {'Clock In/Out Times\n'}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                        ) : (
                                            <View style={{ marginTop: 10, flexDirection: 'row' }}>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '6%',
                                                        borderRightWidth: 1,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        paddingLeft: 10,
                                                    }}>
                                                    <View style={{ flexDirection: 'row' }}>
                                                        <View style={{ flexDirection: 'column' }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    textAlign: 'left',
                                                                }}>
                                                                {'No.\n'}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </View>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '23.5%',
                                                        borderRightWidth: 1,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        padding: 10,
                                                    }}>
                                                    <TouchableOpacity
                                                        onPress={() => {

                                                        }}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text
                                                                    numberOfLines={2}
                                                                    style={{
                                                                        fontSize: switchMerchant ? 10 : 13,
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                    }}>
                                                                    {'Day\n'}

                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '23.5%',
                                                        borderRightWidth: 1,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        padding: 10,
                                                    }}>
                                                    <TouchableOpacity
                                                        onPress={() => {

                                                        }}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text
                                                                    numberOfLines={2}
                                                                    style={{
                                                                        fontSize: switchMerchant ? 10 : 13,
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                    }}>
                                                                    {'Time In\n'}

                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>

                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '23.5%',
                                                        borderRightWidth: 1,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        padding: 10,
                                                    }}>
                                                    <TouchableOpacity
                                                        onPress={() => {

                                                        }}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text
                                                                    numberOfLines={2}
                                                                    style={{
                                                                        fontSize: switchMerchant ? 10 : 13,
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                    }}>
                                                                    {'Time Out\n'}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>

                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '23.5%',
                                                        borderRightWidth: 1,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        padding: 10,
                                                    }}>
                                                    <TouchableOpacity
                                                        onPress={() => {

                                                        }}>
                                                        <View style={{ flexDirection: 'row' }}>
                                                            <View style={{ flexDirection: 'column' }}>
                                                                <Text
                                                                    numberOfLines={2}
                                                                    style={{
                                                                        fontSize: switchMerchant ? 10 : 13,
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                    }}>
                                                                    {'Total Hours\n'}

                                                                </Text>
                                                            </View>
                                                        </View>
                                                    </TouchableOpacity>
                                                </View>
                                            </View>
                                        )}

                                        {!showDetails ? (
                                            <>
                                                {allOutletsEmployeesClock.filter((item) => {
                                                    return filterItem(item);
                                                }).length > 0 ? (
                                                    <FlatList
                                                        showsVerticalScrollIndicator={false}
                                                        ref={flatListRef}
                                                        data={allOutletsEmployeesClock.filter((item) => {
                                                            return filterItem(item);
                                                        })
                                                            .slice(
                                                                (currentPage - 1) * perPage,
                                                                currentPage * perPage,
                                                            )}
                                                        renderItem={renderItem}
                                                        keyExtractor={(item, index) => String(index)}
                                                        style={{ marginTop: 10 }}
                                                    />
                                                ) : (
                                                    <View
                                                        style={{
                                                            height: windowHeight * 0.4,
                                                        }}>
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                height: '100%',
                                                            }}>
                                                            <Text style={{ color: Colors.descriptionColor }}>
                                                                - No Data Available -
                                                            </Text>
                                                        </View>
                                                    </View>
                                                )}
                                            </>
                                        ) : (
                                            <>
                                                {allOutletsEmployeesDetails.filter((item) => {
                                                    return filterItemDetails(item);
                                                }).length > 0 ? (
                                                    <FlatList
                                                        showsVerticalScrollIndicator={false}
                                                        ref={flatListRef}
                                                        data={allOutletsEmployeesDetails.filter((item) => {
                                                            return filterItemDetails(item);
                                                        })
                                                            .slice(
                                                                (currentDetailsPage - 1) * perPage,
                                                                currentDetailsPage * perPage,
                                                            )}
                                                        renderItem={renderItemDetails}
                                                        keyExtractor={(item, index) => String(index)}
                                                        style={{ marginTop: 10 }}
                                                    />
                                                ) : (
                                                    <View
                                                        style={{
                                                            height: windowHeight * 0.4,
                                                        }}>
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                height: '100%',
                                                            }}>
                                                            <Text style={{ color: Colors.descriptionColor }}>
                                                                - No Data Available -
                                                            </Text>
                                                        </View>
                                                    </View>
                                                )}
                                            </>
                                        )}
                                    </View>

                                    {!showDetails ? (
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                marginTop: 10,
                                                width: windowWidth * 0.87,
                                                alignItems: 'center',
                                                alignSelf: 'center',
                                                justifyContent: 'flex-end',
                                                top:
                                                    Platform.OS == 'ios'
                                                        ? pushPagingToTop && keyboardHeight > 0
                                                            ? -keyboardHeight * 1
                                                            : 0
                                                        : 0,
                                                borderRadius:
                                                    pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                                                paddingHorizontal:
                                                    pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                                            }}>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginRight: '1%',
                                                }}>
                                                Items Showed
                                            </Text>
                                            <View
                                                style={{
                                                    width: Platform.OS === 'ios' ? 65 : '13%', //65,
                                                    height: switchMerchant ? 20 : 35,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: 'center',
                                                    paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                                                    //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                                    // paddingTop: '-60%',
                                                    borderWidth: 1,
                                                    borderColor: '#E5E5E5',
                                                    marginRight: '1%',
                                                }}>
                                                <RNPickerSelect
                                                    placeholder={{}}
                                                    useNativeAndroidPickerStyle={false}
                                                    style={{
                                                        inputIOS: {
                                                            fontSize: switchMerchant ? 10 : 14,
                                                            fontFamily: 'NunitoSans-Regular',
                                                            textAlign: 'center',
                                                        },
                                                        inputAndroid: {
                                                            fontSize: switchMerchant ? 10 : 14,
                                                            fontFamily: 'NunitoSans-Regular',
                                                            justifyContent: 'center',
                                                            textAlign: 'center',
                                                            height: 40,
                                                            color: 'black',
                                                        },
                                                        inputAndroidContainer: { width: '100%' },
                                                        //backgroundColor: 'red',
                                                        height: 35,

                                                        chevronContainer: {
                                                            display: 'none',
                                                        },
                                                        chevronDown: {
                                                            display: 'none',
                                                        },
                                                        chevronUp: {
                                                            display: 'none',
                                                        },
                                                    }}
                                                    items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                                        label: 'All',
                                                        value: !showDetails
                                                            ? allOutletsEmployeesClock.length
                                                            : allOutletsEmployeesDetails.length,
                                                    })}
                                                    value={perPage}
                                                    onValueChange={(value) => {
                                                        setPerPage(value);
                                                        logEventAnalytics({
                                                            eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_DD_ITEMS_SHOWED,
                                                            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_DD_ITEMS_SHOWED
                                                        });
                                                    }}
                                                />
                                            </View>

                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginRight: '1%',
                                                }}>
                                                Page
                                            </Text>
                                            <View
                                                style={{
                                                    width: switchMerchant ? 65 : 70,
                                                    height: switchMerchant ? 20 : 35,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: 'center',
                                                    paddingHorizontal: 22,
                                                    borderWidth: 1,
                                                    borderColor: '#E5E5E5',
                                                }}>
                                                {console.log('currentPage')}
                                                {console.log(currentPage)}

                                                <TextInput
                                                    onChangeText={(text) => {
                                                        var currentPageTemp =
                                                            text.length > 0 ? parseInt(text) : 1;

                                                        setCurrentPage(
                                                            currentPageTemp > pageCount
                                                                ? pageCount
                                                                : currentPageTemp < 1
                                                                    ? 1
                                                                    : currentPageTemp,
                                                        );
                                                    }}
                                                    placeholder={
                                                        pageCount !== 0 ? currentPage.toString() : '0'
                                                    }
                                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                    style={{
                                                        color: 'black',
                                                        fontSize: switchMerchant ? 10 : 14,
                                                        fontFamily: 'NunitoSans-Regular',
                                                        marginTop: Platform.OS === 'ios' ? 0 : -15,
                                                        marginBottom: Platform.OS === 'ios' ? 0 : -15,
                                                        textAlign: 'center',
                                                        width: '100%',
                                                    }}
                                                    value={pageCount !== 0 ? currentPage.toString() : '0'}
                                                    defaultValue={
                                                        pageCount !== 0 ? currentPage.toString() : '0'
                                                    }
                                                    keyboardType={'numeric'}
                                                    onFocus={() => {
                                                        setPushPagingToTop(true);
                                                    }}
                                                />
                                            </View>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginLeft: '1%',
                                                    marginRight: '1%',
                                                }}>
                                                of {pageCount}
                                            </Text>
                                            <TouchableOpacity
                                                style={{
                                                    width: switchMerchant ? 30 : 45,
                                                    height: switchMerchant ? 20 : 28,
                                                    backgroundColor: Colors.primaryColor,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}
                                                onPress={() => {
                                                    prevPage();
                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_C_PREV_BUTTON,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_C_PREV_BUTTON
                                                    })
                                                }}>
                                                <MaterialIcons
                                                    name="keyboard-arrow-left"
                                                    size={switchMerchant ? 20 : 25}
                                                    style={{ color: Colors.whiteColor }}
                                                />
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                style={{
                                                    width: switchMerchant ? 30 : 45,
                                                    height: switchMerchant ? 20 : 28,
                                                    backgroundColor: Colors.primaryColor,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}
                                                onPress={() => {
                                                    nextPage();
                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEETPREV_C_NEXT_BUTTON,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_C_NEXT_BUTTON
                                                    })
                                                }}>
                                                <MaterialIcons
                                                    name="keyboard-arrow-right"
                                                    size={switchMerchant ? 20 : 25}
                                                    style={{ color: Colors.whiteColor }}
                                                />
                                            </TouchableOpacity>
                                        </View>
                                    ) : (
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                marginTop: 10,
                                                width: windowWidth * 0.87,
                                                alignItems: 'center',
                                                alignSelf: 'center',
                                                justifyContent: 'flex-end',
                                                top:
                                                    Platform.OS == 'ios'
                                                        ? pushPagingToTop && keyboardHeight > 0
                                                            ? -keyboardHeight * 1
                                                            : 0
                                                        : 0,
                                                borderRadius:
                                                    pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                                                paddingHorizontal:
                                                    pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                                            }}>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginRight: '1%',
                                                }}>
                                                Items Showed
                                            </Text>
                                            <View
                                                style={{
                                                    width: Platform.OS === 'ios' ? 65 : '13%', //65,
                                                    height: switchMerchant ? 20 : 35,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: 'center',
                                                    paddingHorizontal: Platform.OS === 'ios' ? 0 : 0,
                                                    //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                                    // paddingTop: '-60%',
                                                    borderWidth: 1,
                                                    borderColor: '#E5E5E5',
                                                    marginRight: '1%',
                                                }}>
                                                <RNPickerSelect
                                                    placeholder={{}}
                                                    useNativeAndroidPickerStyle={false}
                                                    style={{
                                                        inputIOS: {
                                                            fontSize: switchMerchant ? 10 : 14,
                                                            fontFamily: 'NunitoSans-Regular',
                                                            textAlign: 'center',
                                                        },
                                                        inputAndroid: {
                                                            fontSize: switchMerchant ? 10 : 14,
                                                            fontFamily: 'NunitoSans-Regular',
                                                            justifyContent: 'center',
                                                            textAlign: 'center',
                                                            height: 40,
                                                            color: 'black',
                                                        },
                                                        inputAndroidContainer: { width: '100%' },
                                                        //backgroundColor: 'red',
                                                        height: 35,

                                                        chevronContainer: {
                                                            display: 'none',
                                                        },
                                                        chevronDown: {
                                                            display: 'none',
                                                        },
                                                        chevronUp: {
                                                            display: 'none',
                                                        },
                                                    }}
                                                    items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                                        label: 'All',
                                                        value: !showDetails
                                                            ? allOutletsEmployeesClock.length
                                                            : allOutletsEmployeesDetails.length,
                                                    })}
                                                    value={perPage}
                                                    onValueChange={(value) => {
                                                        setPerPage(value);

                                                        logEventAnalytics({
                                                            eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_DD_ITEMS_SHOWED,
                                                            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_DD_ITEMS_SHOWED
                                                        });
                                                    }}
                                                />
                                            </View>

                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginRight: '1%',
                                                }}>
                                                Page
                                            </Text>
                                            <View
                                                style={{
                                                    width: switchMerchant ? 65 : 70,
                                                    height: switchMerchant ? 20 : 35,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: 'center',
                                                    paddingHorizontal: 22,
                                                    borderWidth: 1,
                                                    borderColor: '#E5E5E5',
                                                }}>
                                                {console.log('currentDetailsPage')}
                                                {console.log(currentDetailsPage)}

                                                <TextInput
                                                    onChangeText={(text) => {
                                                        var currentPageTemp =
                                                            text.length > 0 ? parseInt(text) : 1;
                                                        // console.log('currentDetailsPage pending');
                                                        // console.log(
                                                        //     currentPageTemp > pageCount
                                                        //         ? pageCount
                                                        //         : currentPageTemp < 1
                                                        //             ? 1
                                                        //             : currentPageTemp,
                                                        // );
                                                        setCurrentDetailsPage(
                                                            currentPageTemp > pageCount
                                                                ? pageCount
                                                                : currentPageTemp < 1
                                                                    ? 1
                                                                    : currentPageTemp,
                                                        );

                                                        logEventAnalytics({
                                                            eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEET_TB_PAGE,
                                                            eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_TB_PAGE
                                                        })
                                                    }}
                                                    placeholder={
                                                        pageCount !== 0 ? currentDetailsPage.toString() : '0'
                                                    }
                                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                                    style={{
                                                        color: 'black',
                                                        fontSize: switchMerchant ? 10 : 14,
                                                        fontFamily: 'NunitoSans-Regular',
                                                        marginTop: Platform.OS === 'ios' ? 0 : -15,
                                                        marginBottom: Platform.OS === 'ios' ? 0 : -15,
                                                        textAlign: 'center',
                                                        width: '100%',
                                                    }}
                                                    value={
                                                        pageCount !== 0 ? currentDetailsPage.toString() : '0'
                                                    }
                                                    defaultValue={
                                                        pageCount !== 0 ? currentDetailsPage.toString() : '0'
                                                    }
                                                    keyboardType={'numeric'}
                                                    onFocus={() => {
                                                        setPushPagingToTop(true);
                                                    }}
                                                />
                                            </View>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginLeft: '1%',
                                                    marginRight: '1%',
                                                }}>
                                                of {pageCount}
                                            </Text>
                                            <TouchableOpacity
                                                style={{
                                                    width: switchMerchant ? 30 : 45,
                                                    height: switchMerchant ? 20 : 28,
                                                    backgroundColor: Colors.primaryColor,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}
                                                onPress={() => {
                                                    prevDetailsPage();
                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEETPREV_C_PREV_BUTTON,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_C_PREV_BUTTON
                                                    })
                                                }}>
                                                <MaterialIcons
                                                    name="keyboard-arrow-left"
                                                    size={switchMerchant ? 20 : 25}
                                                    style={{ color: Colors.whiteColor }}
                                                />
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                style={{
                                                    width: switchMerchant ? 30 : 45,
                                                    height: switchMerchant ? 20 : 28,
                                                    backgroundColor: Colors.primaryColor,
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}
                                                onPress={() => {
                                                    nextDetailsPage();
                                                    logEventAnalytics({
                                                        eventName: ANALYTICS.MODULE_EMPLOYEE_E_TIMESHEETPREV_C_NEXT_BUTTON,
                                                        eventNameParsed: ANALYTICS_PARSED.MODULE_EMPLOYEE_E_TIMESHEET_C_NEXT_BUTTON
                                                    })
                                                }}>
                                                <MaterialIcons
                                                    name="keyboard-arrow-right"
                                                    size={switchMerchant ? 20 : 25}
                                                    style={{ color: Colors.whiteColor }}
                                                />
                                            </TouchableOpacity>
                                        </View>
                                    )}
                                </View>
                            </View>
                        </View>
                    </ScrollView>
                </ScrollView>
            </View>
        </UserIdleWrapper>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
    },
    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 20,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
        backgroundColor: Colors.highlightColor,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    confirmBox: {
        // width: '30%',
        // height: '30%',
        // borderRadius: 30,
        // backgroundColor: Colors.whiteColor,
        width: Dimensions.get('window').width * 0.4,
        height: Dimensions.get('window').height * 0.3,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: 'space-between',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: Dimensions.get('window').width * 0.2,
        width: Dimensions.get('window').width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('window').width * 0.03,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('window').width * 0.02,
        top: Dimensions.get('window').width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: 'center',
        top: '20%',
        position: 'absolute',
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalTitleText: {
        fontFamily: 'NunitoSans-Bold',
        textAlign: 'center',
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 18,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        flex: 1,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
        width: '20%',
    },
    modalSaveButton: {
        width: Dimensions.get('window').width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    modalSaveButton1: {
        width: Dimensions.get('window').width * 0.1,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    confirmBox1: {
        width: Dimensions.get('window').width * 0.4,
        height: Dimensions.get('window').height * 0.4,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: 'space-between',
    },
    submitText: {
        height:
            Platform.OS == 'ios'
                ? Dimensions.get('window').height * 0.06
                : Dimensions.get('window').height * 0.07,
        paddingVertical: 5,
        paddingHorizontal: 20,
        flexDirection: 'row',
        color: '#4cd964',
        textAlign: 'center',
        borderRadius: 10,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    headerLeftStyle: {
        width: Dimensions.get('screen').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
export default EmployeeTimeSheet;
