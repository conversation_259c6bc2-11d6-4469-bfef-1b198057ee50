import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
  Alert,
  TextInput,
  Image,
} from 'react-native';
import Colors from '../constant/Colors';
import moment from 'moment';
import { useFocusEffect } from '@react-navigation/native';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import { EXPAND_TAB_TYPE } from '../constant/common';
import { logEventAnalytics } from '../util/common';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';

const { width: windowWidth, height: windowHeight } = Dimensions.get('window');

const CompositeInventoryScreen = (props) => {
  const { navigation } = props;

  // State variables
  const expandTab = CommonStore.useState(s => s.expandTab);
  const currPageStack = CommonStore.useState(s => s.currPageStack);
  const isAlphaUser = UserStore.useState(s => s.isAlphaUser);
  const userName = UserStore.useState(s => s.name);
  const merchantName = MerchantStore.useState(s => s.name);
  const currOutlet = MerchantStore.useState(s => s.currOutlet);
  const [batchInventoryData, setBatchInventoryData] = useState([]);
  const [filteredBatchData, setFilteredBatchData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('expiryDate'); // 'expiryDate', 'quantity', 'productName'
  const [sortOrder, setSortOrder] = useState('asc'); // 'asc', 'desc'

  useFocusEffect(
      useCallback(() => {
        navigation.setOptions({
          headerLeft: () => (
            <TouchableOpacity
              onPress={() => {
                if (isAlphaUser || true) {
                  navigation.navigate('MenuOrderingScreen');
                  CommonStore.update(s => {
                    s.currPage = 'MenuOrderingScreen';
                    s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                  });
                } else {
                  navigation.navigate('Table');
                  CommonStore.update(s => {
                    s.currPage = 'Table';
                    s.currPageStack = [...currPageStack, 'Table'];
                  });
                }
                if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                  CommonStore.update(s => {
                    s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                  });
                }
                logEventAnalytics({
                  eventName: ANALYTICS.MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_LOGO,
                  eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_LOGO,
                });
              }}
              style={styles.headerLeftStyle}>
              <Image
                style={styles.logoStyle}
                resizeMode="contain"
                source={require('../assets/image/logo.png')}
              />
            </TouchableOpacity>
          ),
          headerTitle: () => (
            <View style={styles.headerTitleContainer}>
              <Text style={styles.headerTitleText}>
                Composite Inventory
              </Text>
            </View>
          ),
          headerRight: () => (
            <View style={styles.headerRightContainer}>
              <TouchableOpacity
                onPress={() => {
                  if (global.currUserRole === 'admin') {
                    navigation.navigate('Setting');
                  }
                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_PROFILE,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_INVENTORY_OVERVIEW_C_PROFILE,
                  });
                }}
                style={styles.profileButton}>
                <Text style={styles.userNameText}>{userName}</Text>
                <View style={styles.profileImageContainer}>
                  <Image
                    style={styles.profileImage}
                    source={require('../assets/image/profile.png')}
                  />
                </View>
              </TouchableOpacity>
            </View>
          ),
        });
      }, [navigation, expandTab, currPageStack, isAlphaUser, userName])
    );

  // Mock data structure - replace with actual API call
  const mockBatchData = [
    {
      id: '1',
      productName: 'Organic Tomatoes',
      sku: 'TOM-001',
      batchNumber: 'B2024001',
      quantity: 150,
      unit: 'kg',
      expiryDate: '2024-12-31',
      supplier: 'Fresh Farms Ltd',
      location: 'Warehouse A',
      status: 'active',
    },
    {
      id: '2',
      productName: 'Fresh Milk',
      sku: 'MLK-002',
      batchNumber: 'B2024002',
      quantity: 200,
      unit: 'liters',
      expiryDate: '2024-11-15',
      supplier: 'Dairy Co',
      location: 'Cold Storage B',
      status: 'active',
    },
    {
      id: '3',
      productName: 'Whole Grain Bread',
      sku: 'BRD-003',
      batchNumber: 'B2024003',
      quantity: 75,
      unit: 'pieces',
      expiryDate: '2024-10-20',
      supplier: 'Bakery Supplies',
      location: 'Warehouse A',
      status: 'active',
    },
    {
      id: '4',
      productName: 'Free Range Eggs',
      sku: 'EGG-004',
      batchNumber: 'B2024004',
      quantity: 300,
      unit: 'dozen',
      expiryDate: '2024-11-30',
      supplier: 'Egg Farm',
      location: 'Cold Storage B',
      status: 'active',
    },
    {
      id: '5',
      productName: 'Organic Spinach',
      sku: 'SPN-005',
      batchNumber: 'B2024005',
      quantity: 50,
      unit: 'kg',
      expiryDate: '2024-10-10',
      supplier: 'Green Valley',
      location: 'Warehouse A',
      status: 'expired',
    },
  ];

  // Initialize data
  useEffect(() => {
    loadBatchInventoryData();
  }, []);

  // Filter and sort data when search or sort changes
  useEffect(() => {
    filterAndSortData();
  }, [batchInventoryData, searchQuery, sortBy, sortOrder]);

  // Load batch inventory data
  const loadBatchInventoryData = async () => {
    try {
      setLoading(true);
      // Replace this with actual API call
      // const response = await ApiClient.get('/composite/batch-inventory');
      // setBatchInventoryData(response.data);
      
      // Using mock data for now
      setBatchInventoryData(mockBatchData);
    } catch (error) {
      console.error('Error loading batch inventory data:', error);
      Alert.alert('Error', 'Failed to load batch inventory data');
    } finally {
      setLoading(false);
    }
  };

  // Filter and sort data
  const filterAndSortData = () => {
    let filtered = [...batchInventoryData];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(item =>
        item.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.batchNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.supplier.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'expiryDate':
          aValue = moment(a.expiryDate);
          bValue = moment(b.expiryDate);
          break;
        case 'quantity':
          aValue = parseFloat(a.quantity);
          bValue = parseFloat(b.quantity);
          break;
        case 'productName':
          aValue = a.productName.toLowerCase();
          bValue = b.productName.toLowerCase();
          break;
        default:
          aValue = a[sortBy];
          bValue = b[sortBy];
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredBatchData(filtered);
  };

  // Handle refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadBatchInventoryData();
    setRefreshing(false);
  }, []);

  // Handle sort
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  // Get sort indicator
  const getSortIndicator = (field) => {
    if (sortBy !== field) return '↕';
    return sortOrder === 'asc' ? '↑' : '↓';
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return Colors.tabMint || '#4CAF50';
      case 'expired':
        return Colors.tabRed || '#F44336';
      case 'warning':
        return Colors.tabYellow || '#FF9800';
      default:
        return Colors.mainTxtColor || '#333';
    }
  };

  // Check if item is expiring soon (within 7 days)
  const isExpiringSoon = (expiryDate) => {
    const daysUntilExpiry = moment(expiryDate).diff(moment(), 'days');
    return daysUntilExpiry <= 7 && daysUntilExpiry >= 0;
  };

  // Check if item is expired
  const isExpired = (expiryDate) => {
    return moment(expiryDate).isBefore(moment(), 'day');
  };

  // Render table header
  const renderTableHeader = () => (
    <View style={styles.tableHeader}>
      <TouchableOpacity
        style={[styles.headerCell, { flex: 2 }]}
        onPress={() => handleSort('productName')}
      >
        <Text style={styles.headerText}>
          Product Name {getSortIndicator('productName')}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.headerCell, { flex: 1 }]}
        onPress={() => handleSort('sku')}
      >
        <Text style={styles.headerText}>
          SKU {getSortIndicator('sku')}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.headerCell, { flex: 1 }]}
        onPress={() => handleSort('batchNumber')}
      >
        <Text style={styles.headerText}>
          Batch {getSortIndicator('batchNumber')}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.headerCell, { flex: 1 }]}
        onPress={() => handleSort('quantity')}
      >
        <Text style={styles.headerText}>
          Quantity {getSortIndicator('quantity')}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.headerCell, { flex: 1.5 }]}
        onPress={() => handleSort('expiryDate')}
      >
        <Text style={styles.headerText}>
          Expiry Date {getSortIndicator('expiryDate')}
        </Text>
      </TouchableOpacity>
      
      <View style={[styles.headerCell, { flex: 1.5 }]}>
        <Text style={styles.headerText}>Status</Text>
      </View>
      
      <View style={[styles.headerCell, { flex: 1.5 }]}>
        <Text style={styles.headerText}>Location</Text>
      </View>
    </View>
  );

  // Render table row
  const renderTableRow = ({ item, index }) => {
    const expiringSoon = isExpiringSoon(item.expiryDate);
    const expired = isExpired(item.expiryDate);
    
    return (
      <TouchableOpacity
        style={[
          styles.tableRow,
          index % 2 === 0 && styles.evenRow,
          expired && styles.expiredRow,
          expiringSoon && !expired && styles.expiringSoonRow,
        ]}
        onPress={() => handleRowPress(item)}
      >
        <View style={[styles.cell, { flex: 2 }]}>
          <Text style={[styles.cellText, styles.productNameText]} numberOfLines={2}>
            {item.productName}
          </Text>
        </View>
        
        <View style={[styles.cell, { flex: 1 }]}>
          <Text style={styles.cellText}>{item.sku}</Text>
        </View>
        
        <View style={[styles.cell, { flex: 1 }]}>
          <Text style={styles.cellText}>{item.batchNumber}</Text>
        </View>
        
        <View style={[styles.cell, { flex: 1 }]}>
          <Text style={styles.cellText}>
            {item.quantity} {item.unit}
          </Text>
        </View>
        
        <View style={[styles.cell, { flex: 1.5 }]}>
          <Text style={[
            styles.cellText,
            expired && styles.expiredText,
            expiringSoon && !expired && styles.expiringSoonText,
          ]}>
            {moment(item.expiryDate).format('DD MMM YYYY')}
          </Text>
          {expired && (
            <Text style={styles.expiredDaysText}>
              Expired {moment(item.expiryDate).diff(moment(), 'days') * -1} days ago
            </Text>
          )}
          {expiringSoon && !expired && (
            <Text style={styles.expiringSoonDaysText}>
              Expires in {moment(item.expiryDate).diff(moment(), 'days')} days
            </Text>
          )}
        </View>
        
        <View style={[styles.cell, { flex: 1.5 }]}>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(item.status) }
          ]}>
            <Text style={styles.statusText}>
              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>
          </View>
        </View>
        
        <View style={[styles.cell, { flex: 1.5 }]}>
          <Text style={styles.cellText} numberOfLines={2}>
            {item.location}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Handle row press
  const handleRowPress = (item) => {
    Alert.alert(
      'Batch Details',
      `Product: ${item.productName}\nSKU: ${item.sku}\nBatch: ${item.batchNumber}\nQuantity: ${item.quantity} ${item.unit}\nExpiry: ${moment(item.expiryDate).format('DD MMM YYYY')}\nSupplier: ${item.supplier}\nLocation: ${item.location}`,
      [{ text: 'OK' }]
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateText}>
        {searchQuery ? 'No batches found matching your search' : 'No batch inventory data available'}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Composite Batch Inventory</Text>
        <Text style={styles.headerSubtitle}>
          Manage and monitor batch stock quantities and expiration dates
        </Text>
      </View>

      {/* Search and Controls */}
      <View style={styles.controlsContainer}>
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search products, SKU, batch number..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={Colors.fieldtTxtColor || '#999'}
          />
        </View>
        
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {filteredBatchData.filter(item => !isExpired(item.expiryDate)).length}
            </Text>
            <Text style={styles.statLabel}>Active</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {filteredBatchData.filter(item => isExpiringSoon(item.expiryDate) && !isExpired(item.expiryDate)).length}
            </Text>
            <Text style={styles.statLabel}>Expiring Soon</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {filteredBatchData.filter(item => isExpired(item.expiryDate)).length}
            </Text>
            <Text style={styles.statLabel}>Expired</Text>
          </View>
        </View>
      </View>

      {/* Table */}
      <View style={styles.tableContainer}>
        {renderTableHeader()}
        <FlatList
          data={filteredBatchData}
          renderItem={renderTableRow}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.primaryColor]}
              tintColor={Colors.primaryColor}
            />
          }
          ListEmptyComponent={renderEmptyState}
          contentContainerStyle={filteredBatchData.length === 0 && styles.emptyListContainer}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.fieldtBgColor || '#F5F5F5',
  },
  header: {
    backgroundColor: Colors.primaryColor || '#2196F3',
    padding: 20,
    paddingTop: 40,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.whiteColor || '#FFFFFF',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.whiteColor || '#FFFFFF',
    opacity: 0.8,
  },
  controlsContainer: {
    padding: 20,
    backgroundColor: Colors.whiteColor || '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: Colors.fieldtTxtColor || '#E0E0E0',
  },
  searchContainer: {
    marginBottom: 15,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: Colors.fieldtTxtColor || '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: Colors.whiteColor || '#FFFFFF',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primaryColor || '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: Colors.mainTxtColor || '#666',
    marginTop: 2,
  },
  tableContainer: {
    flex: 1,
    backgroundColor: Colors.whiteColor || '#FFFFFF',
    margin: 20,
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: Colors.primaryColor || '#2196F3',
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  headerCell: {
    paddingHorizontal: 8,
    justifyContent: 'center',
  },
  headerText: {
    color: Colors.whiteColor || '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: Colors.fieldtTxtColor || '#E0E0E0',
  },
  evenRow: {
    backgroundColor: Colors.highlightColor || '#F8F9FA',
  },
  expiredRow: {
    backgroundColor: Colors.lightRed || '#FFEBEE',
  },
  expiringSoonRow: {
    backgroundColor: Colors.tabYellow || '#FFF3E0',
  },
  cell: {
    paddingHorizontal: 8,
    justifyContent: 'center',
  },
  cellText: {
    fontSize: 14,
    color: Colors.mainTxtColor || '#333',
    textAlign: 'center',
  },
  productNameText: {
    fontWeight: '500',
    textAlign: 'left',
  },
  expiredText: {
    color: Colors.tabRed || '#D32F2F',
    fontWeight: 'bold',
  },
  expiringSoonText: {
    color: Colors.tabYellow || '#F57C00',
    fontWeight: 'bold',
  },
  expiredDaysText: {
    fontSize: 11,
    color: Colors.tabRed || '#D32F2F',
    marginTop: 2,
  },
  expiringSoonDaysText: {
    fontSize: 11,
    color: Colors.tabYellow || '#F57C00',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'center',
  },
  statusText: {
    color: Colors.whiteColor || '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyState: {
    padding: 40,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.mainTxtColor || '#666',
    textAlign: 'center',
  },
  emptyListContainer: {
    flexGrow: 1,
  },
  headerLeftStyle: {
      padding: 10,
    },
    logoStyle: {
      width: 124,
      height: 26,
    },
    headerTitleContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    headerTitleText: {
      fontSize: 24,
      fontWeight: 'bold',
      color: Colors.whiteColor || '#FFFFFF',
      textAlign: 'center',
    },
    headerRightContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    profileButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    userNameText: {
      fontFamily: 'NunitoSans-SemiBold',
      fontSize: 16,
      color: Colors.secondaryColor || '#666',
      marginRight: 15,
    },
    profileImageContainer: {
      marginRight: 30,
      width: 40,
      height: 40,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'white',
    },
    profileImage: {
      width: 28,
      height: 28,
      alignSelf: 'center',
    },
});

export default CompositeInventoryScreen;
