import React, { Component, useReducer, useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  Dimensions,
} from "react-native";
import Colors from "../constant/Colors";
import * as Cart from "../util/Cart";
import * as User from "../util/User";
import { setRouteFrom } from '../util/common';
import Styles from "../constant/Styles";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import { mp_username, mp_password, mp_merchant_ID, mp_app_name, mp_verification_key } from '../constant/env';
import FontAwesome from "react-native-vector-icons/FontAwesome";
import Ionicons from "react-native-vector-icons/Ionicons";
import Icons from "react-native-vector-icons/Feather";
import Entypo from "react-native-vector-icons/Entypo";
import Close from "react-native-vector-icons/AntDesign";
// import NumericInput from "react-native-numeric-input";
import Back from "react-native-vector-icons/EvilIcons";
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { SpinPicker } from 'react-native-spin-picker'
import moment from 'moment';
import { createIconSetFromFontello, createIconSetFromIcoMoon } from "react-native-vector-icons";
import molpay from "molpay-mobile-xdk-reactnative-beta";
import { CommonStore } from "../store/commonStore";
import { ORDER_TYPE } from "../constant/common";
import firestore from '@react-native-firebase/firestore';
import { Collections } from "../constant/firebase";
import { UserStore } from "../store/userStore";
import { MerchantStore } from "../store/merchantStore";
import AsyncStorage from "@react-native-async-storage/async-storage";
import AsyncImage from "../components/asyncImage";
/*
 * CartScreen
 * function:
 * *Displays the items inside the cart
 * *Allows user to edit the current items in the cart
 * *Shows other popular items for users to purchase
 * *Place order here, delivery, pickup or dine in
 * 
 * route.params:
 * *test: ??
 * *test1: if 1, redemption purchase
 * *test2: if 1, redemption extension
 * *paymentMethod: the selected payment method
 * *outletData: array of data of this cart is purchasing from
 * *navFrom: the navigation stack of this route, from takeaway or from outlet
 */

const CartScreen = props => {
  const {
    navigation,
    route,
  } = props;

  // const { test, test1, test2, paymentMethod, outletData, navFrom } = route.params;
  const testParam = route.params.test;
  const test1Param = route.params.test1;
  const test2Param = route.params.test2;
  const paymentMethodParam = route.params.paymentMethod;
  const outletDataParam = route.params.outletData;
  const navFromParam = route.params.navFrom;

  const [paymentMethod, setPaymentMethod] = useState(paymentMethodParam);
  const [cartItem, setCartItem] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [value, setValue] = useState("");
  const [editingItemId, setEditingItemId] = useState(null);
  const [outletData, setOutletData] = useState(outletDataParam);
  const [menu, setMenu] = useState([]);
  const [menuItem, setMenuItem] = useState([]);
  const [category, setCategory] = useState([]);
  const [outletMenu, setOutletMenu] = useState([]);
  const [menuItemDetails, setMenuItemDetails] = useState([]);
  const [qty, setQty] = useState([]);
  const [test, setTest] = useState(testParam);
  const [test1, setTest1] = useState(test1Param);
  const [test2, setTest2] = useState(test2Param);
  const [popularOutlet, setPopularOutlet] = useState([]);
  const [popular, setPopular] = useState([]);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [currentMenu, setCurrentMenu] = useState([]);
  const [type, setType] = useState(Cart.getOrderType()); // 0 "Dine In"
  const [schedulteTimeList, setSchedulteTimeList] = useState([]);
  const [schedulteTimeSelected, setSchedulteTimeSelected] = useState('');
  const [totalFloat, setTotalFloat] = useState(0);
  const [taxFloat, setTaxFloat] = useState(0);
  const [navFrom, setNavFrom] = useState(navFromParam);
  const [deliveryQuotation, setDeliveryQuotation] = useState(null);
  const [clicked, setClicked] = useState(1); //delivery
  const [clicked1, setClicked1] = useState(0); //pickup
  const [rev_date, setRev_date] = useState(undefined);

  const [totalPrice, setTotalPrice] = useState(0);
  const [totalTax, setTotalTax] = useState(0);
  const [totalDiscount, setTotalDiscount] = useState(0);

  const [totalPrepareTime, setTotalPrepareTime] = useState(0);

  const cartItems = CommonStore.useState(s => s.cartItems);
  const cartOutletItemsDict = CommonStore.useState(s => s.cartOutletItemsDict);
  const cartOutletItemAddOnDict = CommonStore.useState(s => s.cartOutletItemAddOnDict);
  const cartOutletItemAddOnChoiceDict = CommonStore.useState(s => s.cartOutletItemAddOnChoiceDict);

  const selectedOutletTable = CommonStore.useState(s => s.selectedOutletTable);
  const userCart = CommonStore.useState(s => s.userCart);

  const cartItemsProcessed = CommonStore.useState(s => s.cartItemsProcessed);

  const outletsTaxDict = CommonStore.useState(s => s.outletsTaxDict);

  const orderType = CommonStore.useState(s => s.orderType);

  const selectedUserAddress = UserStore.useState(s => s.selectedUserAddress);
  const firebaseUid = UserStore.useState(s => s.firebaseUid);

  const name = UserStore.useState(s => s.name);

  const currOutlet = MerchantStore.useState(s => s.currOutlet);

  useEffect(() => {
    // if (cartItems.length > 0) {
    //   updateCartItemsDict();
    // }

    updateCartItemsDict();
  }, [cartItems]);

  const updateCartItemsDict = async () => {
    var tempCartOutletItemsDict = {
      ...cartOutletItemsDict,
    };

    var tempCartOutletItemAddOnDict = {
      ...cartOutletItemAddOnDict,
    };

    var tempCartOutletItemAddOnChoiceDict = {
      ...cartOutletItemAddOnChoiceDict,
    };

    var tempOutletsTaxDict = {
      ...outletsTaxDict,
    };

    var tempCartItemsProcessed = [];

    var tempTotalPrice = 0;
    var tempTotalPrepareTime = 0;

    var currOutletId = '';

    for (var i = 0; i < cartItems.length; i++) {
      const tempCartItem = cartItems[i];

      var tempCartItemPrice = 0;

      var tempCartItemAddOnCategorized = {};
      var tempCartItemAddOnCategorizedPrice = {};

      var tempCartItemAddOnParsed = [];

      if (tempCartOutletItemsDict[tempCartItem.itemId] === undefined) {
        // need retrive the actual item, to show price, pic, etc

        const outletItemSnapshot = await firestore().collection(Collections.OutletItem)
          .where('uniqueId', '==', tempCartItem.itemId)
          .limit(1)
          .get();

        if (!outletItemSnapshot.empty) {
          tempCartOutletItemsDict[tempCartItem.itemId] = outletItemSnapshot.docs[0].data();
        }
      }

      // tempCartItemPrice = tempCartItem.quantity * tempCartOutletItemsDict[tempCartItem.itemId].price;
      tempCartItemPrice = tempCartOutletItemsDict[tempCartItem.itemId].price;

      //////////////////////////////////////////////////////////

      currOutletId = tempCartOutletItemsDict[tempCartItem.itemId].outletId;

      if (tempOutletsTaxDict[currOutletId] === undefined) {
        // need retrieve the tax rate of this outlet

        const outletTaxSnapshot = await firestore().collection(Collections.OutletTax)
          .where('outletId', '==', currOutletId)
          .limit(1)
          .get();

        if (!outletTaxSnapshot.empty) {
          tempOutletsTaxDict[currOutletId] = outletTaxSnapshot.docs[0].data();
        }
      }

      //////////////////////////////////////////////////////////

      if (tempCartItem.choices) {
        const tempCartItemChoices = Object.entries(tempCartItem.choices).map(([key, value]) => ({ key: key, value: value }));

        for (var j = 0; j < tempCartItemChoices.length; j++) {
          if (tempCartItemChoices[j].value) {
            // means the addon of this item is picked, need to retrieve the actual addon

            if (tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] === undefined) {
              const outletItemAddOnChoiceSnapshot = await firestore().collection(Collections.OutletItemAddOnChoice)
                .where('uniqueId', '==', tempCartItemChoices[j].key)
                .limit(1)
                .get();

              if (!outletItemAddOnChoiceSnapshot.empty) {
                tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] = outletItemAddOnChoiceSnapshot.docs[0].data();
              }
            }

            tempCartItemPrice += tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key].price;

            // need to retrieve the description/type name of this addon choice

            const tempCartItemAddOnChoice = tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key];

            if (tempCartOutletItemAddOnDict[tempCartItemAddOnChoice.outletItemAddOnId] === undefined) {
              const outletItemAddOnSnapshot = await firestore().collection(Collections.OutletItemAddOn)
                .where('uniqueId', '==', tempCartItemAddOnChoice.outletItemAddOnId)
                .limit(1)
                .get();

              if (!outletItemAddOnSnapshot.empty) {
                tempCartOutletItemAddOnDict[tempCartItemAddOnChoice.outletItemAddOnId] = outletItemAddOnSnapshot.docs[0].data();
              }
            }

            if (tempCartItemAddOnCategorized[tempCartItemAddOnChoice.outletItemAddOnId] === undefined) {
              tempCartItemAddOnCategorized[tempCartItemAddOnChoice.outletItemAddOnId] = [];
            }

            tempCartItemAddOnCategorized[tempCartItemAddOnChoice.outletItemAddOnId].push(tempCartItemAddOnChoice.name);

            if (tempCartItemAddOnCategorizedPrice[tempCartItemAddOnChoice.outletItemAddOnId] === undefined) {
              tempCartItemAddOnCategorizedPrice[tempCartItemAddOnChoice.outletItemAddOnId] = [];
            }

            tempCartItemAddOnCategorizedPrice[tempCartItemAddOnChoice.outletItemAddOnId].push(tempCartItemAddOnChoice.price);
          }
        }

        const tempCartItemAddOnCategorizedList = Object.entries(tempCartItemAddOnCategorized).map(([key, value]) => ({ key: key, value: value }));
        const tempCartItemAddOnCategorizedPriceList = Object.entries(tempCartItemAddOnCategorizedPrice).map(([key, value]) => ({ key: key, value: value }));

        if (tempCartItemAddOnCategorizedList.length > 0) {
          for (var j = 0; j < tempCartItemAddOnCategorizedList.length; j++) {
            const tempCartItemAddOnName = tempCartOutletItemAddOnDict[tempCartItemAddOnCategorizedList[j].key].name;

            tempCartItemAddOnParsed.push({
              name: tempCartItemAddOnName,
              choiceNames: tempCartItemAddOnCategorizedList[j].value,
              prices: tempCartItemAddOnCategorizedPriceList[j].value,
            });
          }
        }
      }

      /////////////////////////////////////////////////////////////////////

      // for add-on group

      if (tempCartItem.addOnGroupList) {
        for (var j = 0; j < tempCartItem.addOnGroupList.length; j++) {
          // now separate all, might change in future

          const addOnGroup = tempCartItem.addOnGroupList[j];

          tempCartItemAddOnParsed.push({
            name: addOnGroup.addOnName,
            addOnId: addOnGroup.outletItemAddOnId,
            choiceNames: [addOnGroup.choiceName],
            prices: [addOnGroup.quantity * addOnGroup.price],
            quantities: [addOnGroup.quantity],
            singlePrices: [addOnGroup.price],
            addOnChoiceIdList: [addOnGroup.outletItemAddOnChoiceId],
            minSelectList: [addOnGroup.minSelect],
            maxSelectList: [addOnGroup.maxSelect],
          });

          tempCartItemPrice += addOnGroup.quantity * addOnGroup.price;
        }
      }

      //////////////////////////////////////////////////////////////////////

      tempCartItemPrice = tempCartItemPrice * tempCartItem.quantity;

      tempCartItemsProcessed.push({
        itemId: tempCartItem.itemId,
        choices: tempCartItem.choices,
        remarks: tempCartItem.remarks,
        fireOrder: tempCartItem.fireOrder,
        cartItemDate: tempCartItem.cartItemDate,
        image: tempCartOutletItemsDict[tempCartItem.itemId].image,
        name: tempCartOutletItemsDict[tempCartItem.itemId].name,
        itemName: tempCartOutletItemsDict[tempCartItem.itemId].name,
        itemSku: tempCartOutletItemsDict[tempCartItem.itemId].sku,
        price: +tempCartItemPrice.toFixed(2),
        quantity: tempCartItem.quantity,
        addOns: tempCartItemAddOnParsed,

        deliveredAt: null,
        cookedAt: null,
        isChecked: false,

        orderType: ORDER_TYPE.DINEIN, // might relocate in other places in future

        prepareTime: tempCartOutletItemsDict[tempCartItem.itemId].prepareTime * tempCartItem.quantity,
      });

      tempTotalPrice += tempCartItemPrice;
      tempTotalPrepareTime += tempCartOutletItemsDict[tempCartItem.itemId].prepareTime * tempCartItem.quantity;
    }

    console.log('tempOutletsTaxDict');
    console.log(tempOutletsTaxDict);
    console.log('tempCartOutletItemsDict');
    console.log(tempCartOutletItemsDict);
    console.log('tempCartOutletItemAddOnDict');
    console.log(tempCartOutletItemAddOnDict);
    console.log('tempCartOutletItemAddOnChoiceDict');
    console.log(tempCartOutletItemAddOnChoiceDict);
    console.log('tempCartItemsProcessed');
    console.log(tempCartItemsProcessed);

    //////////////////////////////////////////////////////////////////////

    CommonStore.update(s => {
      s.outletsTaxDict = tempOutletsTaxDict;
      s.cartOutletItemsDict = tempCartOutletItemsDict;
      s.cartOutletItemAddOnDict = tempCartOutletItemAddOnDict;
      s.cartOutletItemAddOnChoiceDict = tempCartOutletItemAddOnChoiceDict;
      s.cartItemsProcessed = tempCartItemsProcessed;
    });

    setTotalPrice(tempTotalPrice);

    setTotalTax(tempTotalPrice * tempOutletsTaxDict[currOutletId].rate);

    setTotalPrepareTime(tempTotalPrepareTime);
  };

  const setState = () => { };

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity style={{
      }} onPress={() => { props.navigation.goBack(); }}>
        <View
          style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            marginTop: Platform.OS == 'android' ? 9 : 10,
            opacity: 0.8,
          }}>
          <Ionicons
            name="chevron-back"
            size={26}
            color={Colors.whiteColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 20,
              textAlign: 'center',
              fontFamily: 'NunitoSans-SemiBold',
              // lineHeight: 22,
              marginTop: -3,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={{
        justifyContent: 'center',
        alignItems: 'center',
        bottom: -2,
      }}>
        <Text
          style={{
            fontSize: 25,
            // lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 0.8,
          }}>
          Cart
        </Text>
      </View>
    ),
    headerRight: () => (
      <View style={{
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => { props.navigation.navigate('Profile') }}>
          <Image style={{
            width: 32,
            height: 32,
            marginTop: 8,
            marginRight: 25,
          }} source={require('../assets/image/drawer.png')} />
        </TouchableOpacity>
      </View>
    ),
  });

  const MenuItem = (param) => {
    ApiClient.GET(API.getItemAddOnChoice + param).then((result) => {
      setState({ menuItemDetails: result });
    });
  }

  // useEffect(() => {
  //   console.log("CART OUTLET ID", Cart.getOutletId())
  //   getCartItem();
  //   // Cart.setRefreshCartPage(getCartItem.bind(this));
  //   console.log("CART OUTLET", outletData)
  //   ApiClient.GET(API.merchantMenu + outletData.id).then(
  //     (result) => {
  //       console.log("777");
  //       console.log(result);
  //       if (result != undefined) {
  //         if (result.length > 0) {
  //           setState({
  //             category: result[0].category,
  //             menu: result[0].items,
  //           });
  //         }
  //         setState({ outletMenu: result });
  //       }
  //       else {
  //         setState({
  //           category: result[0].category,
  //           menu: result[0].items,
  //         });
  //         setState({ outletMenu: [] });
  //       }
  //     }
  //   );

  //   getPopular()

  //   // setInterval(() => {
  //   //   setState({ deliveryQuotation: Cart.getDeliveryQuotation() == null ? null : Cart.getDeliveryQuotation() })
  //   // }, 2500);

  //   // set delivery/pickup button status
  //   if (Cart.getOrderType() === 1) {
  //     setState({ clicked: 1, clicked1: 0, shouldShow: true })
  //   }
  //   if (Cart.getOrderType() === 2) {
  //     setState({ clicked1: 1, clicked: 0, shouldShow: false })
  //   }
  // }, []);

  // function here
  const sum = (key) => {
    return reduce((a, b) => a + (b[key] || 0), 0);
  }

  const getCartItem = () => {
    setState({ cartItem: Cart.getCartItem() }, () => { calculateFinalTotal() });
    console.log("888", Cart.getCartItem());
  }

  const checkDuplicate = (item, i, j) => {
    for (const r of item) {
      if (r[0] == i && r[1] == j) {
        return true
      }
    }
    return false
  }

  const getPopular = () => {
    var newList = []
    var randomList = []
    ApiClient.GET(API.activeMenu + outletData.id).then((result) => {
      var maxItem = 0
      for (const item of result) {
        maxItem = maxItem + item.items.length
      }
      var maxIteration = 5
      if (maxItem < 5) {
        maxIteration = maxIteration
      }
      var k;
      for (k = 0; k < maxIteration; k++) {
        var i = Math.floor(Math.random() * result.length)
        var j = Math.floor(Math.random() * result[i].items.length)

        while (checkDuplicate(randomList, i, j)) {
          i = Math.floor(Math.random() * result.length)
          j = Math.floor(Math.random() * result[i].items.length)
        }
        newList.push(result[i].items[j])
        randomList.push([i, j])
        setState({ popular: newList })
      }
    });

  }

  const goToPopular = (item) => {
    console.log(outletData, "outletData")
    props.navigation.navigate('MenuItemDetails', { refresh: refresh.bind(this), menuItem: item, outletData: outletData, test: test })
  }

  const renderPopularItem = ({ item, index }) => {
    return (
      <View style={{ width: 180, height: 80, borderWidth: StyleSheet.hairlineWidth, marginRight: 10, borderRadius: 10, justifyContent: "space-around" }}>
        <Text numberOfLines={2} style={{ fontSize: 16, fontWeight: "400", marginLeft: 10, }}>{item.name}</Text>
        <View style={{ flexDirection: "row", marginLeft: 10, marginTop: 10, }}>
          <Text style={{ fontSize: 16, color: "#9c9c9c", }}>{item.price}</Text>
          <TouchableOpacity style={{ marginLeft: 70 }} onPress={() => { goToPopular(item) }}>
            <Close name="pluscircle" size={22} color={Colors.primaryColor} />
          </TouchableOpacity>
        </View>
      </View>
    )
  };

  const refresh = () => {
    setState({ refresh: true });
  }

  const onChangeQty = (e, id) => {
    // const cartItem = cartItem;
    const cartItem = Cart.getCartItem();
    const item = cartItem.find((obj) => obj.itemId === id);
    item.quantity = item.quantity - e;
    if (item.quantity == 0) {
      Cart.deleteCartItem(id);
    } else {
      setState({
        cartItem,
      });
      Cart.updateCartItem(Cart.getCartItem().find((obj) => obj.itemId === id), item);
    }
    console.log("CART", Cart.getCartItem().find((obj) => obj.itemId === id))
  }

  const optional = (id) => {
    const cartItem = cartItem;
    console.log(cartItem);
    const item = cartItem.find((obj) => obj.itemId === id);
    if (item.fireOrder == 0) {
      item.fireOrder = 1;
    } else if (item.fireOrder == 1) {
      item.fireOrder = 0;
    }
    setState({
      cartItem,
    });
  }

  const extend = (id, day) => {
    const body = {
      parkingId: id,
      dayExtend: day,
      paymentMethod: ''
    }
    console.log("PARKING BODY", body)
    ApiClient.POST(API.extendParking, body).then((result) => {
      console.log(result);
      if (result != null) {
        Alert.alert(
          'Success',
          'You have successfully extended',
          [{ text: 'OK', onPress: () => { props.navigation.navigate('ConfirmOrder', { outletData: outletData }); } }],
          { cancelable: false },
        );
        User.getRefreshCurrentAction();
        Cart.clearCart();
        getCartItem();
      }
    })
  }

  const newPurchase = (outletId, itemId, qty) => {
    const body = {
      userId: User.getUserId(),
      outletId: outletId,
      itemId: itemId,
      quantity: qty,
      waiterId: 1,
    }
    ApiClient.POST(API.createUserParking, body).then((result) => {
      console.log(result);
      if (result != null) {
        Alert.alert(
          'Success',
          'You have successfully parked',
          [{ text: 'OK', onPress: () => { props.navigation.navigate('ConfirmOrder', { outletData: outletData }); } }],
          { cancelable: false },
        );
        User.getRefreshCurrentAction();
        Cart.clearCart();
        getCartItem();
      }
    })
  }

  const clearCartItems = async () => {
    await AsyncStorage.removeItem(`${firebaseUid}.cartItems`);

    CommonStore.update(s => {
      s.cartItems = [];
      s.cartItemsProcessed = [];
    });
  };

  const placeUserOrder = async () => {
    // var orderBodyType = '';
    // switch (Cart.getOrderType()) {
    //   case 0:
    //     orderBodyType = 'Dine In';
    //     break;
    //   case 1:
    //     orderBodyType = 'Take Away';
    //     break;
    //   case 2:
    //     orderBodyType = 'Pick Up';
    //     break;
    // }

    const outletSnapshot = await firestore().collection(Collections.Outlet)
      .where('uniqueId', '==', cartOutletItemsDict[cartItems[0].itemId].outletId)
      .limit(1)
      .get();

    var merchant = {};
    var outlet = {};

    if (!outletSnapshot.empty) {
      outlet = outletSnapshot.docs[0].data();

      const merchantSnapshot = await firestore().collection(Collections.Merchant)
        .where('uniqueId', '==', outlet.merchantId)
        .limit(1)
        .get();

      if (!merchantSnapshot.empty) {
        merchant = merchantSnapshot.docs[0].data();
      }
    }

    const orderDateTemp = Date.now();

    var body = {
      // cartItems: cartItems,
      cartItems: cartItemsProcessed,

      // tableId: Cart.getTableNumber(),
      // outletId: Cart.getOutletId(),
      // type: Cart.getOrderType() == 0 ? "Dine In" : "Take Away",
      orderType: ORDER_TYPE.DINEIN,
      paymentMethod: 'Online Banking',
      userVoucherId: null,
      userAddressId: selectedUserAddress ? selectedUserAddress.uniqueId : null,
      orderDate: orderDateTemp,
      // voucherType: "",
      // customTable: "",
      // sessionId: 0,
      // remarks: null,
      // collectionTime: rev_date === undefined ? new Date() : new Date(rev_date),
      totalPrice: totalPrice,
      outletId: cartOutletItemsDict[cartItems[0].itemId].outletId,

      merchantId: merchant.uniqueId,
      outletCover: outlet.cover,
      merchantLogo: merchant.logo,
      outletName: outlet.name,
      merchantName: merchant.name,

      tableId: selectedOutletTable.uniqueId,
      tablePax: selectedOutletTable.seated,
      tableCode: selectedOutletTable.code,
      tax: totalTax,
      taxId: currOutlet ? outletsTaxDict[currOutlet.uniqueId].uniqueId : '',
      discount: totalDiscount,

      waiterName: name,
      waiterId: firebaseUid,

      totalPrepareTime: totalPrepareTime,
      estimatedPreparedDate: moment(orderDateTemp).add(totalPrepareTime, 'second').valueOf(),

      remarks: '',

      outletAddress: outlet.address,
      outletPhone: outlet.phone,
      outletTaxId: currOutlet ? outletsTaxDict[currOutlet.uniqueId].uniqueId : '',
      outletTaxNumber: currOutlet ? outletsTaxDict[currOutlet.uniqueId].taxNumber : '',
      outletTaxName: currOutlet ? outletsTaxDict[currOutlet.uniqueId].name : '',
      outletTaxRate: currOutlet ? outletsTaxDict[currOutlet.uniqueId].rate : 0,

      paymentDetails: null,
    };

    ApiClient.POST(API.createUserOrder, body).then(async (result) => {
      console.log("placeOrder", result);

      if (result) {
        // if (result.success == true) {
        // if (type == 1) {
        //   orderDelivery(result);
        // }
        // else if (type == 2) {
        //   orderPickUp(result);
        // }

        await clearCartItems();

        if (userCart.userId) {
          await deleteUserCart();
        }

        Alert.alert(
          "Success",
          "Your order has been placed",
          [
            {
              text: "OK",
              onPress: () => {
                //if navFrom Takeaway then jump to Home
                if (navFrom == "TAKEAWAY") {
                  props.navigation.jumpTo('Home')
                }
                else {
                  props.navigation.popToTop()
                }
              },
            },
          ],
          { cancelable: false }
        );

        User.getRefreshCurrentAction();

        // Cart.clearCart();

        // getCartItem();
      }
    });
  }

  const placeOrder = () => {
    var orderBodyType = '';
    switch (Cart.getOrderType()) {
      case 0:
        orderBodyType = 'Dine In';
        break;
      case 1:
        orderBodyType = 'Take Away';
        break;
      case 2:
        orderBodyType = 'Pick Up';
        break;
    }

    var body = {
      items: Cart.getCartItem(),
      tableId: Cart.getTableNumber(),
      outletId: Cart.getOutletId(),
      // type: Cart.getOrderType() == 0 ? "Dine In" : "Take Away",
      type: orderBodyType,
      paymentMethod: "Online Banking",
      voucherId: "",
      voucherType: "",
      customTable: "",
      sessionId: 0,
      remarks: null,
      collectionTime: rev_date === undefined ? new Date() : new Date(rev_date),
    };
    console.log("PLACE ORDER BODY", body);

    ApiClient.POST(API.createOrder, body).then((result) => {
      console.log("placeOrder", result);
      if (result.success == true) {
        if (type == 1) {
          orderDelivery(result);
        }
        else if (type == 2) {
          orderPickUp(result);
        }

        Alert.alert(
          "Success",
          "Your order has been placed",
          [
            {
              text: "OK",
              onPress: () => {
                //if navFrom Takeaway then jump to Home
                if (navFrom == "TAKEAWAY") {
                  props.navigation.jumpTo('Home')
                }
                else {
                  props.navigation.popToTop()
                }
              },
            },
          ],
          { cancelable: false }
        );
        User.getRefreshCurrentAction();
        Cart.clearCart();
        getCartItem();
      }
    });
  }

  const orderDelivery = (result) => {
    var deliverBody = {
      orderId: result.id,
      userId: User.getUserId(),
      addressId: Cart.getDeliveryAddress().id,
    }
    ApiClient.POST(API.createOrderDelivery, deliverBody).then((result) => {
      console.log("order delivery", result)
      var body = {
        "serviceType": "MOTORCYCLE",
        "specialRequests": [],
        "stops": [
          {
            // Location information for pick-up point
            "location": {
              "lat": outletData.latlng.split(',')[0],
              "lng": outletData.latlng.split(',')[1]
            },
            "addresses": {
              "ms_MY": {
                "displayString": outletData.address,
                "country": "MY_KUL"
              }
            }
          },
          {
            // Location information for drop-off point (#1)
            "location": {
              "lat": Cart.getDeliveryAddress().lat,
              "lng": Cart.getDeliveryAddress().lng
            },
            "addresses": {
              "ms_MY": {
                "displayString": Cart.getDeliveryAddress().address,
                "country": "MY_KUL"
              }
            }
          }
        ],
        // Pick-up point copntact details
        "requesterContact": {
          "name": "Chris Wong", //get outlet person in charge?
          "phone": "0376886555" //or get waiter?
        },
        "deliveries": [
          {
            // Contact information at the drop-off point (#1)
            "toStop": 1,
            "toContact": {
              "name": User.getName(),
              "phone": User.getUserData().number
            },
            "remarks": Cart.getDeliveryAddress().note
          }
        ]
      };
      if (UTCRevDate != undefined) {
        const scheduleAt = UTCRevDate
        console.log("scheduleAt", scheduleAt)
        body["scheduleAt"] = scheduleAt
        console.log("SCHEDULE BODY", body)
        getScheduleQuotation(body, result)
      }
      else {
        console.log("NOW BODY ", body)
        placeDelivery(Cart.getDeliveryQuotation(), body, result)
      }
    })
  }

  const orderPickUp = (result) => {
    var pickUpBody = {
      orderId: result.id,
      userId: User.getUserId(),
      // addressId: Cart.getDeliveryAddress().id,
    }
    ApiClient.POST(API.createOrderPickUp, pickUpBody).then((result) => {
      console.log("order pickup", result)
    })
  }

  const getScheduleQuotation = (body, order) => {
    ApiClient.POST(API.lalamoveQuotation, body).then((result) => {
      console.log("quotation result", result)
      placeDelivery(result, body, order)
    })
  }

  const placeDelivery = (quotation, body, order) => {
    console.log("Placing delivery", quotation)
    body["quotedTotalFee"] = {
      amount: quotation.totalFee,
      currency: quotation.totalFeeCurrency
    }
    console.log("LALA ORDER BODY", body)
    ApiClient.POST(API.lalamovePlaceOrder, body).then((result) => {
      console.log("lalamvoe place order", result)
      if (result) {
        const lalaOrderId = result.orderRef
        ApiClient.GET(API.lalamoveGetOrderStatus + result.orderRef).then((result) => {
          console.log("lalamove order status", result)

          var updateBody = {
            orderId: order.orderId,
            lalaOrderId: lalaOrderId,
            orderStatus: result.status
          }
          console.log("UPDATE BODY", updateBody)
          ApiClient.PATCH(API.updateOrderDelivery, updateBody).then((result) => {
            console.log("update order delivery", result)
          })
        })
      }
    })
  }

  const molPayment = (amount, orderId) => {
    var paymentSuccess = false
    var paymentDetails = {
      // Optional, REQUIRED when use online Sandbox environment and account credentials.
      'mp_dev_mode': true,

      // Mandatory String. Values obtained from Razer Merchant Services.
      'mp_username': 'api_SB_mykoodoo',
      'mp_password': 'WaaU1IeZ*&(%%',
      'mp_merchant_ID': 'SB_mykoodoo',
      'mp_app_name': 'mykoodoo',
      'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',

      // Mandatory String. Payment values.
      'mp_amount': amount, // Minimum 1.01
      'mp_order_ID': orderId,
      'mp_currency': 'MYR',
      'mp_country': 'MY',

      // Optional String.
      'mp_channel': 'multi', // Use 'multi' for all available channels option. For individual channel seletion, please refer to https://github.com/RazerMS/rms-mobile-xdk-examples/blob/master/channel_list.tsv.
      'mp_bill_description': outletData.name + ' purchase RM' + parseFloat(amount).toFixed(2),
      'mp_bill_name': User.getUserData().name,
      'mp_bill_email': User.getUserData().email,
      'mp_bill_mobile': User.getUserData().number,
    }
    molpay.startMolpay(paymentDetails, (data) => {
      //callback after payment success
      console.log(data);
      if (data) {
        console.log("payment success")
        placeOrder()
      }
    });
  }

  const calculateFinalTotal = () => {
    console.log("cartItem", cartItem)
    const totalFloat = parseFloat(
      cartItem
        .reduce(
          (accumulator, current) =>
            accumulator + current.price * current.quantity,
          0,
        )
    )

    const totalTax = parseFloat(
      (cartItem
        .reduce(
          (accumulator, current) =>
            accumulator + current.price * current.quantity,
          0,
        )
        .toFixed(2) /
        parseFloat(100)) *
      parseFloat(Cart.getTax()),
    )

    //+ '001' //.toFixed() bug, occour only if last significant digit is 5 and if fixing to only 1 less digit, i.e. 1.75.toFixed(1) => 1.7 instead of 1.8
    var taxStr = totalTax.toString().split('.')
    console.log(taxStr)
    if (taxStr.length > 1) {
      taxStr[1] = taxStr[1] + '001'
    }
    const taxFloat = parseFloat(taxStr[0] + '.' + taxStr[1])

    setState({ totalFloat: parseFloat(totalFloat).toFixed(2), taxFloat: parseFloat(taxFloat).toFixed(2) })
  }

  const generateScheduleTime = () => {
    const now = new Date()
    var m = (((now.getMinutes() + 7.5) / 15 | 0) * 15) % 60;
    var h = ((((now.getMinutes() / 105) + .5) | 0) + now.getHours()) % 24;
    const nearestNow = new Date(now.getFullYear(), now.getMonth(), now.getDate(), h, m)
    var startTime = moment(nearestNow, 'YYYY-MM-DD hh:mm')
    var endTime = moment(new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, now.getHours(), now.getMinutes()), 'YYYY-MM-DD hh:mm a');

    var result = [];
    var current = moment(startTime);

    var tempstr = current.format('llll').split(',')
    var daystr = tempstr[0]
    var datestr = tempstr[1].split(' ')
    var yeartimestr = tempstr[2].split(' ')
    var currentNearestTime = daystr + ' ' + datestr[2] + ' ' + datestr[1] + ',' + yeartimestr[2] + ',' + yeartimestr[1] + ',' + yeartimestr[3]
    result.push(currentNearestTime);


    current.add(15, 'minutes');
    while (current <= endTime) {
      var tempstr = current.format('llll').split(',')
      console.log("tempstr", tempstr)
      var daystr = tempstr[0]
      var datestr = tempstr[1].split(' ')
      var yeartimestr = tempstr[2].split(' ')
      var finalstr = daystr + ' ' + datestr[2] + ' ' + datestr[1] + ',' + yeartimestr[2] + ',' + yeartimestr[1] + ',' + yeartimestr[3]
      result.push(finalstr);

      current.add(15, 'minutes');
    }
    console.log("schedule result", result)
    setState({ schedulteTimeList: result, schedulteTimeSelected: result[0], currentNearestTime: currentNearestTime })
  }

  const renderSchedule = (item) => (
    <View style={{ flexDirection: 'row', padding: 20, justifyContent: 'space-between', alignContent: 'center' }}>
      <View style={{ justifyContent: 'flex-start', marginHorizontal: 10 }}>
        <Text>
          {currentNearestTime == item.toString() ? 'TODAY' : item.toString().split(',')[0]}
        </Text>
      </View>
      <View style={{ justifyContent: 'flex-end', marginHorizontal: 10 }}>
        <Text>
          {currentNearestTime == item.toString() ? 'ASAP' : item.toString().split(',')[1]}
        </Text>
      </View>
    </View>
  )

  const proceedToMenuItemDetailsForUpdating = async (item) => {
    const outletItemSnapshot = await firestore().collection(Collections.OutletItem)
      .where('uniqueId', '==', item.itemId)
      .limit(1)
      .get();

    if (!outletItemSnapshot.empty) {
      const outletItem = outletItemSnapshot.docs[0].data();

      CommonStore.update(s => {
        s.selectedOutletItem = outletItem;

        s.onUpdatingCartItem = item;
      });

      props.navigation.navigate("MenuItemDetails", {
        refresh: refresh.bind(this),
        menuItem: outletItem,
        cartItem: item,
        // outletData: selectedOutlet,
      });
    }
  };

  const removeFromCartItems = async (item) => {
    var updateCartItemIndex = 0;

    for (var i = 0; i < cartItems.length; i++) {
      if (cartItems[i].itemId === item.itemId &&
        cartItems[i].cartItemDate === item.cartItemDate) {
        updateCartItemIndex = i;
      }
    }

    const newCartItems = [
      ...cartItems.slice(0, updateCartItemIndex),
      ...cartItems.slice(updateCartItemIndex + 1),
    ];

    if (newCartItems.length > 0) {
      await AsyncStorage.setItem(`${firebaseUid}.cartItems`, JSON.stringify(newCartItems));
    }
    else {
      await AsyncStorage.removeItem(`${firebaseUid}.cartItems`);
    }

    CommonStore.update(s => {
      s.cartItems = newCartItems;
    });
  };

  const renderItem = (params) => {
    // var item = {
    //   ...params.item,
    // };

    // if (cartOutletItemsDict[item.itemId]) {
    //   item.image = cartOutletItemsDict[item.itemId].image;
    //   item.name = cartOutletItemsDict[item.itemId].name;
    //   item.itemName = cartOutletItemsDict[item.itemId].name;

    // }

    const item = params.item;

    console.log('item');
    console.log(item);

    //Font Condition
    var itemTitleFont = 19;

    if (Dimensions.get('screen').width <= 360) {
      itemTitleFont = 15;
      //console.log(Dimensions.get('screen').width)
    }

    const itemTitleTextScale = {
      fontSize: itemTitleFont,
    };

    var itemRemarkFont = 14;

    if (Dimensions.get('screen').width <= 360) {
      itemRemarkFont = 11;
      //console.log(Dimensions.get('screen').width)
    }

    const itemRemarkTextScale = {
      fontSize: itemRemarkFont,
    };

    var itemPriceFont = 14;

    if (Dimensions.get('screen').width <= 360) {
      itemPriceFont = 12;
      //console.log(Dimensions.get('screen').width)
    }

    const itemPriceTextScale = {
      fontSize: itemPriceFont,
    };

    //End Font Condition

    return (
      <>
        <View
          style={{
            flexDirection: "row",
            paddingVertical: 10,
            borderColor: Colors.descriptionColor,
            justifyContent: "flex-start",
            alignContent: "center",
            alignItems: "center",
            display: 'flex',
            // borderBottomWidth: StyleSheet.hairlineWidth,
            marginBottom: 5,
          }}
        >
          <View
            style={[{
              marginRight: 15,
              backgroundColor: Colors.secondaryColor,
              borderRadius: 5,
              alignSelf: "flex-start",
              // flex: 1,            
            }, item.image ? {
              // display: 'flex',
              // alignItems: 'center',
              // justifyContent: 'center',
            } : {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              paddingTop: 5,
              paddingBottom: 5,
            }]}
          >
            {item.image
              ?
              <AsyncImage
                source={{ uri: item.image }}
                item={item}
                style={{
                  // width: 70, 
                  // height: 70,
                  width: Dimensions.get('screen').width * 0.18,
                  height: Dimensions.get('screen').width * 0.18,
                  borderRadius: 5,
                }}
              />
              :
              <Ionicons name="cart-outline" size={45} />
            }
          </View>

          <View style={{
            // backgroundColor: 'red',
            width: '45%',
          }}>
            <View style={{
              display: 'flex',
              // backgroundColor: 'red',
            }}>
              <Text style={[itemTitleTextScale, {
                // fontWeight: "700",
                //fontSize: 19,
                fontFamily: "NunitoSans-Bold",
                color: Colors.mainTxtColor,
              }]}>
                {item.name}
              </Text>

              {/* {item.options ? item.options.map((item, index) => {
              return (
                (item.quantity > 0 ? <Text style={{ color: Colors.descriptionColor, marginTop: 5, fontWeight: '400', fontSize: 11, fontFamily: "NunitoSans-Regular" }}>
                  {" "}
                + {item.quantity} ({item.itemName})
              </Text> : null)
              );
            }) : null} */}

              {item.addOns && item.addOns.length > 0 &&
                item.addOns.map((addOn, index) => {
                  const addOnChoices = addOn.choiceNames.join(', ');

                  return (
                    <Text
                      key={index}
                      style={[itemRemarkTextScale, {
                        color: Colors.descriptionColor,
                        marginTop: 3,
                        // fontWeight: "700",
                        //fontSize: 14,
                        marginBottom: 3,
                        fontFamily: "NunitoSans-Regular"
                      }]}
                    >
                      {/* Size: {item.size == "small" ? "Small" : item.size == "medium" ? "Medium" : item.size == "big" ? "Big" : null} */}
                      {addOn.quantities ?
                        <>
                          {`+ ${addOn.quantities.reduce((accum, value) => accum + value, 0)} (${addOnChoices})`}
                        </>
                        :
                        <>
                          {`${addOn.name} (${addOnChoices})`}
                        </>
                      }
                    </Text>
                  );
                })
              }
            </View>

            {/* <Text style={{ 
            fontWeight: "700", 
            fontSize: 19, 
            fontFamily: 
            "NunitoSans-Regular" 
          }}>
            {test1 == 1 ? item.name : test2 == 1 ? item.name : ('x' + item.quantity + " " + item.name)}
          </Text> */}

            {/* {test1 == 1 ?
            <Text style={{ fontSize: 14, fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}> ({item.quantity} days extension)
          </Text>
            : test2 == 1 ? <Text style={{ fontSize: 14, fontFamily: "NunitoSans-Regular", color: Colors.descriptionColor }}> x{item.quantity} mugs
            </Text> : null} */}

            {item.remarks ? <Text
              style={{
                color: Colors.descriptionColor, marginTop: 0, fontWeight: '400', fontSize: 11, fontFamily: "NunitoSans-Italic"
              }}
            >
              Remarks: {item.remarks}
            </Text> : null}

            {/* {type == 0 ? <TouchableOpacity
              onPress={() => {
                optional(item.itemId);
              }}
            >
              <Text
                style={{
                  color: Colors.primaryColor,
                  marginTop: 3,
                  fontWeight: "700",
                  fontSize: 14,
                  marginBottom: 3,
                  fontFamily: "NunitoSans-Regular"
                }}
              >
                {item.fireOrder == 0 ? "Serve now" : item.fireOrder == 1 ? "Serve later" : null}
              </Text>
            </TouchableOpacity> : null} */}
          </View>

          <View
            style={{
              flexDirection: "row",
              //justifyContent: "flex-end",
              alignItems: "center",
              justifyContent: "space-between",
              paddingHorizontal: '2%',
              width: '24%',
              //backgroundColor: 'blue',
            }}
          >
            <Text
              style={[itemPriceTextScale, {
                color: Colors.descriptionColor,
                //fontSize: 14,
                fontFamily: "NunitoSans-Regular",
              }]}
            >
              RM
            </Text>
            <Text
              style={[itemPriceTextScale, {
                color: Colors.descriptionColor,
                fontFamily: "NunitoSans-Regular",
              }]}
            >
              {(parseFloat(item.price)).toFixed(2)}
            </Text>

            {/* <TouchableOpacity
            onPress={() => {
              if (item.quantity <= 1) {
                Cart.deleteCartItem(item.itemId, item);
                getCartItem();
              }
              else {
                setState({
                  visible: true,
                  editingItemId: item.itemId,
                  qty: item.quantity,
                  value: item.quantity,
                });
              }
            }}
          >
            <Entypo name="cross" size={28} color="red" />
          </TouchableOpacity> */}
          </View>

          <View style={{
            width: '10%',
            //marginLeft: 27,
            alignItems: 'center',
            //backgroundColor: 'red',
          }}>
            <TouchableOpacity
              onPress={() => {
                //console.log("ITEM@@@@@@@@@@@@@@@@@@@@@@@@@@@@", item)

                proceedToMenuItemDetailsForUpdating(item);

                // props.navigation.navigate("MenuItemDetailsUpdate", {
                //   refresh: refresh.bind(this),
                //   menuItem: item.menuItem,
                //   cartItem: item,
                //   outletData: outletData,
                // });
              }}
              style={{
                // width: '10%',
                // marginLeft: 30,
                // backgroundColor: 'green',
              }}
            >
              <Icons name="edit" size={25} color={Colors.primaryColor} />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                removeFromCartItems(item);
              }}
              style={{
                // width: '10%',
                // marginLeft: 30,
                // backgroundColor: 'green',
                marginTop: 10,
                right: 1,
              }}
            >
              <FontAwesome name="trash-o" size={26} color={Colors.tabRed} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={{
          height: 1.5,
          left: '-2%',
          width: '104%',
          backgroundColor: '#C2C1C0',
          opacity: 0.2,
          marginBottom: 4,

          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,
          elevation: 3,
        }}>
        </View>
      </>
    )
  };

  const changeClick = () => { //delivery
    if (clicked == 1) {
      //setState({ clicked: 0 })
    }
    else {
      // setState({ clicked: 1, clicked1: 0, shouldShow: true })
      setClicked(1);
      setClicked1(0);
      // setShouldShow(true);

      Cart.setOrderType(1);

      CommonStore.update(s => {
        s.orderType = ORDER_TYPE.DELIVERY;
      });
    }
  }

  const changeClick1 = () => { //pickup
    if (clicked1 == 1) {
      //setState({ clicked1: 0 })
    }
    else {
      // setState({ clicked1: 1, clicked: 0, shouldShow: false })
      setClicked(0);
      setClicked1(1);
      // setShouldShow(false);

      Cart.setOrderType(2);

      CommonStore.update(s => {
        s.orderType = ORDER_TYPE.PICKUP;
      });
    }
  }

  const deleteUserCart = async () => {
    const body = {
      userCartId: userCart.uniqueId,
    };

    ApiClient.POST(API.deleteUserCart, body).then((result) => {
      if (result && result.status === 'success') {
        console.log('ok');
      }
    });
  };

  // function end

  return cartItemsProcessed.length == 0 ? (
    <View style={styles.container}>
      <View style={{ flex: 1 }}>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Text
            style={{
              color: Colors.descriptionColor,
              textAlign: "center",
              marginTop: 30,
              top: '-5%',
              fontFamily: 'NunitoSans-Regular',
              fontSize: 16,
            }}
          >
            No items in your cart now.
          </Text>
        </View>

        {/* <TouchableOpacity
          onPress={() => {
            props.navigation.navigate("Home");
          }}
        >
          <View style={[Styles.button]}>
            <Text style={{ color: "#ffffff", fontSize: 18 }}>
              Explore outlet now
            </Text>
          </View>
        </TouchableOpacity> */}
      </View>
    </View>
  ) : (

    <View style={styles.container}>
      {/*<ActionSheet
          ref={(o) => (ActionSheet = o)}
          title={'Select Your Payment Method'}
          options={['Online payment', 'Credit/Debit Card', 'cancel', 'Cash on delivery']}
          cancelButtonIndex={2}
          destructiveButtonIndex={2}
          onPress={(index) => {
            console.log(index);
            if (index != 2) {
              setState({
                paymentMethod:
                  index == 0 ? 'Online payment' : 'Cash on delivery',
              });
            }
          }}
        />*/}
      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              getCartItem();
              getPopular()
            }}
          />
        }>

        {/* <View style={{ flexDirection: "row", marginBottom: 10, marginTop: 5 }}>
            <TouchableOpacity style={{
              width: "30%",
              height: 30,
              backgroundColor: clicked === 1 ? Colors.primaryColor : Colors.whiteColor,
              borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor
            }}
              onPress={() => { changeClick() }}
            >
              <Text style={{ color: clicked === 1 ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-Regular" }}>Delivery</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{
              width: "30%",
              height: 30,
              backgroundColor: clicked1 === 1 ? Colors.primaryColor : Colors.whiteColor,
              borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1, borderColor: Colors.descriptionColor, marginLeft: 8
            }}
              onPress={() => { changeClick1() }}
            >
              <Text style={{ color: clicked1 === 1 ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-Regular" }}>Pick-up</Text>
            </TouchableOpacity>
          </View> */}

        {/* <View style={{
            flexDirection: "row",
            marginBottom: 15,
            marginTop: 5,
            paddingHorizontal: 24,
          }}>
            <TouchableOpacity style={{
              // width: "30%",
              // height: 30,
              width: 86,
              backgroundColor: clicked === 1 ? Colors.primaryColor : Colors.whiteColor,
              borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1,
              borderColor: clicked === 1 ? Colors.descriptionColor : "#adadad",
              paddingVertical: 6,
            }}
              onPress={() => { changeClick() }}
            >
              <Text style={{ color: clicked === 1 ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-SemiBold", fontSize: 13 }}>Delivery</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{
              // width: "30%",
              // height: 30,
              width: 86,
              backgroundColor: clicked1 === 1 ? Colors.primaryColor : Colors.whiteColor,
              borderRadius: 40, justifyContent: "center", alignItems: "center", borderWidth: 1,
              borderColor: clicked1 === 1 ? Colors.descriptionColor : "#adadad",
              marginLeft: 8,
              paddingVertical: 6,
            }}
              onPress={() => { changeClick1() }}
            >
              <Text style={{ color: clicked1 === 1 ? Colors.whiteColor : "#adadad", fontFamily: "NunitoSans-SemiBold", fontSize: 13 }}>Pick-up</Text>
            </TouchableOpacity>
          </View> */}

        <View style={{
          width: "100%",
          // height: 60,
          paddingVertical: 16,
          backgroundColor: "#ddebe5",
          justifyContent: "center",
          paddingHorizontal: 28,
          // marginTop: 5,            
        }}>
          <Text style={{
            color: Colors.primaryColor,
            marginLeft: 4,
            fontSize: 17,
            fontFamily: 'NunitoSans-SemiBold',
          }}>Order Summary</Text>
        </View>

        <View style={{
          flexDirection: 'row',
          marginTop: 15,
        }}>
          <FlatList
            style={{ marginBottom: 10 }}
            data={cartItemsProcessed}
            extraData={cartItemsProcessed}
            renderItem={renderItem}
            keyExtractor={(item, index) => String(index)}
            contentContainerStyle={{
              paddingHorizontal: 30,
              paddingBottom: 5,
            }}
          />
        </View>
        {/* <Modal
            style={{ flex: 1 }}
            visible={visible}
            transparent={true}
            animationType="slide">
            <View
              style={{
                backgroundColor: 'rgba(0,0,0,0.5)',
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <View style={styles.confirmBox}>
                <TouchableOpacity
                  onPress={() => {
                    setState({ visible: false });
                    setState({ visible1: false });
                  }}>
                  <View
                    style={{
                      alignSelf: 'flex-end',
                      padding: 16,
                    }}>
                    <Close name="closecircle" size={28} />
                  </View>
                </TouchableOpacity>
                <View>
                  <Text
                    style={{
                      textAlign: 'center',
                      fontWeight: 'bold',
                      fontSize: 16,
                      marginBottom: 5,
                    }}>
                    Do you want to delete all?
                </Text>
                </View>
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%',
                    alignContent: 'center',
                    marginBottom: '6%',
                    height: '50%',
                  }}>
                  <View
                    style={{
                      alignItems: 'center',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        Cart.deleteCartItem(editingItemId);
                        getCartItem();
                        setState({ visible1: false });
                        setState({ visible: false });
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '30%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        borderRadius: 5,
                        height: '75%',
                      }}>
                      <Text style={{ fontSize: 15, color: Colors.primaryColor }}>
                        Yes
                    </Text>
                    </TouchableOpacity>
                    <View style={{ marginLeft: '3%', marginRight: '3%' }}></View>
                    <TouchableOpacity
                      onPress={() => {
                        setState({ visible1: true });
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '30%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        borderRadius: 5,
                        height: '75%',
                      }}>
                      <Text style={{ fontSize: 15, color: Colors.primaryColor }}>
                        No
                    </Text>
                    </TouchableOpacity>
                    
                  </View>
                </View>
              </View>
            </View>
          </Modal> */}
        <Modal
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={visible}
          transparent={true}
          animationType="slide">
          <View
            style={{
              backgroundColor: 'rgba(0,0,0,0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View style={styles.confirmBox}>
              <TouchableOpacity
                onPress={() => {
                  setState({ visible1: false });
                  setState({ visible: false });
                }}>
                <View
                  style={{
                    alignSelf: 'flex-end',
                    padding: 13,
                  }}>
                  <Close name="closecircle" size={28} />
                </View>
              </TouchableOpacity>
              <View>
                <Text
                  style={{
                    textAlign: "center",
                    fontWeight: "bold",
                    fontSize: 16,
                    marginBottom: 10,
                  }}
                >
                  Delete
                </Text>
              </View>
              <View
                style={{
                  justifyContent: "center",
                  alignItems: "center",
                  width: "100%",
                  alignContent: "center",
                  marginBottom: "10%",
                  height: "50%",
                }}
              >
                <View
                  style={{
                    alignItems: "center",
                    marginBottom: "2%",
                  }}
                >
                  {/* <NumericInput
                    value={value}
                    onChange={(value) => setState({ value })}
                    minValue={1}
                    maxValue={qty}
                    totalWidth={200}
                    totalHeight={40}
                    iconSize={25}
                    step={1}
                    valueType="real"
                    rounded
                    textColor={Colors.primaryColor}
                    iconStyle={{ color: "white" }}
                    rightButtonBackgroundColor={Colors.primaryColor}
                    leftButtonBackgroundColor={"grey"}
                  /> */}
                </View>
                <View
                  style={{
                    alignItems: "center",
                    flexDirection: "row",
                    justifyContent: "space-between",
                  }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      onChangeQty(
                        value,
                        editingItemId
                      );
                      getCartItem();
                      setState({ visible1: false });
                      setState({ visible: false });
                      setState({ value: "" });
                    }}
                    style={{
                      backgroundColor: Colors.primaryColor,
                      width: "30%",
                      justifyContent: "center",
                      alignItems: "center",
                      alignContent: "center",
                      borderRadius: 5,
                      height: "75%",
                      marginTop: 10,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 15,
                        color: Colors.whiteColor,
                      }}
                    >
                      Update
                    </Text>
                  </TouchableOpacity>

                </View>
              </View>
            </View>
          </View>
        </Modal>

        {/* <Text style={{
            fontWeight: "bold",
            fontSize: 20,
            marginTop: 10,
            marginBottom: 10,

            paddingHorizontal: 24
          }}>Other popular items</Text>

          <FlatList
            horizontal={true}
            data={popular}
            extraData={popular}
            renderItem={renderPopularItem}
            keyExtractor={(item, index) => String(index)}
            contentContainerStyle={{
              paddingHorizontal: 24,
            }}
          /> */}

        {/* <View style={{ height: 20 }}></View> */}

        <View style={{
          flexDirection: 'row',
          width: "100%",
          //marginLeft: 40,
          marginTop: 0,

          paddingHorizontal: 30,
        }}>
          <View style={{ width: '45%' }}>
            <Text style={styles.description}>Subtotal</Text>
            <Text style={styles.description}>Discount</Text>
            <Text style={styles.description}>
              Tax ({Cart.getTax()}%)
            </Text>
            {type == 1 ? <View>
              <Text style={styles.description}>Delivery Fees</Text>
              <Text style={styles.smallDescription}>
                (This fees is quoted by 3rd party logistic and it might be different as it depends on the exact distance)
              </Text>
            </View> : null}
            <Text style={styles.description}></Text>
          </View>

          <View style={{ width: Dimensions.get('screen').width * 0.18, }}/>
          <View style={{ width: 15, }}/>

          <View style={{ width: '40%', alignSelf: 'flex-start', }}>
            <View style={{ justifyContent: 'space-between', width: '70%', flexDirection: 'row', alignItems: 'center', }}>
              <Text style={styles.price}>
                RM
              </Text>
              <Text style={styles.price}>
                {totalPrice.toFixed(2)}
              </Text>
            </View>
            <View style={{ justifyContent: 'space-between', width: '70%', flexDirection: 'row', alignItems: 'center', }}>
              <Text style={styles.price}>
                RM
              </Text>
              <Text style={styles.price}>
                0.00
              </Text>
            </View>
            <View style={{ justifyContent: 'space-between', width: '70%', flexDirection: 'row', alignItems: 'center', }}>
              <Text style={styles.price}>
                RM
              </Text>
              <Text style={styles.price}>
                {totalTax.toFixed(2)}
              </Text>
            </View>
            {type == 1 ? <Text style={styles.price}>RM {deliveryQuotation == null ? "0.00" : parseFloat(deliveryQuotation.totalFee).toFixed(2)}</Text> : null}
            {/* <Text style={styles.price}></Text>
              <Text style={styles.price}></Text> */}
          </View>
        </View>

        <View style={{
          flexDirection: 'row',
          width: "100%",
          //marginLeft: 40,

          paddingHorizontal: 30,
          marginTop: -20,
          marginBottom: 12,
        }}>
          <View style={{ width: '45%' }}>
            <Text style={styles.total}>TOTAL</Text>
          </View>

          <View style={{ width: Dimensions.get('screen').width * 0.18, }}/>
          <View style={{ width: 15, }}/>

          <View style={{ width: '40%' }}>
            <View style={{ justifyContent: 'space-between', width: '70%', flexDirection: 'row', alignItems: 'center', }}>
              <Text style={styles.totalPrice}>
                RM
              </Text>
              <Text style={styles.totalPrice}>
                {(
                  totalPrice + totalTax
                ).toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        {/* <TouchableOpacity onPress={() => { }} style={[styles.textInput1, {
            // marginHorizontal: 36,
            width: '80%',
            alignSelf: 'center',

          }]}
            onPress={() => {
              setRouteFrom(1);
              // props.navigation.navigate("Voucher", { pageFrom: 'CartScreen' });
              props.navigation.navigate("Voucher", {
                screen: 'Voucher',
                params: {
                  pageFrom: 'CartScreen',
                }
              });
            }}
          >
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
              <Text
                style={{
                  // paddingVertical: 10,
                  fontSize: 15,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                Apply Vouchers
              </Text>
              <View style={{
                // marginLeft: '30%', 
                justifyContent: 'center'
              }}>
                <Entypo name="chevron-thin-down" size={22} />
              </View>
            </View>
          </TouchableOpacity> */}

        {Cart.getVoucher() &&
          <View style={{
            width: "100%",
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: 20,
            justifyContent: 'space-around',
          }}>
            <View style={[styles.pic, {
            }]}>
              {Cart.getVoucher().outlet && Cart.getVoucher().outlet.cover &&
                <Image
                  source={{ uri: Cart.getVoucher().outlet.cover }}
                  style={{ width: 90, height: Platform.OS == 'ios' ? 90 : 90, borderRadius: 10 }}
                />
              }
              {!(Cart.getVoucher().outlet && Cart.getVoucher().outlet.cover) &&
                <Image
                  source={require('../assets/image/extend.png')}
                  style={{ width: 90, height: Platform.OS == 'ios' ? 90 : 90, borderRadius: 10 }}
                />
              }
            </View>

            <Text style={[styles.text, {
              width: '60%',
            }]}>{Cart.getVoucher().GiftCard.description}</Text>
          </View>
        }

        {/* Use cash to checkout for now */}
        {/* <View style={{
            width: '80%',
            alignSelf: 'center',
            marginTop: 20,
          }}>
            <Text style={[styles.payment, {
              // paddingHorizontal: 24,
            }]}>Select your payment method</Text>

            <TouchableOpacity
              onPress={() => {
                setRouteFrom(1);
                props.navigation.navigate("PaymentMethod", { pageFrom: 'CartScreen' });
                // props.navigation.navigate("PaymentMethod", {
                //   screen: 'PaymentMethod',
                //   params: {
                //     pageFrom: 'CartScreen',
                //   }
                // });
              }}
              style={[styles.textInput, {
                // marginHorizontal: 24,
              }]}>
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                // backgroundColor: 'red',
                height: '100%',
              }}>
                <Text style={{
                  // paddingVertical: 14
                  fontSize: 15,
                  fontFamily: 'NunitoSans-Regular',
                  color: Colors.fieldtTxtColor,
                }}>
                  {'Credit/Debit Card'}
                </Text>
                <View style={{
                  // marginLeft: '40%',
                  justifyContent: 'center'
                }}>
                  <Entypo
                    name="chevron-thin-down"
                    size={20}
                    color={Colors.primaryColor}
                  />
                </View>
              </View>
            </TouchableOpacity>
          </View> */}

        {/* {Cart.getPayment() &&
            <View style={[styles.card, {
              backgroundColor: 'transparent',
              width: '100%',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 10,
              justifyContent: 'flex-start',
            }]}>
              <View style={{
                marginRight: Platform.OS == 'ios' ? 120 : 25,
                left: -40,
              }}>
                <View style={[styles.pic, {
                  // width: 0,
                  // height: 0,
                  backgroundColor: 'transparent',
                  marginTop: 0,
                  marginRight: 30,
                }]}>
                  <Image
                    resizeMode={'contain'}
                    source={{ uri: Cart.getPayment().image }}
                    style={{ width: 200, height: 70, borderRadius: 10 }}
                  />
                </View>
              </View>
              <View style={{
                // width: '60%',
              }}>
                <Text style={styles.title}>{Cart.getPayment().cardType}</Text>
                <Text style={styles.text}>{Cart.getPayment().cardNumber.replace(/./g, '*')}</Text>
              </View>
            </View>
          } */}

        {type == 1 ?
          <View style={{
            width: "100%",
            // height: 60, 
            paddingVertical: 16,
            backgroundColor: "#ddebe5",
            justifyContent: "center",
            paddingHorizontal: 28,
            marginTop: 20,
          }}>
            <Text style={{
              color: Colors.primaryColor,
              marginLeft: 4,
              fontSize: 17,
              fontFamily: 'NunitoSans-SemiBold',
            }}>Deliver To</Text>
          </View> : null}

        {type == 1 ? <View style={{
          // height: 100,
          justifyContent: "center",
          // borderBottomWidth: StyleSheet.hairlineWidth,
          paddingHorizontal: 16,
          paddingVertical: 25,
        }}>
          <View style={{ flexDirection: "row", marginLeft: 10, display: 'flex', alignItems: 'center' }}>
            <View style={{ width: "15%" }}>
              <Ionicons name="location-sharp" size={34} color={"red"} />
            </View>
            <View style={{ width: "60%" }}>
              <Text style={{
                fontSize: 14,
                color: Colors.mainTxtColor,
                fontFamily: 'NunitoSans-SemiBold',
              }}>{Cart.getDeliveryAddress() != null || Cart.getDeliveryAddress() != undefined ? Cart.getDeliveryAddress().address : "Select an address"}</Text>
            </View>
            <View style={{ width: "10%" }}></View>

            <TouchableOpacity onPress={() => {
              setRouteFrom(1);
              props.navigation.navigate("Address", { testing: 1 });
            }
            } style={{
              marginLeft: 15,
            }}>
              <Icons name="edit" size={24} color={Colors.primaryColor} />
            </TouchableOpacity>
          </View>
        </View> : null}

        <View style={{
          height: 1.5,
          left: '5%',
          width: '90%',
          backgroundColor: '#C2C1C0',
          opacity: 0.2,
          marginBottom: 4,

          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,
          elevation: 3,
        }}>
        </View>

        {type == 1 ? <View style={{
          // height: 100,
          justifyContent: "center",
          paddingHorizontal: 16,
          paddingVertical: 25,
        }}>
          <View style={{
            flexDirection: "row",
            marginLeft: 16,
            alignItems: 'center',
          }}>
            <TouchableOpacity style={{
              width: 22,
              height: 22,
              backgroundColor:
                Colors.whiteColor,
              // marginTop: 10, 
              borderRadius: 20, borderWidth: 1, justifyContent: "center", borderColor: Colors.primaryColor
            }}
              onPress={() => { }}>
              <View style={{ width: '70%', height: '70%', backgroundColor: Colors.primaryColor, borderRadius: 20, alignSelf: "center" }}>

              </View>
            </TouchableOpacity>
            <View style={{ width: "5%" }}></View>
            <View style={{ width: "15%" }}>
              {/* <MaterialIcons name="delivery-dining" size={40} color={Colors.primaryColor} /> */}
              <Image
                source={require('../assets/image/delivery.png')}
                style={{ width: 34, height: 34, resizeMode: "contain" }}></Image>
            </View>
            <View style={{ width: "55%" }}>
              <View>
                <Text style={{
                  color: Colors.primaryColor,
                  // fontWeight: '700', 
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 16,
                }}>Delivery</Text>
                <Text style={{
                  color: "#9c9c9c",
                  fontSize: 14,
                  colors: Colors.descriptionColor,
                }}>Deliver now (45mins)</Text>
              </View>
            </View>
            <View style={{
              width: "20%",
            }}>
              <TouchableOpacity onPress={() => { setState({ showDateTimePicker: true }), generateScheduleTime() }}>
                <Text style={{
                  color: Colors.primaryColor,
                  // fontWeight: "700",
                  // marginTop: 10
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 14,
                  textAlign: 'left',
                }}>{rev_date === undefined ? "Schedule" : rev_date.split(',')[0] + ' ' + rev_date.split(',')[1]}</Text>
              </TouchableOpacity>
            </View>
            {/* <DateTimePickerModal
                minimumDate={new Date()}
                selectMultiple={false}
                isVisible={showDateTimePicker}
                mode={pickerMode}
                display={pickerMode == "time" ? "spinner" : "default"} //for iOS to use minuteInterval
                maximumDate={new Date(Date.now() + (4320 * 60 * 1000))}
                onConfirm={(text) => {
                  if (pickerMode == 'time') {
                    setState({ rev_time: new Date(text).getHours() + ":" + new Date(text).getMinutes() + ":00" })
                  } else {
                    var date_ob = new Date(text);
                    let date = ("0" + date_ob.getDate()).slice(-2);
                    let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                    let year = date_ob.getFullYear();
                    setState({ rev_date: year + "-" + month + "-" + date, rev_time: new Date(text).getHours() + ":" + new Date(text).getMinutes() + ":00" })
                  }
                  setState({ showDateTimePicker: false })
                }}
                onCancel={() => {
                  setState({ showDateTimePicker: false })
                }}
              /> */}
            <Modal
              supportedOrientations={['landscape', 'portrait']}
              style={{ flex: 1 }}
              visible={showDateTimePicker}
              transparent={true}
              animationType="slide">
              <View
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <View style={styles.scheduleBox}>
                  <TouchableOpacity
                    onPress={() => {
                      setState({ showDateTimePicker: false });
                    }}>
                    <View
                      style={{
                        alignSelf: 'flex-end',
                        padding: 16,
                      }}>
                      <Close name="closecircle" size={28} />
                    </View>
                  </TouchableOpacity>
                  <View>
                    <Text
                      style={{
                        //textAlign: "center",
                        //fontWeight: "bold",
                        fontSize: 11,
                        marginBottom: 5,
                        marginLeft: '20%',
                        color: Colors.fieldtTxtColor
                      }}
                    >
                      Select delivery time
                    </Text>
                    <Text
                      style={{
                        //textAlign: "center",
                        fontWeight: "bold",
                        fontSize: 16,
                        marginBottom: 5,
                        marginLeft: '20%',
                        color: Colors.primaryColor
                      }}
                    >
                      {schedulteTimeSelected.split(',')[0]} {schedulteTimeSelected == 'TODAY,ASAP' ? ' ' : ', '} {schedulteTimeSelected.split(',')[1]}
                    </Text>
                  </View>
                  <View
                    style={{
                      justifyContent: "center",
                      alignItems: "center",
                      width: "100%",
                      alignContent: "center",
                      marginBottom: "6%",
                      height: "50%",
                    }}
                  >
                    <View
                      style={{
                        alignItems: "center",
                        marginBottom: "2%",
                        marginTop: "35%"
                      }}
                    >
                      <SpinPicker
                        data={schedulteTimeList}
                        value={schedulteTimeList[0]}
                        onValueChange={selectedItem => setState({ schedulteTimeSelected: selectedItem })}
                        keyExtractor={number => number.toString()}
                        //showArrows
                        //onInputValueChanged={console.log("hit")}
                        renderItem={renderSchedule} />
                    </View>
                    <View
                      style={{
                        alignItems: "center",
                        flexDirection: "row",
                        justifyContent: "space-between",
                      }}
                    >
                      <TouchableOpacity
                        onPress={() => {
                          console.log("schedulteTimeSelected", schedulteTimeSelected)
                          setState(
                            { rev_date: schedulteTimeSelected, showDateTimePicker: false },
                            () => {
                              console.log("rev_date: schedulteTimeSelected", rev_date)
                              var splitDate = rev_date.split(',')
                              var year = splitDate[2]
                              var time = splitDate[1]
                              var splitDay = splitDate[0].split(' ')
                              var month = splitDay[2]
                              var date = splitDay[1]
                              const UTCRevDate = new Date(month + ' ' + date + ' ' + year + ' ' + time + ' ' + splitDate[3])
                              console.log("UTCRevDate", UTCRevDate)
                              setState({ UTCRevDate: UTCRevDate })
                            })

                        }}
                        style={{
                          backgroundColor: Colors.primaryColor,
                          width: "80%",
                          justifyContent: "center",
                          alignItems: "center",
                          alignContent: "center",
                          borderRadius: 10,
                          height: "70%",
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 24,
                            color: Colors.whiteColor,
                          }}
                        >
                          SCHEDULE
                        </Text>
                      </TouchableOpacity>

                    </View>
                  </View>
                </View>
              </View>
            </Modal>
          </View>
        </View> : null}

        {/* <View style={{ height: 20 }}></View> */}
      </ScrollView>
      <TouchableOpacity
        onPress={() => {
          // if (test1 == 1) {
          //   extend(Cart.getParkingId(), cartItem[0].quantity)
          // }
          // else {
          //   if (type == 1 && (Cart.getDeliveryAddress() == null || Cart.getDeliveryAddress() == undefined)) {
          //     Alert.alert(
          //       'Error',
          //       'Delivery address not selected.',
          //       [{ text: 'OK', onPress: () => { } }],
          //       { cancelable: false },
          //     );
          //   } else if (Cart.getOrderType() != 0) {
          //     var amount = (
          //       parseFloat(totalFloat) +
          //       parseFloat(taxFloat)
          //     ).toFixed(2)
          //     var amount = 5.00 //sandbox only allow maximum RM5
          //     // change to uuidv4 
          //     var orderId = User.getUserId() + ':T' + Date.now().toString()
          //     console.log("ORDER ID", orderId)
          //     //remember to change to this in production
          //     //molPayment(amount, 'orderId')
          //     molPayment(5, orderId)
          //     //placeOrder();
          //   }
          //   else {
          //     placeOrder();
          //   }
          // }

          placeUserOrder();
        }}>

        <View
          style={{
            backgroundColor: Colors.primaryColor,
            padding: 20,
            paddingVertical: 16,
            borderRadius: 10,
            alignItems: 'center',

            marginHorizontal: 48,
            // paddingTop: 32,
            // marginBottom: 24,
          }}>
          <Text style={{
            color: '#ffffff',
            fontSize: 21,
            fontFamily: 'NunitoSans-Regular',
          }}>PLACE ORDER</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
    padding: 16,
    paddingLeft: 0,
    paddingRight: 0,
    paddingTop: 0,
  },
  headerLogo: {
    width: 112,
    height: 25,
  },
  description: {
    paddingVertical: 5,
    fontSize: 13,
    color: Colors.descriptionColor,
    fontFamily: "NunitoSans-Regular",
    // fontWeight: '400',
    marginBottom: 2,
  },
  smallDescription: {
    marginTop: -5,
    paddingVertical: 5,
    fontSize: 10,
    color: "#9c9c9c",
    fontFamily: "NunitoSans-Regular", fontWeight: '400',
    width: '90%',
  },
  payment: {
    // color: Colors.descriptionColor,
    color: Colors.mainTxtColor,
    paddingVertical: 5,
    fontSize: 14,
    // marginTop: 20,
    fontFamily: 'NunitoSans-SemiBold',
    marginBottom: -5,
  },
  total: {
    // paddingVertical: 5,
    fontSize: 16,
    // fontWeight: "700",
    // marginTop: 5,
    fontFamily: "NunitoSans-Bold"
  },
  price: {
    paddingVertical: 5,
    fontSize: 13,
    //alignSelf: "flex-end",
    fontFamily: "NunitoSans-Regular",
    color: Colors.descriptionColor,
    // fontWeight: "400",
    marginBottom: 2,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginTop: 10,
    marginBottom: 10,
  },
  textInput1: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: '#E9CE8B',
    borderRadius: 4,
    marginTop: 20,
    justifyContent: "center",
  },
  totalPrice: {
    color: Colors.primaryColor,
    // paddingVertical: 5,
    fontSize: 16,
    // fontWeight: "700",
    fontFamily: 'NunitoSans-Bold',
    // marginTop: 5,
  },
  confirmBox: {
    width: "60%",
    height: "33%",
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  scheduleBox: {
    width: "80%",
    height: "65%",
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: "100%",
    width: "100%",
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
  },
  pic: {
    backgroundColor: Colors.secondaryColor,
    width: 90, // 90
    height: 90, // 90
    borderRadius: 10,
    alignSelf: 'center',
  },
  card: {
    flex: 1,
    minWidth: Dimensions.get('screen').width - 32,
    minHeight: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 12,
    paddingHorizontal: Platform.OS == 'ios' ? 0 : 20,
    paddingVertical: 15,
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: "center"
  },
  text: {
    fontSize: 15,
    fontFamily: "NunitoSans-Bold",
  },
  text1: {
    fontSize: 13,
    fontFamily: "NunitoSans-Bold",
    marginTop: '2%',
  },
  text2: {
    fontSize: 15,
    fontWeight: '700',
  },
  title: {
    color: Colors.blackColor,
    fontSize: 20,
    fontFamily: "NunitoSans-Bold"
  },
});
export default CartScreen;
