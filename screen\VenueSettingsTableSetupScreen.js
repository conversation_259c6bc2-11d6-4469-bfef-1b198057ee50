import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect } from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  TouchableWithoutFeedback,
  useWindowDimensions,
} from 'react-native';
import { ModalView } from 'react-native-multiple-modals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Switch from 'react-native-switch-pro';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
import {
  isTablet
} from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  RESERVATION_PRIORITY,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
} from '../constant/common';
import { useKeyboard } from '../hooks';
import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
} from '../util/common';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
const alphabet = '**********';
const nanoid = customAlphabet(alphabet, 12);
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import { ScrollView as ScrollViewGH, ScrollView, FlatList } from 'react-native-gesture-handler';
import DraggableFlatList from 'react-native-draggable-flatlist';
import APILocal from '../util/apiLocalReplacers';

const VenueSettingsTableSetupScreen = (props) => {
  const { navigation } = props;

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  // initial room data
  const initialRoomData = [...Array(3)].map((d, index) => {
    return {
      key: `room-${index}`,
      roomName: String(index) + '',
      online: true,
      tables: 10,
      capacity: 46,
      combinations: 2,
      isSelect: false,
      tableData: {
        key: index,
        fromTable: 'Indoor',
        tableName: 'A',
        online: true,
        min: 5,
        max: 6,
        turnTime: '1h 45m',
        priority: 'Medium',
        isTick: false,
        expand: true,
      },
    };
  });

  // initial two table data
  const initialAllTableListData = [];

  // initial table combination data
  const initialAllTableCombinationListData = [
    {
      tableName: 'Indoor',
      expand: true,
      tableData: [...Array(10)].map((d, index) => {
        return {
          key: index,
          fromTable: 'Indoor',
          tableName: String(index) + 'A',
          online: true,
          min: 5,
          max: 6,
          turnTime: '1h 45m',
          priority: 'Medium',
          isTick: false,
        };
      }),
    },
  ];

  // get the page from asyncStorage
  // const openPage = CommonStore.useState((s) => s.venueSettingPage);

  // room page useState
  // const [roomData, setRoomData] = useState(initialRoomData);
  // const [showAddArea, setShowAddArea] = useState(false);
  // const [addModalRoomNameTxt, setAddModalRoomNameTxt] = useState('');
  // const [roomNameFlatListTxt, setRoomNameFlatListTxt] = useState('');

  // table page useState
  const [allTableListData, setAllTableListData] = useState([]);

  // table combination page useState
  // const [allTableCombinationListData, setAllTableCombinationListData] =
  //   useState(initialAllTableCombinationListData);
  // const [showEditTableModal, setShowEditTableModal] = useState(false);
  // const [showAddTableCombinationModal, setShowAddTableCombinationModal] =
  //   useState(false);
  // const [switchOnline, setSwitchOnline] = useState(true);
  // const [comNameModal, setComNameModal] = useState('');
  // const [minModal, setMinModal] = useState('');
  // const [maxModal, setMaxModal] = useState('');
  // const [turnTimeModal, setTurnTimeModal] = useState('');
  // const [shortTimeModal, setShortTimeModal] = useState('');
  // const [bookingModal, setBookingModal] = useState('');
  // const [isNew, setIsNew] = useState(false);

  //reservation page useState
  // const [showAddAvModal, setShowAddAvModal] = useState(false);
  // const [isBasic, setIsBasic] = useState(false);
  // const [isTimeDays, setIsTimeDays] = useState(false);
  // const [isPacing, setIsPacing] = useState(false);
  // const [isRoom, setIsRoom] = useState(false);
  // const [isColor, setIsColor] = useState(false);
  // const [showPacingModal, setShowPacingModal] = useState(false);

  //modal basic tab
  // const [basicReserveName, setBasicReserveName] = useState('');
  // const [switchActive, setSwitchActive] = useState(true);
  // const [basicColor, setBasicColor] = useState('');

  const [roomAreaOption, setRoomAreaOption] = useState([
    // { label: 'area1', value: 'AREA1' },
    // { label: '2', value: '2' },
  ]);

  const [turnTimeOption, setTurnTimeOption] = useState([
    { label: '0h 15m', value: 15 },
    { label: '0h 30m', value: 30 },
    { label: '0h 45m', value: 45 },
    { label: '1h 00m', value: 60 },
    { label: '1h 15m', value: 75 },
    { label: '1h 30m', value: 90 },
    { label: '1h 45m', value: 105 },
    { label: '2h 00m', value: 120 },
    { label: '2h 15m', value: 135 },
    { label: '2h 30m', value: 150 },
    { label: '2h 45m', value: 165 },
    { label: '3h 00m', value: 180 },
    { label: '3h 15m', value: 195 },
    { label: '3h 30m', value: 210 },
    { label: '3h 45m', value: 225 },
    { label: '4h 00m', value: 240 },
    { label: '4h 15m', value: 255 },
    { label: '4h 30m', value: 270 },
    { label: '4h 45m', value: 285 },
    { label: '5h 00m', value: 300 },
  ]);

  const [priorityOption, setPriorityOption] = useState([
    { label: 'Low', value: 'LOW' },
    { label: 'Medium', value: 'MEDIUM' },
    { label: 'High', value: 'HIGH' },
  ]);

  const minMaxOptions = [];
  const [oneMonth, setOneMonth] = useState([]);
  for (let i = 1; i <= 10; i++) {
    minMaxOptions.push({
      label: `${i}`,
      // value: `${i}`,
      value: i,
    });
  }

  /////////////////////////////////////////////////////////////////////////

  // store states

  const outletSections = OutletStore.useState((s) => s.outletSections);
  const outletTables = OutletStore.useState((s) => s.outletTables);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  /////////////////////////////////////////////////////////////////////////

  //use effect start

  useEffect(() => {
    var allTableListDataTemp = [];
    var roomAreaOptionTemp = [];

    for (var i = 0; i < outletSections.length; i++) {
      var outletSection = outletSections[i];

      roomAreaOptionTemp.push({
        label: outletSection.sectionName,
        value: outletSection.uniqueId,
      });

      var rowData = {
        roomName: outletSection.sectionName,
        expand: true,
        tableData: [],

        outletSectionId: outletSection.uniqueId,
      };

      rowData.tableData = outletTables.filter(table => table.outletSectionId === outletSection.uniqueId).map((table, index) => ({
        key: index,
        fromTable: rowData.roomName,
        tableName: String(index) + table.code,
        isOnline: table.isOnline || true,
        paxMin: table.paxMin || 1,
        paxMax: table.paxMax || 6,
        capacity: table.capacity || 6,
        outletSectionId: table.outletSectionId,
        turnTime: table.turnTime || 60,
        priority: table.priority || RESERVATION_PRIORITY.MEDIUM,
        isTick: false,

        tableCapacity: table.capacity,
        tableCode: table.code,
        outletTableId: table.uniqueId,
      }));

      allTableListDataTemp.push(rowData);
    }

    setAllTableListData(allTableListDataTemp);
    setRoomAreaOption(roomAreaOptionTemp);
  }, [outletSections, outletTables]);

  useEffect(() => {
    let tempOneMonth = [];
    for (let i = 1; i <= 31; i++) {
      tempOneMonth.push({ label: i + '', value: false });
    }
    setOneMonth(tempOneMonth);

    // console.log('refreshed');
  }, []);

  // Render table draggable flatlist
  const renderTableItem = ({ item, drag, isActive, index }) => {
    return (
      <View
        style={{
          backgroundColor: 'white',
          margin: 3,
          paddingVertical: 10,
          borderRadius: 10,
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignContent: 'center',
          textAlignVertical: 'center',
          alignItems: 'center',

          borderWidth: 0.5,
          borderColor: 'grey',
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.2,
          shadowRadius: 1.41,
          elevation: 2,
          zIndex: 10001 - index,
        }}>
        <View style={{ flex: 0.5 }}>
          <TouchableOpacity
            style={{ paddingLeft: 10 }}
            onPressIn={drag}
            disabled={isActive}>
            <Plus name="menu" size={30} color={Colors.blackColor} style={{}} />
          </TouchableOpacity>
        </View>
        <View style={{ flex: 0.5 }}>
          <Switch
            value={item.isOnline}
            onSyncPress={(value) => {
              let tempData = [...allTableListData];
              // loop for each table
              tempData = tempData.map((table) => {
                if (item.fromTable == table.roomName)
                  // loop for each row
                  for (let i = 0; i < table.tableData.length; i++) {
                    if (item.key == table.tableData[i].key) {
                      let tempTable = table;
                      tempTable.tableData[i].isOnline = value;
                      return tempTable;
                    }
                  }
                return table;
              });
              setAllTableListData(tempData);
            }}
            circleColorActive={Colors.whiteColor}
            circleColorInactive={Colors.fieldtTxtColor}
            backgroundActive={Colors.primaryColor}
            style={{ height: 22 }}
          />
        </View>
        <View style={{ flex: 1.5 }}>
          <View
            style={{
              width: '90%',
              height: 30,
              backgroundColor: 'gainsboro',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 5,
            }}>
            <TextInput
              defaultValue={item.tableCode}
              onChangeText={(text) => {
                var tempData = [...allTableListData];
                for (let i = 0; i < tempData.length; i++) {
                  if (item.fromTable === tempData[i].roomName) {
                    for (let j = 0; j < tempData[i].tableData.length; j++) {
                      if (item.outletTableId === tempData[i].tableData[j].outletTableId) {
                        tempData[i].tableData[j].tableCode = text;
                      }
                      break;
                    }
                  }
                }
                setAllTableListData(tempData);
              }}
              // placeholderTextColor={Colors.descriptionColor}
              placeholder={item.roomName}
              style={{
                textAlignVertical: 'bottom',
                // textAlign: 'center',
                width: '95%',
                paddingTop: 0,
                top: 4,
              }}
            />
          </View>
        </View>

        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={minMaxOptions}
                value={item.paxMin}
                onValueChange={(value) => {
                  var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].paxMin = parseInt(parsedValue);
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={minMaxOptions}
                value={item.paxMin}
                onValueChange={(value) => {
                  var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].paxMin = parseInt(parsedValue);
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                }}
              />
            </View>
          )}
        </View>
        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={minMaxOptions}
                value={item.capacity}
                onValueChange={(value) => {
                  var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].capacity = parseInt(parsedValue);
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={minMaxOptions}
                value={item.capacity}
                onValueChange={(value) => {
                  var parsedValue = !isNaN(parseInt(value)) ? parseInt(value) : 1;

                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].capacity = parseInt(parsedValue);
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                }}
              />
            </View>
          )}
        </View>
        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={roomAreaOption}
                value={item.outletSectionId ? item.outletSectionId : ''}
                onValueChange={(value) => {
                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].outletSectionId = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                  // console.log(allTableListData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={roomAreaOption}
                value={item.outletSectionId ? item.outletSectionId : ''}
                onValueChange={(value) => {
                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].outletSectionId = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                  // console.log(allTableListData);
                }}
              />
            </View>
          )}
        </View>
        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={turnTimeOption}
                value={item.turnTime}
                onValueChange={(value) => {
                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].turnTime = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={turnTimeOption}
                value={item.turnTime}
                onValueChange={(value) => {
                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].turnTime = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                  // console.log(allTableListData);
                }}
              />
            </View>
          )}
        </View>
        <View style={styles.flexOne}>
          {switchMerchant ? (
            <View style={styles.phoneRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                useNativeAndroidPickerStyle={false}
                style={{
                  inputAndroid: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                  inputIOS: {
                    fontSize: 10,
                    paddingVertical: 5,
                    color: 'black',
                    textAlign: 'center',
                  },
                }}
                items={priorityOption}
                value={item.priority}
                onValueChange={(value) => {
                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].priority = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                }}
              />
            </View>
          ) : (
            <View style={styles.tabletRnPickerViewStyle}>
              <RNPickerSelect
                placeholder={{}}
                style={{
                  inputAndroidContainer: {
                    height: 35,
                    justifyContent: 'center',
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                  inputAndroid: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  inputIOS: {
                    //backgroundColor: '#fafafa',
                    color: 'black',
                    fontFamily: 'NunitoSans-Regular',
                    fontSize: 16,
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    borderRadius: 5,
                    width: '100%',
                    paddingHorizontal: 10,
                    height: 35,
                    paddingLeft: 12,
                    textAlign: 'center',
                  },
                  viewContainer: {
                    backgroundColor: '#fafafa',
                    borderRadius: 4,
                    height: 35,
                    width: '90%',
                    justifyContent: 'center',
                    fontSize: 16,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                  },
                }}
                items={priorityOption}
                value={item.priority}
                onValueChange={(value) => {
                  let tempData = [...allTableListData];
                  // loop for each table
                  tempData = tempData.map((table) => {
                    if (item.fromTable == table.roomName)
                      // loop for each row
                      for (let i = 0; i < table.tableData.length; i++) {
                        if (item.key == table.tableData[i].key) {
                          let tempTable = table;
                          tempTable.tableData[i].priority = value;
                          return tempTable;
                        }
                      }
                    return table;
                  });
                  setAllTableListData(tempData);
                  // console.log(allTableListData);
                }}
              />
            </View>
          )}
        </View>
        <View
          style={[
            {
              alignItems: 'flex-end',
              flex: 0.3,
            },
          ]}>
          <TouchableOpacity
            onPress={() => {
              let tempData = [...allTableListData];
              // loop for each table
              for (let i = 0; i < tempData.length; i++) {
                if (allTableListData[i].roomName === item.fromTable) {
                  for (let j = 0; j < tempData[i].tableData.length; j++) {
                    if (tempData[i].tableData[j].key === item.key) {
                      tempData[i].tableData[j].expandOption =
                        !item.expandOption;
                      break;
                    }
                  }
                }
              }
              setAllTableListData(tempData);
              // console.log(tempData);
              // // console.log(item);
            }}
            style={{}}>
            <Plus
              name="more-vertical"
              size={25}
              color={Colors.blackColor}
              style={{}}
            />
          </TouchableOpacity>

          {/* Popup more options */}
          <View>
            {item.expandOption ? (
              <View
                style={[
                  {
                    // position: 'absolute',
                    width: 200,
                    justifyContent: 'center',
                    alignItems: 'center',
                    // marginLeft: -110,
                    // marginTop: -110,
                    // zIndex: 1,
                    flexDirection: 'column',
                    backgroundColor: '#FFFFFF',
                    borderWidth: 1,
                    borderColor: Colors.highlightColor,
                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 2,
                  },
                  switchMerchant ? { height: 25 } : {},
                ]}>
                <View
                  style={{
                    flex: 1,
                    paddingHorizontal: 10,
                    paddingVertical: 8,
                    borderBottomWidth: 1,
                    width: '100%',
                  }}>
                  <TouchableOpacity
                    style={[
                      {
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                      },
                      switchMerchant ? {} : {},
                    ]}
                    onPress={() => {
                      Alert.alert(
                        'Info',
                        `Are you sure you want to remove table ${item.tableCode}?`,
                        [
                          {
                            text: 'NO',
                            onPress: () => {
                              // deleteOutletTable();
                            },
                          },
                          {
                            text: 'YES',
                            onPress: () => {
                              var body = {
                                outletId: currOutlet.uniqueId,
                                tableId: item.outletTableId,

                                // tableCode: item.code,
                              };

                              // ApiClient.POST(API.deleteOutletTable, body)
                              APILocal.deleteOutletTable({ body: body, uid: firebaseUid })
                                .then((result) => {
                                  if (result && result.status === 'success') {
                                    // setSeatingModal(false);

                                    Alert.alert('Info', 'Table has been removed');
                                  } else {
                                    Alert.alert('Failed to remove table');
                                  }
                                })
                                .catch((err) => {
                                  // console.log(err);
                                });
                            },
                          },
                        ],
                        { cancelable: false },
                      );
                    }}>
                    <View style={[{}, switchMerchant ? {} : {}]}>
                      <Text
                        style={[
                          { marginLeft: 5, justifyContent: 'center' },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        Delete table
                      </Text>
                    </View>
                    <View
                      style={[
                        {
                          paddingLeft: 12,
                          justifyContent: 'center',
                        },
                        switchMerchant ? {} : {},
                      ]}>
                      <Plus
                        name="trash-2"
                        size={13}
                        color={Colors.blackColor}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flex: 1,
                    paddingHorizontal: 10,
                    paddingVertical: 8,
                    // borderBottomWidth: 1,
                    width: '100%',
                  }}>
                  <TouchableOpacity
                    style={[
                      {
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                      },
                      switchMerchant ? {} : {},
                    ]}
                    onPress={() => {
                      var body = {
                        // tableId: selectedSlotId,
                        capacity: item.capacity,
                        code: `${table.roomName} ${table.tableData.length + 1}`,
                        outletSectionId: table.outletSectionId,
                        outletId: currOutlet.uniqueId,

                        isOnline: item.isOnline || true,
                        paxMin: item.paxMin || 1,
                        paxMax: item.paxMax || 6,
                        capacity: item.capacity || 6,
                        turnTime: item.turnTime || 60,
                        priority: item.priority || RESERVATION_PRIORITY.MEDIUM,
                      };

                      // ApiClient.POST(API.createOutletTable, body, false)
                      APILocal.createOutletTable({ body: body, uid: firebaseUid })
                        .then((result) => {
                          if (result && result.uniqueId) {
                            // setAddTableModal(false);

                            Alert.alert('Success', 'Table has been duplicated');
                          } else {
                            Alert.alert('Error', result.message);

                            // setAddTableModal(false);
                          }
                        })
                        .catch((err) => console.log(err));
                    }}>
                    <View style={[{}, switchMerchant ? {} : {}]}>
                      <Text
                        style={[
                          { marginLeft: 5, justifyContent: 'center' },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        Duplicate
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                {/* <View
                  style={{
                    flex: 1,
                    paddingHorizontal: 10,
                    paddingVertical: 8,
                    width: '100%',
                  }}>
                  <TouchableOpacity
                    style={[
                      {
                        flexDirection: 'row',
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                      },
                      switchMerchant ? {} : {},
                    ]}
                    onPress={() => {
                      Alert.alert('Create combination');
                    }}>
                    <View style={[{}, switchMerchant ? {} : {}]}>
                      <Text
                        style={[
                          {
                            marginLeft: 5,
                            justifyContent: 'center',
                            color: 'deepskyblue',
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}>
                        Create combination
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View> */}
              </View>
            ) : null}
          </View>
        </View>
      </View>
    );
  };

  // navigation
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Venue Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  return (
    <View
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: Colors.whiteColor,
        flexDirection: 'column',
      }}>

      {/* Top bar */}
      <View
        style={{
          flexDirection: 'row',
          backgroundColor: Colors.darkBgColor,
          justifyContent: 'space-between',
          alignItems: 'center',
          flex: 0.8,
        }}>
        <View style={{ flex: 0.25 }}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
            onPress={() => {
              props.navigation.navigate('NewSettingsScreen');
            }}>
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'row',
              }}>
              <Plus name="x" size={30} color={Colors.whiteColor} style={{}} />
            </View>
          </TouchableOpacity>
        </View>

        <View
          style={[
            styles.flexOne,
            {
              backgroundColor: Colors.modalBgColor,
            },
          ]}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              height: '100%',
            }}
            onPress={() => {
              //   CommonStore.update((s) => {
              //     s.venueSettingPage = 'table';
              //   });
            }}>
            <Text
              style={{
                textAlign: 'center',
                color: Colors.whiteColor,
                textDecorationLine: 'underline',
              }}>
              Table setup
            </Text>
          </TouchableOpacity>
        </View>
        <View
          style={[
            styles.flexOne,
            {
              backgroundColor: Colors.darkBgColor,
            },
          ]}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              height: '100%',
            }}
            onPress={() => {
              //   CommonStore.update((s) => {
              //     s.venueSettingPage = 'tableCombinations';
              //   });
              navigation.navigate('VenueSettingsCombinationScreen');
            }}>
            <Text
              style={{
                textAlign: 'center',
                color: Colors.whiteColor,
                textDecorationLine: 'none',
              }}>
              Table Combinations
            </Text>
          </TouchableOpacity>
        </View>
        <View
          style={[
            styles.flexOne,
            {
              backgroundColor: Colors.darkBgColor,
            },
          ]}>
          <TouchableOpacity
            style={{
              justifyContent: 'center',
              height: '100%',
            }}
            onPress={() => {
              //   CommonStore.update((s) => {
              //     s.venueSettingPage = 'reservation';
              //   });
              navigation.navigate('VenueSettingsReservationScreen');
            }}>
            <Text
              style={{
                textAlign: 'center',
                color: Colors.whiteColor,
                textDecorationLine: 'none',
              }}>
              Reservation Availability
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Page Content */}
      <View style={{ flex: 9 }}>
        <View
          style={{
            margin: 10,
          }}>
          <ScrollView>
            {/* Each table view */}
            <View
              style={{ flexDirection: 'column', justifyContent: 'flex-start' }}>
              {allTableListData.map((table) => {
                return (
                  <View style={{ flex: 2 }}>
                    {/* hide/unhide table button */}
                    <View
                      style={{
                        padding: 10,
                        justifyContent: 'flex-start',
                        flexDirection: 'row',
                        marginBottom: 10,

                        alignItems: 'center',
                      }}>
                      <View style={{ flex: 1 }}>
                        <TouchableOpacity
                          style={{
                            alignItems: 'center',
                            alignContent: 'center',
                            flexDirection: 'row',
                          }}
                          onPress={() => {
                            // let tempData = [...allTableListData];
                            // tempData = tempData.map((data) => {
                            //   if (table.tableName == data.tableName)
                            //     return {...table, expand: !table.expand};
                            //   return data;
                            // });
                            // setAllTableListData(tempData);
                            let tempData = [...allTableListData];
                            for (let i = 0; i < allTableListData.length; i++) {
                              if (
                                table.roomName === allTableListData[i].roomName
                              ) {
                                tempData[i].expand =
                                  !allTableListData[i].expand;
                                break;
                              }
                            }
                            setAllTableListData(tempData);
                            // console.log(table);
                            // console.log(allTableListData);
                            // console.log(tempData);
                          }}>
                          <Text style={{ paddingHorizontal: 5 }}>
                            {table.roomName}
                          </Text>
                          {table.expand ? (
                            <Plus
                              name="chevron-down"
                              size={20}
                              color={Colors.blackColor}
                              style={{ width: '75%' }}
                            />
                          ) : (
                            <Plus
                              name="chevron-up"
                              size={20}
                              color={Colors.blackColor}
                              style={{ width: '75%' }}
                            />
                          )}
                        </TouchableOpacity>
                      </View>

                      <View style={{ flex: 8 }} />

                      <View style={{ flex: 1 }}>
                        <TouchableOpacity
                          style={{
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#0F1A3C',
                            borderRadius: 5,
                            marginLeft: 10,
                            paddingHorizontal: 10,
                            height: 40,
                            alignItems: 'center',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          onPress={() => {
                            // save all tables for current section

                            var tableList = [];

                            if (allTableListData.find(row => row.outletSectionId === table.outletSectionId)) {
                              tableList = allTableListData.find(row => row.outletSectionId === table.outletSectionId).tableData;
                            }

                            var body = {
                              // tableId: selectedSlotId,
                              // tableList: table.tableData,
                              tableList: tableList,
                            };

                            // console.log('body', body)

                            // ApiClient.POST(API.updateOutletTableMultiple, body, false)
                            APILocal.updateOutletTableMultiple({ body: body, uid: firebaseUid })
                              .then((result) => {
                                if (result && result.status === 'success') {
                                  // setAddTableModal(false);

                                  Alert.alert('Success', 'Table has been saved');
                                } else {
                                  Alert.alert('Error', result.message);

                                  // setAddTableModal(false);
                                }
                              })
                              .catch((err) => console.log(err));
                          }}>
                          <Text
                            style={[
                              {
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: 16,
                                fontFamily: 'NunitoSans-Bold',
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}>
                            SAVE
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>

                    {table.expand ? (
                      <View>
                        {/* Table title */}
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'flex-start',
                            height: 30,
                            alignContent: 'center',
                            textAlignVertical: 'center',
                            alignItems: 'center',
                            // borderWidth: 1,
                          }}>
                          <View style={{ flex: 0.5 }} />
                          <Text style={{ flex: 0.5 }}>Online</Text>
                          <Text style={{ flex: 1.5 }}>Table name</Text>
                          <Text style={styles.flexOne}>Min</Text>
                          <Text style={styles.flexOne}>Max</Text>
                          <Text style={styles.flexOne}>Room area</Text>
                          <Text style={styles.flexOne}>Turn time</Text>
                          <Text style={styles.flexOne}>Booking priority</Text>
                          <View style={{ flex: 0.3 }} />
                        </View>

                        {/* Table content */}
                        <View
                          style={{
                            flexDirection: 'column',
                          }}>
                          {/* Draggable Flatlist */}
                          <DraggableFlatList
                            data={table.tableData}
                            style={{
                              maxHeight: windowHeight * 0.5,
                            }}
                            onDragEnd={({ data }) => {
                              let tempData = [...allTableListData];
                              tempData = tempData.map((table) => {
                                if (data[0].fromTable == table.tableName)
                                  return { ...table, tableData: data };
                                return table;
                              });
                              setAllTableListData(tempData);
                            }}
                            keyExtractor={(item, index) => index.toString()}
                            renderItem={renderTableItem}
                            contentContainerStyle={{}}
                            // scrollEnabled={false}
                            renderPlaceholder={() => (
                              <View
                                style={{
                                  flex: 1,
                                  backgroundColor: 'darkgrey',
                                  borderRadius: 8,
                                }}
                              />
                            )}
                          />

                          {/* Add table button */}
                          <View
                            style={{
                              flex: 1,
                              marginTop: 10,
                              marginHorizontal: 5,
                              marginBottom: 20,
                              padding: 10,
                              flexDirection: 'row',
                            }}>
                            <View style={{ flex: 1 }}>
                              <TouchableOpacity
                                onPress={() => {
                                  // let tempData = [];
                                  // let tempAllData = [...allTableListData];
                                  // for (
                                  //   let i = 0;
                                  //   i < allTableListData.length;
                                  //   i++
                                  // ) {
                                  //   if (
                                  //     allTableListData[i].roomName ===
                                  //     table.roomName
                                  //   ) {
                                  //     tempData = [
                                  //       ...allTableListData[i].tableData,
                                  //     ];
                                  //     tempData.push({
                                  //       key:
                                  //         table.tableData[
                                  //           table.tableData.length - 1
                                  //         ].key + 1,
                                  //       fromTable:
                                  //         table.tableData[
                                  //           table.tableData.length - 1
                                  //         ].fromTable,
                                  //       tableName: 'table',
                                  //       online: true,
                                  //       min: 5,
                                  //       max: 6,
                                  //       roomArea: '',
                                  //       turnTime: '1h 45m',
                                  //       priority: 'Medium',
                                  //       expandOption: false,
                                  //     });
                                  //     tempAllData[i].tableData = tempData;
                                  //     break;
                                  //   }
                                  // }
                                  // setAllTableListData(tempAllData);
                                  // // console.log(tempData);

                                  var body = {
                                    // tableId: selectedSlotId,
                                    capacity: 4,
                                    code: `${table.roomName} ${table.tableData.length + 1}`,
                                    outletSectionId: table.outletSectionId,
                                    outletId: currOutlet.uniqueId,

                                    isOnline: true,
                                    paxMin: 1,
                                    paxMax: 6,
                                    capacity: 6,
                                    turnTime: 60,
                                    priority: RESERVATION_PRIORITY.MEDIUM,
                                  };

                                  // ApiClient.POST(API.postTable, body, false).then((result) => {
                                  //     if (result.success) {
                                  //         getTableBySection(currentSectionArea)
                                  //         setState({ addTableModal: false })
                                  //         // console.log("OOOO", result)
                                  //         // console.log("CODE", tableCode)
                                  //     }
                                  //     else if (result.findDuplicate)
                                  //         Alert.alert(
                                  //             'Error',
                                  //             result.findDuplicate
                                  //         )
                                  // }).catch(err => console.log(err));

                                  // ApiClient.POST(API.createOutletTable, body, false)
                                  APILocal.createOutletTable({ body: body, uid: firebaseUid })
                                    .then((result) => {
                                      if (result && result.uniqueId) {
                                        // setAddTableModal(false);

                                        Alert.alert('Success', 'Table has been added');
                                      } else {
                                        Alert.alert('Error', result.message);

                                        // setAddTableModal(false);
                                      }
                                    })
                                    .catch((err) => console.log(err));
                                }}
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'flex-start',
                                  alignContent: 'flex-start',
                                  textAlignVertical: 'center',
                                  alignItems: 'center',
                                }}>
                                <Plus
                                  name="plus"
                                  size={25}
                                  color="green"
                                  style={{
                                    alignContent: 'center',
                                    alignItems: 'center',
                                    padding: 7,
                                    borderWidth: 1,
                                    width: 40,
                                    height: 40,
                                    borderRadius: 5,
                                  }}
                                />
                                <Text
                                  style={{
                                    marginHorizontal: 4,
                                    paddingHorizontal: 4,
                                    textAlignVertical: 'center',
                                  }}>
                                  Add table
                                </Text>
                              </TouchableOpacity>
                            </View>
                            <View style={{ flex: 9 }} />
                          </View>
                        </View>
                      </View>
                    ) : (
                      <View></View>
                    )}
                  </View>
                );
              })}
            </View>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },

  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },

  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.5,
    height: Dimensions.get('window').height * 0.2,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.4,
    minHeight: Dimensions.get('window').height * 0.1,
    borderRadius: 12,
    backgroundColor: 'rgb(209, 212, 212)',
  },
  flexOne: {
    flex: 1,
    // borderWidth: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    // height: windowHeight * 0.5,
    // width: windowWidth * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.02,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.01,
    top: Dimensions.get('window').height * 0.02,
    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    // flex: 1,
    alignItems: 'center',
    // borderWidth: 1,
  },
  modalBody: {
    flex: 8,
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexDirection: 'column',
  },
  modalBodyRow: {
    flex: 1,
    justifyContent: 'space-around',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    textAlignVertical: 'center',
    fontSize: 28,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: '20%',
  },
  addModalBodySize: {
    width: Dimensions.get('window').width * 0.3,
  },
  addModalBodyContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: Dimensions.get('window').width * 0.01,
    paddingVertical: Dimensions.get('window').height * 0.05,
  },
  addModalButton: {
    backgroundColor: Colors.primaryColor,
    paddingVertical: Dimensions.get('window').height * 0.01,
    paddingHorizontal: Dimensions.get('window').width * 0.02,
    borderRadius: 5,
  },
  // tabletRnPickerStyle: {
  //   inputAndroidContainer: {
  //     height: 35,
  //     justifyContent: 'center',
  //     backgroundColor: '#fafafa',
  //     borderRadius: 4,
  //     shadowColor: '#000',
  //     shadowOffset: {
  //       width: 0,
  //       height: 2,
  //     },
  //     shadowOpacity: 0.22,
  //     shadowRadius: 3.22,
  //     elevation: 1,
  //   },
  //   inputAndroid: {
  //     //backgroundColor: '#fafafa',
  //     color: 'black',
  //     fontFamily: 'NunitoSans-Regular',
  //     fontSize: 16,
  //     borderWidth: 1,
  //     borderColor: Colors.primaryColor,
  //     borderRadius: 5,
  //     width: 145,
  //     paddingHorizontal: 10,
  //     height: 35,
  //     paddingLeft: 12,
  //     textAlign: 'center',
  //   },
  //   inputIOS: {
  //     //backgroundColor: '#fafafa',
  //     color: 'black',
  //     fontFamily: 'NunitoSans-Regular',
  //     fontSize: 16,
  //     borderWidth: 1,
  //     borderColor: Colors.primaryColor,
  //     borderRadius: 5,
  //     width: 145,
  //     paddingHorizontal: 10,
  //     height: 35,
  //     paddingLeft: 12,
  //     textAlign: 'center',
  //   },
  //   viewContainer: {
  //     backgroundColor: '#fafafa',
  //     borderRadius: 4,
  //     height: 35,
  //     width: 145,
  //     justifyContent: 'center',
  //     fontSize: 16,
  //     shadowColor: '#000',
  //     shadowOffset: {
  //       width: 0,
  //       height: 2,
  //     },
  //     shadowOpacity: 0.22,
  //     shadowRadius: 3.22,
  //     elevation: 1,
  //   },
  // },
  tabletRnPickerViewStyle: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
    alignItems: 'flex-start',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  // phoneRnPickerStyle: {
  //   inputAndroid: {
  //     fontSize: 10,
  //     paddingVertical: 5,
  //     color: 'black',
  //     textAlign: 'center',
  //   },
  //   inputIOS: {
  //     fontSize: 10,
  //     paddingVertical: 5,
  //     color: 'black',
  //     textAlign: 'center',
  //   },
  // },
  phoneRnPickerViewStyle: {
    backgroundColor: '#fafafa',
    // backgroundColor: 'green',
    borderRadius: 4,
    height: Dimensions.get('window').height * 0.08,
    width: Dimensions.get('window').width * 0.13,
    justifyContent: 'center',
    fontSize: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    // left: windowWidth * -0.002,
  },
  spaceBetweenFullWidth: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'center',
    marginVertical: Dimensions.get('window').height * 0.01,
    height: isTablet() ? 40 : 35,
  },
});

export default VenueSettingsTableSetupScreen;
