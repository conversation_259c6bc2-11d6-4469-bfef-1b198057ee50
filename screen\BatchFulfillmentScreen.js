import { Text } from "react-native-fast-text";
import React, { useState, useEffect, useCallback } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Alert,
    TouchableOpacity,
    Dimensions,
    useWindowDimensions,
    ActivityIndicator,
    Linking,
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import { TextInput, FlatList } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles';
import Switch from 'react-native-switch-pro';
import GCalendar from '../assets/svg/GCalendar';
import {
    isTablet, listenToCurrOutletIdReservationChanges, updateEPDetailsToUserOrders
} from '../util/common';
import { UserStore } from '../store/userStore';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import { parseValidPriceText } from '../util/common';
import CheckBox from 'react-native-check-box';
import { color } from 'react-native-reanimated';
import Close from 'react-native-vector-icons/AntDesign';
// import NumericInput from 'react-native-numeric-input';
import moment, { isDate } from 'moment';
import Barcode from 'react-native-barcode-builder';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RNPickerSelect from 'react-native-picker-select';
import { EXPAND_TAB_TYPE, ORDER_TYPE, } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import APILocal from '../util/apiLocalReplacers';
import { qrUrl } from '../constant/env';
import Entypo from 'react-native-vector-icons/Entypo';
import firestore from '@react-native-firebase/firestore';
import { Collections } from "../constant/firebase";
import { RadioButton } from "react-native-paper";
import AsyncImage from "../components/asyncImage";

const BatchFulfillmentScreen = (props) => {
    const { navigation } = props;

    const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
    // const [merchantId, setMerchantId] = useState([]);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const epOrderList = CommonStore.useState((s) => s.epOrderList);

    const allOutlets = MerchantStore.useState((s) => s.allOutlets);
    const merchantId = UserStore.useState((s) => s.merchantId);

    const userName = UserStore.useState((s) => s.name);
    const merchantName = MerchantStore.useState((s) => s.name);
    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );

    const currOutlet = MerchantStore.useState((s) => s.currOutlet);
    const [isMounted, setIsMounted] = useState(true);

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const deliveryCreditBalance = OutletStore.useState((s) => s.deliveryCreditBalance);
    const deliveryCreditWalletId = OutletStore.useState((s) => s.deliveryCreditWalletId);

    //////////
    //Added 2024-04-17
    const [integrationId, setIntegrationId] = useState("");
    const [selectedProvince, setSelectedProvince] = useState("");

    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
    const [pickerMode, setPickerMode] = useState('datetime');

    ///////////////////////////////////////////////////////////////////

    // 2024-04-17 - easyparcel details

    const [epStateFrom, setEpStateFrom] = useState('sgr');
    const [epNameFrom, setEpNameFrom] = useState('');
    const [epAddr1From, setEpAddr1From] = useState('');
    const [epCityFrom, setEpCityFrom] = useState('');
    const [epCodeFrom, setEpCodeFrom] = useState('');
    const [epUserCourierName, setEpUserCourierName] = useState('');
    const [epUserPrice, setEpUserPrice] = useState('');
    const [epPickupDate, setEpPickupDate] = useState('');

    const [epStateToTemp, setEpStateToTemp] = useState('sgr');
    const [epNameToTemp, setEpNameToTemp] = useState('');
    const [epPhoneToTemp, setEpPhoneToTemp] = useState('');
    const [epAddr1ToTemp, setEpAddr1ToTemp] = useState('');
    const [epCityToTemp, setEpCityToTemp] = useState('');
    const [epCodeToTemp, setEpCodeToTemp] = useState(''); // postcode

    const [epScheduleDt, setEpScheduleDt] = useState(Date.now());

    const [epRates, setEpRates] = useState([]);

    const [epGetRatesByOrderIdDict, setEpGetRatesByOrderIdDict] = useState({});

    const [shipLoading, setShipLoading] = useState(false);

    const outletItems = OutletStore.useState((s) => s.outletItems);

    const [smsNotification, setSMSNotification] = useState(false);

    const dummylist = [
        {
            orderId: 101,
            fromToWhere: 'Malaysia, Johor > Malaysia Selangor',
            deliveryMethod: 'pickup',
            Courier: 'sample1',
            price: 5
        },
        {
            orderId: 102,
            fromToWhere: 'Malaysia, Johor > Malaysia Selangor',
            deliveryMethod: 'pickup',
            Courier: 'sample2',
            price: 5
        },
        {
            orderId: 103,
            fromToWhere: 'Malaysia, Johor > Malaysia Selangor',
            deliveryMethod: 'pickup',
            Courier: 'sample3',
            price: 5
        },
    ]

    ///////////////////////////////////////////////////////////////////

    ////////////////////
    ///////////////////
    ///////////////////

    navigation.setOptions({
        headerLeft: () => (
            <TouchableOpacity
                onPress={() => {
                    if (isAlphaUser || true) {
                        navigation.navigate('FulfillmentScreen');

                        CommonStore.update((s) => {
                            s.currPage = 'FulfillmentScreen';
                            s.currPageStack = [...currPageStack, 'FulfillmentScreen'];
                        });
                    }
                    else {
                        navigation.navigate('Table');

                        CommonStore.update((s) => {
                            s.currPage = 'Table';
                            s.currPageStack = [...currPageStack, 'Table'];
                        });
                    }
                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                        CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                        });
                    }
                }}
                style={{
                    width: windowWidth * 0.17,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                <Image
                    style={{
                        width: 124,
                        height: 26,
                    }}
                    resizeMode="contain"
                    source={require('../assets/image/logo.png')}
                />
            </TouchableOpacity>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
                        alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
                        marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        bottom: switchMerchant ? '2%' : 0,
                        width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "96%" : "55%",
                    },
                    windowWidth >= 768 && switchMerchant
                        ? { right: windowWidth * 0.1 }
                        : {},
                    windowWidth <= 768
                        ? { right: 20 }
                        : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: 'left',
                        alignItems: 'flex-start',
                        justifyContent: 'flex-start',
                        fontFamily: 'NunitoSans-Bold',
                        color: Colors.whiteColor,
                        opacity: 1,
                        paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
                    }}>
                    Batch Fulfillment
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                }}>
                {outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('Setting');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                    >

                        {userName}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                        }}>
                        <Image
                            style={{
                                width: windowHeight * 0.035,
                                height: windowHeight * 0.035,
                                alignSelf: 'center',
                            }}
                            source={require('../assets/image/profile-pic.jpg')}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////

    // 2024-04-18 - easyparcel changes

    useEffect(() => {
        if (currOutlet && currOutlet.uniqueId) {
            setEpStateFrom(currOutlet.epStateFrom ? currOutlet.epStateFrom : 'sgr');
            setEpNameFrom(currOutlet.epNameFrom ? currOutlet.epNameFrom : '');
            setEpAddr1From(currOutlet.epAddr1From ? currOutlet.epAddr1From : '');
            setEpCityFrom(currOutlet.epCityFrom ? currOutlet.epCityFrom : '');
            setEpCodeFrom(currOutlet.epCodeFrom ? currOutlet.epCodeFrom : '');
        }
    }, [currOutlet]);

    useEffect(() => {
        if (epOrderList && epOrderList.length > 0 &&
            epOrderList[0] && epOrderList[0].uniqueId) {
            if (epOrderList[0].epStateTo) {
                setEpStateToTemp(epOrderList[0].epStateTo);
            }

            if (epOrderList[0].epNameTo) {
                setEpNameToTemp(epOrderList[0].epNameTo);
            }

            if (epOrderList[0].epPhoneTo) {
                setEpPhoneToTemp(epOrderList[0].epPhoneTo);
            }

            if (epOrderList[0].epAddr1To) {
                setEpAddr1ToTemp(epOrderList[0].epAddr1To);
            }

            if (epOrderList[0].epCityTo) {
                setEpCityToTemp(epOrderList[0].epCityTo);
            }

            if (epOrderList[0].epCodeTo) {
                setEpCodeToTemp(epOrderList[0].epCodeTo);
            }

            if (epOrderList[0].epScheduleDt) {
                setEpScheduleDt(epOrderList[0].epScheduleDt);
            }
            if (epOrderList[0].epUserCourierName) {
                setEpUserCourierName(epOrderList[0].epUserCourierName);
            }
            if (epOrderList[0].epUserPrice) {
                setEpUserPrice(epOrderList[0].epUserPrice);
            }
            if (epOrderList[0].epPickupDate) {
                setEpPickupDate(epOrderList[0].epPickupDate);
            }
        }
    }, [
        epOrderList,
    ]);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////

    // 2024-04-18 - easyparcel changes

    const renderOrderItems = ({ item, index }) => {
        return (
            <>
                <View style={{ flexDirection: "row", alignItems: 'center', marginTop: 15 }}>
                    <Text style={{ fontSize: 16, marginLeft: 5, width: '10%' }}>
                        #{item.orderId}
                    </Text>
                    <Text style={{ fontSize: 16, width: '33%' }}>
                        {item.fromToWhere}
                    </Text>

                    <Text style={{ fontSize: 16, width: '10%' }}>
                        {item.deliveryMethod}
                    </Text>

                    <Text style={{ fontSize: 16, width: '35%' }}>
                        {item.Courier}
                    </Text>
                    <Text style={{ fontSize: 16, width: '10%' }}>
                        RM {(item.price).toFixed(2)}
                    </Text>
                </View>
                {/* <View style={{ width: '100%', height: 1, backgroundColor: Colors.descriptionColor, marginTop: 10, }} /> */}
            </>
        );

    };

    // const foundItem = outletItems.find(findItem => findItem.uniqueId === cartItem.itemId); foundItem.skuMerchant ? foundItem.skuMerchant : "N/A"

    const renderRates = ({ item, index }) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    setEpGetRatesByOrderIdDict({
                        [epOrderList[0].uniqueId]: item,
                    });
                }}
                style={{ flexDirection: "row", alignItems: 'center', marginTop: 10, }}>
                <RadioButton
                    status={
                        (epGetRatesByOrderIdDict[epOrderList[0].uniqueId]
                            &&
                            epGetRatesByOrderIdDict[epOrderList[0].uniqueId].service_id === item.service_id)
                            ?
                            'checked'
                            :
                            'unchecked'
                    }
                />
                <View style={{ width: '10%', marginLeft: 10, }}>
                    {item.courier_logo ?
                        <AsyncImage style={[
                            {
                                width: 80,
                                height: 70,
                                top: 3,
                            },
                            switchMerchant
                                ? {
                                    width:
                                        windowWidth * 0.05,
                                    height:
                                        windowHeight * 0.05,
                                    alignSelf: 'center',
                                }
                                : {},
                        ]}
                            resizeMode="contain"
                            // source={require('../assets/image/pgeon_logo.png')}
                            source={{
                                uri: item.courier_logo,
                            }}
                        />
                        :
                        <View style={[
                            {
                                width: 80,
                                height: 70,
                                top: 3,
                                backgroundColor: Colors.secondaryColor,
                            },
                        ]} />
                    }
                </View>

                <View style={{ width: '50%', }}>
                    <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Regular', width: '95%', }} numberOfLines={1}>
                        {item.service_name ? item.service_name : 'N/A'}
                    </Text>
                    <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Bold' }}>
                        {`RM ${item.price ? item.price : 'N/A'}`}
                    </Text>

                </View>
                <View style={{ width: '25%', }}>
                    <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Regular' }}>
                        Scheduled pickup for parcel
                    </Text>
                    <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Bold' }}>
                        {item.scheduled_start_date ? item.scheduled_start_date : moment().format('YYYY MMM DD')}
                    </Text>
                </View>
                <View style={{ width: '15%', }}>
                    <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Regular' }}>
                        Pickup Date
                    </Text>
                    <Text style={{ fontSize: 16, marginLeft: 10, fontFamily: 'NunitoSans-Bold' }}>
                        {item.pickup_date ? item.pickup_date : moment().format('YYYY MMM DD')}
                    </Text>
                </View>
            </TouchableOpacity>
        );
    };

    const epRateChecking = (orderList, outlet, cb) => {
        let body = {
            objList: orderList.map(order => {
                return {
                    epCodeFrom,
                    epStateFrom,
                    epCountryFrom: 'MY',
                    epCodeTo: epCodeToTemp,
                    epStateTo: epStateToTemp,
                    epCountryTo: 'MY',

                    weight: Math.ceil(order.cartItems.reduce((accum, item) => {
                        return accum + (item.weight ? item.weight : 0.1);
                    }, 0)),

                    epScheduleDt: epScheduleDt ? moment(epScheduleDt).format('YYYY-MM-DD') : moment().add(1, 'day').format('YYYY-MM-DD'),
                };
            }),
        };


        ApiClient.POST(API.epRateChecking, body)
            .then((result) => {
                console.log('result');
                console.log(result);

                typeof cb === 'function' && cb(result);

                // if (result && result.status === 'success') {
                // } else {
                //   Alert.alert(
                //     'Error',
                //     'Please try again later.',
                //   );
                // }
            })
            .catch((err) => {
                console.log(err);

                // Alert.alert(
                //   'Error',
                //   'Please try again later.',
                // );
            });
    };

    const epMakingOrder = (orderList, outlet, rates, cb) => {
        let body = {
            objList: orderList.map((order, index) => {
                let serviceId = '';
                if (epGetRatesByOrderIdDict[order.uniqueId] && epGetRatesByOrderIdDict[order.uniqueId].service_id) {
                    serviceId = epGetRatesByOrderIdDict[order.uniqueId].service_id;
                }

                if (serviceId === '' && rates[index]) {
                    serviceId = rates[index].service_id;
                }

                let sendEmail = outlet.email;

                if (order.userEmail && !order.userEmail.startsWith('user-') &&
                    order.userEmail.length < 28) {
                    // valid email

                    sendEmail = order.userEmail;
                }

                return {
                    epCodeFrom,
                    epStateFrom,
                    epCountryFrom: 'MY',
                    epCodeTo: epCodeToTemp,
                    epStateTo: epStateToTemp,
                    epCountryTo: 'MY',

                    weight: Math.ceil(order.cartItems.reduce((accum, item) => {
                        return accum + (item.weight ? item.weight : 0.1);
                    }, 0)),

                    epContent: order.cartItems.map(item => item.itemName).join(', '),
                    epQuantity: order.cartItems.reduce((accum, item) => accum + item.quantity, 0),

                    epServiceId: serviceId,

                    epNameFrom,
                    epPhoneFrom: outlet.phone ? outlet.phone : '',
                    epAddr1From,
                    epCityFrom,

                    epNameTo: epNameToTemp,
                    epPhoneTo: epPhoneToTemp,
                    epAddr1To: epAddr1ToTemp,
                    epCityTo: epCityToTemp,

                    epSms: true,
                    epEmail: sendEmail,

                    epScheduleDt: epScheduleDt ? moment(epScheduleDt).format('YYYY-MM-DD') : moment().add(1, 'day').format('YYYY-MM-DD'),
                };
            }),
        };

        ApiClient.POST(API.epMakingOrder, body)
            .then((result) => {
                console.log('result');
                console.log(result);

                typeof cb === 'function' && cb(result);

                // if (result && result.status === 'success') {
                // } else {
                //   Alert.alert(
                //     'Error',
                //     'Please try again later.',
                //   );
                // }
            })
            .catch((err) => {
                console.log(err);

                // Alert.alert(
                //   'Error',
                //   'Please try again later.',
                // );
            });
    };

    const epMakingOrderPayment = (orderList, outlet, epMakingOrderResultList, totalDeliveryCharges, cb) => {
        let body = {
            objList: orderList.map((order, index) => {
                return {
                    epOrderNo: epMakingOrderResultList[index].order_number,
                };
            }),

            totalDeliveryCharges,
            deliveryCreditWalletId,
            merchantId,
        };

        ApiClient.POST(API.epMakingOrderPayment, body)
            .then((result) => {
                console.log('result');
                console.log(result);

                typeof cb === 'function' && cb(result);

                // if (result && result.status === 'success') {
                // } else {
                //   Alert.alert(
                //     'Error',
                //     'Please try again later.',
                //   );
                // }
            })
            .catch((err) => {
                console.log(err);

                // Alert.alert(
                //   'Error',
                //   'Please try again later.',
                // );
            });
    };

    ///////////////////////////////////////////////////////////

    // function end

    return (

        <UserIdleWrapper disabled={!isMounted}>
            <View
                style={[
                    styles.container,
                    !isTablet()
                        ? {
                            transform: [{ scaleX: 1 }, { scaleY: 1 }],
                        }
                        : {},
                ]}>
                <View
                    style={[
                        styles.sidebar,
                        !isTablet()
                            ? {
                                width: windowWidth * 0.08,
                            }
                            : {},
                        switchMerchant
                            ? {
                                // width: '10%'
                            }
                            : {},
                    ]}>
                    <SideBar
                        navigation={props.navigation}
                        selectedTab={10}
                        expandSettings
                    />
                </View>

                <ScrollView
                    showsVerticalScrollIndicator={false}
                    // scrollEnabled={switchMerchant}
                    style={{ backgroundColor: Colors.highlightColor, }}
                    contentContainerStyle={{
                        paddingBottom: windowHeight * 0.05,
                        backgroundColor: Colors.highlightColor,
                    }}>
                    <View style={{
                        width: windowWidth * 0.865,
                        alignSelf: 'center',
                    }}>
                        <TouchableOpacity
                            style={{
                                height: windowHeight * 0.055,
                                flexDirection: 'row',
                                alignContent: 'center',
                                alignItems: 'center',
                                marginTop: 10,
                            }}
                            onPress={() => {
                                props.navigation.navigate('History');
                                CommonStore.update((s) => {
                                    s.currPage = 'History';
                                    s.currPageStack = [...currPageStack, 'History'];
                                });

                                if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                                    CommonStore.update((s) => {
                                        s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                                    });
                                }
                            }}>
                            <Icon
                                name="chevron-left"
                                size={switchMerchant ? 20 : 30}
                                color={Colors.primaryColor}
                            />
                            <Text
                                style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 14 : 17,
                                    color: Colors.primaryColor,
                                    marginBottom: Platform.OS === 'ios' ? 0 : 1,
                                }}>
                                Back
                            </Text>
                        </TouchableOpacity>
                    </View>
                    <View style={{
                        padding: 20,
                        backgroundColor: 'white',
                        width: windowWidth * 0.85,
                        borderRadius: 5,
                        alignSelf: 'center',
                        marginTop: 20,
                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: 5,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        // elevation: 1,
                        elevation: 3,
                    }}>
                        <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 10, borderRadius: 10, paddingBottom: 20, }}>
                            <Text style={{ fontSize: 18, fontFamily: 'NunitoSans-SemiBold' }}>
                                Only order(s) fulfill requirements below allow to use bulk fulfillment
                            </Text>
                            <Text style={{ fontSize: 18, fontFamily: 'NunitoSans-Bold', marginTop: 10, }}>
                                {'\u2022'} Paid order, Order with customer, Not fulfilled order, Not cancelled order and Not archived order
                            </Text>
                        </View>
                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                alignItems: 'center',
                                paddingHorizontal: 5,
                                marginTop: 15,
                                flex: 1
                            }}>
                            <View style={{ marginRight: 25, width: '20%', height: 80, }}>
                                <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                                    {'N/A'}
                                </Text>
                                <Text style={{ fontSize: 16, marginTop: 5 }}>
                                    Number of orders
                                </Text>
                            </View>

                            <View style={{ marginRight: 25, width: '20%', height: 80, }}>
                                <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                                    {'N/A'}
                                </Text>
                                <Text style={{ fontSize: 16, marginTop: 5 }}>
                                    Pickup parcel
                                </Text>
                            </View>

                            <View style={{ marginRight: 25, width: '20%', height: 80, }}>
                                <Text style={{ fontWeight: 'bold', fontSize: 18 }}>
                                    {'N/A'}
                                </Text>
                                <Text style={{ fontSize: 16, marginTop: 5 }}>
                                    Dropoff parcel
                                </Text>
                            </View>

                        </View>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <CheckBox
                                style={{
                                    paddingVertical: 10,
                                    marginRight: 10,
                                    zIndex: 1,
                                }}
                                onClick={() => {
                                    setSMSNotification(!smsNotification);
                                }}
                                checkBoxColor={Colors.fieldtBgColor}
                                uncheckedCheckBoxColor={Colors.tabGrey}
                                checkedCheckBoxColor={Colors.primaryColor}
                                isChecked={smsNotification}
                            />
                            <Text style={{ fontSize: 16, }}>
                                SMS notification for all parcel(RM0.20/SMS)
                            </Text>
                        </View>
                        <View
                            style={{
                                borderBottomWidth: 0.7,
                                borderColor: 'grey', // Change the color as needed
                                marginVertical: 17, // Adjust margin as needed
                            }} />

                        {
                            dummylist
                                ?
                                <FlatList
                                    showsVerticalScrollIndicator={false}
                                    // data={historyOrders.slice(0).sort((a, b) => {
                                    //   return b.orderDate - a.orderDate;
                                    // })}
                                    data={dummylist}
                                    renderItem={renderOrderItems}
                                    keyExtractor={(item, index) => String(index)}
                                    contentContainerStyle={{
                                        paddingBottom: 40,
                                    }}
                                />
                                :
                                <></>
                        }
                    </View>
                </ScrollView>
            </View >

        </UserIdleWrapper >

    );

};


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        fontFamily: 'NunitoSans-Regular',
    },
    container1: {
        flex: 1,
        backgroundColor: '#ffffff',
        flexDirection: 'row',
        fontFamily: 'NunitoSans-Regular',
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },
    listItem: {
        fontFamily: 'NunitoSans-Regular',
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
    },
    content: {
        padding: 20,
        width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
        // backgroundColor: 'lightgrey',
        backgroundColor: Colors.highlightColor,
    },
    textInput: {
        //fontFamily: 'NunitoSans-Regular',
        //width: 300,
        marginTop: 10,
        color: 'black',
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 0,
        flex: 1,
        flexDirection: 'row',
        paddingLeft: 10,
    },
});
export default BatchFulfillmentScreen;
