import React, { Component, useState, useEffect } from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  Modal,
  Dimensions
} from 'react-native';
import Colors from '../constant/Colors';
import Styles from '../constant/Styles';
import auth from '@react-native-firebase/auth';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as Token from '../util/Token';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FlatList, Swipeable } from 'react-native-gesture-handler';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import TableBar from './components/TableBar';
import CheckBox from '@react-native-community/checkbox';
import { CommonStore } from '../store/commonStore';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { OutletStore } from '../store/outletStore';
import { ORDER_TYPE, USER_ORDER_STATUS } from '../constant/common';
import moment from 'moment';

const TakeAwayScreen = props => {
  const {
    navigation,
  } = props;

  const [orderId, setOrderId] = useState('');
  const [showItem, setShowItem] = useState({});
  const [currToPrioritizeOrder, setCurrToPrioritizeOrder] = useState({});
  const [item, setItem] = useState({});
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);

  const setState = () => { };

  const [takeAwayOrders, setTakeAwayOrders] = useState([]);

  const userOrders = OutletStore.useState(s => s.userOrders);
  const userOrdersDict = OutletStore.useState(s => s.userOrdersDict);
  const userOrdersTableDict = OutletStore.useState(s => s.userOrdersTableDict);

  const [refreshRate, setRefreshRate] = useState(new Date());

  useEffect(() => {
    var userOrdersTemp = userOrders.filter(order =>
      (order.orderType === ORDER_TYPE.DELIVERY || order.orderType === ORDER_TYPE.PICKUP) &&
      (
        // order.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
        order.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED ||
        order.orderStatus === USER_ORDER_STATUS.ORDER_PREPARING
      ));

    userOrdersTemp.sort((a, b) => b.isPrioritizedOrder - a.isPrioritizedOrder);

    setTakeAwayOrders(userOrdersTemp);
  }, [userOrders]);

  // useEffect(() => {
  //   setTimeout(() => {
  //     setRefreshRate(new Date());

  //     checkOvertimeOrders();
  //   }, 30000);

  //   checkOvertimeOrders();
  // }, [refreshRate]);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity style={{
      }} onPress={() => { props.navigation.goBack(); }}>
        <View
          style={{
            marginLeft: 10,
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            marginTop: Platform.OS == 'android' ? 9 : 10,
            opacity: 0.8,
          }}>
          <Icon
            name="chevron-back"
            size={26}
            color={Colors.whiteColor}
            style={{}}
          />

          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 20,
              textAlign: 'center',
              fontFamily: 'NunitoSans-SemiBold',
              // lineHeight: 22,
              //marginTop: -3,
              marginBottom: Platform.OS == 'android' ? 2 : 0,
            }}>
            Back
          </Text>
        </View>
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View style={{
        justifyContent: 'center',
        alignItems: 'center',
        bottom: -2,
      }}>
        <Text
          style={{
            fontSize: 25,
            // lineHeight: 25,
            textAlign: 'center',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 0.8,
          }}>
          Take Away
        </Text>
      </View>
    ),
    headerRight: () => (
      <View style={{
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => { props.navigation.navigate('Profile') }}>
          <Image style={{
            width: 32,
            height: 32,
            marginTop: 8,
            marginRight: 25,
          }} source={require('../assets/image/drawer.png')} />
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount= () => {
  //   getOrderHistory();
  //   setInterval(() => {
  //     getOrderHistory();
  //   }, 5000);
  // }

  const getOrderHistory = () => {
    ApiClient.GET(API.getCurrentTakeAwayOrder + User.getOutletId())
      .then((result) => {
        //console.log(API.getCurrentTakeAwayOrder + User.getOutletId())
        setState({ orderId: result });
      })
      .catch((err) => {
        console.log(err);
      });
  }

  const showOrderItem = (item) => {
    if (item.uniqueId !== showItem.uniqueId)
      setShowItem(item);
    else
      setShowItem({});
  }

  // const checkOrderItem = (id, e, i) => {
  //   ApiClient.POST(API.orderDeliver + id)
  //     .then((result) => {
  //       if (result === true) getOrderHistory();
  //     })
  //     .catch((err) => Alert('Error', 'Something went wrong'));
  // }

  // const uncheck = (id, e, i) => {
  //   ApiClient.POST(API.orderDeliverUndo + id)
  //     .then((result) => {
  //       if (result === true) getOrderHistory();
  //       //console.log('getOrderHistory');
  //       //console.log(getOrderHistory);
  //     })
  //     .catch((err) => Alert('Error', 'Something went wrong'));
  //   //.catch(err => {console.log(err)})
  // }

  const checkOrderItem = (item, orderItem) => {
    var body = {
      itemId: item.itemId,
      cartItemDate: item.cartItemDate,
      orderId: orderItem.uniqueId,
    };

    ApiClient.POST(API.orderDeliver, body).then(result => {
      // if (result === true)
      // getOrderHistory()
      //console.log('getOrderHistory');
      //console.log(getOrderHistory);
    }).catch(err => Alert('Error', 'Something went wrong'))
    //.catch(err => {console.log(err)})
  }

  const uncheck = (item, orderItem) => {
    var body = {
      itemId: item.itemId,
      cartItemDate: item.cartItemDate,
      orderId: orderItem.uniqueId,
    };

    ApiClient.POST(API.orderDeliverUndo, body).then(result => {
      // if (result === true)
      // getOrderHistory()
      //console.log('getOrderHistory');
      //console.log(getOrderHistory);
    }).catch(err => Alert('Error', 'Something went wrong'))
    //.catch(err => {console.log(err)})
  }

  const renderOrderItems = ({ item }, orderItem) => {
    // return (
    //   <View style={styles.bottomPart}>
    //     <View style={[styles.topPart, { flex: 0.5 }]}>
    //       <CheckBox
    //         value={item.isChecked}
    //         onValueChange={() => {
    //           item.isChecked ? uncheck(item.uniqueId)
    //             : checkOrderItem(item.uniqueId);
    //         }}
    //       />
    //     </View>
    //     <View style={[styles.topPart, { flex: 2 }]}>
    //       <Text>{item.name}</Text>
    //       <Text style={{ fontSize: 15, color: 'grey' }}>{item.remarks}</Text>
    //     </View>
    //     <View style={styles.topPart}>
    //       <Text>x{item.quantity}</Text>
    //     </View>
    //     <View style={styles.topPart}>
    //       <Text>RM{item.totalPrice}</Text>
    //     </View>
    //   </View>
    // );

    return (
      <View>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          // backgroundColor: 'red',
          marginBottom: 5,
          marginTop: 5,
          marginLeft: 10,
          marginRight: 15,
        }}>
          <View style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            // backgroundColor: 'blue',
          }}>
            <CheckBox style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }], marginTop: 3 }}
              value={item.isChecked} onValueChange={() => {
                item.isChecked ? uncheck(item, orderItem) : checkOrderItem(item, orderItem)
              }} />

            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 15, marginLeft: 5, width: '80%' }}>{item.name}</Text>
            {/* {item.remarks && item.remarks.length > 0 ? <Text style={{ fontSize: 15, color: 'grey' }}>{item.remarks}</Text> : <></>} */}

            <Text style={{ color: '#747474', fontFamily: 'NunitoSans-Bold', marginLeft: 10, }}>x{item.quantity}</Text>
          </View>

          {/* <Text style={{ fontFamily: 'NunitoSans-Bold' }}>RM{(item.price).toFixed(2)}</Text> */}
        </View>

        {item.remarks && item.remarks.length > 0 ?
          <View style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            // backgroundColor: 'red',
            // marginBottom: 5,
            // marginTop: 5,
            marginLeft: 10,
            marginRight: 15,
            marginTop: -15,
            marginBottom: 5,
          }}>
            <View style={{
              flexDirection: 'row',
              justifyContent: 'flex-start',
              alignItems: 'center',
              // backgroundColor: 'blue',
            }}>

              <CheckBox style={{
                opacity: 0,
              }} />
              <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 14, marginLeft: 5 }}>{item.remarks}</Text>
            </View>
          </View>
          : <></>
        }
      </View>
    );
  };

  const checkOvertimeOrders = async () => {
    for (var i = 0; i < takeAwayOrders.length; i++) {
      const waitingTime = ((moment().valueOf() - takeAwayOrders[i].estimatedPreparedDate) / (1000 * 60));

      if (waitingTime >= 300) {
        // await cancelOrder(takeAwayOrders[i], false);
      }
    }
  };

  const cancelOrder = async (param, showAlert = true) => {
    var body = {
      orderId: param.uniqueId,
      tableId: param.tableId,
    };
    ApiClient.POST(API.cancelUserOrderByMerchant, body, false).then((result) => {
      if (result && result.status === 'success') {
        if (showAlert) {
          Alert.alert(
            'Success',
            'The takeaway order has been cancelled',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          // setModalCancelVisibility(false);
        }
      } else {
        if (showAlert) {
          Alert.alert(
            "Failed",
            'Your request has failed',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
          // setModalCancelVisibility(false);
        }
      }
    });
  }

  const prioritizeOrder = (param) => {
    var body = {
      orderId: param
    };
    ApiClient.POST(API.prioritizeOrder, body, false).then((result) => {
      if (result !== null) {
        Alert.alert(
          'Success',
          'The order has been prioritized',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
        // setState({ visible: false, visible1: false });
        setVisible1(false);
      } else {
        Alert.alert(
          "Failed",
          'Your request has failed',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
        // setState({ visible: false, visible1: false });
        setVisible1(false);
      }
    });
  }

  const rightAction = (item) => {
    return (
      <TouchableOpacity
        style={{
          height: "100%",
          width: "20%",
          pointerEvents: 'box-none',
          justifyContent: 'center',
          alignItems: 'center',
          alignContent: 'center',
          alignSelf: "center",
          backgroundColor: Colors.primaryColor,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          borderTopRightRadius: 10,
          borderBottomRightRadius: 10,
        }}
        onPress={() => {
          // item.customTable == "TAKE AWAY" ?
          //   setState({
          //     currToPrioritizeOrder: item,
          //     visible: true
          //   }) :
          //   setState({
          //     currToPrioritizeOrder: item,
          //     visible1: true
          //   });

          prioritizeOrder(item.uniqueId);
        }}
      >
        <MaterialCommunityIcons
          name="message-alert-outline"
          size={30}
          color={Colors.whiteColor}
          style={{ marginTop: 10 }}
        />
        <Text style={{ color: Colors.whiteColor, fontSize: 8 }}>{item.customTable == "TAKE AWAY" ? "Prioritize Takeaway" : "Prioritize Order"}</Text>
      </TouchableOpacity>
    )

  };

  const renderOrder = ({ item }) => {
    const orderEstimatedTime = ((moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60));

    return (
      <View
        style={[
          styles.insideFlat,
          {

            borderWidth: 1,
            // borderColor:
            //   item.estimateTime > 20
            //     ? Colors.tabRed
            //     : item.estimateTime < 20 && item.estimateTime >= 15
            //       ? Colors.tabYellow
            //       : Colors.tabGrey,

            // paddingHorizontal: 0,
          },
        ]}>
        <Swipeable
          renderRightActions={() => rightAction(item)}
        >
          <View
            style={{

              flexDirection: 'row',
              alignSelf: 'center',
              borderBottomWidth: 1,
              borderBottomColor: '#E5E4E2',
              width: '100%',
            }}>
            <View style={[styles.topPart, { flex: 1.2, alignItems: 'center', marginLeft: 10 }]}>
              <Text
                ellipsizeMode='tail'
                numberOfLines={1}
                style={{
                  fontSize: 15,
                  paddingVertical: 20,
                  fontFamily: 'NunitoSans-Bold',
                  width: '120%',
                  // backgroundColor: 'red',
                }}>
                #T{item.orderId}
              </Text>
            </View>
            <View style={[styles.topPart, { flex: 2, alignItems: 'center' }]}>
              <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-SemiBold' }}>{item.orderType === ORDER_TYPE.DELIVERY ? 'Delivery' : 'Takeaway'}</Text>

              {
                item.preorderPackageId
                  ?
                  <View style={{ width: '100%', alignItems: 'center' }}>
                    <Text
                      style={{
                        fontSize: 12,
                        fontFamily: 'NunitoSans-SemiBold',
                        alignSelf: 'center',
                        paddingVertical: 3,
                        color: Colors.secondaryColor,
                      }}>
                      {'Preorder'}
                    </Text>

                    <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 10, color: Colors.darkBgColor }}>
                      {`${moment(item.preorderCollectionDate).format('DD/MM/YYYY')} ${moment(item.preorderCollectionTime).format('hh:mma')}`}
                    </Text>
                  </View>
                  :
                  <View style={{ width: '100%' }}>
                    {orderEstimatedTime < 300 ?
                      <Text
                        style={{
                          fontSize: 12,
                          fontFamily: 'NunitoSans-SemiBold',
                          alignSelf: 'center',
                          paddingVertical: 3,
                        }}>
                        Waiting Time: {` ${orderEstimatedTime < 0 ? 0 : orderEstimatedTime.toFixed(0)}mins`}
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 10 }}>
                          {' '}
                        </Text>
                      </Text>
                      :
                      <Text
                        style={{
                          fontSize: 12,
                          fontFamily: 'NunitoSans-SemiBold',
                          alignSelf: 'center',
                          paddingVertical: 3,
                          color: Colors.tabRed
                        }}>
                        {'OverTime Order'}
                        <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 10 }}>
                          {' '}
                        </Text>
                      </Text>
                    }
                  </View>
              }
            </View>

            <View style={styles.topPart}>
              {/* {console.log("{item}", item.subtotal)}*/}
              <Text
                style={{
                  color: Colors.primaryColor,
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 14,
                }}>
                RM{(item.totalPrice).toFixed(2)}
              </Text>
            </View>

            <View style={{
              opacity: item.isPrioritizedOrder ? 100 : 0,
              justifyContent: 'center',
              bottom: 1,
              marginRight: '2%',
            }}>
              <FontAwesome name={'star'} size={18} color={Colors.primaryColor} />
            </View>

            <TouchableOpacity
              onPress={() => {
                showOrderItem(item);
              }}
              style={{
                marginRight: 10,
              }}>
              <View style={styles.topPart}>
                <Icon
                  name={
                    showItem.uniqueId === item.uniqueId ? 'chevron-up' : 'chevron-down'
                  }
                  size={25}
                  style={{
                    color:
                      item.cartItems[0] == undefined ? 'grey' : Colors.primaryColor,
                  }}
                />
              </View>
            </TouchableOpacity>
          </View>
          {showItem.uniqueId === item.uniqueId ? (
            <View>
              <FlatList
                data={item.cartItems}
                renderItem={(listItem) => renderOrderItems(listItem, item)}
                keyExtractor={(item, index) => String(index)}
              />
            </View>
          ) : null}
        </Swipeable>
      </View>
    );
  };

  const renderModal = (item) => {
    return (
      <View>
        {item && item.uniqueId &&
          <>
            <Modal
              supportedOrientations={['landscape', 'portrait']}
              style={{ flex: 1 }}
              visible={visible}
              transparent={true}
              animationType="slide">
              <View
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: Dimensions.get('window').height,
                }}>
                <View style={styles.confirmBox}>
                  <View style={{ marginTop: 40 }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        fontWeight: '700',
                        fontSize: 18,
                      }}>
                      Prioritize Takeaway
                    </Text>
                  </View>
                  <View style={{ marginTop: 20 }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 25,
                        width: "80%",
                        alignSelf: "center"
                      }}>
                      Priotize Takeaway for {item.user == null ? "" : item.user.name}, Transaction#{item.customTable == "TAKE AWAY" ? "T" : ""}{item.id}
                    </Text>
                  </View>
                  <View style={{ height: 45 }}></View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'center',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '50%',
                      alignContent: 'center',
                      zIndex: 6000,
                    }}>

                  </View>
                  <View
                    style={{
                      alignSelf: 'center',
                      marginTop: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 250,
                      height: 40,
                      alignContent: 'center',
                      flexDirection: "row",
                      marginTop: Dimensions.get('screen').height === 800 && Dimensions.get('screen').width === 1280 ? 32 : 40,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        prioritizeOrder(item.id)
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 80,
                        borderBottomLeftRadius: 10,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth
                      }}>
                      <Text style={{ fontSize: 22, color: Colors.primaryColor }}>
                        Confirm
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setState({ visible: false });
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 80,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth
                      }}>
                      <Text style={{ fontSize: 22, color: Colors.descriptionColor }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>
            <Modal
              supportedOrientations={['landscape', 'portrait']}
              style={{ flex: 1 }}
              visible={visible1}
              transparent={true}
              animationType="slide">
              <View
                style={{
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: Dimensions.get('window').height,
                }}>
                <View style={styles.confirmBox}>
                  <View style={{ marginTop: 40 }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        fontWeight: '700',
                        fontSize: 18,
                      }}>
                      Prioritize Transaction
                    </Text>
                  </View>
                  <View style={{
                    marginTop: 20,
                  }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        color: Colors.descriptionColor,
                        fontSize: 25,
                        width: "80%",
                        alignSelf: "center"
                      }}>
                      Priotize Transaction for Table{item.table === null ? "" : item.table.code}, Transaction#{item.customTable == "TAKE AWAY" ? "T" : ""}{item.id}
                    </Text>
                  </View>
                  <View style={{ height: 45 }}></View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignSelf: 'center',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '50%',
                      alignContent: 'center',
                      zIndex: 6000,
                    }}>

                  </View>
                  <View
                    style={{
                      alignSelf: 'center',
                      marginTop: 20,
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 250,
                      height: 40,
                      alignContent: 'center',
                      flexDirection: "row",
                      marginTop: Dimensions.get('screen').height === 800 && Dimensions.get('screen').width === 1280 ? 32 : 40,
                    }}>
                    <TouchableOpacity
                      onPress={() => {
                        prioritizeOrder(item.id)
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 80,
                        borderBottomLeftRadius: 10,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth
                      }}>
                      <Text style={{ fontSize: 22, color: Colors.primaryColor }}>
                        Confirm
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => {
                        setState({ visible1: false });
                      }}
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '70%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 80,
                        borderBottomRightRadius: 10,
                        borderTopWidth: StyleSheet.hairlineWidth
                      }}>
                      <Text style={{ fontSize: 22, color: Colors.descriptionColor }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>
          </>
        }
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {renderModal(currToPrioritizeOrder)}

      {/* <View
        style={{
          flex: 2,
          backgroundColor: Colors.fieldtBgColor,
          paddingHorizontal: 20,
          alignItems: 'center',
          justifyContent: 'center',
          alignContent: 'center',
        }}> */}
      <View style={{ flex: 1.8, backgroundColor: Colors.fieldtBgColor, paddingHorizontal: 15, paddingVertical: 15, paddingBottom: 5, alignItems: 'center', justifyContent: 'center', alignContent: 'center' }}>
        <TableBar orderTables={orderId} />
      </View>
      <View style={{ paddingBottom: 10 }}></View>
      <View style={{ flex: 7, paddingBottom: 10, }}>
        <FlatList
          style={{ paddingVertical: 10, paddingTop: 10 }}
          data={takeAwayOrders}
          renderItem={renderOrder}
          keyExtractor={(item, index) => String(index)}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  topFlat: {
    backgroundColor: 'white',
    shadowColor: 'grey',
    shadowRadius: 3,
    shadowOpacity: 20,
    shadowOffset: { width: 1, height: 1 },
    margin: 10,
    borderRadius: 8,
    alignItems: 'center',
    paddingTop: 15,
    height: 60,
    width: 60,
  },

  section: {
    height: '20%',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  circle: {
    height: 65,
    width: 65,
    borderRadius: 65 / 2,
    borderWidth: 1,
    borderColor: Colors.secondaryColor,
    justifyContent: 'center',
    alignItems: 'center',
  },
  smallCircle: {
    width: 25,
    height: 25,
    backgroundColor: Colors.primaryColor,
    borderRadius: 12.5,
    position: 'absolute',
    top: -5,
    right: -5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  smallCircleFont: {
    color: 'white',
    fontFamily: 'NunitoSans-Bold',
    fontSize: 15,
  },
  circleIcon: {
    width: '50%',
    height: '50%',
    resizeMode: 'contain',
  },
  insideFlat: {
    // paddingHorizontal: 4,
    paddingLeft: 4,
    marginHorizontal: 10,
    marginBottom: 20,
    flex: 1,
    borderRadius: 10,
    overflow: 'hidden',
    //elevation: 0,
  },

  topPart: {
    justifyContent: 'center',
    flex: 1,
    alignSelf: 'center',
  },
  bottomPart: {
    flexDirection: 'row',
    padding: 10,
    paddingHorizontal: 10,
  },
  primaryFont: {
    fontFamily: 'NunitoSans-Regular',
  },

  confirmBox: {
    width: 350,
    height: 280,
    borderRadius: 10,
    backgroundColor: Colors.whiteColor,
  },
});
export default TakeAwayScreen;
