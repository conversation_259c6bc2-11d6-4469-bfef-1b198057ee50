import { Text } from "react-native-fast-text";
import React, {
  useState,
  useEffect,
  useCallback,
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Dimensions,
  Alert,
  Modal as ModalComponent,
  TextInput,
  KeyboardAvoidingView,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import { Platform } from 'react-native';
import Colors from '../../constant/Colors';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Switch from 'react-native-switch-pro';
import SideBar from '../SideBar';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import DropDownPicker from 'react-native-dropdown-picker';
import ApiClient from '../../util/ApiClient';
import API from '../../constant/API';
import moment from 'moment';
import Styles from '../../constant/Styles';
import {
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  isTablet
} from '../../util/common';
import { MerchantStore } from '../../store/merchantStore';
import { OutletStore } from '../../store/outletStore';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { CommonStore } from '../../store/commonStore';
import { UserStore } from '../../store/userStore';
import RNPickerSelect from 'react-native-picker-select';
import { parseValidPriceText } from '../../util/common';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AntDesign from 'react-native-vector-icons/AntDesign';
// import Barcode from 'react-native-barcode-builder';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  EXPAND_TAB_TYPE,
} from '../../constant/common';
import { useKeyboard } from '../../hooks';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
} from '../../util/common';
import { qrUrl } from '../../constant/env';
import { printUserOrder } from '../../util/printer';
import { Collections } from '../../constant/firebase';
import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../../components/asyncImage';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
const alphabet = '0123456789';
const nanoid = customAlphabet(alphabet, 12);
import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import DraggableFlatList from 'react-native-draggable-flatlist';
import { LOYALTY_CAMPAIGN_TYPE } from '../../constant/loyalty';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../../components/userIdleWrapper';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const MainLoyaltySettingsScreen = (props) => {
  const { navigation, } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  var dummyTier = [
    { name: 'Silver Member', totalSpend: 'RM 800.00', cashback: '8%' },
    { name: 'Gold Member', totalSpend: 'RM 1500.00', cashback: '12%' },
    { name: 'Platinum Member', totalSpend: 'RM 3000.00', cashback: '16%' },
    { name: 'Silver Member', totalSpend: 'RM 800.00', cashback: '8%' },
    { name: 'Gold Member', totalSpend: 'RM 1500.00', cashback: '12%' },
    { name: 'Platinum Member', totalSpend: 'RM 3000.00', cashback: '16%' },
    { name: 'Silver Member', totalSpend: 'RM 800.00', cashback: '8%' },
    { name: 'Gold Member', totalSpend: 'RM 1500.00', cashback: '12%' },
    { name: 'Platinum Member', totalSpend: 'RM 3000.00', cashback: '16%' },
  ];
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);

  // show use state
  const [showLoyaltyMain, setShowLoyaltyMain] = useState(false);
  const [showLoyaltyCashback, setShowLoyaltyCashback] = useState(false);
  const [showLoyaltyTransaction, setShowLoyaltyTransaction] = useState(false);
  const [showLoyaltyGuestData, setShowLoyaltyGuestData] = useState(false);
  const [showLoyaltyAutomated, setShowLoyaltyAutomated] = useState(false);

  // modal use state
  const [showLoyaltyAutomatedReminder1, setShowLoyaltyAutomatedReminder1] =
    useState(false);
  const [showLoyaltyAutomatedReminder2, setShowLoyaltyAutomatedReminder2] =
    useState(false);
  const [showLoyaltyAutomatedReminder3, setShowLoyaltyAutomatedReminder3] =
    useState(false);
  const [showLoyaltyAutomatedCampaign, setShowLoyaltyAutomatedCampaign] =
    useState(false);
  const [showLoyaltyAutomatedSignUp, setShowLoyaltyAutomatedSignUp] =
    useState(false);
  const [tierUpgradeModal, setTierUpgradeModal] = useState(false);
  const [addTierModal, setAddTierModal] = useState(false);
  const [editTierModal, setEditTierModal] = useState(false);

  // switch use state
  const [
    switchTransactionCashbackNotification,
    setSwitchTransactionCashbackNotification,
  ] = useState(false);
  const [
    switchTransactionRedeemNotification,
    setSwitchTransactionRedeemNotification,
  ] = useState(false);
  const [
    switchTransactionDeductNotification,
    setSwitchTransactionDeductNotification,
  ] = useState(false);
  const [switchGuestCaptureCashback, setSwitchGuestCaptureCashback] =
    useState(false);
  const [switchGuestCaptureRedemption, setSwitchGuestCaptureRedemption] =
    useState(false);
  const [switchAutomatedReminderActive1, setSwitchAutomatedReminderActive1] =
    useState(false);
  const [switchAutomatedReminderActive2, setSwitchAutomatedReminderActive2] =
    useState(false);
  const [switchAutomatedReminderActive3, setSwitchAutomatedReminderActive3] =
    useState(false);
  const [switchAutomatedCampaignActive, setSwitchAutomatedCampaignActive] =
    useState(false);

  const [switchEnableTierSystem, setSwitchEnableTierSystem] = useState(false);
  const [switchTierUpgradeByTotalSpend, setSwitchTierUpgradeByTotalSpend] =
    useState(false);
  const [switchTierUpgradeByTotalVisits, setSwitchTierUpgradeByTotalVisits] =
    useState(false);
  const [switchTierUpgradeByBoth, setSwitchTierUpgradeByBoth] = useState(false);
  const [switchSignup, setSwitchSignup] = useState(false);
  const [switch1stVisit, setSwitch1stVisit] = useState(false);
  const [switchRisk, setSwitchRisk] = useState(false);
  const [switchLapsed, setSwitchLapsed] = useState(false);
  const [switchLost, setSwitchLost] = useState(false);
  const [switchBirthday, setSwitchBirthday] = useState(false);
  const [switchGrowth, setSwitchGrowth] = useState(false);
  const [switchSpender, setSwitchSpender] = useState(false);

  // dropdownpicker use state
  const [dropTransactionSecurity, setDropTransactionSecurity] = useState('');
  const [dropGuestLastName, setDropGuestLastName] = useState('');
  const [dropGuestGender, setDropGuestGender] = useState('');
  const [dropGuestEmail, setDropGuestEmail] = useState('');
  const [dropGuestDob, setDropGuestDob] = useState('');
  const [dropTierCashback, setDropTierCashback] = useState('0.10');
  const [dropAutomatedReminderModal1, setDropAutomatedReminderModal1] =
    useState('');
  const [dropAutomatedReminderModal2, setDropAutomatedReminderModal2] =
    useState('');
  const [dropAutomatedReminderModal3, setDropAutomatedReminderModal3] =
    useState('');
  const [dropPastTime, setDropPastTime] = useState('1');
  const [dropSignupReward, setDropSignupReward] = useState('');
  const [dropSignupApply, setDropSignupApply] = useState('');
  const [drop1stExp, setDrop1stExp] = useState('');
  const [dropRiskVisited, setDropRiskVisited] = useState('');
  const [dropRiskExp, setDropRiskExp] = useState('');
  const [dropLapsedVisited, setDropLapsedVisited] = useState('');
  const [dropLapsedExp, setDropLapsedExp] = useState('');
  const [dropLostVisited, setDropLostVisited] = useState('');
  const [dropLostExp, setDropLostExp] = useState('');
  const [dropBirthdayIsIn, setDropBirthdayIsIn] = useState('');
  const [dropBirthdayExp, setDropBirthdayExp] = useState('');
  const [dropGrowthAfter, setDropGrowthAfter] = useState('');
  const [dropGrowthExp, setDropGrowthExp] = useState('');
  const [dropSpenderExp, setDropSpenderExp] = useState('');
  const [dropSignupExp, setDropSignupExp] = useState('');
  const [dropNewCashback, setDropNewCashback] = useState('');
  const [dropEditCashback, setDropEditCashback] = useState('');

  // time use state
  const [showRemTime1, setShowRemTime1] = useState(false);
  const [remTime1, setRemTime1] = useState(moment());
  const [isRemTime1, setIsRemTime1] = useState(false);
  const [showRemTime2, setShowRemTime2] = useState(false);
  const [remTime2, setRemTime2] = useState(moment());
  const [isRemTime2, setIsRemTime2] = useState(false);
  const [showRemTime3, setShowRemTime3] = useState(false);
  const [remTime3, setRemTime3] = useState(moment());
  const [isRemTime3, setIsRemTime3] = useState(false);
  const [show1stVisit, setShow1stVisit] = useState(false);
  const [fstVisitTime, setFstVisitTime] = useState(moment());
  const [showRisk, setShowRisk] = useState(false);
  const [riskTime, setRiskTime] = useState(moment());
  const [showLapsed, setShowLapsed] = useState(false);
  const [lapsedTime, setLapsedTime] = useState(moment());
  const [showLost, setShowLost] = useState(false);
  const [lostTime, setLostTime] = useState(moment());
  const [showBirthday, setShowBirthday] = useState(false);
  const [birthdayTime, setBirthdayTime] = useState(moment());
  const [showGrowth, setShowGrowth] = useState(false);
  const [GrowthTime, setGrowthTime] = useState(moment());
  const [showSpender, setShowSpender] = useState(false);
  const [SpenderTime, setSpenderTime] = useState(moment());

  // other use state
  const [isRem1, setIsRem1] = useState(false);
  const [isRem2, setIsRem2] = useState(false);
  const [isRem3, setIsRem3] = useState(false);
  const [is1st, setIs1st] = useState(false);
  const [isRisk, setIsRisk] = useState(false);
  const [isLapsed, setIsLapsed] = useState(false);
  const [isLost, setIsLost] = useState(false);
  const [isBirthday, setIsBirthday] = useState(false);
  const [isGrowth, setIsGrowth] = useState(false);
  const [isSpender, setIsSpender] = useState(false);
  const [isSignup, setIsSignup] = useState(false);

  // textinput use state
  const [fstSmsText, setFstSmsText] = useState('');
  const [riskSmsText, setRiskSmsText] = useState('');
  const [lapsedSmsText, setLapsedSmsText] = useState('');
  const [lostSmsText, setLostSmsText] = useState('');
  const [birthdaySmsText, setBirthdaySmsText] = useState('');
  const [growthSmsText, setGrowthSmsText] = useState('');
  const [spenderSmsText, setSpenderSmsText] = useState('');
  const [signupSmsText, setSignupSmsText] = useState('');
  const [signupCashback, setSignupCashback] = useState('');
  const [spenderGuestSpend, setSpenderGuestSpend] = useState('');
  const [newTierName, setNewTierName] = useState('');
  const [newTierTotalSpend, setNewTierTotalSpend] = useState('0');
  const [newTierTotalVisits, setNewTierTotalVisits] = useState('0');
  const [newTierVoucherId, setNewTierVoucherId] = useState('');

  const [newTierCashback, setNewTierCashback] = useState('10%');
  const [dummyTierData, setDummyTierData] = useState(dummyTier);
  const [tierSelected, setTierSelected] = useState('');
  const [editTierName, setEditTierName] = useState('');
  const [editTierSpend, setEditTierSpend] = useState('0');
  const [editTierVisits, setEditTierVisits] = useState('0');
  const [editTierVoucherId, setEditTierVoucherId] = useState('');

  const [loyaltyTierLevels, setLoyaltyTierLevels] = useState([]);
  const [selectedLoyaltyTierLevelId, setSelectedLoyaltyTierLevelId] =
    useState('');
  const [cashbackPercentageDropdownShowed, setCashbackPercentageDropdownShowed] = useState(false)

  const loyaltyCampaigns = OutletStore.useState((s) => s.loyaltyCampaigns.filter(campaign => campaign.loyaltyCampaignType !== LOYALTY_CAMPAIGN_TYPE.SIGN_UP));

  const loyaltyTier = OutletStore.useState((s) => s.loyaltyTier);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const merchantId = UserStore.useState((s) => s.merchantId);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const [temp, setTemp] = useState('');

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  ////////////////////////////////////////////////////////////

  // 2025-02-14 - voucher to tag for tier

  const [taggableVoucherDropdownList, setTaggableVoucherDropdownList] = useState([]);
  // const [taggableVoucherId, setTaggableVoucherId] = useState('');

  const taggableVouchers = OutletStore.useState(s => s.taggableVouchers);

  ////////////////////////////////////////////////////////////

  useEffect(() => {
    setTaggableVoucherDropdownList(
      [
        {
          label: 'N/A',
          value: '',
        }
      ].concat(
        taggableVouchers.filter(voucher => {
          var endTime = moment(voucher.promoTimeEnd);
          var endDate = moment(voucher.promoDateEnd).set({
            hour: endTime.get('hour'),
            minute: endTime.get('minute'),
          });
          if (!moment().isSameOrAfter(endDate)) {
            return true;
          }
          else {
            return false;
          }
        }).map((item) => ({ label: `${item.campaignName} [${item.voucherType.replaceAll('_', ' ')}]`, value: item.uniqueId })),
      ),
    );
  }, [taggableVouchers]);

  ////////////////////////////////////////////////////////////

  // useEffect start

  useEffect(() => {
    if (loyaltyTier && loyaltyTier.uniqueId) {
      // got saved settings

      setLoyaltyTierLevels(loyaltyTier.levels);

      setDropTierCashback(loyaltyTier.baseCashbackRate.toFixed(2));
      setSwitchEnableTierSystem(loyaltyTier.isActive);
      setSwitchTierUpgradeByTotalSpend(loyaltyTier.isCountByTotalSpents);
      setSwitchTierUpgradeByTotalVisits(loyaltyTier.isCountByTotalVisits);
      setDropPastTime(loyaltyTier.countPastTimeMonths.toString());
    } else {
    }
  }, [loyaltyTier]);

  useEffect(() => {
    setShowLoyaltyMain(true);
    // setShowLoyaltyCashback(false);
    // setShowLoyaltyTransaction(false);
    // setShowLoyaltyGuestData(false);
    // setShowLoyaltyAutomated(false);
  }, []);

  const manyMonthDropDownItem = [];
  const manyDaysDropDownItem = [];
  const manyVisitDropDownItem = [];
  for (let i = 0; i < 400; i++) {
    manyDaysDropDownItem.push({
      label: `${i} days`,
      value: `${i}_DAYS`,
    });
    if (i === 0) {
      manyVisitDropDownItem.push({
        label: '1st Visit',
        value: '1ST_VISIT',
      });
      manyMonthDropDownItem.push({
        label: '1 month',
        value: '1',
      });
    } else if (i === 1) {
      manyVisitDropDownItem.push({
        label: '2th visit',
        value: '2ND_VISIT',
      });
    } else if (i === 2) {
      manyVisitDropDownItem.push({
        label: '3rd visit',
        value: '3RD_VISIT',
      });
    } else {
      manyVisitDropDownItem.push({
        label: `${i + 1}th Visit`,
        value: `${i + 1}TH_VISIT`,
      });
    }
    if (i < 36 && i !== 0) {
      manyMonthDropDownItem.push({
        label: `${i + 1} months`,
        value: i + 1,
      });
    }
  }

  // drop down item
  let cashbackItem = [];
  for (let i = 0; i <= 100; i++) {
    cashbackItem.push({
      label: `${i}%`,
      // value: i + '%',
      value: (i / 100).toFixed(2),
    });
  }

  // earn 10x points
  cashbackItem.push({
    label: `${1000}%`,
    // value: i + '%',
    value: (1000 / 100).toFixed(2),
  })

  const renderTierSummary = (item, index) => {
    return (
      // Card View
      (<View
        style={{
          height: windowHeight * 0.05,
          backgroundColor: '#E5E7E9',
          width: '100%',
          flexDirection: 'row',
          // padding: 20,
          // borderWidth: 1,
          justifyContent: 'space-between',
        }}>
        <View
          style={{
            flexDirection: 'row',
            padding: 10,
          }}>
          <View style={{ padding: 2 }}>
            <Plus name="menu" size={16} color="black" style={{}} />
          </View>
          <View style={{ paddingLeft: 10 }}>
            <Text>Gold member (10% points)</Text>
          </View>
        </View>
        <View style={{ padding: 10 }}>
          <Text>RM 800.00 spent</Text>
        </View>
      </View>)
    );
  };

  const renderTierProgression = ({ item, index }) => {
    // console.log(index);
    // // console.log('dummytierdata length ' + dummyTierData.length);

    return (
      // Card View
      (<View
        style={{
          // height: windowHeight * 0.05,
          // backgroundColor: '#E5E7E9',
          // width: '100%',
          height: '100%',
          flexDirection: 'row',
          paddingVertical: 5,
          // borderWidth: 1,
          justifyContent: 'space-between',
        }}>
        {loyaltyTierLevels.length === 1 ? (
          <View>
            {/* a head only */}
            {/* Head view */}
            <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
              {/* Head circle */}
              <View>
                <FontAwesome
                  name="circle"
                  size={30}
                  color="#2E86C1"
                  style={{ alignSelf: 'center' }}
                />
              </View>
            </View>
            {/* body view */}
            <View style={{ alignItems: 'center', paddingHorizontal: 2 }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {item.levelName}
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {`RM ${Number(item.levelTotalSpents).toFixed(2)}`}
              </Text>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {`${(item.levelCashbackRate * 100).toFixed(0)}%`}
                </Text>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {' points'}
                </Text>
              </View>
            </View>
          </View>
        ) : index === 0 ? (
          <View>
            {/* a head and line to right */}
            {/* Head view */}
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
              {/* Head circle */}
              <View>
                <Text
                  style={{
                    position: 'absolute',
                    left: 9,
                    top: switchMerchant ? 7 : 6,
                    color: Colors.whiteColor,
                    zIndex: 1000,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {index + 1}
                </Text>
                <FontAwesome
                  name="circle"
                  size={30}
                  color="#2E86C1"
                  style={{ alignSelf: 'center' }}
                />
              </View>
              {/* Line view */}
              <View
                style={{
                  height: 7,
                  width: switchMerchant ? 20 : 35,
                  marginTop: 12,
                  backgroundColor: '#2E86C1',
                }}
              />
            </View>
            {/* body view */}
            <View style={{ alignItems: 'center', paddingHorizontal: 2 }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {item.levelName}
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {`RM ${item.levelTotalSpents.toFixed(2)}`}
              </Text>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {`${(item.levelCashbackRate * 100).toFixed(0)}%`}
                </Text>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {' points'}
                </Text>
              </View>
            </View>
          </View>
        ) : index === loyaltyTierLevels.length - 1 ? (
          <View>
            {/* a head and line to left */}
            {/* Head view */}
            <View style={{ flexDirection: 'row', justifyContent: 'flex-start' }}>
              {/* Line view */}
              <View
                style={{
                  height: 7,
                  width: switchMerchant ? 20 : 35,
                  marginTop: 12,
                  backgroundColor: '#2E86C1',
                }}
              />
              {/* Head circle */}
              <View>
                <Text
                  style={{
                    position: 'absolute',
                    left: 9,
                    top: switchMerchant ? 7 : 6,
                    color: Colors.whiteColor,
                    zIndex: 1000,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {parseFloat(index) + 1}
                </Text>
                <FontAwesome
                  name="circle"
                  size={30}
                  color="#2E86C1"
                  style={{ alignSelf: 'center' }}
                />
              </View>
            </View>
            {/* body view */}
            <View style={{ alignItems: 'center', paddingHorizontal: 2, }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {item.levelName}
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {`RM ${Number(item.levelTotalSpents).toFixed(2)}`}
              </Text>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {`${(item.levelCashbackRate * 100).toFixed(0)}%`}
                </Text>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {' points'}
                </Text>
              </View>
            </View>
          </View>
        ) : (
          <View>
            {/* a head and lines on both side */}
            {/* Head view */}
            <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
              {/* Line view */}
              <View
                style={{
                  height: 7,
                  width: 50,
                  marginTop: 12,
                  backgroundColor: '#2E86C1',
                }}
              />
              {/* Head circle */}
              <View>
                <Text
                  style={{
                    position: 'absolute',
                    left: 9,
                    top: switchMerchant ? 7 : 6,
                    color: Colors.whiteColor,
                    zIndex: 1000,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {index + 1}
                </Text>
                <FontAwesome
                  name="circle"
                  size={30}
                  color="#2E86C1"
                  style={{ alignSelf: 'center' }}
                />
              </View>
              {/* Line view */}
              <View
                style={{
                  height: 7,
                  width: 50,
                  marginTop: 12,
                  backgroundColor: '#2E86C1',
                }}
              />
            </View>
            {/* body view */}
            <View style={{ alignItems: 'center', paddingHorizontal: 2 }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {item.levelName}
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {`RM ${item.levelTotalSpents.toFixed(2)}`}
              </Text>
              <View style={{ flexDirection: 'row' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {`${(item.levelCashbackRate * 100).toFixed(0)}%`}
                </Text>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {' points'}
                </Text>
              </View>
            </View>
          </View>
        )}
      </View>)
    );
  };

  const renderTierMenu = useCallback((params) => {
    const { item, drag, isActive } = params;

    const index = params.getIndex();

    return (
      <View>
        <TouchableOpacity
          onPress={() => {
            setSelectedLoyaltyTierLevelId(item.levelId);

            setEditTierName(item.levelName);
            setEditTierSpend(item.levelTotalSpents.toFixed(2));
            setEditTierVisits(item.levelTotalVisits ? item.levelTotalVisits.toString() : 0);
            setDropEditCashback((item.levelCashbackRate ? item.levelCashbackRate : 0).toFixed(2));

            if (taggableVoucherDropdownList.find(voucherDropdown => voucherDropdown.value === item.levelVoucherId)) {
              setEditTierVoucherId(item.levelVoucherId);
            }
            else {
              setEditTierVoucherId('');
            }

            setEditTierModal(true);
            setTierSelected(index);

            // // console.log("h")
          }}>
          {/* <View
          style={{
            flexDirection: 'row',
            paddingHorizontal: 20,
            paddingBottom: 15,
            paddingTop: 10,
            display: 'flex',
            justifyContent: 'space-between',
            flexDirection: 'row',
          }}> */}
          <View
            style={{
              height: switchMerchant
                ? windowHeight * 0.1
                : ((!switchMerchant && windowWidth < 1000 && windowHeight <= 610)) ?
                  windowHeight * 0.08 :
                  (!switchMerchant && Dimensions.get('screen').height < 670 && Dimensions.get('screen').width < 1350) ?
                    windowHeight * 0.08 :
                    windowHeight * 0.05,
              backgroundColor: '#ffffff',
              width: '100%',
              flexDirection: 'row',
              // padding: 20,
              // borderWidth: 1,
              minHeight: 50,
              justifyContent: 'space-between',
              borderBottomColor: '#EBEDEF',
              borderBottomWidth: index === loyaltyTierLevels.length - 1 ? 0 : 1,
              borderBottomLeftRadius:
                index === loyaltyTierLevels.length - 1 ? 10 : 0,
              borderBottomRightRadius:
                index === loyaltyTierLevels.length - 1 ? 10 : 0,
              borderTopRightRadius: index === 0 ? 10 : 0,
              borderTopLeftRadius: index === 0 ? 10 : 0,
            }}>
            <View
              style={{
                flexDirection: 'row',
                padding: 10,
              }}>
              <TouchableOpacity
                onLongPress={drag}
                onPress={() => {
                  // console.log(data.length);
                  // console.log(index);
                }}>
                <View style={{ padding: 2 }}>
                  <Plus
                    name="menu"
                    size={switchMerchant ? 15 : 20}
                    color="black"
                    style={{ bottom: switchMerchant ? 2 : 1 }}
                  />
                </View>
              </TouchableOpacity>
              <View style={{ paddingLeft: 10, flexDirection: 'row' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {item.levelName}
                </Text>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {' '}
                </Text>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {typeof item.levelCashbackRate === 'number' ? `${(item.levelCashbackRate * 100).toFixed(0)}%` : `N/A`}
                </Text>
              </View>
            </View>
            <View style={{ padding: 10, flexDirection: 'row' }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {typeof item.levelTotalSpents === 'number' ? `RM ${item.levelTotalSpents.toFixed(2)}` : 'N/A'}
              </Text>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                {' spend'}
              </Text>
            </View>
          </View>
          {/* </View> */}
        </TouchableOpacity>
      </View>
    );
  });

  const renderLoyaltyCampaign = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={() => {
          // setShowLoyaltyAutomatedCampaign(true);
          // setIsRem1(false);
          // setIsRem2(false);
          // setIsRem3(false);
          // setIs1st(false);
          // setIsRisk(true);
          // setIsLapsed(false);
          // setIsLost(false);
          // setIsBirthday(false);
          // setIsGrowth(false);
          // setIsSpender(false);
          // setIsSignup(false);

          CommonStore.update((s) => {
            s.selectedLoyaltyCampaignEdit = item;
          });

          props.navigation.navigate('NewLoyaltyCampaign');
        }}
        style={{
          paddingHorizontal: windowWidth * 0.01,
          paddingVertical: windowHeight * 0.02,
          paddingTop: windowHeight * 0.01,
          borderBottomColor: '#EBEDEF',
          borderBottomWidth: 1,
          width: '100%',
        }}>
        <View
          style={{
            // borderWidth: 1,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
                color: switch1stVisit ? Colors.primaryColor : Colors.tabRed,
              }}>
              <Text>{item.isActive ? 'Active' : 'Inactive'}</Text>
            </Text>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
              }}>
              {item.campaignName}
            </Text>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
                color: '#808B96',
              }}>
              {/* {`Send ${dropRiskVisited} after last visit - ${moment(
                riskTime,
              ).format('hh:mm A')} - ${dropRiskExp} expiration`} */}
              {item.campaignDescription}
            </Text>
          </View>
          <View
            style={{
              flexDirection: 'row',
            }}>
            <Text
              style={{
                fontFamily: 'NunitoSans-Bold',
                fontSize: switchMerchant ? 10 : 14,
              }}>
              {dropRiskVisited}
            </Text>
            <Plus
              name="chevron-right"
              size={switchMerchant ? 20 : 25}
              color={Colors.darkBgColor}
              style={{ bottom: 1 }}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const saveLoyaltyTierSettings = () => {

    var body = {
      levels: loyaltyTierLevels,

      isCountByTotalSpents: switchTierUpgradeByTotalSpend,
      isCountByTotalVisits: switchTierUpgradeByTotalVisits,
      countPastTimeMonths: parseInt(dropPastTime),
      isActive: switchEnableTierSystem,
      baseCashbackRate: parseFloat(dropTierCashback),

      merchantId,
      outletId: currOutlet.uniqueId,

      loyaltyTierId:
        loyaltyTier && loyaltyTier.uniqueId ? loyaltyTier.uniqueId : null,
    };

    // console.log(body);

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    ApiClient.POST(API.saveLoyaltyTierSettings, body).then((result) => {
      if (result && result.status === 'success') {
        Alert.alert('Success', 'Tier system settings has been saved');
      } else {
        Alert.alert('Error', 'Unable to save the tier system settings');
      }

      CommonStore.update((s) => {
        s.isLoading = false;
      });
    });
  };

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const userName = UserStore.useState((s) => s.name);

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={[{
            width: 124,
            height: 26,
          }, switchMerchant ? {
            transform: [
              { scaleX: 0.7 },
              { scaleY: 0.7 }
            ],
          } : {}]}
          resizeMode="contain"
          source={require('../../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Loyalty Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }} />
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >
            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  return (

    <UserIdleWrapper disabled={!isMounted}>
      <View
        style={[
          styles.container,
          !isTablet() ? { transform: [{ scaleX: 1 }, { scaleY: 1 }] } : {},
          {
            ...getTransformForScreenInsideNavigation(),
          }
        ]}>
        {/* Sidebar */}
        {/* <View
          style={[
            styles.sidebar,
            !isTablet() ? { width: windowWidth * 0.08 } : {},
            switchMerchant
              ? {
                // width: '10%'
              }
              : {},
            {
              width: windowWidth * 0.08,
            }
          ]}>
          <SideBar
            navigation={props.navigation}
            selectedTab={1}
            expandOperation
          />
        </View> */}
        <DateTimePickerModal
          isVisible={showRemTime1}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setRemTime1(moment(text).local());

            setShowRemTime1(false);
          }}
          onCancel={() => {
            setShowRemTime1(false);
          }}
        />
        <DateTimePickerModal
          isVisible={showRemTime2}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setRemTime2(moment(text).local());

            setShowRemTime2(false);
          }}
          onCancel={() => {
            setShowRemTime2(false);
          }}
        />
        <DateTimePickerModal
          isVisible={showRemTime3}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setRemTime3(moment(text).local());

            setShowRemTime3(false);
          }}
          onCancel={() => {
            setShowRemTime3(false);
          }}
        />
        <DateTimePickerModal
          isVisible={show1stVisit}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setFstVisitTime(moment(text).local());

            setShow1stVisit(false);
          }}
          onCancel={() => {
            setShow1stVisit(false);
          }}
        />
        <DateTimePickerModal
          isVisible={showRisk}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setRiskTime(moment(text).local());

            setShowRisk(false);
          }}
          onCancel={() => {
            setShowRisk(false);
          }}
        />
        <DateTimePickerModal
          isVisible={showLapsed}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setLapsedTime(moment(text).local());

            setShowLapsed(false);
          }}
          onCancel={() => {
            setShowLapsed(false);
          }}
        />
        <DateTimePickerModal
          isVisible={showLost}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setLostTime(moment(text).local());

            setShowLost(false);
          }}
          onCancel={() => {
            setShowLost(false);
          }}
        />
        <DateTimePickerModal
          isVisible={showBirthday}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setBirthdayTime(moment(text).local());

            setShowBirthday(false);
          }}
          onCancel={() => {
            setShowBirthday(false);
          }}
        />
        <DateTimePickerModal
          isVisible={showGrowth}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setGrowthTime(moment(text).local());

            setShowGrowth(false);
          }}
          onCancel={() => {
            setShowGrowth(false);
          }}
        />
        <DateTimePickerModal
          isVisible={showSpender}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setSpenderTime(moment(text).local());

            setShowSpender(false);
          }}
          onCancel={() => {
            setShowSpender(false);
          }}
        />
        {/* Modal start */}

        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={showLoyaltyAutomatedReminder1}
          transparent
          animationType="slide">
          <KeyboardAvoidingView
            behavior={'padding'}
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              // minHeight: windowHeight,
            }}>
            <View style={[
              styles.confirmBox,
              {
                width: Dimensions.get('window').width * 0.5,
                height: Dimensions.get('window').height * 0.2,

                ...getTransformForModalInsideNavigation(),
              }
            ]}>
              <View
                style={{
                  position: 'absolute',
                  padding: 14,
                  right: windowWidth * 0.015,
                  top: windowHeight * 0.01,
                  zIndex: 1000,
                }}>
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    setShowLoyaltyAutomatedReminder1(false);
                  }}>
                  <View style={{}}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Done
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: '100%',
                  height: windowHeight * 0.08,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: Colors.whiteColor,
                  borderTopRightRadius: 12,
                  borderTopLeftRadius: 12,
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 16 : 24,
                  }}>
                  Reminder {isRem1 ? '1' : isRem2 ? '2' : '3'}
                </Text>
              </View>
              <View
                style={{
                  paddingVertical: windowHeight * 0.01,
                }}>
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                  }}>
                  <View
                    style={{
                      borderBottomWidth: switchAutomatedReminderActive1 ? 1 : 0,
                      borderBottomColor: '#EBEDEF',
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingVertical: windowHeight * 0.02,
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Active
                    </Text>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchAutomatedReminderActive1}
                      onSyncPress={(statusTemp) =>
                        setSwitchAutomatedReminderActive1(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                  {switchAutomatedReminderActive1 ? (
                    <View>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          borderBottomColor: '#EBEDEF',
                          flexDirection: 'row',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Send when guest hasn't visited for
                        </Text>
                        {switchMerchant ? (
                          <View
                            style={{
                              backgroundColor: '#fafafa',
                              // backgroundColor: 'green',
                              borderRadius: 4,
                              height: switchMerchant
                                ? windowHeight * 0.08
                                : 35,
                              width: switchMerchant
                                ? windowWidth * 0.13
                                : 145,
                              justifyContent: 'center',
                              fontSize: switchMerchant ? 10 : 16,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              left: windowWidth * -0.002,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                              useNativeAndroidPickerStyle={false}
                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroid: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={manyDaysDropDownItem}
                              value={dropAutomatedReminderModal1}
                              onValueChange={(value) => {
                                setDropAutomatedReminderModal1(value);
                              }}
                            />
                          </View>
                        ) : (
                          <View
                            style={{
                              justifyContent: 'flex-start',
                              flexDirection: 'row',

                              alignItems: 'flex-start',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroidContainer: {
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  justifyContent: 'center',
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,

                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                                inputAndroid: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                viewContainer: {
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                              }}
                              // use tables of current floor
                              items={manyDaysDropDownItem}
                              value={dropAutomatedReminderModal1}
                              onValueChange={(value) => {
                                setDropAutomatedReminderModal1(value);
                              }}
                            />
                          </View>
                        )}
                      </View>
                      <View
                        style={{
                          // borderWidth: 1,
                          flexDirection: 'row',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          zIndex: -1,
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          What time of the day should it be sent?
                        </Text>
                        {isRem1 ? (
                          <TouchableOpacity
                            onPress={() => {
                              setShowRemTime1(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(remTime1).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        ) : isRem2 ? (
                          <TouchableOpacity
                            onPress={() => {
                              setShowRemTime2(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(remTime2).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        ) : (
                          <TouchableOpacity
                            onPress={() => {
                              setShowRemTime3(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(remTime3).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        )}
                      </View>
                    </View>
                  ) : null}
                </View>
                <Text
                  style={{
                    textAlign: 'center',
                    paddingVertical: windowHeight * 0.01,
                    color: '#808B96',
                    fontSize: switchMerchant ? 10 : 16,
                    fontFamily: 'NunitoSans-Bold',
                  }}>
                  Customize after how many days & after what time guests will be
                  reminded via SMS
                </Text>
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalView>
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={showLoyaltyAutomatedReminder2}
          transparent
          animationType="slide">
          <KeyboardAvoidingView
            behavior={'padding'}
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              // minHeight: windowHeight,
            }}>
            <View style={[
              styles.confirmBox,
              {
                width: Dimensions.get('window').width * 0.5,
                height: Dimensions.get('window').height * 0.2,

                ...getTransformForModalInsideNavigation(),
              }
            ]}>
              <View
                style={{
                  position: 'absolute',
                  padding: 14,
                  right: windowWidth * 0.015,
                  top: windowHeight * 0.01,
                  zIndex: 1000,
                }}>
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    setShowLoyaltyAutomatedReminder2(false);
                  }}>
                  <View style={{}}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Done
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: '100%',
                  height: windowHeight * 0.08,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: Colors.whiteColor,
                  borderTopRightRadius: 12,
                  borderTopLeftRadius: 12,
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 16 : 24,
                  }}>
                  Reminder {isRem1 ? '1' : isRem2 ? '2' : '3'}
                </Text>
              </View>
              <View
                style={{
                  paddingVertical: windowHeight * 0.01,
                }}>
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                  }}>
                  <View
                    style={{
                      borderBottomWidth: switchAutomatedReminderActive2 ? 1 : 0,
                      borderBottomColor: '#EBEDEF',
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingVertical: windowHeight * 0.02,
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Active
                    </Text>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchAutomatedReminderActive2}
                      onSyncPress={(statusTemp) =>
                        setSwitchAutomatedReminderActive2(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                  {switchAutomatedReminderActive2 ? (
                    <View>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          borderBottomColor: '#EBEDEF',
                          flexDirection: 'row',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Send when guest hasn't visited for
                        </Text>
                        {switchMerchant ? (
                          <View
                            style={{
                              backgroundColor: '#fafafa',
                              // backgroundColor: 'green',
                              borderRadius: 4,
                              height: switchMerchant
                                ? windowHeight * 0.08
                                : 35,
                              width: switchMerchant
                                ? windowWidth * 0.13
                                : 145,
                              justifyContent: 'center',
                              fontSize: switchMerchant ? 10 : 16,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              left: windowWidth * -0.002,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                              useNativeAndroidPickerStyle={false}
                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroid: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={manyDaysDropDownItem}
                              value={dropAutomatedReminderModal2}
                              onValueChange={(value) => {
                                setDropAutomatedReminderModal2(value);
                              }}
                            />
                          </View>
                        ) : (
                          <View
                            style={{
                              justifyContent: 'flex-start',
                              flexDirection: 'row',

                              alignItems: 'flex-start',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroidContainer: {
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  justifyContent: 'center',
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,

                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                                inputAndroid: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                viewContainer: {
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                              }}
                              items={manyDaysDropDownItem}
                              value={dropAutomatedReminderModal2}
                              onValueChange={(value) => {
                                setDropAutomatedReminderModal2(value);
                              }}
                            />
                          </View>
                        )}
                      </View>
                      <View
                        style={{
                          // borderWidth: 1,
                          flexDirection: 'row',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          zIndex: -1,
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          What time of the day should it be sent?
                        </Text>
                        <TouchableOpacity
                          onPress={() => {
                            setShowRemTime2(true);
                          }}
                          style={{
                            // height: 50,
                            height: switchMerchant ? 35 : 40,
                            paddingHorizontal: switchMerchant ? 7 : 20,
                            backgroundColor: Colors.fieldtBgColor,
                            //marginBottom: 20,
                            width: switchMerchant ? 100 : 160,
                            //marginHorizontal: 10,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            alignContent: 'center',
                            borderColor: '#E5E5E5',
                            borderWidth: 1,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            borderRadius: 12,
                          }}>
                          <EvilIcons
                            name="clock"
                            size={switchMerchant ? 20 : 25}
                            color={Colors.primaryColor}
                          />
                          <Text
                            style={{
                              //marginLeft: 15,
                              color: 'black',
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                            }}>
                            {moment(remTime2).format('hh:mm A')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : null}
                </View>
                <Text
                  style={{
                    textAlign: 'center',
                    paddingVertical: windowHeight * 0.01,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 16,
                    color: '#808B96',
                  }}>
                  Customize after how many days & after what time guests will be
                  reminded via SMS
                </Text>
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalView>
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={showLoyaltyAutomatedReminder3}
          transparent
          animationType="slide">
          <KeyboardAvoidingView
            behavior={'padding'}
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              // minHeight: windowHeight,
            }}>
            <View style={[
              styles.confirmBox,
              {
                width: Dimensions.get('window').width * 0.5,
                height: Dimensions.get('window').height * 0.2,

                ...getTransformForModalInsideNavigation(),
              }
            ]}>
              <View
                style={{
                  position: 'absolute',
                  padding: 14,
                  right: windowWidth * 0.015,
                  top: windowHeight * 0.01,
                  zIndex: 1000,
                }}>
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    setShowLoyaltyAutomatedReminder3(false);
                  }}>
                  <View style={{}}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Done
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: '100%',
                  height: windowHeight * 0.08,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: Colors.whiteColor,
                  borderTopRightRadius: 12,
                  borderTopLeftRadius: 12,
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 16 : 24,
                  }}>
                  Reminder {isRem1 ? '1' : isRem2 ? '2' : '3'}
                </Text>
              </View>
              <View
                style={{
                  paddingVertical: windowHeight * 0.01,
                }}>
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                  }}>
                  <View
                    style={{
                      borderBottomWidth: switchAutomatedReminderActive3 ? 1 : 0,
                      borderBottomColor: '#EBEDEF',
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingVertical: windowHeight * 0.02,
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Active
                    </Text>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchAutomatedReminderActive3}
                      onSyncPress={(statusTemp) =>
                        setSwitchAutomatedReminderActive3(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                  {switchAutomatedReminderActive3 ? (
                    <View>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          borderBottomColor: '#EBEDEF',
                          flexDirection: 'row',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Send when guest hasn't visited for
                        </Text>
                        {switchMerchant ? (
                          <View
                            style={{
                              backgroundColor: '#fafafa',
                              // backgroundColor: 'green',
                              borderRadius: 4,
                              height: switchMerchant
                                ? windowHeight * 0.08
                                : 35,
                              width: switchMerchant
                                ? windowWidth * 0.13
                                : 145,
                              justifyContent: 'center',
                              fontSize: switchMerchant ? 10 : 16,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              left: windowWidth * -0.002,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                              useNativeAndroidPickerStyle={false}
                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroid: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={manyDaysDropDownItem}
                              value={dropAutomatedReminderModal3}
                              onValueChange={(value) => {
                                setDropAutomatedReminderModal3(value);
                              }}
                            />
                          </View>
                        ) : (
                          <View
                            style={{
                              justifyContent: 'flex-start',
                              flexDirection: 'row',

                              alignItems: 'flex-start',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroidContainer: {
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  justifyContent: 'center',
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,

                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                                inputAndroid: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                viewContainer: {
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                              }}
                              items={manyDaysDropDownItem}
                              value={dropAutomatedReminderModal3}
                              onValueChange={(value) => {
                                setDropAutomatedReminderModal3(value);
                              }}
                            />
                          </View>
                        )}
                      </View>
                      <View
                        style={{
                          // borderWidth: 1,
                          flexDirection: 'row',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          zIndex: -1,
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          What time of the day should it be sent?
                        </Text>
                        <TouchableOpacity
                          onPress={() => {
                            setShowRemTime3(true);
                          }}
                          style={{
                            // height: 50,
                            height: switchMerchant ? 35 : 40,
                            paddingHorizontal: switchMerchant ? 7 : 20,
                            backgroundColor: Colors.fieldtBgColor,
                            //marginBottom: 20,
                            width: switchMerchant ? 100 : 160,
                            //marginHorizontal: 10,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            alignContent: 'center',
                            borderColor: '#E5E5E5',
                            borderWidth: 1,
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            borderRadius: 12,
                          }}>
                          <EvilIcons
                            name="clock"
                            size={switchMerchant ? 20 : 25}
                            color={Colors.primaryColor}
                          />
                          <Text
                            style={{
                              //marginLeft: 15,
                              color: 'black',
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                            }}>
                            {moment(remTime3).format('hh:mm A')}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : null}
                </View>
                <Text
                  style={{
                    textAlign: 'center',
                    paddingVertical: windowHeight * 0.01,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 16,
                    color: '#808B96',
                  }}>
                  Customize after how many days & after what time guests will be
                  reminded via SMS
                </Text>
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalView>
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={showLoyaltyAutomatedCampaign}
          transparent
          animationType="slide">
          <KeyboardAvoidingView
            behavior={'padding'}
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              // minHeight: windowHeight,
            }}>
            <View style={[
              styles.confirmBox,
              {
                width: Dimensions.get('window').width * 0.5,
                height: Dimensions.get('window').height * 0.2,

                ...getTransformForModalInsideNavigation(),
              }
            ]}>
              <View
                style={{
                  position: 'absolute',
                  padding: 14,
                  right: windowWidth * 0.015,
                  top: windowHeight * 0.01,
                  zIndex: 1000,
                }}>
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    setShowLoyaltyAutomatedCampaign(false);
                  }}>
                  <View style={{}}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Done
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: '100%',
                  height: windowHeight * 0.08,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: Colors.whiteColor,
                  borderTopRightRadius: 12,
                  borderTopLeftRadius: 12,
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 16 : 24,
                  }}>
                  {is1st
                    ? '1st visit'
                    : isRisk
                      ? 'At risk'
                      : isLapsed
                        ? 'Lapsed'
                        : isLost
                          ? 'Lost'
                          : isBirthday
                            ? 'Birthday'
                            : isGrowth
                              ? 'Growth'
                              : 'Bis spender'}
                </Text>
              </View>
              <View
                style={{
                  paddingVertical: windowHeight * 0.01,
                }}>
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                  }}>
                  <View
                    style={{
                      borderBottomWidth:
                        (switch1stVisit && is1st) ||
                          (switchRisk && isRisk) ||
                          (switchLapsed && isLapsed) ||
                          (switchLost && isLost) ||
                          (switchBirthday && isBirthday) ||
                          (switchGrowth && isGrowth) ||
                          (switchSpender && isSpender)
                          ? 1
                          : 0,
                      borderBottomColor: '#EBEDEF',
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingVertical: windowHeight * 0.02,
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      t>
                      Active
                    </Text>
                    {is1st ? (
                      <Switch
                        width={42}
                        style={{}}
                        value={switch1stVisit}
                        onSyncPress={(statusTemp) =>
                          setSwitch1stVisit(statusTemp)
                        }
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                    ) : isRisk ? (
                      <Switch
                        width={42}
                        style={{}}
                        value={switchRisk}
                        onSyncPress={(statusTemp) => setSwitchRisk(statusTemp)}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                    ) : isLapsed ? (
                      <Switch
                        width={42}
                        style={{}}
                        value={switchLapsed}
                        onSyncPress={(statusTemp) => setSwitchLapsed(statusTemp)}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                    ) : isLost ? (
                      <Switch
                        width={42}
                        style={{}}
                        value={switchLost}
                        onSyncPress={(statusTemp) => setSwitchLost(statusTemp)}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                    ) : isBirthday ? (
                      <Switch
                        width={42}
                        style={{}}
                        value={switchBirthday}
                        onSyncPress={(statusTemp) =>
                          setSwitchBirthday(statusTemp)
                        }
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                    ) : isGrowth ? (
                      <Switch
                        width={42}
                        style={{}}
                        value={switchGrowth}
                        onSyncPress={(statusTemp) => setSwitchGrowth(statusTemp)}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                    ) : isSpender ? (
                      <Switch
                        width={42}
                        style={{}}
                        value={switchSpender}
                        onSyncPress={(statusTemp) => setSwitchSpender(statusTemp)}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                    ) : null}
                  </View>
                  {(switch1stVisit && is1st) ||
                    (switchRisk && isRisk) ||
                    (switchLapsed && isLapsed) ||
                    (switchLost && isLost) ||
                    (switchBirthday && isBirthday) ||
                    (switchGrowth && isGrowth) ||
                    (switchSpender && isSpender) ? (
                    <View>
                      {!is1st && !isGrowth && !isSpender ? (
                        <View
                          style={{
                            borderBottomWidth: 1,
                            borderBottomColor: '#EBEDEF',
                            flexDirection: 'row',
                            paddingHorizontal:
                              windowWidth * 0.01,
                            paddingVertical:
                              windowHeight * 0.02,
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            zIndex: 1000,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            Send when guest{' '}
                            {isBirthday ? 'birthday is in' : "hasn't visited for"}
                          </Text>
                          <View
                            style={{
                              width: 160,
                              height: switchMerchant ? 35 : 40,
                            }}>
                            {isRisk ? (
                              switchMerchant ? (
                                <View
                                  style={{
                                    backgroundColor: '#fafafa',
                                    // backgroundColor: 'green',
                                    borderRadius: 4,
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    justifyContent: 'center',
                                    fontSize: switchMerchant ? 10 : 16,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    left: windowWidth * -0.002,
                                  }}>
                                  <RNPickerSelect
                                    // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    useNativeAndroidPickerStyle={false}
                                    placeholder={{ label: 'Customer' }}
                                    style={{
                                      inputAndroid: {
                                        fontSize: switchMerchant ? 10 : 16,
                                        paddingVertical: 5,
                                        color: 'black',
                                        textAlign: 'center',
                                      },
                                      inputIOS: {
                                        fontSize: switchMerchant ? 10 : 16,
                                        paddingVertical: 5,
                                        color: 'black',
                                        textAlign: 'center',
                                      },
                                    }}
                                    items={manyDaysDropDownItem}
                                    value={dropRiskVisited}
                                    onValueChange={(value) => {
                                      setDropRiskVisited(value);
                                    }}
                                  />
                                </View>
                              ) : (
                                <View
                                  style={{
                                    justifyContent: 'flex-start',
                                    flexDirection: 'row',

                                    alignItems: 'flex-start',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                  }}>
                                  <RNPickerSelect
                                    // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                    placeholder={{ label: 'Customer' }}
                                    style={{
                                      inputAndroidContainer: {
                                        height: switchMerchant
                                          ? windowHeight * 0.08
                                          : 35,
                                        justifyContent: 'center',
                                        backgroundColor: '#fafafa',
                                        borderRadius: 4,

                                        shadowColor: '#000',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                      },
                                      inputAndroid: {
                                        //backgroundColor: '#fafafa',
                                        color: 'black',
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 16,
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        borderRadius: 5,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        paddingHorizontal: 10,
                                        height: 35,
                                        paddingLeft: 12,
                                        textAlign: 'center',
                                      },
                                      inputIOS: {
                                        //backgroundColor: '#fafafa',
                                        color: 'black',
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 16,
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        borderRadius: 5,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        paddingHorizontal: 10,
                                        height: 35,
                                        paddingLeft: 12,
                                        textAlign: 'center',
                                      },
                                      viewContainer: {
                                        backgroundColor: '#fafafa',
                                        borderRadius: 4,
                                        height: switchMerchant
                                          ? windowHeight * 0.08
                                          : 35,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        justifyContent: 'center',
                                        fontSize: switchMerchant ? 10 : 16,
                                        shadowColor: '#000',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                      },
                                    }}
                                    items={manyDaysDropDownItem}
                                    value={dropRiskVisited}
                                    onValueChange={(value) => {
                                      setDropRiskVisited(value);
                                    }}
                                  />
                                </View>
                              )
                            ) : isLapsed ? (
                              switchMerchant ? (
                                <View
                                  style={{
                                    backgroundColor: '#fafafa',
                                    // backgroundColor: 'green',
                                    borderRadius: 4,
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    justifyContent: 'center',
                                    fontSize: switchMerchant ? 10 : 16,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    left: windowWidth * -0.002,
                                  }}>
                                  <RNPickerSelect
                                    // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    useNativeAndroidPickerStyle={false}
                                    placeholder={{ label: 'Customer' }}
                                    style={{
                                      inputAndroid: {
                                        fontSize: switchMerchant ? 10 : 16,
                                        paddingVertical: 5,
                                        color: 'black',
                                        textAlign: 'center',
                                      },
                                      inputIOS: {
                                        fontSize: switchMerchant ? 10 : 16,
                                        paddingVertical: 5,
                                        color: 'black',
                                        textAlign: 'center',
                                      },
                                    }}
                                    items={manyDaysDropDownItem}
                                    value={dropLapsedVisited}
                                    onValueChange={(value) => {
                                      setDropLapsedVisited(value);
                                    }}
                                  />
                                </View>
                              ) : (
                                <View
                                  style={{
                                    justifyContent: 'flex-start',
                                    flexDirection: 'row',

                                    alignItems: 'flex-start',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                  }}>
                                  <RNPickerSelect
                                    // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                    placeholder={{ label: 'Customer' }}
                                    style={{
                                      inputAndroidContainer: {
                                        height: switchMerchant
                                          ? windowHeight * 0.08
                                          : 35,
                                        justifyContent: 'center',
                                        backgroundColor: '#fafafa',
                                        borderRadius: 4,

                                        shadowColor: '#000',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                      },
                                      inputAndroid: {
                                        //backgroundColor: '#fafafa',
                                        color: 'black',
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 16,
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        borderRadius: 5,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        paddingHorizontal: 10,
                                        height: 35,
                                        paddingLeft: 12,
                                        textAlign: 'center',
                                      },
                                      inputIOS: {
                                        //backgroundColor: '#fafafa',
                                        color: 'black',
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 16,
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        borderRadius: 5,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        paddingHorizontal: 10,
                                        height: 35,
                                        paddingLeft: 12,
                                        textAlign: 'center',
                                      },
                                      viewContainer: {
                                        backgroundColor: '#fafafa',
                                        borderRadius: 4,
                                        height: switchMerchant
                                          ? windowHeight * 0.08
                                          : 35,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        justifyContent: 'center',
                                        fontSize: switchMerchant ? 10 : 16,
                                        shadowColor: '#000',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                      },
                                    }}
                                    items={manyDaysDropDownItem}
                                    value={dropLapsedVisited}
                                    onValueChange={(value) => {
                                      setDropLapsedVisited(value);
                                    }}
                                  />
                                </View>
                              )
                            ) : isLost ? (
                              switchMerchant ? (
                                <View
                                  style={{
                                    backgroundColor: '#fafafa',
                                    // backgroundColor: 'green',
                                    borderRadius: 4,
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    justifyContent: 'center',
                                    fontSize: switchMerchant ? 10 : 16,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    left: windowWidth * -0.002,
                                  }}>
                                  <RNPickerSelect
                                    // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    useNativeAndroidPickerStyle={false}
                                    placeholder={{ label: 'Customer' }}
                                    style={{
                                      inputAndroid: {
                                        fontSize: switchMerchant ? 10 : 16,
                                        paddingVertical: 5,
                                        color: 'black',
                                        textAlign: 'center',
                                      },
                                      inputIOS: {
                                        fontSize: switchMerchant ? 10 : 16,
                                        paddingVertical: 5,
                                        color: 'black',
                                        textAlign: 'center',
                                      },
                                    }}
                                    items={manyDaysDropDownItem}
                                    value={dropLostVisited}
                                    onValueChange={(value) => {
                                      setDropLostVisited(value);
                                    }}
                                  />
                                </View>
                              ) : (
                                <View
                                  style={{
                                    justifyContent: 'flex-start',
                                    flexDirection: 'row',

                                    alignItems: 'flex-start',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                  }}>
                                  <RNPickerSelect
                                    // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                    placeholder={{ label: 'Customer' }}
                                    style={{
                                      inputAndroidContainer: {
                                        height: switchMerchant
                                          ? windowHeight * 0.08
                                          : 35,
                                        justifyContent: 'center',
                                        backgroundColor: '#fafafa',
                                        borderRadius: 4,

                                        shadowColor: '#000',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                      },
                                      inputAndroid: {
                                        //backgroundColor: '#fafafa',
                                        color: 'black',
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 16,
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        borderRadius: 5,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        paddingHorizontal: 10,
                                        height: 35,
                                        paddingLeft: 12,
                                        textAlign: 'center',
                                      },
                                      inputIOS: {
                                        //backgroundColor: '#fafafa',
                                        color: 'black',
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 16,
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        borderRadius: 5,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        paddingHorizontal: 10,
                                        height: 35,
                                        paddingLeft: 12,
                                        textAlign: 'center',
                                      },
                                      viewContainer: {
                                        backgroundColor: '#fafafa',
                                        borderRadius: 4,
                                        height: switchMerchant
                                          ? windowHeight * 0.08
                                          : 35,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        justifyContent: 'center',
                                        fontSize: switchMerchant ? 10 : 16,
                                        shadowColor: '#000',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                      },
                                    }}
                                    items={manyDaysDropDownItem}
                                    value={dropLostVisited}
                                    onValueChange={(value) => {
                                      setDropLostVisited(value);
                                    }}
                                  />
                                </View>
                              )
                            ) : isBirthday ? (
                              switchMerchant ? (
                                <View
                                  style={{
                                    backgroundColor: '#fafafa',
                                    // backgroundColor: 'green',
                                    borderRadius: 4,
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    justifyContent: 'center',
                                    fontSize: switchMerchant ? 10 : 16,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    left: windowWidth * -0.002,
                                  }}>
                                  <RNPickerSelect
                                    // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    useNativeAndroidPickerStyle={false}
                                    placeholder={{ label: 'Customer' }}
                                    style={{
                                      inputAndroid: {
                                        fontSize: switchMerchant ? 10 : 16,
                                        paddingVertical: 5,
                                        color: 'black',
                                        textAlign: 'center',
                                      },
                                      inputIOS: {
                                        fontSize: switchMerchant ? 10 : 16,
                                        paddingVertical: 5,
                                        color: 'black',
                                        textAlign: 'center',
                                      },
                                    }}
                                    items={manyDaysDropDownItem}
                                    value={dropBirthdayIsIn}
                                    onValueChange={(value) => {
                                      setDropBirthdayIsIn(value);
                                    }}
                                  />
                                </View>
                              ) : (
                                <View
                                  style={{
                                    justifyContent: 'flex-start',
                                    flexDirection: 'row',

                                    alignItems: 'flex-start',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                  }}>
                                  <RNPickerSelect
                                    // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                    // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                    placeholder={{ label: 'Customer' }}
                                    style={{
                                      inputAndroidContainer: {
                                        height: switchMerchant
                                          ? windowHeight * 0.08
                                          : 35,
                                        justifyContent: 'center',
                                        backgroundColor: '#fafafa',
                                        borderRadius: 4,

                                        shadowColor: '#000',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                      },
                                      inputAndroid: {
                                        //backgroundColor: '#fafafa',
                                        color: 'black',
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 16,
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        borderRadius: 5,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        paddingHorizontal: 10,
                                        height: 35,
                                        paddingLeft: 12,
                                        textAlign: 'center',
                                      },
                                      inputIOS: {
                                        //backgroundColor: '#fafafa',
                                        color: 'black',
                                        fontFamily: 'NunitoSans-Regular',
                                        fontSize: switchMerchant ? 10 : 16,
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        borderRadius: 5,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        paddingHorizontal: 10,
                                        height: 35,
                                        paddingLeft: 12,
                                        textAlign: 'center',
                                      },
                                      viewContainer: {
                                        backgroundColor: '#fafafa',
                                        borderRadius: 4,
                                        height: switchMerchant
                                          ? windowHeight * 0.08
                                          : 35,
                                        width: switchMerchant
                                          ? windowWidth * 0.13
                                          : 145,
                                        justifyContent: 'center',
                                        fontSize: switchMerchant ? 10 : 16,
                                        shadowColor: '#000',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                      },
                                    }}
                                    items={manyDaysDropDownItem}
                                    value={dropBirthdayIsIn}
                                    onValueChange={(value) => {
                                      setDropBirthdayIsIn(value);
                                    }}
                                  />
                                </View>
                              )
                            ) : null}
                          </View>
                        </View>
                      ) : isGrowth ? (
                        <View
                          style={{
                            borderBottomWidth: 1,
                            borderBottomColor: '#EBEDEF',
                            flexDirection: 'row',
                            paddingHorizontal:
                              windowWidth * 0.01,
                            paddingVertical:
                              windowHeight * 0.02,
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            zIndex: 1000,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            Send after every
                          </Text>
                          {switchMerchant ? (
                            <View
                              style={{
                                backgroundColor: '#fafafa',
                                // backgroundColor: 'green',
                                borderRadius: 4,
                                height: switchMerchant
                                  ? windowHeight * 0.08
                                  : 35,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : 145,
                                justifyContent: 'center',
                                fontSize: switchMerchant ? 10 : 16,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                left: windowWidth * -0.002,
                              }}>
                              <RNPickerSelect
                                // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                useNativeAndroidPickerStyle={false}
                                placeholder={{ label: 'Customer' }}
                                style={{
                                  inputAndroid: {
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingVertical: 5,
                                    color: 'black',
                                    textAlign: 'center',
                                  },
                                  inputIOS: {
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingVertical: 5,
                                    color: 'black',
                                    textAlign: 'center',
                                  },
                                }}
                                items={manyVisitDropDownItem}
                                value={dropGrowthAfter}
                                onValueChange={(value) => {
                                  setDropGrowthAfter(value);
                                }}
                              />
                            </View>
                          ) : (
                            <View
                              style={{
                                justifyContent: 'flex-start',
                                flexDirection: 'row',

                                alignItems: 'flex-start',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,
                              }}>
                              <RNPickerSelect
                                // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                placeholder={{ label: 'Customer' }}
                                style={{
                                  inputAndroidContainer: {
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    justifyContent: 'center',
                                    backgroundColor: '#fafafa',
                                    borderRadius: 4,

                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                  },
                                  inputAndroid: {
                                    //backgroundColor: '#fafafa',
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 16,
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    borderRadius: 5,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    paddingHorizontal: 10,
                                    height: 35,
                                    paddingLeft: 12,
                                    textAlign: 'center',
                                  },
                                  inputIOS: {
                                    //backgroundColor: '#fafafa',
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 16,
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    borderRadius: 5,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    paddingHorizontal: 10,
                                    height: 35,
                                    paddingLeft: 12,
                                    textAlign: 'center',
                                  },
                                  viewContainer: {
                                    backgroundColor: '#fafafa',
                                    borderRadius: 4,
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    justifyContent: 'center',
                                    fontSize: switchMerchant ? 10 : 16,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                  },
                                }}
                                items={manyVisitDropDownItem}
                                value={dropGrowthAfter}
                                onValueChange={(value) => {
                                  setDropGrowthAfter(value);
                                }}
                              />
                            </View>
                          )}
                        </View>
                      ) : isSpender ? (
                        <View
                          style={{
                            borderBottomWidth: 1,
                            borderBottomColor: '#EBEDEF',
                            flexDirection: 'row',
                            paddingHorizontal:
                              windowWidth * 0.01,
                            paddingVertical:
                              windowHeight * 0.02,
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            zIndex: -1,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            Send every time guest spends
                          </Text>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <Text
                              style={{
                                paddingRight:
                                  windowWidth * 0.01,
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 14,
                              }}>
                              RM
                            </Text>
                            <TextInput
                              placeholder="0.00"
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: 160,
                                height: switchMerchant ? 35 : 40,
                                borderRadius: 5,
                                padding: 5,
                                marginVertical: 5,
                                // borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingLeft: 10,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Regular',
                              }}
                              //iOS
                              // clearTextOnFocus
                              selectTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              // onFocus={() => {
                              //   setTemp(spenderGuestSpend)
                              //   setSpenderGuestSpend('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (spenderGuestSpend == '') {
                              //     setSpenderGuestSpend(temp);
                              //   }
                              // }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                setSpenderGuestSpend(text);
                              }}
                              keyboardType={'decimal-pad'}
                              defaultValue={spenderGuestSpend}
                            />
                          </View>
                        </View>
                      ) : null}
                      <View
                        style={{
                          borderBottomWidth: 1,
                          flexDirection: 'row',
                          borderBottomColor: '#EBEDEF',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          What time of the day should it be sent?
                        </Text>
                        {is1st ? (
                          <TouchableOpacity
                            onPress={() => {
                              setShow1stVisit(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(fstVisitTime).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        ) : isRisk ? (
                          <TouchableOpacity
                            onPress={() => {
                              setShowRisk(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(riskTime).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        ) : isLapsed ? (
                          <TouchableOpacity
                            onPress={() => {
                              setShowLapsed(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(lapsedTime).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        ) : isLost ? (
                          <TouchableOpacity
                            onPress={() => {
                              setShowLost(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(lostTime).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        ) : isBirthday ? (
                          <TouchableOpacity
                            onPress={() => {
                              setShowBirthday(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(birthdayTime).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        ) : isGrowth ? (
                          <TouchableOpacity
                            onPress={() => {
                              setShowGrowth(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(GrowthTime).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        ) : isSpender ? (
                          <TouchableOpacity
                            onPress={() => {
                              setShowSpender(true);
                            }}
                            style={{
                              // height: 50,
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              //marginBottom: 20,
                              width: switchMerchant ? 100 : 160,
                              //marginHorizontal: 10,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              borderRadius: 12,
                            }}>
                            <EvilIcons
                              name="clock"
                              size={switchMerchant ? 20 : 25}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                //marginLeft: 15,
                                color: 'black',
                                fontSize: switchMerchant ? 10 : 14,
                                fontFamily: 'NunitoSans-Regular',
                              }}>
                              {moment(SpenderTime).format('hh:mm A')}
                            </Text>
                          </TouchableOpacity>
                        ) : null}
                      </View>
                      <View
                        style={{
                          // borderWidth: 1,
                          flexDirection: 'row',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          zIndex: 999,
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Expiration
                        </Text>
                        <View
                          style={{
                            width: 160,
                            height: switchMerchant ? 35 : 40,
                          }}>
                          {is1st ? (
                            switchMerchant ? (
                              <View
                                style={{
                                  backgroundColor: '#fafafa',
                                  // backgroundColor: 'green',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  left: windowWidth * -0.002,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  useNativeAndroidPickerStyle={false}
                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroid: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={drop1stExp}
                                  onValueChange={(value) => {
                                    setDrop1stExp(value);
                                  }}
                                />
                              </View>
                            ) : (
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row',

                                  alignItems: 'flex-start',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroidContainer: {
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,

                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      justifyContent: 'center',
                                      fontSize: switchMerchant ? 10 : 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={drop1stExp}
                                  onValueChange={(value) => {
                                    setDrop1stExp(value);
                                  }}
                                />
                              </View>
                            )
                          ) : isRisk ? (
                            switchMerchant ? (
                              <View
                                style={{
                                  backgroundColor: '#fafafa',
                                  // backgroundColor: 'green',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  left: windowWidth * -0.002,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  useNativeAndroidPickerStyle={false}
                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroid: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropRiskExp}
                                  onValueChange={(value) => {
                                    setDropRiskExp(value);
                                  }}
                                />
                              </View>
                            ) : (
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row',

                                  alignItems: 'flex-start',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroidContainer: {
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,

                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      justifyContent: 'center',
                                      fontSize: switchMerchant ? 10 : 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropRiskExp}
                                  onValueChange={(value) => {
                                    setDropRiskExp(value);
                                  }}
                                />
                              </View>
                            )
                          ) : isLapsed ? (
                            switchMerchant ? (
                              <View
                                style={{
                                  backgroundColor: '#fafafa',
                                  // backgroundColor: 'green',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  left: windowWidth * -0.002,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  useNativeAndroidPickerStyle={false}
                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroid: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropLapsedExp}
                                  onValueChange={(value) => {
                                    setDropLapsedExp(value);
                                  }}
                                />
                              </View>
                            ) : (
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row',

                                  alignItems: 'flex-start',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroidContainer: {
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,

                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      justifyContent: 'center',
                                      fontSize: switchMerchant ? 10 : 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropLapsedExp}
                                  onValueChange={(value) => {
                                    setDropLapsedExp(value);
                                  }}
                                />
                              </View>
                            )
                          ) : isLost ? (
                            switchMerchant ? (
                              <View
                                style={{
                                  backgroundColor: '#fafafa',
                                  // backgroundColor: 'green',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  left: windowWidth * -0.002,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  useNativeAndroidPickerStyle={false}
                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroid: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropLostExp}
                                  onValueChange={(value) => {
                                    setDropLostExp(value);
                                  }}
                                />
                              </View>
                            ) : (
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row',

                                  alignItems: 'flex-start',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroidContainer: {
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,

                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      justifyContent: 'center',
                                      fontSize: switchMerchant ? 10 : 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropLostExp}
                                  onValueChange={(value) => {
                                    setDropLostExp(value);
                                  }}
                                />
                              </View>
                            )
                          ) : isBirthday ? (
                            switchMerchant ? (
                              <View
                                style={{
                                  backgroundColor: '#fafafa',
                                  // backgroundColor: 'green',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  left: windowWidth * -0.002,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  useNativeAndroidPickerStyle={false}
                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroid: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropBirthdayExp}
                                  onValueChange={(value) => {
                                    setDropBirthdayExp(value);
                                  }}
                                />
                              </View>
                            ) : (
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row',

                                  alignItems: 'flex-start',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroidContainer: {
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,

                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      justifyContent: 'center',
                                      fontSize: switchMerchant ? 10 : 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropBirthdayExp}
                                  onValueChange={(value) => {
                                    setDropBirthdayExp(value);
                                  }}
                                />
                              </View>
                            )
                          ) : isGrowth ? (
                            switchMerchant ? (
                              <View
                                style={{
                                  backgroundColor: '#fafafa',
                                  // backgroundColor: 'green',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  left: windowWidth * -0.002,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  useNativeAndroidPickerStyle={false}
                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroid: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropGrowthExp}
                                  onValueChange={(value) => {
                                    setDropGrowthExp(value);
                                  }}
                                />
                              </View>
                            ) : (
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row',

                                  alignItems: 'flex-start',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroidContainer: {
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,

                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      justifyContent: 'center',
                                      fontSize: switchMerchant ? 10 : 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropGrowthExp}
                                  onValueChange={(value) => {
                                    setDropGrowthExp(value);
                                  }}
                                />
                              </View>
                            )
                          ) : isSpender ? (
                            switchMerchant ? (
                              <View
                                style={{
                                  backgroundColor: '#fafafa',
                                  // backgroundColor: 'green',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  left: windowWidth * -0.002,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  useNativeAndroidPickerStyle={false}
                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroid: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropSpenderExp}
                                  onValueChange={(value) => {
                                    setDropSpenderExp(value);
                                  }}
                                />
                              </View>
                            ) : (
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row',

                                  alignItems: 'flex-start',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                  placeholder={{ label: 'Customer' }}
                                  style={{
                                    inputAndroidContainer: {
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,

                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 35,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 35,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      justifyContent: 'center',
                                      fontSize: switchMerchant ? 10 : 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                  }}
                                  items={manyDaysDropDownItem}
                                  value={dropSpenderExp}
                                  onValueChange={(value) => {
                                    setDropSpenderExp(value);
                                  }}
                                />
                              </View>
                            )
                          ) : null}
                        </View>
                      </View>
                      <ScrollView
                        style={{
                          borderBottomWidth: 1,
                          borderColor: 'black',
                          height: windowHeight * 0.34,
                        }}>
                        <View
                          style={{
                            backgroundColor: 'rgb(209, 212, 212)',
                            paddingVertical:
                              windowHeight * 0.03,
                            paddingHorizontal:
                              windowWidth * 0.01,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 9 : 18,
                            }}>
                            SMS text
                          </Text>
                          <View
                            style={{
                              flexDirection: 'row',
                              width: '100%',
                              justifyContent: 'space-between',
                            }}>
                            <Text
                              style={{
                                borderRadius: 999,
                                padding: 1,
                                borderWidth: 1,
                                textAlignVertical: 'center',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}>
                              {`  en  `}
                            </Text>
                            <TouchableOpacity
                              style={{
                                padding: 5,
                                borderRadius: 999,
                                borderWidth: 1,
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                }}>
                                Submit
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                        <View style={{}}>
                          {is1st ? (
                            <TextInput
                              style={[
                                {
                                  backgroundColor: Colors.whiteColor,
                                  width: '100%',
                                  height: windowHeight * 0.15,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  paddingVertical:
                                    windowHeight * 0.02,
                                  paddingHorizontal:
                                    windowWidth * 0.01,
                                  textAlignVertical: 'top',
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                              placeholder="SMS Text here..."
                              placeholderTextColor={Colors.descriptionColor}
                              //iOS
                              // clearTextOnFocus
                              selectTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              // onFocus={() => {
                              //   setTemp(fstSmsText)
                              //   setFstSmsText('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (fstSmsText == '') {
                              //     setFstSmsText(temp);
                              //   }
                              // }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                setFstSmsText(text);
                              }}
                              defaultValue={fstSmsText}
                              multiline
                            />
                          ) : isRisk ? (
                            <TextInput
                              style={[
                                {
                                  backgroundColor: Colors.whiteColor,
                                  width: '100%',
                                  height: windowHeight * 0.15,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  paddingVertical:
                                    windowHeight * 0.02,
                                  paddingHorizontal:
                                    windowWidth * 0.01,
                                  textAlignVertical: 'top',
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                              placeholder="SMS Text here..."
                              placeholderTextColor={Colors.descriptionColor}
                              //iOS
                              // clearTextOnFocus
                              selectTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              // onFocus={() => {
                              //   setTemp(riskSmsText)
                              //   setRiskSmsText('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (riskSmsText == '') {
                              //     setRiskSmsText(temp);
                              //   }
                              // }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                setRiskSmsText(text);
                              }}
                              defaultValue={riskSmsText}
                              multiline
                            />
                          ) : isLapsed ? (
                            <TextInput
                              style={[
                                {
                                  backgroundColor: Colors.whiteColor,
                                  width: '100%',
                                  height: windowHeight * 0.15,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  paddingVertical:
                                    windowHeight * 0.02,
                                  paddingHorizontal:
                                    windowWidth * 0.01,
                                  textAlignVertical: 'top',
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                              placeholder="SMS Text here..."
                              placeholderTextColor={Colors.descriptionColor}
                              //iOS
                              // clearTextOnFocus
                              selectTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              // onFocus={() => {
                              //   setTemp(lapsedSmsText)
                              //   setLapsedSmsText('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (lapsedSmsText == '') {
                              //     setLapsedSmsText(temp);
                              //   }
                              // }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                setLapsedSmsText(text);
                              }}
                              defaultValue={lapsedSmsText}
                              multiline
                            />
                          ) : isLost ? (
                            <TextInput
                              style={[
                                {
                                  backgroundColor: Colors.whiteColor,
                                  width: '100%',
                                  height: windowHeight * 0.15,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  paddingVertical:
                                    windowHeight * 0.02,
                                  paddingHorizontal:
                                    windowWidth * 0.01,
                                  textAlignVertical: 'top',
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                              placeholder="SMS Text here..."
                              placeholderTextColor={Colors.descriptionColor}
                              //iOS
                              // clearTextOnFocus
                              selectTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              // onFocus={() => {
                              //   setTemp(lostSmsText)
                              //   setLostSmsText('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (lostSmsText == '') {
                              //     setLostSmsText(temp);
                              //   }
                              // }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                setLostSmsText(text);
                              }}
                              defaultValue={lostSmsText}
                              multiline
                            />
                          ) : isBirthday ? (
                            <TextInput
                              style={[
                                {
                                  backgroundColor: Colors.whiteColor,
                                  width: '100%',
                                  height: windowHeight * 0.15,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  paddingVertical:
                                    windowHeight * 0.02,
                                  paddingHorizontal:
                                    windowWidth * 0.01,
                                  textAlignVertical: 'top',
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                              placeholder="SMS Text here..."
                              placeholderTextColor={Colors.descriptionColor}
                              //iOS
                              // clearTextOnFocus
                              selectTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              // onFocus={() => {
                              //   setTemp(birthdaySmsText)
                              //   setBirthdaySmsText('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (birthdaySmsText == '') {
                              //     setBirthdaySmsText(temp);
                              //   }
                              // }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                setBirthdaySmsText(text);
                              }}
                              defaultValue={birthdaySmsText}
                              multiline
                            />
                          ) : isGrowth ? (
                            <TextInput
                              style={[
                                {
                                  backgroundColor: Colors.whiteColor,
                                  width: '100%',
                                  height: windowHeight * 0.15,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  paddingVertical:
                                    windowHeight * 0.02,
                                  paddingHorizontal:
                                    windowWidth * 0.01,
                                  textAlignVertical: 'top',
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                              placeholder="SMS Text here..."
                              placeholderTextColor={Colors.descriptionColor}
                              //iOS
                              // clearTextOnFocus
                              selectTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              // onFocus={() => {
                              //   setTemp(growthSmsText)
                              //   setGrowthSmsText('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (growthSmsText == '') {
                              //     setGrowthSmsText(temp);
                              //   }
                              // }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                setGrowthSmsText(text);
                              }}
                              defaultValue={growthSmsText}
                              multiline
                            />
                          ) : isSpender ? (
                            <TextInput
                              style={[
                                {
                                  backgroundColor: Colors.whiteColor,
                                  width: '100%',
                                  height: windowHeight * 0.15,
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: '#E5E5E5',
                                  paddingVertical:
                                    windowHeight * 0.02,
                                  paddingHorizontal:
                                    windowWidth * 0.01,
                                  textAlignVertical: 'top',
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Regular',
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                              placeholder="SMS Text here..."
                              placeholderTextColor={Colors.descriptionColor}
                              //iOS
                              // clearTextOnFocus
                              selectTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              // onFocus={() => {
                              //   setTemp(spenderSmsText)
                              //   setSpenderSmsText('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (spenderSmsText == '') {
                              //     setSpenderSmsText(temp);
                              //   }
                              // }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                setSpenderSmsText(text);
                              }}
                              defaultValue={spenderSmsText}
                              multiline
                            />
                          ) : null}
                        </View>
                        <Text
                          style={{
                            paddingTop: windowHeight * 0.02,
                            paddingBottom: windowHeight * 0.01,
                            backgroundColor: 'rgb(209, 212, 212)',
                            paddingHorizontal:
                              windowWidth * 0.01,
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          SMS Preview
                        </Text>
                        <Text
                          style={{
                            paddingBottom: windowHeight * 0.01,
                            borderBottomWidth: 1,
                            backgroundColor: 'rgb(209, 212, 212)',
                            borderBottomColor: '#EBEDEF',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            paddingHorizontal:
                              windowWidth * 0.01,
                          }}>
                          {is1st
                            ? fstSmsText === ''
                              ? 'SMS Preview Here...'
                              : `${fstSmsText}`
                            : isRisk
                              ? riskSmsText === ''
                                ? 'SMS Preview Here...'
                                : `${riskSmsText}`
                              : isLapsed
                                ? lapsedSmsText === ''
                                  ? 'SMS Preview Here...'
                                  : `${lapsedSmsText}`
                                : isLost
                                  ? lostSmsText === ''
                                    ? 'SMS Preview Here...'
                                    : `${lostSmsText}`
                                  : isBirthday
                                    ? birthdaySmsText === ''
                                      ? 'SMS Preview Here...'
                                      : `${birthdaySmsText}`
                                    : isGrowth
                                      ? growthSmsText === ''
                                        ? 'SMS Preview Here...'
                                        : `${growthSmsText}`
                                      : isSpender
                                        ? spenderSmsText === ''
                                          ? 'SMS Preview Here...'
                                          : `${spenderSmsText}`
                                        : null}
                        </Text>
                      </ScrollView>
                    </View>
                  ) : null}
                </View>
                <Text
                  style={{
                    textAlign: 'center',
                    paddingVertical: windowHeight * 0.01,
                    zIndex: -1,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                    color: '#808B96',
                  }}>
                  We suggest including a reward, such as 2-for-1 or a discount, to
                  incentivize guests to come back.
                </Text>
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalView>
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={showLoyaltyAutomatedSignUp}
          transparent
          animationType="slide">
          <KeyboardAvoidingView
            behavior={'padding'}
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              // minHeight: windowHeight,
            }}>
            <View style={[
              styles.confirmBox,
              {
                width: Dimensions.get('window').width * 0.5,
                height: Dimensions.get('window').height * 0.2,

                ...getTransformForModalInsideNavigation(),
              }
            ]}>
              <View
                style={{
                  position: 'absolute',
                  padding: 14,
                  right: windowWidth * 0.015,
                  top: windowHeight * 0.01,
                  zIndex: 1000,
                }}>
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    setShowLoyaltyAutomatedSignUp(false);
                  }}>
                  <View style={{}}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Done
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  width: '100%',
                  height: windowHeight * 0.08,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: Colors.whiteColor,
                  borderTopRightRadius: 12,
                  borderTopLeftRadius: 12,
                }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 16 : 24,
                  }}>
                  Sign up rewards
                </Text>
              </View>
              <View
                style={{
                  paddingVertical: windowHeight * 0.01,
                }}>
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                  }}>
                  <View
                    style={{
                      borderBottomWidth: switchSignup ? 1 : 0,
                      borderBottomColor: '#EBEDEF',
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingVertical: windowHeight * 0.02,
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Active
                    </Text>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchSignup}
                      onSyncPress={(statusTemp) => setSwitchSignup(statusTemp)}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                  {switchSignup ? (
                    <View>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          borderBottomColor: '#EBEDEF',
                          flexDirection: 'row',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          zIndex: 1000,
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Apply to
                        </Text>
                        {switchMerchant ? (
                          <View
                            style={{
                              backgroundColor: '#fafafa',
                              // backgroundColor: 'green',
                              borderRadius: 4,
                              height: switchMerchant
                                ? windowHeight * 0.08
                                : 35,
                              width: switchMerchant
                                ? windowWidth * 0.13
                                : 145,
                              justifyContent: 'center',
                              fontSize: switchMerchant ? 10 : 16,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              left: windowWidth * -0.002,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                              useNativeAndroidPickerStyle={false}
                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroid: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={[
                                {
                                  label: 'Self-Signup Link',
                                  value: 'SELF-SIGNUP_LINK',
                                },
                                {
                                  label: 'In-House Signups',
                                  value: 'IN-HOUSE_SIGNUPS',
                                },
                                {
                                  label: 'Self-Signup Link & In-House Signups',
                                  VALUE: 'SELF-SIGNUP_LINK_&_IN-HOUSE_SIGNUPS',
                                },
                              ]}
                              value={dropSignupApply}
                              onValueChange={(value) => {
                                setDropSignupApply(value);
                              }}
                            />
                          </View>
                        ) : (
                          <View
                            style={{
                              justifyContent: 'flex-start',
                              flexDirection: 'row',

                              alignItems: 'flex-start',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroidContainer: {
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  justifyContent: 'center',
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,

                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                                inputAndroid: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                viewContainer: {
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                              }}
                              items={[
                                {
                                  label: 'Self-Signup Link',
                                  value: 'SELF-SIGNUP_LINK',
                                },
                                {
                                  label: 'In-House Signups',
                                  value: 'IN-HOUSE_SIGNUPS',
                                },
                                {
                                  label: 'Self-Signup Link & In-House Signups',
                                  VALUE: 'SELF-SIGNUP_LINK_&_IN-HOUSE_SIGNUPS',
                                },
                              ]}
                              value={dropSignupApply}
                              onValueChange={(value) => {
                                setDropSignupApply(value);
                              }}
                            />
                          </View>
                        )}
                      </View>
                      <View
                        style={{
                          borderBottomWidth: 1,
                          borderBottomColor: '#EBEDEF',
                          flexDirection: 'row',
                          paddingHorizontal:
                            windowWidth * 0.01,
                          paddingVertical: windowHeight * 0.02,
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Reward type
                        </Text>
                        {switchMerchant ? (
                          <View
                            style={{
                              backgroundColor: '#fafafa',
                              // backgroundColor: 'green',
                              borderRadius: 4,
                              height: switchMerchant
                                ? windowHeight * 0.08
                                : 35,
                              width: switchMerchant
                                ? windowWidth * 0.13
                                : 145,
                              justifyContent: 'center',
                              fontSize: switchMerchant ? 10 : 16,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              left: windowWidth * -0.002,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                              useNativeAndroidPickerStyle={false}
                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroid: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  fontSize: switchMerchant ? 10 : 16,
                                  paddingVertical: 5,
                                  color: 'black',
                                  textAlign: 'center',
                                },
                              }}
                              items={[
                                {
                                  label: 'Credit',
                                  value: 'CASHBACK_CREDIT',
                                },
                                {
                                  label: 'Custom Rewards',
                                  value: 'CUSTOM_REWARDS',
                                },
                              ]}
                              value={dropSignupReward}
                              onValueChange={(value) => {
                                setDropSignupReward(value);
                              }}
                            />
                          </View>
                        ) : (
                          <View
                            style={{
                              justifyContent: 'flex-start',
                              flexDirection: 'row',

                              alignItems: 'flex-start',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}>
                            <RNPickerSelect
                              // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                              // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                              placeholder={{ label: 'Customer' }}
                              style={{
                                inputAndroidContainer: {
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  justifyContent: 'center',
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,

                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                                inputAndroid: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                inputIOS: {
                                  //backgroundColor: '#fafafa',
                                  color: 'black',
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 16,
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  borderRadius: 5,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  paddingLeft: 12,
                                  textAlign: 'center',
                                },
                                viewContainer: {
                                  backgroundColor: '#fafafa',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: switchMerchant
                                    ? windowWidth * 0.13
                                    : 145,
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                },
                              }}
                              items={[
                                {
                                  label: 'Credit',
                                  value: 'CASHBACK_CREDIT',
                                },
                                {
                                  label: 'Custom Rewards',
                                  value: 'CUSTOM_REWARDS',
                                },
                              ]}
                              value={dropSignupReward}
                              onValueChange={(value) => {
                                setDropSignupReward(value);
                              }}
                            />
                          </View>
                        )}
                      </View>
                      {dropSignupReward === 'CUSTOM_REWARDS' ? (
                        <View
                          style={{
                            // borderWidth: 1,
                            flexDirection: 'row',
                            paddingHorizontal:
                              windowWidth * 0.01,
                            paddingVertical:
                              windowHeight * 0.02,
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            zIndex: -1,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            Expiration
                          </Text>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <Text
                              style={{
                                paddingRight:
                                  windowWidth * 0.01,
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}>
                              RM
                            </Text>
                            <TextInput
                              placeholder="0.00"
                              placeholderTextColor={Platform.select({
                                ios: '#a9a9a9',
                              })}
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: 200,
                                height: switchMerchant ? 35 : 40,
                                borderRadius: 5,
                                padding: 5,
                                marginVertical: 5,
                                // borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingLeft: 10,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Regular',
                              }}
                              //iOS
                              // clearTextOnFocus
                              selectTextOnFocus
                              //////////////////////////////////////////////
                              //Android
                              // onFocus={() => {
                              //   setTemp(signupCashback)
                              //   setSignupCashback('');
                              // }}
                              ///////////////////////////////////////////////
                              //When textinput is not selected
                              // onEndEditing={() => {
                              //   if (signupCashback == '') {
                              //     setSignupCashback(temp);
                              //   }
                              // }}
                              //////////////////////////////////////////////
                              onChangeText={(text) => {
                                setSignupCashback(parseValidPriceText(text));
                              }}
                              keyboardType={'decimal-pad'}
                              defaultValue={signupCashback}
                            />
                          </View>
                        </View>
                      ) : (
                        <View
                          style={{
                            // borderWidth: 1,
                            flexDirection: 'row',
                            paddingHorizontal:
                              windowWidth * 0.01,
                            paddingVertical:
                              windowHeight * 0.02,
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            zIndex: -1,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            Signup points amount
                          </Text>
                          {switchMerchant ? (
                            <View
                              style={{
                                backgroundColor: '#fafafa',
                                // backgroundColor: 'green',
                                borderRadius: 4,
                                height: switchMerchant
                                  ? windowHeight * 0.08
                                  : 35,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : 145,
                                justifyContent: 'center',
                                fontSize: switchMerchant ? 10 : 16,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                left: windowWidth * -0.002,
                              }}>
                              <RNPickerSelect
                                // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                useNativeAndroidPickerStyle={false}
                                placeholder={{ label: 'Customer' }}
                                style={{
                                  inputAndroid: {
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingVertical: 5,
                                    color: 'black',
                                    textAlign: 'center',
                                  },
                                  inputIOS: {
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingVertical: 5,
                                    color: 'black',
                                    textAlign: 'center',
                                  },
                                }}
                                items={manyDaysDropDownItem}
                                value={dropSignupExp}
                                onValueChange={(value) => {
                                  setDropSignupExp(value);
                                }}
                              />
                            </View>
                          ) : (
                            <View
                              style={{
                                justifyContent: 'flex-start',
                                flexDirection: 'row',

                                alignItems: 'flex-start',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,
                              }}>
                              <RNPickerSelect
                                // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                placeholder={{ label: 'Customer' }}
                                style={{
                                  inputAndroidContainer: {
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    justifyContent: 'center',
                                    backgroundColor: '#fafafa',
                                    borderRadius: 4,

                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                  },
                                  inputAndroid: {
                                    //backgroundColor: '#fafafa',
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 16,
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    borderRadius: 5,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    paddingHorizontal: 10,
                                    height: 35,
                                    paddingLeft: 12,
                                    textAlign: 'center',
                                  },
                                  inputIOS: {
                                    //backgroundColor: '#fafafa',
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 16,
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    borderRadius: 5,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    paddingHorizontal: 10,
                                    height: 35,
                                    paddingLeft: 12,
                                    textAlign: 'center',
                                  },
                                  viewContainer: {
                                    backgroundColor: '#fafafa',
                                    borderRadius: 4,
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    justifyContent: 'center',
                                    fontSize: switchMerchant ? 10 : 16,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                  },
                                }}
                                items={manyDaysDropDownItem}
                                value={dropSignupExp}
                                onValueChange={(value) => {
                                  setDropSignupExp(value);
                                }}
                              />
                            </View>
                          )}
                        </View>
                      )}
                      <ScrollView
                        style={{
                          borderBottomWidth: 1,
                          borderColor: 'black',
                          height: windowHeight * 0.34,
                        }}>
                        <View
                          style={{
                            backgroundColor: 'rgb(209, 212, 212)',
                            paddingVertical:
                              windowHeight * 0.03,
                            paddingHorizontal:
                              windowWidth * 0.01,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                            }}>
                            SMS text
                          </Text>
                          <View
                            style={{
                              flexDirection: 'row',
                              width: '100%',
                              justifyContent: 'space-between',
                            }}>
                            <Text
                              style={{
                                borderRadius: 999,
                                padding: 1,
                                borderWidth: 1,
                                textAlignVertical: 'center',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                              }}>
                              {`  en  `}
                            </Text>
                            <TouchableOpacity
                              style={{
                                padding: 5,
                                borderRadius: 999,
                                borderWidth: 1,
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Submit
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>
                        <View style={{}}>
                          <TextInput
                            style={[
                              {
                                backgroundColor: Colors.whiteColor,
                                width: '100%',
                                height: windowHeight * 0.15,
                                borderRadius: 5,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                paddingVertical:
                                  windowHeight * 0.02,
                                paddingHorizontal:
                                  windowWidth * 0.01,
                                textAlignVertical: 'top',
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Regular',
                              },
                              switchMerchant
                                ? {
                                  fontSize: 10,
                                }
                                : {},
                            ]}
                            placeholder="SMS Text here..."
                            placeholderTextColor={Colors.descriptionColor}
                            //iOS
                            // clearTextOnFocus
                            selectTextOnFocus
                            //////////////////////////////////////////////
                            //Android
                            // onFocus={() => {
                            //   setTemp(signupSmsText)
                            //   setSignupSmsText('');
                            // }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            // onEndEditing={() => {
                            //   if (signupSmsText == '') {
                            //     setSignupSmsText(temp);
                            //   }
                            // }}
                            //////////////////////////////////////////////
                            onChangeText={(text) => {
                              setSignupSmsText(text);
                            }}
                            defaultValue={signupSmsText}
                            multiline
                          />
                        </View>
                        <Text
                          style={{
                            paddingTop: windowHeight * 0.02,
                            paddingBottom: windowHeight * 0.01,
                            backgroundColor: 'rgb(209, 212, 212)',
                            paddingHorizontal:
                              windowWidth * 0.01,

                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          SMS Preview
                        </Text>
                        <Text
                          style={{
                            paddingBottom: windowHeight * 0.01,
                            // borderBottomWidth: 1,
                            backgroundColor: 'rgb(209, 212, 212)',
                            // borderBottomColor: '#EBEDEF',
                            paddingHorizontal:
                              windowWidth * 0.01,
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {signupSmsText === ''
                            ? 'SMS Preview Here...'
                            : `${signupSmsText}`}
                        </Text>
                      </ScrollView>
                    </View>
                  ) : null}
                </View>
                <Text
                  style={{
                    textAlign: 'center',
                    paddingVertical: windowHeight * 0.01,
                    zIndex: -1,
                    color: '#808B96',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  When guests sign-up they will automatically get RM15 points
                  added to their account.
                </Text>
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalView>

        {/* Tier Upgrade Modal */}
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={tierUpgradeModal}
          transparent>
          <KeyboardAvoidingView
            behavior={'padding'}
            style={styles.modalContainer}>
            {/* View for whole modal */}
            <View
              style={{
                width: windowWidth * 0.4,
                // Height: windowHeight * 0.47,
                backgroundColor: Colors.whiteColor,
                borderRadius: 10,
                padding: windowWidth * 0.02,
                paddingBottom: 0,
                alignItems: 'center',
                // borderWidth:1

                ...getTransformForModalInsideNavigation(),
              }}>
              {/* Top */}
              <View>
                <Text
                  style={{
                    color: 'black',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 16 : 24,
                  }}>
                  Tier Upgrade
                </Text>
              </View>
              {/* Body */}
              <View
                style={{
                  // justifyContent: 'space-between',
                  // borderWidth:1,
                  width: '100%',
                }}>
                {/* content details */}
                <View
                  style={
                    {
                      // justifyContent: 'center',
                      // borderWidth:1
                    }
                  }>
                  {/* Name */}
                  <View
                    style={{
                      // justifyContent: 'center',
                      // alignSelf: 'center',
                      // alignContent: 'center',
                      // marginTop: 20,
                      width: '100%',

                      // borderWidth: 1,
                    }}>
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        paddingVertical: 5,
                        // borderWidth: 1,
                      }}>
                      <View style={{ justifyContent: 'center', width: '40%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          By total spend
                        </Text>
                      </View>
                      {/* <View style={{width: '10%', marginLeft: 100}}> */}
                      <Switch
                        width={42}
                        style={{}}
                        value={switchTierUpgradeByTotalSpend}
                        onSyncPress={(statusTemp) => {
                          setSwitchTierUpgradeByTotalSpend(statusTemp);
                        }}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                      {/* </View> */}
                    </View>
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        paddingVertical: 5,
                      }}>
                      <View style={{ justifyContent: 'center', width: '40%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          By total visits
                        </Text>
                      </View>
                      <Switch
                        width={42}
                        style={{}}
                        value={switchTierUpgradeByTotalVisits}
                        onSyncPress={(statusTemp) => {
                          setSwitchTierUpgradeByTotalVisits(statusTemp);
                        }}
                        circleColorActive={Colors.primaryColor}
                        circleColorInactive={Colors.fieldtTxtColor}
                        backgroundActive="#dddddd"
                      />
                    </View>
                    {/* <View
                    style={{
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                      width: '100%',
                      paddingVertical: 5,
                    }}>
                    <View style={{ justifyContent: 'center', width: '80%' }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 16,
                        }}>
                        By total spend and total visits
                      </Text>
                    </View>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchTierUpgradeByBoth}
                      onSyncPress={(statusTemp) => {
                        setSwitchTierUpgradeByBoth(statusTemp);
                      }}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View> */}
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        // marginTop: 10,
                        paddingVertical: 5,
                        // borderWidth:1
                      }}>
                      <View style={{ width: '40%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Past time to count
                        </Text>
                      </View>
                      {switchMerchant ? (
                        <View
                          style={{
                            backgroundColor: '#fafafa',
                            // backgroundColor: 'green',
                            borderRadius: 4,
                            height: switchMerchant
                              ? windowHeight * 0.08
                              : 35,
                            width: switchMerchant
                              ? windowWidth * 0.13
                              : 145,
                            justifyContent: 'center',
                            fontSize: switchMerchant ? 10 : 16,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            left: windowWidth * -0.002,
                          }}>
                          <RNPickerSelect
                            // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                            // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                            useNativeAndroidPickerStyle={false}
                            placeholder={{ label: 'Pick a month' }}
                            style={{
                              inputAndroid: {
                                fontSize: switchMerchant ? 10 : 16,
                                paddingVertical: 5,
                                color: 'black',
                                textAlign: 'center',
                              },
                              inputIOS: {
                                fontSize: switchMerchant ? 10 : 16,
                                paddingVertical: 5,
                                color: 'black',
                                textAlign: 'center',
                              },
                            }}
                            items={manyMonthDropDownItem}
                            value={dropPastTime}
                            onValueChange={(value) => {
                              setDropPastTime(value);
                            }}
                          />
                        </View>
                      ) : (
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            flexDirection: 'row',

                            alignItems: 'flex-start',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}>
                          <RNPickerSelect
                            // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                            // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                            placeholder={{ label: 'Pick a month' }}
                            style={{
                              inputAndroidContainer: {
                                height: switchMerchant
                                  ? windowHeight * 0.08
                                  : 35,
                                justifyContent: 'center',
                                backgroundColor: '#fafafa',
                                borderRadius: 4,

                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              },
                              inputAndroid: {
                                //backgroundColor: '#fafafa',
                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : 145,
                                paddingHorizontal: 10,
                                height: 35,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              inputIOS: {
                                //backgroundColor: '#fafafa',
                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : 145,
                                paddingHorizontal: 10,
                                height: 35,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              viewContainer: {
                                backgroundColor: '#fafafa',
                                borderRadius: 4,
                                height: switchMerchant
                                  ? windowHeight * 0.08
                                  : 35,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : 145,
                                justifyContent: 'center',
                                fontSize: switchMerchant ? 10 : 16,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              },
                            }}
                            items={manyMonthDropDownItem}
                            value={dropPastTime}
                            onValueChange={(value) => {
                              setDropPastTime(value);
                            }}
                          />
                        </View>
                      )}
                    </View>
                  </View>
                </View>

                {/* Buttons */}
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 35,
                  }}>
                  {/* Done button */}
                  <TouchableOpacity
                    disabled={false}
                    onPress={() => {
                      setTierUpgradeModal(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '55.5%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 40 : 60,
                      borderBottomLeftRadius: 10,
                      borderRightWidth: StyleSheet.hairlineWidth,
                      borderTopWidth: StyleSheet.hairlineWidth,
                      // borderWidth:1
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Regular',
                      }}>
                      Done
                    </Text>
                  </TouchableOpacity>
                  {/* Cancel button */}
                  <TouchableOpacity
                    onPress={() => {
                      // setState({ visible: false });
                      setTierUpgradeModal(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '55%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 40 : 60,
                      borderBottomRightRadius: 10,
                      borderTopWidth: StyleSheet.hairlineWidth,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        color: Colors.descriptionColor,
                        fontFamily: 'NunitoSans-Regular',
                      }}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalView>

        {/* Add new tier modal */}
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={addTierModal}
          transparent>
          <KeyboardAvoidingView
            behavior={'padding'}
            style={styles.modalContainer}>
            {/* View for whole modal */}
            <View
              style={{
                width: windowWidth * 0.4,
                backgroundColor: Colors.whiteColor,
                borderRadius: 12,
                padding: windowWidth * 0.02,
                paddingBottom: 0,
                alignItems: 'center',
                // borderWidth:1

                ...getTransformForModalInsideNavigation(),
              }}>
              {/* Top */}
              <View>
                <Text
                  style={{
                    color: 'black',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 16 : 24,
                  }}>
                  New Loyalty Tier
                </Text>
              </View>
              {/* Body */}
              <View
                style={{
                  // justifyContent: 'space-between',
                  // borderWidth:1,
                  width: '100%',
                }}>
                {/* content details */}
                <View
                  style={
                    {
                      // justifyContent: 'center',
                      // borderWidth:1
                    }
                  }>
                  {/* Name */}
                  <View
                    style={{
                      // justifyContent: 'center',
                      // alignSelf: 'center',
                      // alignContent: 'center',
                      // marginTop: 20,
                      width: '100%',

                      // borderWidth: 1,
                    }}>
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        paddingVertical: 5,
                      }}>
                      <View style={{ justifyContent: 'center', width: '40%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Name
                        </Text>
                      </View>
                      {/* Text input view */}
                      <View style={{ width: '35%' }}>
                        <TextInput
                          placeholder="Enter tier name"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: '100%',
                            height: switchMerchant ? 35 : 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: switchMerchant ? 2 : 5,
                            // borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Regular',
                          }}
                          //iOS
                          // clearTextOnFocus
                          selectTextOnFocus
                          //////////////////////////////////////////////
                          //Android
                          // onFocus={() => {
                          //   setTemp(newTierName)
                          //   setNewTierName('');
                          // }}
                          ///////////////////////////////////////////////
                          //When textinput is not selected
                          // onEndEditing={() => {
                          //   if (newTierName == '') {
                          //     setNewTierName(temp);
                          //   }
                          // }}
                          //////////////////////////////////////////////
                          onChangeText={(text) => {
                            setNewTierName(text);
                          }}
                          keyboardType={'default'}
                          defaultValue={newTierName}
                        />
                      </View>
                    </View>
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        paddingVertical: 5,
                      }}>
                      <View style={{ justifyContent: 'center', width: '40%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Total spend
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'flex-end',
                          width: '60%',
                        }}>
                        <View style={{ paddingRight: 5 }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            RM
                          </Text>
                        </View>

                        {/* Text input view */}
                        <View style={{ width: '58%' }}>
                          <TextInput
                            placeholder="0.00"
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            keyboardType={'decimal-pad'}
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: '100%',
                              height: switchMerchant ? 35 : 40,
                              borderRadius: 5,
                              padding: 5,
                              marginVertical: switchMerchant ? 2 : 5,
                              // borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Regular',
                            }}
                            //iOS
                            // clearTextOnFocus
                            selectTextOnFocus
                            //////////////////////////////////////////////
                            //Android
                            // onFocus={() => {
                            //   setTemp(newTierTotalSpend)
                            //   setNewTierTotalSpend('');
                            // }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            // onEndEditing={() => {
                            //   if (newTierTotalSpend == '') {
                            //     setNewTierTotalSpend(temp);
                            //   }
                            // }}
                            //////////////////////////////////////////////
                            onChangeText={(text) => {
                              setNewTierTotalSpend(parseValidPriceText(text));
                            }}
                            defaultValue={newTierTotalSpend != '' ? newTierTotalSpend : ''}
                          />
                        </View>
                      </View>
                    </View>

                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        paddingVertical: 5,
                      }}>
                      <View style={{ justifyContent: 'center', width: '40%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Total visits
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'flex-end',
                          width: '60%',
                        }}>
                        {/* <View style={{ paddingRight: 5 }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          RM
                        </Text>
                      </View> */}

                        {/* Text input view */}
                        <View style={{ width: '58%' }}>
                          <TextInput
                            placeholder="0.00"
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            keyboardType={'decimal-pad'}
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: '100%',
                              height: switchMerchant ? 35 : 40,
                              borderRadius: 5,
                              padding: 5,
                              marginVertical: switchMerchant ? 2 : 5,
                              // borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Regular',
                            }}
                            //iOS
                            // clearTextOnFocus
                            selectTextOnFocus
                            //////////////////////////////////////////////
                            //Android
                            // onFocus={() => {
                            //   setTemp(newTierTotalVisits)
                            //   setNewTierTotalVisits('');
                            // }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            // onEndEditing={() => {
                            //   if (newTierTotalVisits == '') {
                            //     setNewTierTotalVisits(temp);
                            //   }
                            // }}
                            //////////////////////////////////////////////
                            onChangeText={(text) => {
                              setNewTierTotalVisits(text);
                            }}
                            defaultValue={newTierTotalVisits != '' ? newTierTotalVisits : ''}
                          />
                        </View>
                      </View>
                    </View>

                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        paddingTop: 12,
                      }}>
                      <View style={{ justifyContent: 'center', width: '60%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Points
                        </Text>
                      </View>
                      {switchMerchant ? (
                        <View
                          style={{
                            backgroundColor: '#fafafa',
                            // backgroundColor: 'green',
                            borderRadius: 4,
                            height: switchMerchant
                              ? windowHeight * 0.08
                              : 35,
                            width: '34%',
                            justifyContent: 'center',
                            fontSize: switchMerchant ? 10 : 16,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            left: windowWidth * -0.002,
                          }}>
                          <RNPickerSelect
                            // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                            // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                            useNativeAndroidPickerStyle={false}
                            placeholder={{ label: 'Pick points percentage' }}
                            style={{
                              inputAndroid: {
                                fontSize: switchMerchant ? 10 : 16,
                                paddingVertical: 5,
                                color: 'black',
                                textAlign: 'center',
                              },
                              inputIOS: {
                                fontSize: switchMerchant ? 10 : 16,
                                paddingVertical: 5,
                                color: 'black',
                                textAlign: 'center',
                              },
                            }}
                            items={cashbackItem}
                            value={dropNewCashback}
                            onValueChange={(value) => {
                              setDropNewCashback(value);
                            }}
                          />
                        </View>
                      ) : (
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            flexDirection: 'row',

                            alignItems: 'flex-start',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            // width:windowWidth * 0.1
                          }}>
                          <RNPickerSelect
                            // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                            // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                            placeholder={{ label: 'Pick a points percentage' }}
                            style={{
                              inputAndroidContainer: {
                                height: switchMerchant
                                  ? windowHeight * 0.08
                                  : 40,
                                justifyContent: 'center',
                                backgroundColor: '#fafafa',
                                borderRadius: 4,
                                // borderWidth:50,

                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              },
                              inputAndroid: {
                                //backgroundColor: '#fafafa',
                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : windowWidth * 0.125,
                                paddingHorizontal: 10,
                                height: 40,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              inputIOS: {
                                //backgroundColor: '#fafafa',
                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                // borderWidth: 1,
                                // borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : 145,
                                paddingHorizontal: 10,
                                height: 40,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              viewContainer: {
                                backgroundColor: '#fafafa',
                                borderRadius: 4,
                                height: switchMerchant
                                  ? windowHeight * 0.08
                                  : 40,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : windowWidth * 0.125,
                                justifyContent: 'center',
                                fontSize: switchMerchant ? 10 : 16,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                // borderWidth:1,
                                elevation: 1,
                              },
                            }}
                            items={cashbackItem}
                            value={dropNewCashback}
                            onValueChange={(value) => {
                              setDropNewCashback(value);
                            }}
                          />
                        </View>
                      )}
                    </View>

                    {/* Voucher To Tag */}

                    {
                      taggableVoucherDropdownList.find(voucherOption => {
                        if (voucherOption.value === newTierVoucherId) {
                          return true;
                        }
                      })
                        ?
                        <View
                          style={{
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            flexDirection: 'row',
                            width: '100%',
                            paddingTop: 12,
                          }}>
                          <View style={{ justifyContent: 'center', width: '60%' }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 16,
                              }}>
                              Voucher To Sent
                            </Text>
                          </View>

                          <>
                            {switchMerchant ? (
                              <View
                                style={{
                                  backgroundColor: '#fafafa',
                                  // backgroundColor: 'green',
                                  borderRadius: 4,
                                  height: switchMerchant
                                    ? windowHeight * 0.08
                                    : 35,
                                  width: '34%',
                                  justifyContent: 'center',
                                  fontSize: switchMerchant ? 10 : 16,
                                  shadowColor: '#000',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  left: windowWidth * -0.002,
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  useNativeAndroidPickerStyle={false}
                                  placeholder={{ label: 'Choose the voucher' }}
                                  style={{
                                    inputAndroid: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      fontSize: switchMerchant ? 10 : 16,
                                      paddingVertical: 5,
                                      color: 'black',
                                      textAlign: 'center',
                                    },
                                  }}
                                  items={taggableVoucherDropdownList}
                                  value={newTierVoucherId}
                                  onValueChange={(value) => {
                                    if (value) {
                                      setNewTierVoucherId(value);
                                    }
                                  }}
                                />
                              </View>
                            ) : (
                              <View
                                style={{
                                  justifyContent: 'flex-start',
                                  flexDirection: 'row',

                                  alignItems: 'flex-start',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  // width:windowWidth * 0.1
                                }}>
                                <RNPickerSelect
                                  // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                  // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                  placeholder={{ label: 'Choose the voucher' }}
                                  style={{
                                    inputAndroidContainer: {
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 40,
                                      justifyContent: 'center',
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      // borderWidth:50,

                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                    },
                                    inputAndroid: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : windowWidth * 0.125,
                                      paddingHorizontal: 10,
                                      height: 40,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    inputIOS: {
                                      //backgroundColor: '#fafafa',
                                      color: 'black',
                                      fontFamily: 'NunitoSans-Regular',
                                      fontSize: switchMerchant ? 10 : 16,
                                      // borderWidth: 1,
                                      // borderColor: Colors.primaryColor,
                                      borderRadius: 5,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : 145,
                                      paddingHorizontal: 10,
                                      height: 40,
                                      paddingLeft: 12,
                                      textAlign: 'center',
                                    },
                                    viewContainer: {
                                      backgroundColor: '#fafafa',
                                      borderRadius: 4,
                                      height: switchMerchant
                                        ? windowHeight * 0.08
                                        : 40,
                                      width: switchMerchant
                                        ? windowWidth * 0.13
                                        : windowWidth * 0.125,
                                      justifyContent: 'center',
                                      fontSize: switchMerchant ? 10 : 16,
                                      shadowColor: '#000',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      // borderWidth:1,
                                      elevation: 1,
                                    },
                                  }}
                                  items={taggableVoucherDropdownList}
                                  value={newTierVoucherId}
                                  onValueChange={(value) => {
                                    if (value) {
                                      setNewTierVoucherId(value);
                                    }
                                  }}
                                />
                              </View>
                            )}
                          </>

                        </View>
                        :
                        <></>
                    }
                  </View>
                </View>

                {/* Buttons */}
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: switchMerchant ? windowHeight * 0.06 : 35,
                  }}>
                  {/* Done button */}
                  <TouchableOpacity
                    disabled={false}
                    onPress={() => {
                      // add data also
                      setNewTierName('');
                      setNewTierTotalSpend('0');
                      setNewTierTotalVisits('0');
                      setDropNewCashback('');

                      setLoyaltyTierLevels([
                        ...loyaltyTierLevels,
                        {
                          levelId: uuidv4(),
                          levelName: newTierName,
                          levelCashbackRate: parseFloat(dropNewCashback),

                          levelTotalSpents: parseFloat(newTierTotalSpend),
                          levelTotalVisits: parseInt(newTierTotalVisits),

                          levelVoucherId: newTierVoucherId,

                          orderIndex: loyaltyTierLevels.length,
                          isActive: true,
                        },
                      ]);

                      setAddTierModal(false);

                      // let temp = dummyTierData;

                      // temp.push({
                      //   name: newTierName,
                      //   totalSpend: 'RM ' + newTierTotalSpend,
                      //   cashback: newTierCashback,
                      // });
                      // setDummyTierData(temp);

                      // // console.log('zhe bian lah');
                      // // console.log(dummyTier);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '55.5%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 40 : 60,
                      borderBottomLeftRadius: 10,
                      borderRightWidth: StyleSheet.hairlineWidth,
                      borderTopWidth: StyleSheet.hairlineWidth,
                      // borderWidth:1
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Regular',
                      }}>
                      Add
                    </Text>
                  </TouchableOpacity>
                  {/* Cancel button */}
                  <TouchableOpacity
                    onPress={() => {
                      // setState({ visible: false });
                      setAddTierModal(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '55.5%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 40 : 60,
                      borderBottomRightRadius: 10,
                      borderTopWidth: StyleSheet.hairlineWidth,
                      zIndex: -3,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        color: Colors.descriptionColor,
                        fontFamily: 'NunitoSans-Regular',
                      }}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalView>

        {/* Edit tier modal */}
        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={editTierModal}
          transparent>
          <KeyboardAvoidingView
            behavior={'padding'}
            style={[styles.modalContainer, {
              // backgroundColor: Colors.tabRed,
            }]}>
            {/* View for whole modal */}
            <View
              style={{
                width: windowWidth * 0.4,
                height: switchMerchant ? windowHeight * 0.8 : windowHeight * 0.42,
                backgroundColor: Colors.whiteColor,
                borderRadius: 10,
                padding: windowWidth * 0.02,
                paddingBottom: 0,
                alignItems: 'center',
                // borderWidth:1

                ...getTransformForModalInsideNavigation(),
              }}>
              <View
                style={{
                  position: 'absolute',
                  right: windowWidth * 0.02,
                  top: windowWidth * 0.02,
                  // top: switchMerchant ? windowHeight * 0.05 : windowHeight * 0.01,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setEditTierModal(false);
                  }}>
                  <AntDesign
                    name="closecircle"
                    size={switchMerchant ? 15 : 25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
              </View>
              {/* Top */}
              <View>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 16 : 24,
                  }}>
                  {editTierName}
                </Text>
              </View>
              {/* Body */}
              <View
                style={{
                  // justifyContent: 'space-between',
                  // borderWidth:1,
                  width: '100%',

                  // width: windowWidth * 0.4,

                  // backgroundColor: Colors.tabRed,
                }}>
                {/* content details */}
                <View
                  style={
                    {
                      backgroundColor: Colors.whiteColor,

                      // justifyContent: 'center',
                      // borderWidth:1

                      // width: '100%',
                      // backgroundColor: 'white',
                    }
                  }>
                  {/* Name */}
                  <View
                    style={{
                      // justifyContent: 'center',
                      // alignSelf: 'center',
                      // alignContent: 'center',
                      // marginTop: 20,
                      width: '100%',

                      backgroundColor: Colors.whiteColor,

                      // backgroundColor: 'white',
                      // paddingBottom: 20,

                      // borderWidth: 1,
                    }}>
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        paddingVertical: switchMerchant ? 2 : 5,
                        // borderWidth: 1,
                      }}>
                      <View style={{ justifyContent: 'center', width: '40%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Name
                        </Text>
                      </View>
                      {/* Text input view */}
                      <View style={{ width: '35%' }}>
                        <TextInput
                          placeholder="Silver Member"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: '100%',
                            height: switchMerchant ? 35 : 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            // borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Regular',
                          }}
                          //iOS
                          // clearTextOnFocus
                          selectTextOnFocus
                          //////////////////////////////////////////////
                          //Android
                          // onFocus={() => {
                          //   setTemp(editTierName)
                          //   setEditTierName('');
                          // }}
                          ///////////////////////////////////////////////
                          //When textinput is not selected
                          // onEndEditing={() => {
                          //   if (editTierName == '') {
                          //     setEditTierName(temp);
                          //   }
                          // }}
                          //////////////////////////////////////////////
                          onChangeText={(text) => {
                            setEditTierName(text);
                          }}
                          keyboardType={'default'}
                          defaultValue={editTierName}
                        />
                      </View>
                    </View>
                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        paddingTop: 6,
                      }}>
                      <View style={{ justifyContent: 'center', width: '40%', paddingBottom: 10, }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Total spend
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'flex-end',
                          width: '60%',
                          paddingBottom: 10,
                        }}>
                        <View style={{ paddingRight: 5 }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 16,
                            }}>
                            RM
                          </Text>
                        </View>

                        {/* Text input view */}
                        <View style={{ width: '58%' }}>
                          <TextInput
                            placeholder="800.00"
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: '100%',
                              height: switchMerchant ? 35 : 40,
                              borderRadius: 5,
                              padding: 5,
                              // marginVertical: 5,
                              // borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Regular',
                            }}
                            //iOS
                            // clearTextOnFocus
                            selectTextOnFocus
                            //////////////////////////////////////////////
                            //Android
                            // onFocus={() => {
                            //   setTemp(editTierSpend)
                            //   setEditTierSpend('');
                            // }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            // onEndEditing={() => {
                            //   if (editTierSpend == '') {
                            //     setEditTierSpend(temp);
                            //   }
                            // }}
                            //////////////////////////////////////////////
                            onChangeText={(text) => {
                              setEditTierSpend(parseValidPriceText(text));
                            }}
                            keyboardType={'decimal-pad'}
                            defaultValue={editTierSpend}
                          />
                        </View>
                      </View>
                    </View>

                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        // paddingVertical: switchMerchant ? 2 : 5,
                      }}>
                      <View style={{ justifyContent: 'center', width: '40%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Total visits
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'flex-end',
                          width: '60%',
                        }}>
                        {/* <View style={{ paddingRight: 5 }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          RM
                        </Text>
                      </View> */}

                        {/* Text input view */}
                        <View style={{ width: '58%' }}>
                          <TextInput
                            placeholder="0.00"
                            placeholderTextColor={Platform.select({
                              ios: '#a9a9a9',
                            })}
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: '100%',
                              height: switchMerchant ? 35 : 40,
                              borderRadius: 5,
                              padding: 5,
                              marginVertical: 5,
                              // borderWidth: 1,
                              borderColor: '#E5E5E5',
                              paddingLeft: 10,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: 'NunitoSans-Regular',
                            }}
                            //iOS
                            // clearTextOnFocus
                            selectTextOnFocus
                            //////////////////////////////////////////////
                            //Android
                            // onFocus={() => {
                            //   setTemp(editTierVisits)
                            //   setEditTierVisits('');
                            // }}
                            ///////////////////////////////////////////////
                            //When textinput is not selected
                            // onEndEditing={() => {
                            //   if (editTierVisits == '') {
                            //     setEditTierVisits(temp);
                            //   }
                            // }}
                            //////////////////////////////////////////////
                            onChangeText={(text) => {
                              setEditTierVisits(text);
                            }}
                            keyboardType={'decimal-pad'}
                            defaultValue={editTierVisits}
                          />
                        </View>
                      </View>
                    </View>

                    <View
                      style={{
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        width: '100%',
                        marginTop: switchMerchant ? 10 : 10,
                      }}>
                      <View style={{ justifyContent: 'center', width: '60%' }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 16,
                          }}>
                          Points
                        </Text>
                      </View>
                      {switchMerchant ? (
                        <View
                          style={{
                            backgroundColor: '#fafafa',
                            // backgroundColor: 'green',
                            // borderWidth:1,
                            borderRadius: 4,
                            height: switchMerchant
                              ? windowHeight * 0.08
                              : 35,
                            width: '34%',
                            justifyContent: 'center',
                            fontSize: switchMerchant ? 10 : 16,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            left: windowWidth * -0.002,
                          }}>
                          <RNPickerSelect
                            // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                            // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                            useNativeAndroidPickerStyle={false}
                            placeholder={{ label: 'Pick a points percentage' }}
                            style={{
                              inputAndroid: {
                                fontSize: switchMerchant ? 10 : 16,
                                paddingVertical: 5,
                                color: 'black',
                                textAlign: 'center',
                              },
                              inputIOS: {
                                fontSize: switchMerchant ? 10 : 16,
                                paddingVertical: 5,
                                color: 'black',
                                textAlign: 'center',
                              },
                            }}
                            items={cashbackItem}
                            value={dropEditCashback}
                            onValueChange={(value) => {
                              setDropEditCashback(value);
                            }}
                          />
                        </View>
                      ) : (
                        <View
                          style={{
                            justifyContent: 'flex-start',
                            flexDirection: 'row',

                            alignItems: 'flex-start',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            // zIndex: -1,
                            // borderWidth:1
                          }}>
                          <RNPickerSelect
                            // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                            // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                            placeholder={{ label: 'Pick a points percentage' }}
                            style={{
                              inputAndroidContainer: {
                                height: switchMerchant
                                  ? windowHeight * 0.08
                                  : 35,
                                justifyContent: 'center',
                                backgroundColor: '#fafafa',
                                borderRadius: 4,
                                borderWidth: 1,

                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              },
                              inputAndroid: {
                                //backgroundColor: '#fafafa',
                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : 145,
                                paddingHorizontal: 10,
                                height: 35,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              inputIOS: {
                                //backgroundColor: '#fafafa',
                                color: 'black',
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 16,
                                // borderWidth: 1,
                                // borderColor: Colors.primaryColor,
                                borderRadius: 5,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : '34%',
                                paddingHorizontal: 10,
                                height: 35,
                                paddingLeft: 12,
                                textAlign: 'center',
                              },
                              viewContainer: {
                                backgroundColor: '#fafafa',
                                borderRadius: 4,
                                height: switchMerchant
                                  ? windowHeight * 0.08
                                  : 35,
                                width: switchMerchant
                                  ? windowWidth * 0.13
                                  : 145,
                                justifyContent: 'center',
                                fontSize: switchMerchant ? 10 : 16,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                              },
                            }}
                            items={cashbackItem}
                            value={dropEditCashback}
                            onValueChange={(value) => {
                              setDropEditCashback(value);
                            }}
                          />
                        </View>
                      )}
                    </View>

                    {/* Voucher To Tag */}

                    {
                      taggableVoucherDropdownList.find(voucherOption => {
                        if (voucherOption.value === editTierVoucherId) {
                          return true;
                        }
                      })
                        ?
                        <View
                          style={{
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            flexDirection: 'row',
                            width: '100%',
                            marginTop: switchMerchant ? 10 : 10,

                            // paddingBottom: 20,
                          }}>
                          <View style={{ justifyContent: 'center', width: '60%' }}>
                            <Text
                              style={{
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 10 : 16,
                              }}>
                              Voucher To Sent
                            </Text>
                          </View>
                          {switchMerchant ? (
                            <View
                              style={{
                                backgroundColor: '#fafafa',
                                // backgroundColor: 'green',
                                // borderWidth:1,
                                borderRadius: 4,
                                height: switchMerchant
                                  ? windowHeight * 0.08
                                  : 35,
                                width: '34%',
                                justifyContent: 'center',
                                fontSize: switchMerchant ? 10 : 16,
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                left: windowWidth * -0.002,
                              }}>
                              <RNPickerSelect
                                // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}
                                useNativeAndroidPickerStyle={false}
                                placeholder={{ label: 'Choose the voucher' }}
                                style={{
                                  inputAndroid: {
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingVertical: 5,
                                    color: 'black',
                                    textAlign: 'center',
                                  },
                                  inputIOS: {
                                    fontSize: switchMerchant ? 10 : 16,
                                    paddingVertical: 5,
                                    color: 'black',
                                    textAlign: 'center',
                                  },
                                }}
                                items={taggableVoucherDropdownList}
                                value={editTierVoucherId}
                                onValueChange={(value) => {
                                  if (value) {
                                    setEditTierVoucherId(value);
                                  }
                                }}
                              />
                            </View>
                          ) : (
                            <View
                              style={{
                                justifyContent: 'flex-start',
                                flexDirection: 'row',

                                alignItems: 'flex-start',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                // zIndex: -1,
                                // borderWidth:1
                              }}>
                              <RNPickerSelect
                                // placeholderStyle={[switchMerchant?{fontSize: 10}:{}]}
                                // contentContainerStyle={[switchMerchant?{fontSize: 10}:{}]}

                                placeholder={{ label: 'Choose the voucher' }}
                                style={{
                                  inputAndroidContainer: {
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    justifyContent: 'center',
                                    backgroundColor: '#fafafa',
                                    borderRadius: 4,
                                    borderWidth: 1,

                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                  },
                                  inputAndroid: {
                                    //backgroundColor: '#fafafa',
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 16,
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    borderRadius: 5,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    paddingHorizontal: 10,
                                    height: 35,
                                    paddingLeft: 12,
                                    textAlign: 'center',
                                  },
                                  inputIOS: {
                                    //backgroundColor: '#fafafa',
                                    color: 'black',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 16,
                                    // borderWidth: 1,
                                    // borderColor: Colors.primaryColor,
                                    borderRadius: 5,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : '34%',
                                    paddingHorizontal: 10,
                                    height: 35,
                                    paddingLeft: 12,
                                    textAlign: 'center',
                                  },
                                  viewContainer: {
                                    backgroundColor: '#fafafa',
                                    borderRadius: 4,
                                    height: switchMerchant
                                      ? windowHeight * 0.08
                                      : 35,
                                    width: switchMerchant
                                      ? windowWidth * 0.13
                                      : 145,
                                    justifyContent: 'center',
                                    fontSize: switchMerchant ? 10 : 16,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                  },
                                }}
                                items={taggableVoucherDropdownList}
                                value={editTierVoucherId}
                                onValueChange={(value) => {
                                  if (value) {
                                    setEditTierVoucherId(value);
                                  }
                                }}
                              />
                            </View>
                          )}
                        </View>
                        :
                        <></>
                    }
                  </View>
                </View>

                {/* Buttons */}
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',

                    // marginTop: 20,
                    // backgroundColor: 'white',

                    // marginTop: switchMerchant ? windowHeight * 0.075 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 10 : 35,
                  }}>
                  {/* Done button */}
                  <TouchableOpacity
                    disabled={false}
                    onPress={() => {
                      setLoyaltyTierLevels(
                        loyaltyTierLevels.map((level, i) =>
                          level.levelId === selectedLoyaltyTierLevelId
                            ? {
                              ...level,
                              levelName: editTierName,
                              levelCashbackRate: parseFloat(dropEditCashback),

                              levelTotalSpents: parseFloat(editTierSpend),
                              levelTotalVisits: parseFloat(editTierVisits),

                              levelVoucherId: editTierVoucherId,
                            }
                            : level,
                        ),
                      );

                      setEditTierModal(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '55.5%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 40 : 60,
                      borderBottomLeftRadius: 10,
                      borderRightWidth: StyleSheet.hairlineWidth,
                      borderTopWidth: StyleSheet.hairlineWidth,
                      // borderWidth:1
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Regular',
                      }}>
                      Save
                    </Text>
                  </TouchableOpacity>
                  {/* Cancel button */}
                  <TouchableOpacity
                    onPress={() => {
                      // setState({ visible: false });
                      setEditTierModal(false);
                    }}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: '55.5%',
                      justifyContent: 'center',
                      alignItems: 'center',
                      alignContent: 'center',
                      height: switchMerchant ? 40 : 60,
                      borderBottomRightRadius: 10,
                      borderTopWidth: StyleSheet.hairlineWidth,
                      // zIndex: -3,
                    }}>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 16,
                        color: Colors.descriptionColor,
                        fontFamily: 'NunitoSans-Regular',
                      }}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalView>

        {/* Modal end */}
        {showLoyaltyMain ? (
          <View
            style={{
              width:
                Platform.OS === 'ios' || !switchMerchant
                  ? windowWidth * (1 - Styles.sideBarWidth)
                  : windowWidth * 0.875,
              height: '100%',
            }}>
            <View
              style={{
                paddingTop: windowHeight * 0.027,
                paddingHorizontal: 20,
              }}>
              <View>
                <TouchableOpacity
                  onPress={() => {
                    setShowLoyaltyMain(false);
                    setShowLoyaltyCashback(true);
                  }}
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    // borderBottomWidth: 1,
                    width: '100%',
                    borderRadius: 10,
                    backgroundColor: '#ffffff',
                    paddingVertical: windowHeight * 0.02,
                  }}>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Points & tier settings
                    </Text>
                    <Plus
                      name="chevron-right"
                      size={switchMerchant ? 20 : 25}
                      color={Colors.darkBgColor}
                      style={{ bottom: 1 }}
                    />
                  </View>
                </TouchableOpacity>
              </View>
              <View>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                    color: '#808B96',
                  }}>
                  Set your points and manage your loyalty member tiers
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    setShowLoyaltyAutomated(true);
                    setShowLoyaltyMain(false);
                  }}
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    // borderBottomWidth: 1,
                    width: '100%',
                    borderRadius: 10,
                    backgroundColor: '#ffffff',
                    paddingVertical: windowHeight * 0.02,
                  }}>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Automated marketing settings
                    </Text>
                    <Plus
                      name="chevron-right"
                      size={switchMerchant ? 20 : 25}
                      color={Colors.darkBgColor}
                      style={{ bottom: 1 }}
                    />
                  </View>
                </TouchableOpacity>
              </View>
              <Text
                style={{
                  marginVertical: windowHeight * 0.02,
                  paddingHorizontal: windowWidth * 0.01,
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                  color: '#808B96',
                }}>
                Incentivize guests to sign up to your loyalty program by adding
                rewards
              </Text>
              {/* <View>
              
              <TouchableOpacity
                onPress={() => {
                  setShowLoyaltyGuestData(true);
                  setShowLoyaltyMain(false);
                }}
                style={{
                  paddingHorizontal: windowWidth * 0.01,
                  // borderBottomWidth: 1,
                  width: '100%',
                  borderRadius: 10,
                  backgroundColor: '#ffffff',
                  paddingVertical: windowHeight * 0.02,
                }}>
                <View
                  style={{
                    // borderWidth: 1,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                    Guest data acquisition settings
                  </Text>
                  <Plus
                    name="chevron-right"
                    size={switchMerchant ? 20 : 25}
                    color={Colors.darkBgColor}
                    style={{ bottom: 1 }}
                  />
                </View>
              </TouchableOpacity>
            </View>
            <View>
              <Text
                style={{
                  paddingHorizontal: windowWidth * 0.01,
                  marginVertical: windowHeight * 0.02,
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                  color: '#808B96',
                }}>
                Manage what data you want to collect from your guests and when
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setShowLoyaltyTransaction(true);
                  setShowLoyaltyMain(false);
                }}
                style={{
                  paddingHorizontal: windowWidth * 0.01,
                  // borderBottomWidth: 1,
                  width: '100%',
                  borderRadius: 10,
                  backgroundColor: '#ffffff',
                  paddingVertical: windowHeight * 0.02,
                }}>
                <View
                  style={{
                    // borderWidth: 1,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                    Transaction communication settings
                  </Text>
                  <Plus
                    name="chevron-right"
                    size={switchMerchant ? 20 : 25}
                    color={Colors.darkBgColor}
                    style={{ bottom: 1 }}
                  />
                </View>
              </TouchableOpacity>
            </View> */}
            </View>
          </View>
        ) : null}

        {/* Cashback system */}
        {showLoyaltyCashback ? (
          <View
            style={{
              width:
                Platform.OS === 'ios' || !switchMerchant
                  ? windowWidth * (1 - Styles.sideBarWidth)
                  : windowWidth * 0.875,
              height: '100%',
              // paddingBottom:20
              // borderWidth: 1,
            }}>
            {/* Header */}
            <View
              style={{
                height: switchMerchant
                  ? windowHeight * 0.1
                  : windowHeight * 0.064,
                backgroundColor: '#ffffff',
                width: '100%',
                flexDirection: 'row',
                paddingLeft: 20,
              }}>
              <TouchableOpacity
                // style={{
                //   // position: 'absolute',
                //   // borderWidth: 1,
                //   // justifyContent: 'center',
                //   // alignItems: 'center',
                //   flexDirection:'row'
                // }}
                onPress={() => {
                  setShowLoyaltyMain(true);
                  setShowLoyaltyCashback(false);
                }}
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <Plus
                    name="chevron-left"
                    size={switchMerchant ? 20 : 25}
                    color={Colors.darkBgColor}
                    style={{
                      //bottom: 1 
                    }}
                  />
                  <Text
                    style={[
                      switchMerchant
                        ? {
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 10,

                        }
                        : {
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 16,
                          top: 1,
                        },
                    ]}>
                    Loyalty
                  </Text>
                </View>
              </TouchableOpacity>
              <Text
                style={{
                  textAlign: 'center',
                  alignSelf: 'center',
                  width: '85%',
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 20 : 26,
                }}>
                Tier System
              </Text>
            </View>

            <ScrollView
              style={{
                paddingTop: windowHeight * 0.03,
                // marginBottom: 50,
                // height:'110%',
                paddingHorizontal: 20,
                // borderWidth: 1,
              }}>
              <View
                style={{
                  marginTop: '0.5%',
                  marginBottom: '2%',
                  width: '100%',
                  alignItems: 'flex-end',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    saveLoyaltyTierSettings();
                  }}
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 130,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                      marginBottom: 10,
                    },
                    switchMerchant
                      ? {
                        height: 35,
                        width: 120,
                      }
                      : {},
                  ]}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: switchMerchant ? 10 : 16,
                      color: Colors.whiteColor,
                    }}>
                    {isLoading ? "Loading..." : "SAVE"}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Base cashback percentage */}
              <View
                style={{
                  paddingHorizontal: windowWidth * 0.01,
                  paddingVertical: windowHeight * 0.02,
                  // borderBottomWidth: 1,
                  width: '100%',
                  borderRadius: 10,
                  backgroundColor: '#ffffff',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text
                  style={{
                    alignSelf: 'center',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                    color: Colors.blackColor,
                  }}>
                  Base points percentage
                </Text>
                <View>
                  {cashbackItem.find(
                    (item) => item.value === dropTierCashback,
                  ) ? (
                    <DropDownPicker
                      items={cashbackItem}
                      arrowStyle={switchMerchant ? {
                        // borderWidth: 1,
                        height: '200%',
                      } : {}}
                      containerStyle={{ height: switchMerchant ? 35 : 40 }}
                      placeholder="10%"
                      placeholderStyle={{
                        color: Colors.descriptionColor,
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      style={{
                        backgroundColor: '#fafafa',
                        width: 200,
                      }}
                      labelStyle={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      itemStyle={{
                        justifyContent: 'flex-start',
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        marginLeft: 5,
                      }}
                      dropDownStyle={{ backgroundColor: '#fafafa' }}
                      onChangeItem={(item) => {
                        setDropTierCashback(item.value);
                      }}
                      onOpen={() => { setCashbackPercentageDropdownShowed(true) }}
                      onClose={() => { setCashbackPercentageDropdownShowed(false) }}
                      defaultValue={dropTierCashback}
                      multiple={false}
                    />
                  ) : (
                    <></>
                  )}
                </View>
              </View>
              {/* View until enable tier system */}
              <View style={{ zIndex: -5 }}>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,
                    color: '#808B96',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Set the percentage of points guests get when spending money in
                  your loyalty system.
                </Text>
                <View
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    // borderWidth: 1,
                    width: '100%',
                    borderTopLeftRadius: 10,
                    borderTopRightRadius: 10,
                    backgroundColor: '#ffffff',
                    borderBottomWidth: 1,
                    borderBottomColor: '#EBEDEF',
                    paddingVertical: windowHeight * 0.02,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: switchMerchant ? 10 : 14,
                    }}>
                    Enable tier system
                  </Text>
                  <Switch
                    width={42}
                    style={{}}
                    value={switchEnableTierSystem}
                    onSyncPress={(statusTemp) =>
                      setSwitchEnableTierSystem(statusTemp)
                    }
                    circleColorActive={Colors.primaryColor}
                    circleColorInactive={Colors.fieldtTxtColor}
                    backgroundActive="#dddddd"
                  />
                </View>
              </View>
              {/* View until tier upgrade */}
              <View style={{ zIndex: -5 }}>
                <TouchableOpacity
                  onPress={() => {
                    setTierUpgradeModal(true);
                  }}
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    // borderBottomWidth: 1,
                    width: '100%',
                    borderBottomLeftRadius: 10,
                    borderBottomRightRadius: 10,
                    backgroundColor: '#ffffff',
                    paddingVertical: windowHeight * 0.02,
                  }}
                  disabled={cashbackPercentageDropdownShowed}
                >
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Tier upgrade
                    </Text>
                    <View
                      style={{
                        flexDirection: 'row',
                        paddingRight: 5,
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                          bottom: switchMerchant ? 2 : 0,
                        }}>
                        {switchTierUpgradeByBoth
                          ? 'By total spend and total visits for the past '
                          : switchTierUpgradeByTotalSpend
                            ? 'By total spend for the past '
                            : 'By total visits for the past '}
                      </Text>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                          bottom: switchMerchant ? 2 : 0,
                        }}>
                        {dropPastTime}
                      </Text>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                          bottom: switchMerchant ? 2 : 0,
                        }}>
                        {` month(s)`}
                      </Text>
                      <Plus
                        name="chevron-right"
                        size={switchMerchant ? 20 : 25}
                        color={Colors.darkBgColor}
                        style={{ bottom: 1 }}
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
              {/* View until add new tiers */}
              <View>
                <Text
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    marginVertical: windowHeight * 0.02,
                    color: '#808B96',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Determine whether guests are upgraded by visits, spend, or
                  either of both
                </Text>

                <Text
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    paddingBottom: windowHeight * 0.01,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,

                    // marginVertical: windowHeight * 0.02,
                  }}>
                  Loyalty tiers
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    // Add a modal here to add new tier
                    setAddTierModal(true);
                  }}
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    // borderBottomWidth: 1,
                    width: '100%',
                    borderRadius: 10,
                    backgroundColor: '#ffffff',
                    paddingVertical: windowHeight * 0.02,
                  }}>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        color: Colors.primaryColor,
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 16,
                      }}>
                      Add new tiers
                    </Text>
                    <Plus
                      name="chevron-right"
                      size={switchMerchant ? 20 : 25}
                      color={Colors.darkBgColor}
                      style={{ bottom: 1 }}
                    />
                  </View>
                </TouchableOpacity>
              </View>
              <View>
                <DraggableFlatList
                  // simultaneousHandlers={scrollViewGHRef}
                  // nestedScrollEnabled={true}
                  // scrollEnabled={draggingScroll}
                  // autoscrollThreshold={scrollViewGHRef}
                  // scrollEnabled={true}
                  data={loyaltyTierLevels}
                  onDragBegin={() => { }}
                  onDragEnd={({ data, from, to }) => {
                    // setDummyTierData(data);

                    var loyaltyTierLevelsTemp = [];
                    for (var i = 0; i < data.length; i++) {
                      loyaltyTierLevelsTemp.push({
                        ...data[i],
                        orderIndex: i,
                      });
                    }

                    setLoyaltyTierLevels(loyaltyTierLevelsTemp);
                  }}
                  // extraData={outletItems}
                  renderItem={renderTierMenu}
                  keyExtractor={(item, index) => `draggable-item-${index}`}
                  contentContainerStyle={{
                    // paddingLeft: 5,
                    // paddingRight: 5,
                    paddingTop: 10,
                  }}
                />
              </View>
              {/* Text view to prompt user the usage of draggable list */}
              <View>
                <Text
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    marginVertical: windowHeight * 0.02,
                    fontFamily: 'NunitoSans-Bold',
                    color: '#808B96',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Tier points percentages override the base points %
                </Text>
              </View>
              <View style={{ justifyContent: 'center', flexDirection: 'row' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {'This is the current tier progression from '}
                </Text>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 15,
                  }}>
                  lowest to highest
                </Text>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  .
                </Text>
              </View>
              <View style={{ justifyContent: 'center', flexDirection: 'row' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {'You can change the order by dragging the  '}
                </Text>
                <Plus
                  name="menu"
                  size={switchMerchant ? 20 : 20}
                  color="black"
                  style={{ padding: 0 }}
                />
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  {'  icon next to the tier.'}
                </Text>
              </View>
              {/* horizontal flatlist to show current tier progression */}
              <View style={{ paddingTop: 20 }}>
                {/* <Text>dsds</Text> */}
                {/* <FlatList
                  style={{
                    // marginTop: 10,
                    // paddingBottom:5
                  }}
                  nestedScrollEnabled={true}
                  data={dummyTierData}
                  renderItem={renderTierProgression}
                  keyExtractor={(item, index) => String(index)}
                /> */}
                <FlatList
                  horizontal
                  // pagingEnabled={true}
                  showsHorizontalScrollIndicator
                  // legacyImplementation={false}
                  data={loyaltyTierLevels}
                  renderItem={renderTierProgression}
                  keyExtractor={(item, index) => String(index)}
                  style={{ alignSelf: 'center' }}
                  contentContainerStyle={{
                    // justifyContent: 'center',
                    // alignItems: 'center',
                    alignSelf: 'center',
                    // padding: 10,
                    // paddingTop: 30,
                    // paddingLeft: 5,
                    // paddingRight: 70
                    // paddingRight: 5,
                  }}
                // style={{width: SCREEN_WIDTH + 5, height: '100%'}}
                />
                <Text> </Text>
                <Text> </Text>
                <Text> </Text>
                <Text> </Text>
              </View>
            </ScrollView>
          </View>
        ) : null}
        {showLoyaltyTransaction ? (
          <View
            style={{
              width:
                Platform.OS === 'ios' || !switchMerchant
                  ? windowWidth * (1 - Styles.sideBarWidth)
                  : windowWidth * 0.875,
              height: '100%',
            }}>
            <View
              style={{
                height: switchMerchant
                  ? windowHeight * 0.1
                  : windowHeight * 0.064,
                backgroundColor: '#ffffff',
                width: '100%',
                flexDirection: 'row',
                paddingLeft: 20,
              }}>
              <TouchableOpacity
                style={{
                  // position: 'absolute',
                  // borderWidth: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => {
                  setShowLoyaltyMain(true);
                  setShowLoyaltyTransaction(false);
                }}>
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <Plus
                    name="chevron-left"
                    size={switchMerchant ? 20 : 25}
                    color={Colors.darkBgColor}
                    style={{ bottom: 1 }}
                  />
                  <Text
                    style={[
                      switchMerchant
                        ? {
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 10,
                          bottom: 1.5,
                        }
                        : {
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 16,
                          bottom: 1,
                        },
                    ]}>
                    Loyalty
                  </Text>
                </View>
              </TouchableOpacity>
              <Text
                style={{
                  textAlign: 'center',
                  alignSelf: 'center',
                  width: '85%',
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 20 : 26,
                }}>
                Transaction Communication
              </Text>
            </View>
            <View
              style={{
                paddingTop: windowHeight * 0.01,
                paddingHorizontal: 20,
              }}>
              <View>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,

                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Transaction guest notifications
                </Text>
                <View
                  style={{
                    // paddingHorizontal: windowWidth * 0.01,
                    // borderBottomWidth: 1,
                    width: '100%',
                    borderRadius: 10,
                    backgroundColor: '#ffffff',
                    paddingVertical: windowHeight * 0.02,
                  }}>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingBottom: windowHeight * 0.02,
                      justifyContent: 'space-between',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Credit notification
                    </Text>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchTransactionCashbackNotification}
                      onSyncPress={(statusTemp) =>
                        setSwitchTransactionCashbackNotification(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                  <View
                    style={{
                      borderBottomWidth: 1,
                      borderBottomColor: '#EBEDEF',
                      borderTopColor: '#EBEDEF',
                      borderTopWidth: 1,
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingVertical: windowHeight * 0.02,
                      justifyContent: 'space-between',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Redeem notification
                    </Text>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchTransactionRedeemNotification}
                      onSyncPress={(statusTemp) =>
                        setSwitchTransactionRedeemNotification(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingTop: windowHeight * 0.02,
                      justifyContent: 'space-between',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Deduct notification
                    </Text>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchTransactionDeductNotification}
                      onSyncPress={(statusTemp) =>
                        setSwitchTransactionDeductNotification(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                </View>
              </View>
              <View>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,
                    color: '#808B96',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Send guests transactional SMS receipts for claims, redemptions,
                  deductions, and additions
                </Text>
              </View>
              <View>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Redemption security
                </Text>
                <View
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    // borderBottomWidth: 1,
                    width: '100%',
                    borderRadius: 10,
                    backgroundColor: '#ffffff',
                    paddingVertical: windowHeight * 0.02,
                  }}>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <Text
                      style={{
                        textAlignVertical: 'center',
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Redemption security validation via
                    </Text>
                    <DropDownPicker
                      items={[
                        {
                          label: 'Admin Password',
                          value: 'ADMIN_PASSWORD',
                        },
                        {
                          label: 'OTP',
                          value: 'OTP',
                        },
                        {
                          label: 'Admin Password Or OTP',
                          value: 'ADMIN_PASSWORD_OR_OTP',
                        },
                      ]}
                      containerStyle={{ height: switchMerchant ? 35 : 40 }}
                      placeholder="Payment options"
                      placeholderStyle={{
                        color: Colors.descriptionColor,
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      style={{
                        backgroundColor: '#fafafa',
                        width: 200,
                      }}
                      labelStyle={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      itemStyle={{
                        justifyContent: 'flex-start',
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        marginLeft: 5,
                      }}
                      dropDownStyle={{ backgroundColor: '#fafafa' }}
                      onChangeItem={(item) => {
                        setDropTransactionSecurity(item.value);
                      }}
                      defaultValue={dropTransactionSecurity}
                      multiple={false}
                    />
                  </View>
                </View>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,
                    color: '#808B96',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Decide what level of security you want when guests redeem points
                  or campaigns
                </Text>
              </View>
              <View>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>{` `}</Text>
              </View>
            </View>
          </View>
        ) : null}
        {showLoyaltyGuestData ? (
          <View
            style={{
              width:
                Platform.OS === 'ios' || !switchMerchant
                  ? windowWidth * (1 - Styles.sideBarWidth)
                  : windowWidth * 0.875,
              height: '100%',
            }}>
            <View
              style={{
                height: switchMerchant
                  ? windowHeight * 0.1
                  : windowHeight * 0.064,
                backgroundColor: '#ffffff',
                width: '100%',
                flexDirection: 'row',
                paddingLeft: 20,
              }}>
              <TouchableOpacity
                style={{
                  // position: 'absolute',
                  // borderWidth: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => {
                  setShowLoyaltyMain(true);
                  setShowLoyaltyGuestData(false);
                }}>
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <Plus
                    name="chevron-left"
                    size={switchMerchant ? 20 : 25}
                    color={Colors.darkBgColor}
                    style={{ bottom: 1 }}
                  />
                  <Text
                    style={[
                      switchMerchant
                        ? {
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 10,
                          bottom: 1.5,
                        }
                        : {
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 16,
                          bottom: 1,
                        },
                    ]}>
                    Loyalty
                  </Text>
                </View>
              </TouchableOpacity>
              <Text
                style={{
                  textAlign: 'center',
                  alignSelf: 'center',
                  width: '85%',
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 20 : 26,
                }}>
                Guest data acquisition
              </Text>
            </View>
            <ScrollView
              style={{
                paddingTop: windowHeight * 0.01,
                paddingHorizontal: 20,
              }}>
              <View>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,

                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Guest data management
                </Text>
                <View
                  style={{
                    // paddingHorizontal: windowWidth * 0.01,
                    // borderBottomWidth: 1,
                    width: '100%',
                    borderRadius: 10,
                    backgroundColor: '#ffffff',
                    paddingVertical: windowHeight * 0.02,
                  }}>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingBottom: windowHeight * 0.02,
                      justifyContent: 'space-between',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Capture data upon points claim
                    </Text>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchGuestCaptureCashback}
                      onSyncPress={(statusTemp) =>
                        setSwitchGuestCaptureCashback(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                  <View
                    style={{
                      borderTopWidth: 1,
                      borderTopColor: '#EBEDEF',
                      flexDirection: 'row',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingTop: windowHeight * 0.02,
                      justifyContent: 'space-between',
                    }}>
                    <Text
                      style={{
                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Capture data upon redemption
                    </Text>
                    <Switch
                      width={42}
                      style={{}}
                      value={switchGuestCaptureRedemption}
                      onSyncPress={(statusTemp) =>
                        setSwitchGuestCaptureRedemption(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                </View>
              </View>
              <View>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,

                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Data gathering options
                </Text>
                <View
                  style={{
                    // paddingHorizontal: windowWidth * 0.01,
                    // borderBottomWidth: 1,
                    zIndex: 1000,
                    width: '100%',
                    borderRadius: 10,
                    backgroundColor: '#ffffff',
                    paddingVertical: windowHeight * 0.02,
                    overflow: 'visible',
                  }}>
                  <View
                    style={{
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      paddingHorizontal: windowWidth * 0.01,
                      flexDirection: 'row',
                      paddingBottom: windowHeight * 0.02,
                      justifyContent: 'space-between',
                      zIndex: 5,
                    }}>
                    <Text
                      style={{
                        textAlignVertical: 'center',

                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Last name
                    </Text>
                    <DropDownPicker
                      items={[
                        {
                          label: 'Disabled',
                          value: 'DISABLED',
                        },
                        {
                          label: 'Optional',
                          value: 'OPTIONAL',
                        },
                        {
                          label: 'Mandatory',
                          value: 'MANDATORY',
                        },
                      ]}
                      containerStyle={{ height: switchMerchant ? 35 : 40 }}
                      placeholder="Disabled"
                      placeholderStyle={{
                        color: Colors.descriptionColor,
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      style={{
                        backgroundColor: '#fafafa',
                        width: 200,
                      }}
                      labelStyle={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      itemStyle={{
                        justifyContent: 'flex-start',
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        marginLeft: 5,
                      }}
                      dropDownStyle={{ backgroundColor: '#fafafa' }}
                      onChangeItem={(item) => {
                        setDropGuestLastName(item.value);
                      }}
                      defaultValue={dropGuestLastName}
                      multiple={false}
                    />
                  </View>
                  <View
                    style={{
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      paddingHorizontal: windowWidth * 0.01,
                      flexDirection: 'row',
                      paddingVertical: windowHeight * 0.02,
                      justifyContent: 'space-between',
                      zIndex: 4,
                    }}>
                    <Text
                      style={{
                        textAlignVertical: 'center',

                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Gender
                    </Text>
                    <DropDownPicker
                      items={[
                        {
                          label: 'Disabled',
                          value: 'DISABLED',
                        },
                        {
                          label: 'Optional',
                          value: 'OPTIONAL',
                        },
                        {
                          label: 'Mandatory',
                          value: 'MANDATORY',
                        },
                      ]}
                      containerStyle={{ height: switchMerchant ? 35 : 40 }}
                      placeholder="Disabled"
                      placeholderStyle={{
                        color: Colors.descriptionColor,
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      style={{
                        backgroundColor: '#fafafa',
                        width: 200,
                      }}
                      labelStyle={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      itemStyle={{
                        justifyContent: 'flex-start',
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        marginLeft: 5,
                      }}
                      dropDownStyle={{ backgroundColor: '#fafafa' }}
                      onChangeItem={(item) => {
                        setDropGuestGender(item.value);
                      }}
                      defaultValue={dropGuestGender}
                      multiple={false}
                    />
                  </View>
                  <View
                    style={{
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      paddingHorizontal: windowWidth * 0.01,
                      flexDirection: 'row',
                      paddingVertical: windowHeight * 0.02,
                      justifyContent: 'space-between',
                      zIndex: 3,
                    }}>
                    <Text
                      style={{
                        textAlignVertical: 'center',

                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Email
                    </Text>
                    <DropDownPicker
                      items={[
                        {
                          label: 'Disabled',
                          value: 'DISABLED',
                        },
                        {
                          label: 'Optional',
                          value: 'OPTIONAL',
                        },
                        {
                          label: 'Mandatory',
                          value: 'MANDATORY',
                        },
                      ]}
                      containerStyle={{ height: switchMerchant ? 35 : 40 }}
                      placeholder="Disabled"
                      placeholderStyle={{
                        color: Colors.descriptionColor,
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      style={{
                        backgroundColor: '#fafafa',
                        width: 200,
                      }}
                      labelStyle={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      itemStyle={{
                        justifyContent: 'flex-start',
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        marginLeft: 5,
                      }}
                      dropDownStyle={{ backgroundColor: '#fafafa' }}
                      onChangeItem={(item) => {
                        setDropGuestEmail(item.value);
                      }}
                      defaultValue={dropGuestEmail}
                      multiple={false}
                    />
                  </View>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      paddingHorizontal: windowWidth * 0.01,
                      paddingTop: windowHeight * 0.02,
                      zIndex: 2,
                    }}>
                    <Text
                      style={{
                        textAlignVertical: 'center',

                        fontFamily: 'NunitoSans-Bold',
                        fontSize: switchMerchant ? 10 : 14,
                      }}>
                      Date of birth
                    </Text>
                    <DropDownPicker
                      items={[
                        {
                          label: 'Disabled',
                          value: 'DISABLED',
                        },
                        {
                          label: 'Optional',
                          value: 'OPTIONAL',
                        },
                        {
                          label: 'Mandatory',
                          value: 'MANDATORY',
                        },
                      ]}
                      containerStyle={{ height: switchMerchant ? 35 : 40 }}
                      placeholder="Disabled"
                      placeholderStyle={{
                        color: Colors.descriptionColor,
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      style={{
                        backgroundColor: '#fafafa',
                        width: 200,
                      }}
                      labelStyle={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                      }}
                      itemStyle={{
                        justifyContent: 'flex-start',
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        marginLeft: 5,
                      }}
                      dropDownStyle={{ backgroundColor: '#fafafa' }}
                      onChangeItem={(item) => {
                        setDropGuestDob(item.value);
                      }}
                      defaultValue={dropGuestDob}
                      multiple={false}
                    />
                  </View>
                </View>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
            </ScrollView>
          </View>
        ) : null}
        {showLoyaltyAutomated ? (
          <View
            style={{
              width:
                Platform.OS === 'ios' || !switchMerchant
                  ? windowWidth * (1 - Styles.sideBarWidth)
                  : windowWidth * 0.875,
              height: '100%',
            }}>
            <View
              style={{
                height: switchMerchant
                  ? windowHeight * 0.1
                  : windowHeight * 0.064,
                backgroundColor: '#ffffff',
                width: '100%',
                flexDirection: 'row',
                paddingLeft: 20,
              }}>
              <TouchableOpacity
                style={{
                  // position: 'absolute',
                  // borderWidth: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
                onPress={() => {
                  setShowLoyaltyMain(true);
                  setShowLoyaltyAutomated(false);
                }}>
                <View
                  style={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row',
                  }}>
                  <Plus
                    name="chevron-left"
                    size={switchMerchant ? 20 : 25}
                    color={Colors.darkBgColor}
                    style={{ bottom: 1 }}
                  />
                  <Text
                    style={[
                      switchMerchant
                        ? {
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 10,
                          bottom: 1.5,
                        }
                        : {
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 16,
                          bottom: 1,
                        },
                    ]}>
                    Loyalty
                  </Text>
                </View>
              </TouchableOpacity>
              <Text
                style={{
                  textAlign: 'center',
                  alignSelf: 'center',
                  width: '85%',
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 20 : 26,
                }}>
                Automated marketing
              </Text>
            </View>
            <ScrollView
              style={{
                paddingTop: windowHeight * 0.027,
                paddingHorizontal: 20,
              }}>
              {/* <View>
              <Text
                style={{
                  marginVertical: windowHeight * 0.02,
                  paddingHorizontal: windowWidth * 0.01,

                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                }}>
                Automated reminders
              </Text>
              <View
                style={{
                  // paddingHorizontal: windowWidth * 0.01,
                  // borderBottomWidth: 1,
                  width: '100%',
                  borderRadius: 10,
                  backgroundColor: '#ffffff',
                  paddingVertical: windowHeight * 0.02,
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setShowLoyaltyAutomatedReminder1(true);
                    setIsRemTime1(true);
                    setIsRemTime2(false);
                    setIsRemTime3(false);
                    setIsRem1(true);
                    setIsRem2(false);
                    setIsRem3(false);
                    setIs1st(false);
                    setIsRisk(false);
                    setIsLapsed(false);
                    setIsLost(false);
                    setIsBirthday(false);
                    setIsGrowth(false);
                    setIsSpender(false);
                    setIsSignup(false);
                  }}
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    paddingBottom: windowHeight * 0.02,
                    borderBottomColor: '#EBEDEF',
                    borderBottomWidth: 1,
                    width: '100%',
                  }}>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <View>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                        }}>
                        {switchAutomatedReminderActive1
                          ? dropAutomatedReminderModal1 + ' reminder'
                          : 'Reminder 1'}
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                        }}>
                        {switchAutomatedReminderActive1
                          ? moment(remTime1).format('hh:mm A')
                          : 'Disabled'}
                      </Text>
                      <Plus
                        name="chevron-right"
                        size={switchMerchant ? 20 : 25}
                        color={Colors.darkBgColor}
                        style={{ bottom: 2 }}
                      />
                    </View>
                  </View>
                </TouchableOpacity>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowLoyaltyAutomatedReminder2(true);
                      setIsRemTime1(false);
                      setIsRemTime2(true);
                      setIsRemTime3(false);
                      setIsRem1(false);
                      setIsRem2(true);
                      setIsRem3(false);
                      setIs1st(false);
                      setIsRisk(false);
                      setIsLapsed(false);
                      setIsLost(false);
                      setIsBirthday(false);
                      setIsGrowth(false);
                      setIsSpender(false);
                      setIsSignup(false);
                    }}
                    style={{
                      paddingHorizontal: windowWidth * 0.01,
                      paddingVertical: windowHeight * 0.02,
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {switchAutomatedReminderActive2
                            ? dropAutomatedReminderModal2 + ' reminder'
                            : 'Reminder 2'}
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {switchAutomatedReminderActive2
                            ? moment(remTime2).format('hh:mm A')
                            : 'Disabled'}
                        </Text>
                        <Plus
                          name="chevron-right"
                          size={switchMerchant ? 20 : 25}
                          color={Colors.darkBgColor}
                          style={{ bottom: 2 }}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowLoyaltyAutomatedReminder3(true);
                      setIsRemTime1(false);
                      setIsRemTime2(false);
                      setIsRemTime3(true);
                      setIsRem1(false);
                      setIsRem2(false);
                      setIsRem3(true);
                      setIs1st(false);
                      setIsRisk(false);
                      setIsLapsed(false);
                      setIsLost(false);
                      setIsBirthday(false);
                      setIsGrowth(false);
                      setIsSpender(false);
                      setIsSignup(false);
                    }}
                    style={{
                      paddingHorizontal: windowWidth * 0.01,
                      paddingTop: windowHeight * 0.02,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {switchAutomatedReminderActive3
                            ? dropAutomatedReminderModal3 + ' reminder'
                            : 'Reminder 3'}
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {switchAutomatedReminderActive3
                            ? moment(remTime3).format('hh:mm A')
                            : 'Disabled'}
                        </Text>
                        <Plus
                          name="chevron-right"
                          size={switchMerchant ? 20 : 25}
                          color={Colors.darkBgColor}
                          style={{ bottom: 2 }}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
              <Text
                style={{
                  marginVertical: windowHeight * 0.02,
                  paddingHorizontal: windowWidth * 0.01,
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: switchMerchant ? 10 : 14,
                  color: '#808B96',
                }}>
                Notify guests how much points they have when they haven't
                visited for the set amount of days
              </Text>
            </View> */}
              <View>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Automated campaigns
                </Text>
                <View
                  style={{
                    // paddingHorizontal: windowWidth * 0.01,
                    // borderBottomWidth: 1,
                    width: '100%',
                    borderRadius: 10,
                    backgroundColor: '#ffffff',
                    paddingVertical: windowHeight * 0.02,
                  }}>
                  <FlatList
                    data={loyaltyCampaigns.filter(loyaltyCampaign => {
                      if (currOutlet.uniqueId && loyaltyCampaign.outletId === currOutlet.uniqueId) {
                        return true;
                      }
                      else {
                        return false;
                      }
                    })}
                    renderItem={renderLoyaltyCampaign}
                    keyExtractor={(item, index) => String(index)}
                    style={{
                      width: '100%',
                      // backgroundColor: 'red',
                    }}
                  // initialNumToRender={8}
                  />

                  {/* <TouchableOpacity
                  onPress={() => {
                    setShowLoyaltyAutomatedCampaign(true);
                    setIsRem1(false);
                    setIsRem2(false);
                    setIsRem3(false);
                    setIs1st(true);
                    setIsRisk(false);
                    setIsLapsed(false);
                    setIsLost(false);
                    setIsBirthday(false);
                    setIsGrowth(false);
                    setIsSpender(false);
                    setIsSignup(false);
                  }}
                  style={{
                    paddingHorizontal: windowWidth * 0.01,
                    paddingBottom: windowHeight * 0.02,
                    borderBottomColor: '#EBEDEF',
                    borderBottomWidth: 1,
                    width: '100%',
                  }}>
                  <View
                    style={{
                      // borderWidth: 1,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <View>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                          color: switch1stVisit
                            ? Colors.primaryColor
                            : Colors.tabRed,
                        }}>
                        {switch1stVisit ? 'Active' : 'Inactive'}
                      </Text>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                        }}>
                        1st visit campaign
                      </Text>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                          color: '#808B96',
                        }}>
                        {`Send after 1st time claiming cashback - ${moment(
                          fstVisitTime,
                        ).format('hh:mm A')} - ${drop1stExp} expiration`}
                      </Text>
                    </View>
                    <View
                      style={{
                        flexDirection: 'row',
                      }}>
                      <Text
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: switchMerchant ? 10 : 14,
                        }}>
                        1 day
                      </Text>
                      <Plus
                        name="chevron-right"
                        size={switchMerchant ? 20 : 25}
                        color={Colors.darkBgColor}
                        style={{ bottom: 1.5 }}
                      />
                    </View>
                  </View>
                </TouchableOpacity>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowLoyaltyAutomatedCampaign(true);
                      setIsRem1(false);
                      setIsRem2(false);
                      setIsRem3(false);
                      setIs1st(false);
                      setIsRisk(true);
                      setIsLapsed(false);
                      setIsLost(false);
                      setIsBirthday(false);
                      setIsGrowth(false);
                      setIsSpender(false);
                      setIsSignup(false);
                    }}
                    style={{
                      paddingHorizontal: windowWidth * 0.01,
                      paddingVertical: windowHeight * 0.02,
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: switchRisk
                              ? Colors.primaryColor
                              : Colors.tabRed,
                          }}>
                          {switchRisk ? 'Active' : 'Inactive'}
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          At risk campaign
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: '#808B96',
                          }}>
                          {`Send ${dropRiskVisited} after last visit - ${moment(
                            riskTime,
                          ).format('hh:mm A')} - ${dropRiskExp} expiration`}
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {dropRiskVisited}
                        </Text>
                        <Plus
                          name="chevron-right"
                          size={switchMerchant ? 20 : 25}
                          color={Colors.darkBgColor}
                          style={{ bottom: 2 }}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowLoyaltyAutomatedCampaign(true);
                      setIsRem1(false);
                      setIsRem2(false);
                      setIsRem3(false);
                      setIs1st(false);
                      setIsRisk(false);
                      setIsLapsed(true);
                      setIsLost(false);
                      setIsBirthday(false);
                      setIsGrowth(false);
                      setIsSpender(false);
                      setIsSignup(false);
                    }}
                    style={{
                      paddingHorizontal: windowWidth * 0.01,
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      paddingVertical: windowHeight * 0.02,
                      // paddingTop: windowHeight * 0.02,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: switchLapsed
                              ? Colors.primaryColor
                              : Colors.tabRed,
                          }}>
                          {switchLapsed ? 'Active' : 'Inactive'}
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Lapsed campaign
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: '#808B96',
                          }}>
                          {`Send ${dropLapsedVisited} after last visit - ${moment(
                            lapsedTime,
                          ).format('hh:mm A')} - ${dropLapsedExp} expiration`}
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {dropLapsedVisited}
                        </Text>
                        <Plus
                          name="chevron-right"
                          size={switchMerchant ? 20 : 25}
                          color={Colors.darkBgColor}
                          style={{ bottom: 2 }}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowLoyaltyAutomatedCampaign(true);
                      setIsRem1(false);
                      setIsRem2(false);
                      setIsRem3(false);
                      setIs1st(false);
                      setIsRisk(false);
                      setIsLapsed(false);
                      setIsLost(true);
                      setIsBirthday(false);
                      setIsGrowth(false);
                      setIsSpender(false);
                      setIsSignup(false);
                    }}
                    style={{
                      paddingHorizontal: windowWidth * 0.01,
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      paddingVertical: windowHeight * 0.02,
                      // paddingTop: windowHeight * 0.02,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: switchLost
                              ? Colors.primaryColor
                              : Colors.tabRed,
                          }}>
                          {switchLost ? 'Active' : 'Inactive'}
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Lost campaign
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: '#808B96',
                          }}>
                          {`Send ${dropLostVisited} after last visit - ${moment(
                            lostTime,
                          ).format('hh:mm A')} - ${dropLostExp} expiration`}
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {dropLostVisited}
                        </Text>
                        <Plus
                          name="chevron-right"
                          size={switchMerchant ? 20 : 25}
                          color={Colors.darkBgColor}
                          style={{ bottom: 2 }}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowLoyaltyAutomatedCampaign(true);
                      setIsRem1(false);
                      setIsRem2(false);
                      setIsRem3(false);
                      setIs1st(false);
                      setIsRisk(false);
                      setIsLapsed(false);
                      setIsLost(false);
                      setIsBirthday(true);
                      setIsGrowth(false);
                      setIsSpender(false);
                      setIsSignup(false);
                    }}
                    style={{
                      paddingHorizontal: windowWidth * 0.01,
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      paddingVertical: windowHeight * 0.02,
                      // paddingTop: windowHeight * 0.02,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: switchBirthday
                              ? Colors.primaryColor
                              : Colors.tabRed,
                          }}>
                          {switchBirthday ? 'Active' : 'Inactive'}
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Birthday campaign
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: '#808B96',
                          }}>
                          {`Send ${dropBirthdayIsIn} before birthday - ${moment(
                            birthdayTime,
                          ).format('hh:mm A')} - ${dropBirthdayExp} expiration`}
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {dropBirthdayIsIn}
                        </Text>
                        <Plus
                          name="chevron-right"
                          size={switchMerchant ? 20 : 25}
                          color={Colors.darkBgColor}
                          style={{ bottom: 2 }}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowLoyaltyAutomatedCampaign(true);
                      setIsRem1(false);
                      setIsRem2(false);
                      setIsRem3(false);
                      setIs1st(false);
                      setIsRisk(false);
                      setIsLapsed(false);
                      setIsLost(false);
                      setIsBirthday(false);
                      setIsGrowth(true);
                      setIsSpender(false);
                      setIsSignup(false);
                    }}
                    style={{
                      paddingHorizontal: windowWidth * 0.01,
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      paddingVertical: windowHeight * 0.02,
                      // paddingTop: windowHeight * 0.02,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: switchGrowth
                              ? Colors.primaryColor
                              : Colors.tabRed,
                          }}>
                          {switchGrowth ? 'Active' : 'Inactive'}
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Growth campaign
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: '#808B96',
                          }}>
                          {`Send after every ${dropGrowthAfter} earning cashback - ${moment(
                            GrowthTime,
                          ).format('hh:mm A')} - ${dropGrowthExp} expiration`}
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {dropGrowthAfter}
                        </Text>
                        <Plus
                          name="chevron-right"
                          size={switchMerchant ? 20 : 25}
                          color={Colors.darkBgColor}
                          style={{ bottom: 2 }}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowLoyaltyAutomatedCampaign(true);
                      setIsRem1(false);
                      setIsRem2(false);
                      setIsRem3(false);
                      setIs1st(false);
                      setIsRisk(false);
                      setIsLapsed(false);
                      setIsLost(false);
                      setIsBirthday(false);
                      setIsGrowth(false);
                      setIsSpender(true);
                      setIsSignup(false);
                    }}
                    style={{
                      paddingHorizontal: windowWidth * 0.01,
                      borderBottomColor: '#EBEDEF',
                      borderBottomWidth: 1,
                      paddingVertical: windowHeight * 0.02,
                      // paddingTop: windowHeight * 0.02,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: switchSpender
                              ? Colors.primaryColor
                              : Colors.tabRed,
                          }}>
                          {switchSpender ? 'Active' : 'Inactive'}
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Big spender campaign
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: '#808B96',
                          }}>
                          {`Send after every RM ${spenderGuestSpend} earned - ${moment(
                            SpenderTime,
                          ).format('hh:mm A')} - ${dropSpenderExp} expiration`}
                        </Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>{`RM ${spenderGuestSpend}`}</Text>
                        <Plus
                          name="chevron-right"
                          size={switchMerchant ? 20 : 25}
                          color={Colors.darkBgColor}
                          style={{ bottom: 2 }}
                        />
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                  }}>
                  <TouchableOpacity
                    onPress={() => {
                      setShowLoyaltyAutomatedSignUp(true);
                      setIsRem1(false);
                      setIsRem2(false);
                      setIsRem3(false);
                      setIs1st(false);
                      setIsRisk(false);
                      setIsLapsed(false);
                      setIsLost(false);
                      setIsBirthday(false);
                      setIsGrowth(false);
                      setIsSpender(false);
                      setIsSignup(true);
                    }}
                    style={{
                      paddingHorizontal: windowWidth * 0.01,
                      paddingTop: windowHeight * 0.02,
                      width: '100%',
                    }}>
                    <View
                      style={{
                        // borderWidth: 1,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <View>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                            color: switchSignup
                              ? Colors.primaryColor
                              : Colors.tabRed,
                          }}>
                          {switchSignup ? 'Active' : 'Inactive'}
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          Sign up reward
                        </Text>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 10 : 14,
                          }}>
                          {dropSignupReward === 'CUSTOM_REWARDS'
                            ? 'Sent to guests signing up via ' +
                            dropSignupApply +
                            ' - Credit reward: RM ' +
                            signupCashback
                            : 'Sent to guests signup via ' +
                            dropSignupApply +
                            ' - ' +
                            dropSignupExp +
                            ' expiration'}
                        </Text>
                      </View>
                      <Plus
                        name="chevron-right"
                        size={switchMerchant ? 20 : 25}
                        color={Colors.darkBgColor}
                        style={{ bottom: 1.5 }}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
               */}
                </View>
                <Text
                  style={{
                    marginVertical: windowHeight * 0.02,
                    paddingHorizontal: windowWidth * 0.01,
                    color: '#808B96',
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 10 : 14,
                  }}>
                  Use rewards, such as 2-for-1 or a discount, to get more
                  returning guests.
                </Text>
              </View>

              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
              <View>
                <Text>{` `}</Text>
              </View>
            </ScrollView>
          </View>
        ) : null}
      </View>
    </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },

  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },

  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
    marginTop: 10,
    marginHorizontal: 20,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.5,
    height: Dimensions.get('window').height * 0.2,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  confirmBox: {
    width: Dimensions.get('window').width * 0.4,
    minHeight: Dimensions.get('window').height * 0.1,
    borderRadius: 12,
    backgroundColor: 'rgb(209, 212, 212)',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MainLoyaltySettingsScreen;
