import { Text } from "react-native-fast-text";
import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useMemo,
} from 'react';
import {
  StyleSheet,
  Image,
  View,
  Alert,
  Dimensions,
  TouchableOpacity,
  SectionList,
  ListView,
  Linking,
  Modal as ModalComponent,
  Platform,
  ActivityIndicator,
  StatusBar,
  TextInput,
  KeyboardAvoidingView,
  Touchable,
  useWindowDimensions,
} from 'react-native';
import { ModalView as ModalViewComponent } from 'react-native-multiple-modals';
import { ScrollView, FlatList } from "react-native-gesture-handler";
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Feather from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AIcon from 'react-native-vector-icons/AntDesign';
// import Swipeout from 'react-native-swipeout';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import Styles from '../constant/Styles';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import moment, { now } from 'moment';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import Switch from 'react-native-switch-pro';
import {
  isTablet
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
import { CommonStore } from '../store/commonStore';
import DropDownPicker from 'react-native-dropdown-picker';
import {
  USER_RESERVATION_STATUS,
  USER_RESERVATION_STATUS_PARSED,
  EXPAND_TAB_TYPE,
} from '../constant/common';
// import { Table } from 'react-native-table-component';
import GCalendar from '../assets/svg/GCalendar';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import RNPickerSelect from 'react-native-picker-select';
import { color } from 'react-native-reanimated';
import APILocal from '../util/apiLocalReplacers';
import { useNetInfo } from "@react-native-community/netinfo";
import { useKeyboard } from '../hooks';
const ModalView = Platform.OS === 'android' ? ModalComponent : ModalViewComponent;

const ReservationScreen = React.memo((props) => {
  const { navigation } = props;

  const netInfo = useNetInfo();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [keyboardHeight] = useKeyboard();

  const [table, setTable] = useState([]);
  const [reserve, setReserve] = useState([{}]);
  const [newReservationStatus, setNewReservationStatus] = useState(false);

  const [tableModalVisibility, setTableModalVisibility] = useState(false);

  //This useState to check status of reservation to change color//

  const [statusReject, setStatusReject] = useState(false);
  const [statusConfirm, setStatusConfirm] = useState(false);
  const [statusNoShow, setStatusNoShow] = useState(false);
  //const [statusNoAction, setStatusNoAction] = useState(true);
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [selectedUserTagList, setSelectedUserTagList] = useState([]);
  const [userTagList, setUserTagList] = useState([]);
  const [userTagDropdownList, setUserTagDropdownList] = useState([]);

  const [selectedOutletTableId, setSelectedOutletTableId] = useState('');
  const [selectedReservation, setSelectedReservation] = useState({});

  const [addReservationModal, setAddReservationModal] = useState(false);


  const [filterType, setFilterType] = useState(0);

  const [userReservations, setUserReservations] = useState([]);

  const [selectTable, setSelectTable] = useState('');
  const [openSelectTable, setOpenSelectTable] = useState(false);
  const [filteredOutletTablesForRendered, setFilteredOutletTablesForRendered] =
    useState([]);
  const [filteredOutletTables, setFilteredOutletTables] = useState([]);
  const [seatingModal, setSeatingModal] = useState(false);
  const [seatingPax, setSeatingPax] = useState(0);

  const [reservationId, setReservationId] = useState('');
  const [reservationCustomerName, setReservationCustomerName] = useState('');
  const [reservationPhone, setReservationPhone] = useState('');
  const [reservationPax, setReservationPax] = useState('');
  const [reservationRemark, setReservationRemark] = useState('');
  const [reservationDate, setReservationDate] = useState(moment());
  const [reservationTime, setReservationTime] = useState(moment());

  // Testing calendar selection
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [rev_date, setRev_date] = useState(
    moment().endOf(Date.now()).toDate(),
    // moment().subtract(6, 'days').toDate(),
  );
  const [rev_date1, setRev_date1] = useState(
    // moment().endOf(Date.now()).toDate(),
    moment().add(6, 'days').toDate(),
  );

  const [showReservationDatePicker, setShowReservationDatePicker] =
    useState(false);
  const [showReservationTimePicker, setShowReservationTimePicker] =
    useState(false);

  const [filteredReservations, setFilteredReservations] = useState([]);
  const [reservationIntervalMin, setReservationIntervalMin] = useState(120);
  const [reservationIntervalStartTime, setReservationIntervalStartTime] =
    useState(moment());
  const [reservationIntervalEndTime, setReservationIntervalEndTime] = useState(
    moment(),
  );

  ////////////////////////////////////////////////////////////////

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const allOutletsUserOrdersDone = OutletStore.useState(
    (s) => s.allOutletsUserOrdersDone,
  );
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const outletSections = OutletStore.useState((s) => s.outletSections);
  const selectedOutletSection = CommonStore.useState(
    (s) => s.selectedOutletSection,
  );
  const userOrdersTableDict = OutletStore.useState(
    (s) => s.userOrdersTableDict,
  );
  const selectedOutletTable = CommonStore.useState(
    (s) => s.selectedOutletTable,
  );

  const userReservationsRaw = OutletStore.useState((s) => s.userReservations);
  const userReservationsUserIdDict = OutletStore.useState(
    (s) => s.userReservationsUserIdDict,
  );
  const userReservationsDict = OutletStore.useState(
    (s) => s.userReservationsDict,
  );

  const merchantId = UserStore.useState((s) => s.merchantId);
  const userName = UserStore.useState((s) => s.name);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);
  const merchantName = MerchantStore.useState((s) => s.name);
  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const selectedCustomerEdit = CommonStore.useState(
    (s) => s.selectedCustomerEdit,
  );
  const crmUsers = OutletStore.useState((s) => s.crmUsers);
  const crmUsersDict = OutletStore.useState((s) => s.crmUsersDict);

  const outletTables = OutletStore.useState((s) => s.outletTables);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  useEffect(() => {
    // var filteredReservationsTemp = [];

    setFilteredReservations(
      userReservations.filter((item) =>
        moment(item.reservationTime).isSame(reservationDate, 'day'),
      ),
    );

    // setFilteredReservations(filteredReservationsTemp);
  }, [userReservations, reservationDate]);

  useEffect(() => {
    if (
      currOutlet &&
      currOutlet.reservationIntervalHour &&
      currOutlet.reservationIntervalMin
    ) {
      setReservationIntervalMin(
        currOutlet.reservationIntervalHour * 60 +
        currOutlet.reservationIntervalMin,
      );
    } else {
      setReservationIntervalMin(120);
    }
  }, [currOutlet]);

  useEffect(() => {
    const newRevDateTime = moment(
      `${moment(reservationDate).format('DD MMM YYYY')} ${moment(
        reservationTime,
      ).format('hh:mm A')}`,
      'DD MMM YYYY hh:mm A',
    ).valueOf();

    setReservationIntervalStartTime(
      moment(newRevDateTime).subtract(reservationIntervalMin, 'minute'),
    );
    setReservationIntervalEndTime(
      moment(newRevDateTime).add(reservationIntervalMin, 'minute'),
    );
  }, [reservationIntervalMin, reservationDate, reservationTime]);

  useEffect(() => {
    if (
      outletSections.length > 0 &&
      selectedOutletSection.uniqueId === undefined
    ) {
      CommonStore.update((s) => {
        s.selectedOutletSection = outletSections[0];
      });
    }
  }, [outletSections.length]);

  useEffect(() => {
    if (selectedOutletSection.uniqueId) {
      var filteredOutletTablesTemp = outletTables.filter(
        (table) => table.outletSectionId === selectedOutletSection.uniqueId,
      );

      filteredOutletTablesTemp.sort((a, b) => {
        if (a.joinedTables && b.joinedTables) {
          return b.joinedTables.length - a.joinedTables.length;
        } else if (a.joinedTables && !b.joinedTables) {
          return -1;
        } else if (!a.joinedTables && b.joinedTables) {
          return 1;
        } else if (!a.joinedTables && !b.joinedTables) {
          return 0;
        }
      });

      const MAX_COL_NUM = 6;

      var filteredOutletTablesTempFirstPass = [];

      if (filteredOutletTablesTemp.length > 0) {
        filteredOutletTablesTempFirstPass = [filteredOutletTablesTemp[0]];

        if (filteredOutletTablesTemp.length > 1) {
          var colRemaining = MAX_COL_NUM;
          if (filteredOutletTablesTemp[0].joinedTables) {
            colRemaining -= filteredOutletTablesTemp[0].joinedTables.length;
          } else {
            colRemaining -= 1;
          }

          for (var i = 1; i < filteredOutletTablesTemp.length; i++) {
            const currCol = i % MAX_COL_NUM;

            const currTable = filteredOutletTablesTemp[i];

            // console.log(currTable);

            if (colRemaining <= 0) {
              // start with new row

              colRemaining = MAX_COL_NUM;

              filteredOutletTablesTempFirstPass.push(currTable);
            } else if (colRemaining > 0) {
              if (!currTable.joinedTables) {
                // squeeze unjoined table

                filteredOutletTablesTempFirstPass.push(currTable);
              } else if (
                currTable.joinedTables &&
                currTable.joinedTables.length <= colRemaining
              ) {
                // squeeze remaining tables

                filteredOutletTablesTempFirstPass.push(currTable);
              } else {
                for (var j = 0; j < colRemaining; j++) {
                  filteredOutletTablesTempFirstPass.push({
                    emptyLayout: true,
                  });
                }

                colRemaining = MAX_COL_NUM;

                filteredOutletTablesTempFirstPass.push(currTable);
              }
            }

            if (currTable.joinedTables) {
              colRemaining -= currTable.joinedTables.length;
            } else {
              colRemaining -= 1;
            }
          }
        }
      }

      setFilteredOutletTables(filteredOutletTablesTemp);
      setFilteredOutletTablesForRendered(filteredOutletTablesTempFirstPass);
    }

    if (selectedOutletTable && selectedOutletTable.uniqueId) {
      CommonStore.update((s) => {
        s.selectedOutletTable =
          outletTables.find(
            (table) => table.uniqueId === selectedOutletTable.uniqueId,
          ) || {};
      });
    }
  }, [selectedOutletSection, outletTables, userOrdersTableDict]);

  useEffect(() => {
    if (selectedOutletTableId === '' && outletTables.length > 0) {
      const outletTablesFiltered = outletTables.filter((table) => {
        if (table.seated <= 0) {
          return true;
        } else {
          return false;
        }
      });

      if (outletTablesFiltered.length > 0) {
        setSelectedOutletTableId(outletTablesFiltered[0].uniqueId);
      }
    }
  }, [outletTables, selectedOutletTableId]);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <TouchableOpacity
        onPress={() => {
          if (isAlphaUser || true) {
            navigation.navigate('MenuOrderingScreen');

            CommonStore.update((s) => {
              s.currPage = 'MenuOrderingScreen';
              s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
            });
          }
          else {
            navigation.navigate('Table');

            CommonStore.update((s) => {
              s.currPage = 'Table';
              s.currPageStack = [...currPageStack, 'Table'];
            });
          }
          if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.OPERATION;
            });
          }
        }}
        style={styles.headerLeftStyle}>
        <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        />
      </TouchableOpacity>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            // justifyContent: Platform.OS === 'android' ? 'center' : 'flex-start',
            // alignItems: Platform.OS === 'android' ? 'center' : 'flex-start',
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            ...global.getHeaderTitleStyle(),
          },
          // windowWidth >= 768 && switchMerchant
          //   ? { right: windowWidth * 0.1 }
          //   : {},
          // windowWidth <= 768
          //   ? { right: 20 }
          //   : {},
        ]}>
        <Text
          style={{
            fontSize: switchMerchant ? 20 : 24,
            // lineHeight: 25,
            textAlign: 'left',
            alignItems: 'flex-start',
            justifyContent: 'flex-start',
            fontFamily: 'NunitoSans-Bold',
            color: Colors.whiteColor,
            opacity: 1,
            paddingLeft: Platform.OS === 'ios' ? "1%" : 0,
          }}>
          Reservation
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        {outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: 'white',
            width: 0.5,
            height: windowHeight * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
          }}></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate('Setting');
            }
          }}
          style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[{
              fontFamily: 'NunitoSans-SemiBold',
              fontSize: switchMerchant ? 10 : 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }, switchMerchant ? { width: windowWidth / 8 } : {}]}
            numberOfLines={switchMerchant ? 1 : 1}
          >

            {userName}
          </Text>
          <View
            style={{
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'white',
            }}>
            <Image
              style={{
                width: windowHeight * 0.035,
                height: windowHeight * 0.035,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            />
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   setInterval(() => {
  //     refresh();
  //   }, 5000);
  //   //getReservationList();
  //   //a refresh();
  //   ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
  //     setState({ newReservationStatus: result.reservationStatus });
  //   });
  // }

  const renderTableLayout = ({ item, index }) => {
    // var orderEstimatedTime = 0;
    // var orderStatus = '';
    // var orderDate = '';

    // const newRevDateTime = moment(
    //   `${moment(reservationDate).format('DD MMM YYYY')} ${moment(
    //     reservationTime,
    //   ).format('hh:mm A')}`,
    //   'DD MMM YYYY hh:mm A',
    // ).valueOf();
    // var tempUserReservation = [];
    // tempUserReservation = userReservationsRaw.filter((order) => {
    //   if (
    //     order.outletId === currOutletId &&
    //     moment(newRevDateTime).isSame(order.reservationTime)
    //   ) {
    //     return true;
    //   } else {
    //     return false;
    //   }
    // });

    // if (
    //   userOrdersTableDict[item.uniqueId] &&
    //   userOrdersTableDict[item.uniqueId][0]
    // ) {
    //   orderEstimatedTime =
    //     (moment().valueOf() -
    //       userOrdersTableDict[item.uniqueId][0].estimatedPreparedDate) /
    //     (1000 * 60);
    //   var days = Math.floor(orderEstimatedTime / 60 / 24);
    //   var hours = Math.floor((orderEstimatedTime / 60) % 24);
    //   var minutes = Math.floor(orderEstimatedTime % 60);
    //   orderStatus = userOrdersTableDict[item.uniqueId][0].orderStatus;
    //   orderDate = userOrdersTableDict[item.uniqueId][0].orderDate;
    // }

    var bookedReservation = null;

    var isAvailable = true;
    for (var i = 0; i < filteredReservations.length; i++) {
      if (
        filteredReservations[i].tableId === item.uniqueId &&
        moment(filteredReservations[i].reservationTime).isSameOrAfter(
          reservationIntervalStartTime,
        ) &&
        moment(filteredReservations[i].reservationTime).isBefore(
          reservationIntervalEndTime,
        )
      ) {
        // console.log('here x 3', filteredReservations[i].status)

        if (filteredReservations[i].status === 'CANCELED') {
          isAvailable = true;
          break;
        }

        bookedReservation = filteredReservations[i];

        isAvailable = false;
        break;
      }
      // console.log(i + ': ', filteredReservations[i]);
    }

    if (
      (moment().isSame(reservationIntervalStartTime, 'day') ||
        moment().isSame(reservationIntervalEndTime, 'day')) &&
      item.seated > 0
    ) {
      for (var i = 0; i < filteredReservations.length; i++) {
        // console.log('here here', filteredReservations[i].status)
        if (filteredReservations[i].status !== 'CANCELED') {
          isAvailable = false;
        }
      }
    }

    if (
      // item.code != null && item.capacity != null && item.seated == 0
      isAvailable &&
      item.code != null
    ) {
      // not seated tables
      return (
        <TouchableOpacity
          style={[
            styles.emptyTableDisplay,
            {
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10,
              borderRadius: 8,
              //backgroundColor: 'blue',
              ...(item.joinedTables && {
                width:
                  (item.joinedTables.length == 2
                    ? windowWidth *
                    0.12 *
                    item.joinedTables.length
                    : item.joinedTables.length == 3
                      ? windowWidth *
                      0.12 *
                      item.joinedTables.length
                      : item.joinedTables.length == 4
                        ? windowWidth *
                        0.12 *
                        item.joinedTables.length
                        : windowWidth * 0.12) +
                  (item.joinedTables.length - 1) * 20,
              }),
            },
          ]}
          onPress={() => {
            CommonStore.update((s) => {
              s.selectedOutletTable = item;
            });

            setSeatingModal(true);
          }}>
          <View
            style={[
              styles.emptyTableDisplay,
              {
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },

                backgroundColor: Colors.primaryColor,
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
                borderRadius: 8,
                ...(item.joinedTables && {
                  width:
                    (item.joinedTables.length == 2
                      ? windowWidth *
                      0.12 *
                      item.joinedTables.length
                      : item.joinedTables.length == 3
                        ? windowWidth *
                        0.12 *
                        item.joinedTables.length
                        : item.joinedTables.length == 4
                          ? windowWidth *
                          0.12 *
                          item.joinedTables.length
                          : windowWidth * 0.12) +
                    (item.joinedTables.length - 1) * 20,
                }),
              },
            ]}>
            <View
              style={{
                flex: 1,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text></Text>

                <View
                  style={{
                    //flex:0.2,
                    alignItems: 'flex-end',
                  }}>
                  <Text
                    style={[
                      styles.tableCode,
                      switchMerchant ? { fontSize: 7 } : { fontSize: 11 },
                      {
                        color: Colors.whiteColor,
                      },
                    ]}
                    numberOfLines={1}
                    ellipsizeMode="tail">
                    {item.code}
                  </Text>
                </View>
              </View>

              <View style={{ alignItems: 'center' }}>
                <Text
                  style={[
                    styles.tableCode,
                    {
                      fontSize: switchMerchant ? 10 : 13,
                      color: Colors.whiteColor,
                    },
                  ]}>
                  Seats: 0/{item.capacity}
                  {/* Available */}
                </Text>
              </View>
              <View
                style={[
                  // styles.tableCode,
                  {
                    alignItems: 'flex-end',
                  },
                ]}>
                <Text
                  style={[
                    {
                      fontSize: switchMerchant ? 10 : 13,
                      color: Colors.whiteColor,
                      fontFamily: 'NunitoSans-Bold',
                      // color: Colors.whiteColor,
                    },
                    switchMerchant ? { fontSize: 7 } : { fontSize: 11 },
                  ]}
                  numberOfLines={1}
                  ellipsizeMode="tail">
                  {'Available'}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      );
    }
    // else if (item.seated > 0 || tempUserReservation.length > 0) {
    else if (item.code != null && !isAvailable) {
      // var dys = Math.floor(moment().diff(item.updatedAt, 'hours') / 24);
      // var hrs = Math.floor(
      //   (moment().diff(item.updatedAt, 'minutes') / 60) % 24,
      // );
      // var mins = Math.floor(moment().diff(item.updatedAt, 'minutes') % 60);

      return (
        <TouchableOpacity
          style={[
            styles.emptyTableDisplay,
            {
              backgroundColor: Colors.secondaryColor,
              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
              borderRadius: 8,

              // ...((item.joinedTables) && {
              //   marginRight: 10 * (item.joinedTables.length),
              // }),

              ...(item.joinedTables && {
                // width: windowWidth * 0.135 * (item.joinedTables.length),
                width:
                  (item.joinedTables.length == 2
                    ? windowWidth *
                    0.12 *
                    item.joinedTables.length
                    : item.joinedTables.length == 3
                      ? windowWidth *
                      0.12 *
                      item.joinedTables.length
                      : item.joinedTables.length == 4
                        ? windowWidth *
                        0.12 *
                        item.joinedTables.length
                        : windowWidth * 0.12) +
                  (item.joinedTables.length - 1) * 20,
              }),
            },
          ]}
          onPress={() => {
            if (bookedReservation === null) {
              Alert.alert(
                'Info',
                `Table has been seated`,
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
              );
            } else {
              Alert.alert(
                'Info',
                `This table has been seated or reserved at ${moment(
                  bookedReservation.reservationTime,
                ).format(
                  'DD MMM YYYY hh:mm A',
                )}.\n\nPlease choose another reservation date or time`,
                [{ text: 'OK', onPress: () => { } }],
                { cancelable: false },
              );
            }
          }}>
          <View
            style={{
              flex: 1,
              justifyContent: 'space-between',
            }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text></Text>

              <View
                style={{
                  //flex:0.2,
                  alignItems: 'flex-end',
                }}>
                <Text
                  style={[
                    styles.tableCode,
                    {
                      color: Colors.blackColor,
                    },
                    switchMerchant ? { fontSize: 7 } : { fontSize: 11 },
                  ]}
                  numberOfLines={1}
                  ellipsizeMode="tail">
                  {item.code}
                </Text>
              </View>
            </View>

            <View style={{ alignItems: 'center' }}>
              <Text
                style={[
                  {
                    fontSize: switchMerchant ? 10 : 13,
                    color: Colors.blackColor,
                    fontFamily: 'NunitoSans-Bold',
                  },
                ]}>
                {bookedReservation
                  ? `Seats: ${bookedReservation.pax}/${item.capacity}`
                  : `Seats: ${item.seated}/${item.capacity}`}
                {/* {bookedReservation ? `Reserved at ${moment(bookedReservation.reservationTime).format('hh:mm A')}` : 'Seated now'} */}
              </Text>
            </View>
            <View
              style={[
                // styles.tableCode,
                {
                  alignItems: 'flex-end',
                },
              ]}>
              <Text
                style={[
                  {
                    fontSize: switchMerchant ? 10 : 13,
                    color: Colors.blackColor,
                    fontFamily: 'NunitoSans-Bold',
                    // color: Colors.whiteColor,
                  },
                  switchMerchant ? { fontSize: 7 } : { fontSize: 11 },
                ]}
                numberOfLines={1}
                ellipsizeMode="tail">
                {bookedReservation
                  ? `Reserved at ${moment(
                    bookedReservation.reservationTime,
                  ).format('DD MMM YYYY hh:mm A')}`
                  : 'Seated now'}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      );
    }
  };

  const renderSection = ({ item }) => {
    return (
      <View style={{ flexDirection: 'row' }}>
        <TouchableOpacity
          onPress={() => {
            CommonStore.update((s) => {
              s.selectedOutletSection = item;
            });
          }}>
          <View
            style={[
              styles.sectionAreaButton,
              item.uniqueId == selectedOutletSection.uniqueId
                ? { backgroundColor: Colors.primaryColor }
                : null,
              switchMerchant ? { height: 35 } : {},
            ]}>
            <Text
              style={[
                styles.sectionAreaButtonTxt,
                item.uniqueId == selectedOutletSection.uniqueId
                  ? { color: Colors.whiteColor }
                  : null,
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              {item.sectionName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const refresh = () => {
    ApiClient.GET(API.getOutletReservation + User.getOutletId()).then(
      (result) => {
        setState({ table: result });
        // console.log('result: ', result);
      },
    );
  };

  const acceptReservation = (param) => {
    const tableCode = outletTables.find(
      (table) => table.uniqueId === selectedOutletTableId,
    ).code;

    var body = {
      reservationId: param.uniqueId,
      tableId: selectedOutletTableId,
      tableCode: tableCode,

      userId: param.userId,
      pax: param.pax,
      reservationTime: param.reservationTime,
      outletName: currOutlet.name,
    };

    (
      netInfo.isInternetReachable && netInfo.isConnected
        ?
        ApiClient.POST(API.acceptReservation, body)
        :
        APILocal.acceptReservation({ body: body })
    )
      .then((result) => {
        if (result.status) {
          Alert.alert(
            'Success',
            'Reservation has been accepted',
            [{ text: 'OK', onPress: () => { } }],
            { cancelable: false },
          );
        }
      });
  };

  const getReservationList = () => {
    ApiClient.GET(API.getOutletReservation + User.getOutletId())
      .then((result) => {
        setState({ reservation: result });
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const seatedReservation = (param) => {
    const tableCode = outletTables.find(
      (table) => table.uniqueId === selectedOutletTableId,
    ).code;

    var body = {
      reservationId: selectedReservation.uniqueId,
      // seated: 1,
      tableCode: tableCode,
      tableId: selectedOutletTableId,
    };

    // ApiClient.POST(API.seatedReservation, body, false)
    APILocal.seatedReservation({ body: body })
      .then((result) => {
        if (result.status) {
          // getReservationList()
          Alert.alert('Success', 'Reservation has been seated');

          setTableModalVisibility(false);
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const noShowReservation = (param) => {
    var body = {
      reservationId: param,
      // seated: 1,
    };

    // ApiClient.POST(API.noShowReservation, body, false)
    APILocal.noShowReservation({ body: body })
      .then((result) => {
        if (result.status) {
          // getReservationList()
          Alert.alert('Success', 'Reservation has been updated');
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const cancelReservation = (param) => {
    var body = {
      reservationId: param.uniqueId,
      // seated: 1,

      userId: param.userId,
      pax: param.pax,
      reservationTime: param.reservationTime,
      outletName: currOutlet.name,
    };

    (
      netInfo.isInternetReachable && netInfo.isConnected
        ?
        ApiClient.POST(API.cancelReservation, body, false)
        :
        APILocal.cancelReservation({ body: body })
    )
      .then((result) => {
        if (result.status) {
          // getReservationList()
          Alert.alert('Success', 'Reservation has been cancelled');
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const undoReservation = (param) => {
    var body = {
      reservationId: param,
      // seated: 1,
    };

    // ApiClient.POST(API.undoReservation, body, false)
    APILocal.undoReservation({ body: body })
      .then((result) => {
        if (result.status) {
          // getReservationList()
          Alert.alert('Success', 'Reservation has been undone');
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const switchReservationStatus = (value) => {
    // var body = {
    //   outletId: User.getOutletId(),
    // };

    var body = {
      // outletId: User.getOutletId()
      outletId: currOutlet.uniqueId,
      reservationStatus: value,
    };

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // ApiClient.POST(API.switchReservationStatus, body)
    APILocal.switchReservationStatus({ body: body })
      .then((result) => {
        if (result.status) {
          setTimeout(() => {
            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }, 1000);
        }
        // if (result.reservationStatus == true) {
        //   Alert.alert(
        //     'Success',
        //     'Open Rservation',
        //     [{ text: 'OK', onPress: () => { } }],
        //     { cancelable: false },
        //   );
        // }

        // if (result.reservationStatus == false) {
        //   Alert.alert(
        //     'Success',
        //     'Close Rservation',
        //     [{ text: 'OK', onPress: () => { } }],
        //     { cancelable: false },
        //   );
        // }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const createReservation = () => {
    const tableCode = outletTables.find(
      (table) => table.uniqueId === selectedOutletTableId,
    ).code;

    const newRevDateTime = moment(
      `${moment(reservationDate).format('DD MMM YYYY')} ${moment(
        reservationTime,
      ).format('hh:mm A')}`,
      'DD MMM YYYY hh:mm A',
    ).valueOf();

    var body = {
      outletId: currOutlet.uniqueId,
      pax: seatingPax,
      reservationTime: newRevDateTime,

      merchantId: merchantId,
      outletCover: currOutlet.cover,
      merchantLogo: merchantLogo,
      outletName: currOutlet.name,
      merchantName: merchantName,

      userName: reservationCustomerName,
      userPhone: reservationPhone,
      userEmail: '',

      // tableId: selectedOutletTableId,
      // tableCode: tableCode,

      tableId: selectedOutletTable.uniqueId,
      tableCode: selectedOutletTable.code,

      remarks: reservationRemark,
    };

    if (
      reservationCustomerName !== '' &&
      reservationPhone !== '' &&
      selectTable !== '' &&
      reservationDate !== '' &&
      reservationTime !== '' &&
      reservationRemark !== ''
    ) {
      // ApiClient.POST(API.createUserReservationByMerchant, body, false)
      APILocal.createUserReservationByMerchant({
        body: body,
        uid: firebaseUid,
      })
        .then((result) => {
          if (result.status) {
            Alert.alert('Success', 'Reservation has been created');
            setAddReservationModal(false);
          }
        })
        .catch((err) => {
          // console.log(err);
        });
    } else {
      alert(
        'Please fill in all the information.',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );
    }
  };

  const updateReservation = () => {
    const tableCode = outletTables.find(
      (table) => table.uniqueId === selectedOutletTableId,
    ).code;

    const newRevDateTime = moment(
      `${moment(reservationDate).format('DD MMM YYYY')} ${moment(
        reservationTime,
      ).format('hh:mm A')}`,
      'DD MMM YYYY hh:mm A',
    ).valueOf();

    var body = {
      outletId: currOutlet.uniqueId,
      pax: seatingPax,
      reservationTime: newRevDateTime,

      merchantId: merchantId,
      outletCover: currOutlet.cover,
      merchantLogo: merchantLogo,
      outletName: currOutlet.name,
      merchantName: merchantName,

      userName: reservationCustomerName,
      userPhone: reservationPhone,
      userEmail: '',

      // tableId: selectedOutletTableId,
      // tableCode: tableCode,

      tableId: selectedOutletTable.uniqueId,
      tableCode: selectedOutletTable.code,

      remarks: reservationRemark,

      reservationId: reservationId,
    };

    if (
      reservationCustomerName !== '' &&
      reservationPhone !== '' &&
      selectTable !== '' &&
      reservationDate !== '' &&
      reservationTime !== '' &&
      reservationRemark !== ''
    ) {
      // ApiClient.POST(API.createUserReservationByMerchant, body, false)
      APILocal.updateUserReservationByMerchant({
        body: body,
        uid: firebaseUid,
      })
        .then((result) => {
          if (result.status) {
            Alert.alert('Success', 'Reservation has been updated');
            setAddReservationModal(false);
          }
        })
        .catch((err) => {
          // console.log(err);
        });
    } else {
      alert(
        'Please fill in all the information.',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );
    }
  };

  const getUserReservationPhone = (param) => {
    ApiClient.GET(API.getUserReservationPhone + param).then((result) => {
      if (result == null) {
        Alert.alert(
          'Error',
          'No customer phone number found',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      } else {
        Alert.alert(
          'Customer Phone:',
          result.user.number,
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
      }
    });
  };

  // const seatedReservation = () => {
  //   var body = {
  //     reservationId: '4',
  //     seated: 2,
  //   };
  //   ApiClient.POST(API.seatedReservation, body).then((result) => {

  //     if (result.success == true) {
  //       Alert.alert(
  //         'Success',
  //         'The customer has been seated',
  //         [{ text: 'OK', onPress: () => { } }],
  //         { cancelable: false },
  //       );
  //     }
  //   });
  // }

  // function here
  const renderTable = ({ item }) => (
    // console.log(item),
    (
      <View style={styles.tablebox}>
        <View style={styles.countIndicator}>
          <Text style={{ color: Colors.whiteColor, fontSize: 20 }}>5</Text>
        </View>
        <Text
          style={{
            fontWeight: 'bold',
            alignSelf: 'center',
            fontSize: 30,
            flex: 1,
          }}>
          B1
        </Text>
        <TouchableOpacity style={{ alignSelf: 'flex-end' }} onPress={() => { }}>
          <Feather name="chevron-down" size={40} color={Colors.primaryColor} />
        </TouchableOpacity>
      </View>
    )
  );

  // const renderUserTagItem = ({ item, index }) => {
  //    if(item.emailList.includes('<EMAIL>')){
  //     return(
  //       <View>
  //         <Text>
  //           {item.name}

  //         </Text>
  //       </View>
  //     )
  //   }

  //}

  useEffect(() => {
    var userTagListTemp = [];

    if (crmUserTags.length > 0) {
      for (var i = 0; i < crmUserTags.length; i++) {
        if (crmUserTags[i].emailList.includes(userReservations.userEmail)) {
          userTagListTemp.push(crmUserTags[i]);
        }
      }
    }

    setUserTagList(userTagListTemp);
  }, [crmUserTags]);

  useEffect(() => {
    if (crmUserTags.length > 0) {
      setUserTagDropdownList(
        crmUserTags
          .filter((item) => {
            var isExisted = false;

            for (var i = 0; i < userTagList.length; i++) {
              if (userTagList[i].uniqueId === item.uniqueId) {
                isExisted = true;
                break;
              }
            }

            return !isExisted;
          })
          .map((item) => {
            return { label: item.name, value: item.uniqueId };
          }),
      );
    }
  }, [crmUserTags, userTagList]);

  useEffect(() => {
    var userReservationsTemp = [];

    if (filterType == 0) {
      //Prioritize
      userReservationsTemp = userReservationsRaw;
    }
    if (filterType == 1) {
      //Prioritize
      userReservationsTemp = userReservationsRaw.filter(
        (order) => order.status === USER_RESERVATION_STATUS.PENDING,
      );
    }
    if (filterType == 2) {
      //orderid
      userReservationsTemp = userReservationsRaw.filter(
        (order) => order.status === USER_RESERVATION_STATUS.ACCEPTED,
      );
    }
    if (filterType == 3) {
      //date time
      userReservationsTemp = userReservationsRaw.filter(
        (order) => order.status === USER_RESERVATION_STATUS.SEATED,
      );
    }
    if (filterType == 4) {
      //Name
      userReservationsTemp = userReservationsRaw.filter(
        (order) => order.status === USER_RESERVATION_STATUS.SERVED,
      );
    }
    if (filterType == 5) {
      //Waiting Time
      userReservationsTemp = userReservationsRaw.filter(
        (order) => order.status === USER_RESERVATION_STATUS.CANCELED,
      );
    }
    if (filterType == 6) {
      //Waiting Time
      userReservationsTemp = userReservationsRaw.filter(
        (order) => order.status === USER_RESERVATION_STATUS.NO_SHOW,
      );
    }

    setUserReservations(userReservationsTemp);
  }, [filterType, userReservationsRaw]);

  useEffect(() => {
    if (currOutletId !== '') {
      setUserReservations(
        userReservationsRaw.filter((order) => {
          if (
            order.outletId === currOutletId &&
            moment(rev_date).isBefore(order.reservationTime) &&
            moment(rev_date1).isAfter(order.reservationTime)
          ) {
            return true;
          } else if (
            order.outletId === currOutletId &&
            moment(rev_date).isSame(order.reservationTime, 'day') ||
            moment(rev_date1).isSame(order.reservationTime, 'day')
          ) {
            return true;
          } else {
            return false;
          }
        }),
      );
    }
  }, [rev_date, rev_date1, currOutletId, userReservationsRaw]);

  // const renderRow = ({ item }) => {
  //   // var userTags = [];

  //   // // for (var i = 0; i < item.outletItemSkuList.length; i++) {
  //   //   // if (crmUserTags[item.outletItemSkuList[i]]) {
  //   //   //   userTags.push(crmUserTags[[item.outletItemSkuList[i]]][0].name);
  //   //   // }
  //   // //}
  //   // if (crmUserTags) {
  //   //   userTags.push(crmUserTags.name);
  //   // }

  //   var showNewTag = false;
  //   if (
  //     userReservationsUserIdDict[item.userId] &&
  //     userReservationsUserIdDict[item.userId].length === 1
  //   ) {
  //     showNewTag = true;
  //   }

  //   var swipeBtns = [];

  //   if (item.status === USER_RESERVATION_STATUS.SEATED) {
  //     swipeBtns.push(
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Undo{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="rotate-ccw"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="rotate-ccw"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabCyan,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Alert.alert('Alert', 'Do you want to undo this reservation?', [
  //             {
  //               text: 'YES',
  //               onPress: () => {
  //                 // setStatusReject(true);

  //                 undoReservation(item.uniqueId);
  //               },
  //             },
  //             {
  //               text: 'NO',
  //               onPress: () => { },
  //             },
  //           ]);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Call{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="phone-call"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="phone-call"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.primaryColor,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Linking.openURL(`tel:${item.userPhone}`);
  //           //   deleteNote(rowData);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               No-Show
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="alert-triangle"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="alert-triangle"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabGrey,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           //   deleteNote(rowData);
  //           // setStatusNoShow(true)

  //           noShowReservation(item.uniqueId);
  //         },
  //       },
  //     );
  //   } else if (item.status === USER_RESERVATION_STATUS.PENDING) {
  //     swipeBtns.push(
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Call{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="phone-call"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="phone-call"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.primaryColor,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Linking.openURL(`tel:${item.userPhone}`);
  //           //   deleteNote(rowData);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               No-Show
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="alert-triangle"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="alert-triangle"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabGrey,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           //   deleteNote(rowData);
  //           // setStatusNoShow(true)

  //           noShowReservation(item.uniqueId);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               {' '}
  //               {item.status === USER_RESERVATION_STATUS.ACCEPTED
  //                 ? 'Seated'
  //                 : 'Accept'}{' '}
  //             </Text>
  //             {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
  //               // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
  //               <>
  //                 {switchMerchant ? (
  //                   <MIcon
  //                     name="seat-outline"
  //                     size={13}
  //                     color={Colors.whiteColor}
  //                   />
  //                 ) : (
  //                   <MIcon
  //                     name="seat-outline"
  //                     size={36}
  //                     color={Colors.whiteColor}
  //                   />
  //                 )}
  //               </>
  //             ) : (
  //               <>
  //                 {switchMerchant ? (
  //                   <Feather
  //                     name="user-check"
  //                     size={10}
  //                     color={Colors.whiteColor}
  //                     style={{ marginTop: 3 }}
  //                   />
  //                 ) : (
  //                   <Feather
  //                     name="user-check"
  //                     size={31}
  //                     color={Colors.whiteColor}
  //                     style={{ marginTop: 3 }}
  //                   />
  //                 )}
  //               </>
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabGold,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           if (
  //             item.status === USER_RESERVATION_STATUS.PENDING ||
  //             item.status === USER_RESERVATION_STATUS.NO_SHOW ||
  //             item.status === USER_RESERVATION_STATUS.CANCELED
  //           ) {
  //             // setSelectedReservation(item);
  //             // setTableModalVisibility(true);
  //             acceptReservation(item);
  //           } else {
  //             // acceptReservation(item);
  //             setSelectedReservation(item);
  //             setTableModalVisibility(true);
  //           }
  //           // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
  //           //   acceptReservation(item);
  //           // } else {
  //           //   setSelectedReservation(item);
  //           //   setTableModalVisibility(true);
  //           // }
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Reject{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="user-x"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 3 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="user-x"
  //                 size={31}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 3 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabRed,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Alert.alert('Alert', 'Do you want to reject this reservation?', [
  //             {
  //               text: 'YES',
  //               onPress: () => {
  //                 // setStatusReject(true);

  //                 cancelReservation(item);
  //               },
  //             },
  //             {
  //               text: 'NO',
  //               onPress: () => { },
  //             },
  //           ]);
  //         },
  //       },
  //     );
  //   } else if (item.status === USER_RESERVATION_STATUS.CANCELED) {
  //     swipeBtns.push(
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Undo{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="rotate-ccw"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="rotate-ccw"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabCyan,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Alert.alert('Alert', 'Do you want to undo this reservation?', [
  //             {
  //               text: 'YES',
  //               onPress: () => {
  //                 // setStatusReject(true);

  //                 undoReservation(item.uniqueId);
  //               },
  //             },
  //             {
  //               text: 'NO',
  //               onPress: () => { },
  //             },
  //           ]);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Call{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="phone-call"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="phone-call"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.primaryColor,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Linking.openURL(`tel:${item.userPhone}`);
  //           //   deleteNote(rowData);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               No-Show
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="alert-triangle"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="alert-triangle"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabGrey,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           //   deleteNote(rowData);
  //           // setStatusNoShow(true)

  //           noShowReservation(item.uniqueId);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               {' '}
  //               {item.status === USER_RESERVATION_STATUS.ACCEPTED
  //                 ? 'Seated'
  //                 : 'Accept'}{' '}
  //             </Text>
  //             {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
  //               // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
  //               <>
  //                 {switchMerchant ? (
  //                   <MIcon
  //                     name="seat-outline"
  //                     size={13}
  //                     color={Colors.whiteColor}
  //                   />
  //                 ) : (
  //                   <MIcon
  //                     name="seat-outline"
  //                     size={36}
  //                     color={Colors.whiteColor}
  //                   />
  //                 )}
  //               </>
  //             ) : (
  //               <>
  //                 {switchMerchant ? (
  //                   <Feather
  //                     name="user-check"
  //                     size={10}
  //                     color={Colors.whiteColor}
  //                     style={{ marginTop: 3 }}
  //                   />
  //                 ) : (
  //                   <Feather
  //                     name="user-check"
  //                     size={31}
  //                     color={Colors.whiteColor}
  //                     style={{ marginTop: 3 }}
  //                   />
  //                 )}
  //               </>
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabGold,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           if (
  //             item.status === USER_RESERVATION_STATUS.PENDING ||
  //             item.status === USER_RESERVATION_STATUS.NO_SHOW ||
  //             item.status === USER_RESERVATION_STATUS.CANCELED
  //           ) {
  //             // setSelectedReservation(item);
  //             // setTableModalVisibility(true);
  //             acceptReservation(item);
  //           } else {
  //             // acceptReservation(item);
  //             setSelectedReservation(item);
  //             setTableModalVisibility(true);
  //           }
  //           // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
  //           //   acceptReservation(item);
  //           // } else {
  //           //   setSelectedReservation(item);
  //           //   setTableModalVisibility(true);
  //           // }
  //         },
  //       },
  //     );
  //   } else if (item.status === USER_RESERVATION_STATUS.NO_SHOW) {
  //     swipeBtns.push(
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Undo{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="rotate-ccw"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="rotate-ccw"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabCyan,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Alert.alert('Alert', 'Do you want to undo this reservation?', [
  //             {
  //               text: 'YES',
  //               onPress: () => {
  //                 // setStatusReject(true);

  //                 undoReservation(item.uniqueId);
  //               },
  //             },
  //             {
  //               text: 'NO',
  //               onPress: () => { },
  //             },
  //           ]);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Call{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="phone-call"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="phone-call"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.primaryColor,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Linking.openURL(`tel:${item.userPhone}`);
  //           //   deleteNote(rowData);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               {' '}
  //               {item.status === USER_RESERVATION_STATUS.ACCEPTED
  //                 ? 'Seated'
  //                 : 'Accept'}{' '}
  //             </Text>
  //             {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
  //               // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
  //               <>
  //                 {switchMerchant ? (
  //                   <MIcon
  //                     name="seat-outline"
  //                     size={13}
  //                     color={Colors.whiteColor}
  //                   />
  //                 ) : (
  //                   <MIcon
  //                     name="seat-outline"
  //                     size={36}
  //                     color={Colors.whiteColor}
  //                   />
  //                 )}
  //               </>
  //             ) : (
  //               <>
  //                 {switchMerchant ? (
  //                   <Feather
  //                     name="user-check"
  //                     size={10}
  //                     color={Colors.whiteColor}
  //                     style={{ marginTop: 3 }}
  //                   />
  //                 ) : (
  //                   <Feather
  //                     name="user-check"
  //                     size={31}
  //                     color={Colors.whiteColor}
  //                     style={{ marginTop: 3 }}
  //                   />
  //                 )}
  //               </>
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabGold,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           if (
  //             item.status === USER_RESERVATION_STATUS.PENDING ||
  //             item.status === USER_RESERVATION_STATUS.NO_SHOW ||
  //             item.status === USER_RESERVATION_STATUS.CANCELED
  //           ) {
  //             // setSelectedReservation(item);
  //             // setTableModalVisibility(true);
  //             acceptReservation(item);
  //           } else {
  //             // acceptReservation(item);
  //             setSelectedReservation(item);
  //             setTableModalVisibility(true);
  //           }
  //           // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
  //           //   acceptReservation(item);
  //           // } else {
  //           //   setSelectedReservation(item);
  //           //   setTableModalVisibility(true);
  //           // }
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Reject{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="user-x"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 3 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="user-x"
  //                 size={31}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 3 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabRed,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Alert.alert('Alert', 'Do you want to reject this reservation?', [
  //             {
  //               text: 'YES',
  //               onPress: () => {
  //                 // setStatusReject(true);

  //                 cancelReservation(item);
  //               },
  //             },
  //             {
  //               text: 'NO',
  //               onPress: () => { },
  //             },
  //           ]);
  //         },
  //       },
  //     );
  //   } else {
  //     swipeBtns.push(
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Undo{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="rotate-ccw"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="rotate-ccw"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabCyan,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Alert.alert('Alert', 'Do you want to undo this reservation?', [
  //             {
  //               text: 'YES',
  //               onPress: () => {
  //                 // setStatusReject(true);

  //                 undoReservation(item.uniqueId);
  //               },
  //             },
  //             {
  //               text: 'NO',
  //               onPress: () => { },
  //             },
  //           ]);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Call{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="phone-call"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="phone-call"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.primaryColor,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Linking.openURL(`tel:${item.userPhone}`);
  //           //   deleteNote(rowData);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               No-Show
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="alert-triangle"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="alert-triangle"
  //                 size={30}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 5 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabGrey,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           //   deleteNote(rowData);
  //           // setStatusNoShow(true)

  //           noShowReservation(item.uniqueId);
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               {' '}
  //               {item.status === USER_RESERVATION_STATUS.ACCEPTED
  //                 ? 'Seated'
  //                 : 'Accept'}{' '}
  //             </Text>
  //             {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
  //               // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
  //               <>
  //                 {switchMerchant ? (
  //                   <MIcon
  //                     name="seat-outline"
  //                     size={13}
  //                     color={Colors.whiteColor}
  //                   />
  //                 ) : (
  //                   <MIcon
  //                     name="seat-outline"
  //                     size={36}
  //                     color={Colors.whiteColor}
  //                   />
  //                 )}
  //               </>
  //             ) : (
  //               <>
  //                 {switchMerchant ? (
  //                   <Feather
  //                     name="user-check"
  //                     size={10}
  //                     color={Colors.whiteColor}
  //                     style={{ marginTop: 3 }}
  //                   />
  //                 ) : (
  //                   <Feather
  //                     name="user-check"
  //                     size={31}
  //                     color={Colors.whiteColor}
  //                     style={{ marginTop: 3 }}
  //                   />
  //                 )}
  //               </>
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabGold,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           if (
  //             item.status === USER_RESERVATION_STATUS.PENDING ||
  //             item.status === USER_RESERVATION_STATUS.NO_SHOW ||
  //             item.status === USER_RESERVATION_STATUS.CANCELED
  //           ) {
  //             // setSelectedReservation(item);
  //             // setTableModalVisibility(true);
  //             acceptReservation(item);
  //           } else {
  //             // acceptReservation(item);
  //             setSelectedReservation(item);
  //             setTableModalVisibility(true);
  //           }
  //           // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
  //           //   acceptReservation(item);
  //           // } else {
  //           //   setSelectedReservation(item);
  //           //   setTableModalVisibility(true);
  //           // }
  //         },
  //       },
  //       {
  //         component: (
  //           <View
  //             style={{
  //               justifyContent: 'center',
  //               alignItems: 'center',
  //               alignContent: 'center',
  //               flex: 1,
  //             }}>
  //             <Text
  //               style={[
  //                 { fontFamily: 'NunitoSans-Bold', color: 'white' },
  //                 switchMerchant ? { fontSize: 10 } : {},
  //               ]}>
  //               Reject{' '}
  //             </Text>
  //             {switchMerchant ? (
  //               <Feather
  //                 name="user-x"
  //                 size={10}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 3 }}
  //               />
  //             ) : (
  //               <Feather
  //                 name="user-x"
  //                 size={31}
  //                 color={Colors.whiteColor}
  //                 style={{ marginTop: 3 }}
  //               />
  //             )}
  //           </View>
  //         ),
  //         backgroundColor: Colors.tabRed,
  //         underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
  //         onPress: () => {
  //           Alert.alert('Alert', 'Do you want to reject this reservation?', [
  //             {
  //               text: 'YES',
  //               onPress: () => {
  //                 // setStatusReject(true);

  //                 cancelReservation(item);
  //               },
  //             },
  //             {
  //               text: 'NO',
  //               onPress: () => { },
  //             },
  //           ]);
  //         },
  //       },
  //     );
  //   }

  //   var borderColor = Colors.tabYellow;
  //   if (item.status === USER_RESERVATION_STATUS.CANCELED) {
  //     borderColor = Colors.tabRed;
  //   } else if (
  //     item.status === USER_RESERVATION_STATUS.SEATED ||
  //     item.status === USER_RESERVATION_STATUS.ACCEPTED
  //   ) {
  //     borderColor = Colors.primaryColor;
  //   } else if (item.status === USER_RESERVATION_STATUS.NO_SHOW) {
  //     borderColor = Colors.tabGrey;
  //   }

  //   return (
  //     <Swipeout
  //       right={swipeBtns}
  //       autoClose="true"
  //       buttonWidth={windowWidth * 0.06}
  //       style={{
  //         marginBottom: 10,
  //         // borderRadius: 15,
  //         borderRadius: 5,
  //         marginHorizontal: 10,
  //         borderWidth: 1,
  //         borderColor: borderColor,
  //       }}>
  //       <TouchableOpacity onPress={() => {
  //         setAddReservationModal(true);
  //         setReservationCustomerName(item.userName);
  //         setReservationPhone(item.userPhone);
  //         setReservationDate(item.reservationTime);
  //         setSeatingPax(item.pax);
  //         setSelectTable('existed');

  //         setReservationId(item.uniqueId);
  //         if (item.tableId) {
  //           CommonStore.update((s) => {
  //             s.selectedOutletTable =
  //               outletTables.find(
  //                 (table) => table.uniqueId === item.tableId,
  //               ) || {};
  //           });
  //         }
  //         setReservationRemark(item.remarks);
  //       }}>
  //         <View
  //           style={[
  //             styles.rowItem,
  //             switchMerchant
  //               ? {
  //                 width: windowWidth * 0.36,
  //               }
  //               : {},
  //           ]}>
  //           <View
  //             style={{
  //               position: 'absolute',
  //               right: 7,
  //               top: 5,
  //             }}>
  //             <Text
  //               style={[
  //                 {
  //                   fontFamily: 'NunitoSans-SemiBold',
  //                   color: borderColor,
  //                   fontSize: 14,
  //                 },
  //                 switchMerchant
  //                   ? {
  //                     fontSize: 10,
  //                   }
  //                   : {},
  //               ]}>
  //               {USER_RESERVATION_STATUS_PARSED[item.status]}
  //             </Text>
  //           </View>

  //           <View style={{ width: '38%', marginLeft: 10 }}>
  //             <TouchableOpacity
  //               onPress={() => {
  //                 var crmUser = null;

  //                 if (item.crmUserId !== undefined) {
  //                   for (var i = 0; i < crmUsers.length; i++) {
  //                     if (item.crmUserId === crmUsers[i].uniqueId) {
  //                       crmUser = crmUsers[i];
  //                       break;
  //                     }
  //                   }
  //                 }

  //                 if (!crmUser) {
  //                   for (var i = 0; i < crmUsers.length; i++) {
  //                     if (item.userId === crmUsers[i].firebaseUid) {
  //                       crmUser = crmUsers[i];
  //                       break;
  //                     }
  //                   }
  //                 }

  //                 if (crmUser) {
  //                   CommonStore.update(
  //                     (s) => {
  //                       s.selectedCustomerEdit = crmUser;
  //                       // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

  //                       s.routeParams = {
  //                         pageFrom: 'Reservation',
  //                       };
  //                     },
  //                     () => {
  //                       navigation.navigate('NewCustomer');
  //                     },
  //                   );
  //                 }
  //               }}>
  //               <View
  //                 style={
  //                   {
  //                     //alignItems: 'center',
  //                   }
  //                 }>
  //                 <Text
  //                   style={[
  //                     {
  //                       textAlign: 'left',
  //                       fontWeight: 'bold',
  //                       marginBottom: 5,
  //                       fontSize: 20,
  //                       width: Platform.OS == 'ios' ? '100%' : '100%',
  //                     },
  //                     switchMerchant
  //                       ? {
  //                         fontSize: 10,
  //                       }
  //                       : {},
  //                   ]}>
  //                   {item.userName ? item.userName : '-'}
  //                 </Text>
  //               </View>
  //               {showNewTag ? (
  //                 <View style={{}}>
  //                   <Text
  //                     style={[
  //                       {
  //                         fontFamily: 'NunitoSans-SemiBold',
  //                         color: Colors.primaryColor,
  //                         fontSize: 14,
  //                         //textAlign: 'center'
  //                       },
  //                       switchMerchant
  //                         ? {
  //                           fontSize: 10,
  //                         }
  //                         : {},
  //                     ]}>
  //                     New
  //                   </Text>
  //                 </View>
  //               ) : (
  //                 <View
  //                   style={
  //                     {
  //                       // position: 'absolute',
  //                       // right: 15,
  //                       // top: 10,
  //                       //alignSelf: 'center'
  //                     }
  //                   }>
  //                   <Text
  //                     style={[
  //                       {
  //                         fontFamily: 'NunitoSans-SemiBold',
  //                         color: Colors.primaryColor,
  //                         fontSize: 14,
  //                         //textAlign: 'center'
  //                       },
  //                       switchMerchant
  //                         ? {
  //                           fontSize: 10,
  //                         }
  //                         : {},
  //                     ]}>
  //                     Existing
  //                   </Text>
  //                 </View>
  //               )}
  //             </TouchableOpacity>
  //             {/* <Text style={{ fontSize: 18 }}>CUSTOMER</Text> */}
  //             {/* <Text>{userTags}</Text> */}
  //             {/* <FlatList
  //               //data={userTagList}
  //               data={crmUserTags}
  //               renderItem={renderUserTagItem}
  //               keyExtractor={(item, index) => String(index)}
  //               style={{ marginTop: 10, paddingTop: 10 }}
  //             /> */}
  //             {/* {userReservations.length >= 10 ?
  //             <View>
  //               <Text>
  //                 Old
  //               </Text>
  //             </View>
  //             : <View>
  //               <Text>
  //                 hi
  //               </Text>
  //             </View> } */}
  //           </View>
  //           <View style={{ alignItems: 'center', width: '22%' }}>
  //             <Text
  //               style={[
  //                 { fontSize: 20, fontFamily: 'NunitoSans-Bold' },
  //                 switchMerchant
  //                   ? {
  //                     fontSize: 10,
  //                   }
  //                   : {},
  //               ]}>
  //               {item.pax}
  //             </Text>
  //             <Text
  //               style={[
  //                 { fontSize: 18, fontFamily: 'NunitoSans-Light' },
  //                 switchMerchant
  //                   ? {
  //                     fontSize: 8,
  //                   }
  //                   : {},
  //               ]}>
  //               PAX
  //             </Text>
  //           </View>
  //           <View
  //             style={[
  //               { alignItems: 'center', width: '38%' },
  //               switchMerchant
  //                 ? {
  //                   top: windowHeight * -0.019,
  //                 }
  //                 : {},
  //             ]}>
  //             <View style={{ flexDirection: 'row' }}>
  //               <Text
  //                 style={[
  //                   {
  //                     fontSize: 13,
  //                     fontFamily: 'NunitoSans-Light',
  //                     marginTop: 10,
  //                     width: '30%',
  //                   },
  //                   switchMerchant
  //                     ? {
  //                       fontSize: 10,
  //                     }
  //                     : {},
  //                 ]}>
  //                 Date :{' '}
  //               </Text>
  //               <Text
  //                 style={[
  //                   {
  //                     fontSize: 13,
  //                     fontFamily: 'NunitoSans-Bold',
  //                     marginTop: 10,
  //                     width: '70%',
  //                   },
  //                   switchMerchant
  //                     ? {
  //                       fontSize: 10,
  //                     }
  //                     : {},
  //                 ]}>
  //                 {moment(item.reservationTime).format('DD MMM YYYY')}
  //               </Text>
  //             </View>
  //             <View style={{ flexDirection: 'row', alignItems: 'center' }}>
  //               <Text
  //                 style={[
  //                   {
  //                     fontSize: 13,
  //                     fontFamily: 'NunitoSans-Light',
  //                     width: '30%',
  //                   },
  //                   switchMerchant
  //                     ? {
  //                       fontSize: 10,
  //                     }
  //                     : {},
  //                 ]}>
  //                 Time :{' '}
  //               </Text>
  //               <Text
  //                 style={[
  //                   {
  //                     fontSize: 13,
  //                     fontFamily: 'NunitoSans-Bold',
  //                     width: '70%',
  //                   },
  //                   switchMerchant
  //                     ? {
  //                       fontSize: 10,
  //                     }
  //                     : {},
  //                 ]}>
  //                 {moment(item.reservationTime).format('hh:mm A')}
  //               </Text>
  //             </View>
  //           </View>
  //         </View>
  //       </TouchableOpacity>
  //     </Swipeout>
  //   );
  // };

  //   return (
  //     <Swipeout
  //       right={swipeBtns}
  //       autoClose="true"
  //       style={{ marginBottom: 10, borderRadius: 15, marginRight: 10 }}
  //     >
  //       <View >
  //         <View
  //           style={styles.rowItem}>
  //           <View style={{ alignItems: 'center' }}>
  //             <Text style={{ fontWeight: 'bold', fontSize: 20 }}>{item.user !== null? item.user.name : "-"}</Text>
  //             <Text style={{ fontSize: 18 }}>CUSTOMER</Text>
  //           </View>
  //           <View style={{ alignItems: 'center' }}>
  //             <Text style={{ fontWeight: 'bold', fontSize: 20 }}>{item.numberOfPersons}</Text>
  //             <Text style={{ fontSize: 18 }}>PAX</Text>
  //           </View>
  //           <View style={{ alignItems: 'center' }}>
  //             <View style={{ flexDirection: 'row' }}>
  //               <Text style={{ fontSize: 18 }}>Date : </Text>
  //               <Text style={{ fontSize: 18 }}>{moment(item.reservationTime).format("D/M/YYYY")}</Text>
  //             </View>
  //             <View style={{ flexDirection: 'row' }}>
  //               <Text style={{ fontSize: 18 }}>Time : </Text>
  //               <Text style={{ fontSize: 18 }}>{moment(item.reservationTime).format("h:mmA")}</Text>
  //             </View>
  //           </View>
  //         </View>
  //       </View>
  //     </Swipeout>
  //   );
  // }

  const MemoizedTouchableOpacity = React.memo(TouchableOpacity);

  const viewNote = (rowData) => {
    props.navigator.push({
      title: 'The Note',
      component: ViewNote,
      passProps: {
        noteText: rowData,
        noteId: noteId(rowData),
      },
    });
  };
  // function end

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>
    (<View
      style={[
        styles.container,
        !isTablet()
          ? {
            transform: [{ scaleX: 1 }, { scaleY: 1 }],
          }
          : {},
      ]}>
      {/* <View
        style={[
          styles.sidebar,
          !isTablet()
            ? {
              width: windowWidth * Styles.sideBarWidth,
            }
            : {},
          switchMerchant
            ? {
              // width: '10%'
            }
            : {},
        ]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={1}
          expandOperation={true}
        />
      </View> */}
      <View style={styles.content}>
        {/* <FlatList
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          style={{ padding: 10 }}
          data={table}
          renderItem={renderTable}
          keyExtractor={(item) => item.id}
        /> */}

        <DateTimePickerModal
          // supportedOrientations={['landscape', 'portrait']}
          isVisible={showDateTimePicker}
          mode={'date'}
          onConfirm={(text) => {
            setRev_date(moment(text).startOf('day'));
            // console.log('time0 + ' + moment(text).format('DD MM YYYY'));
            setShowDateTimePicker(false);
          }}
          onCancel={() => {
            setShowDateTimePicker(false);
          }}
          style={{ zIndex: 1000 }}
          maximumDate={moment(rev_date1).toDate()}
        />

        <DateTimePickerModal
          // supportedOrientations={['landscape', 'portrait']}
          isVisible={showDateTimePicker1}
          mode={'date'}
          onConfirm={(text) => {
            setRev_date1(moment(text).endOf('day'));
            setShowDateTimePicker1(false);
            // console.log('rev' + rev_date1);
          }}
          onCancel={() => {
            setShowDateTimePicker1(false);
            // console.log('rev' + rev_date);
          }}
          style={{ zIndex: 1000 }}
          minimumDate={moment(rev_date).toDate()}
        />

        <DateTimePickerModal
          isVisible={showReservationDatePicker}
          supportedOrientations={['portrait', 'landscape']}
          mode={'date'}
          minimumDate={Date.now()}
          onConfirm={(text) => {
            setReservationDate(moment(text));

            setShowReservationDatePicker(false);
          }}
          onCancel={() => {
            setShowReservationDatePicker(false);
          }}
        />

        <DateTimePickerModal
          isVisible={showReservationTimePicker}
          supportedOrientations={['portrait', 'landscape']}
          mode={'time'}
          onConfirm={(text) => {
            setReservationTime(moment(text));

            setShowReservationTimePicker(false);
          }}
          onCancel={() => {
            setShowReservationTimePicker(false);
          }}
        />

        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={seatingModal}
          transparent={true}>
          <View
            style={{
              flex: 1,
              backgroundColor: Colors.modalBgColor,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <View
              style={[
                {
                  width: windowWidth * 0.3,
                  height: windowHeight * 0.5,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 12,
                  padding: windowWidth * 0.04,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                },
                switchMerchant
                  ? {
                    height: windowHeight * 0.7,
                    paddingTop: windowHeight * 0.06,
                  }
                  : {},
              ]}>
              <TouchableOpacity
                style={[
                  styles.closeButton,
                  switchMerchant
                    ? {
                      position: 'absolute',
                      top: windowWidth * 0.02,
                      right: windowWidth * 0.02,
                    }
                    : {},
                ]}
                onPress={() => {
                  setSeatingModal(false);
                }}>
                {switchMerchant ? (
                  <AIcon
                    name="closecircle"
                    size={15}
                    color={Colors.fieldtTxtColor}
                  />
                ) : (
                  <AIcon
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                )}
              </TouchableOpacity>
              <View style={(styles.modalTitle, { alignItems: 'center' })}>
                <Text
                  style={[
                    styles.modalTitleText,
                    switchMerchant
                      ? {
                        fontSize: 16,
                        // top: windowWidth * -0.03,
                      }
                      : {},
                  ]}>
                  Table
                </Text>
                <View
                  style={[
                    styles.tableSlotDisplay,
                    {
                      // width: selectedOutletTable.code.length <= 3 ? windowWidth * 0.1 : selectedOutletTable.code.length <= 6 ? windowWidth * 0.22 : selectedOutletTable.code.length <= 10 ? windowWidth * 0.25 : windowWidth * 0.1 ,
                      ...(selectedOutletTable.code &&
                        selectedOutletTable.code.length && {
                        width: 27 * selectedOutletTable.code.length,
                      }),
                      minWidth: 140,
                      maxWidth: 200,
                      height: windowWidth * 0.1,
                      marginHorizontal: 5,
                      paddingHorizontal: 0,
                    },
                    switchMerchant
                      ? {
                        // top: windowWidth * -0.05,
                      }
                      : {},
                  ]}>
                  <Text
                    style={[
                      styles.modalTitleText,
                      {
                        fontSize: 16,
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {selectedOutletTable.code}
                  </Text>
                </View>
              </View>
              <View
                style={[
                  styles.modalBody,
                  { width: '100%', alignItems: 'center' },
                  switchMerchant
                    ? {
                      // top: windowHeight * -0.06,
                      right: windowWidth * 0.016,
                    }
                    : {},
                ]}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                  }}>
                  <View style={{ flex: 1 }} />
                  <View
                    style={[
                      {
                        justifyContent: 'space-between',
                        flexDirection: 'row',
                        flex: 1.0,
                        borderWidth: StyleSheet.hairlineWidth,
                        borderColor: Colors.fieldtTxtColor,
                        width: windowWidth * 0.08,
                        // borderWidth: 4
                      },
                      switchMerchant
                        ? { left: windowWidth * 0.016 }
                        : {},
                    ]}>
                    <TouchableOpacity
                      style={{
                        backgroundColor: Colors.primaryColor,
                        width: windowWidth * 0.02,
                        height: windowWidth * 0.02,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        seatingPax >= 1 ? setSeatingPax(seatingPax - 1) : null;
                      }}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 18,
                            color: Colors.whiteColor,
                            bottom: windowHeight * 0.002,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        -
                      </Text>
                    </TouchableOpacity>
                    <View
                      style={[
                        {
                          alignItems: 'center',
                          justifyContent: 'center',
                        },
                        switchMerchant
                          ? {
                            width: '30%',
                            right: windowWidth * 0.026,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            color: Colors.primaryColor,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              // borderWidth: 1
                            }
                            : {},
                        ]}>
                        {seatingPax}
                      </Text>
                    </View>
                    <TouchableOpacity
                      style={[
                        {
                          backgroundColor: Colors.primaryColor,
                          width: windowWidth * 0.02,
                          height: windowWidth * 0.02,
                          alignItems: 'center',
                          justifyContent: 'center',
                        },
                        switchMerchant ? { position: 'absolute', right: 0 } : {},
                      ]}
                      onPress={() => {
                        seatingPax <
                          selectedOutletTable.capacity -
                          selectedOutletTable.seated
                          ? setSeatingPax(seatingPax + 1)
                          : null;
                      }}>
                      <Text
                        style={[
                          {
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 18,
                            color: Colors.whiteColor,
                            bottom: windowHeight * 0.002,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        +
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={{ flex: 1, alignItems: 'center' }}>
                    <Text
                      style={[
                        {
                          fontFamily: 'NunitoSans-SemiBold',
                          fontSize: 16,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            left: windowWidth * 0.012,
                          }
                          : {},
                      ]}>
                      Capacity{' '}
                      {selectedOutletTable.capacity -
                        selectedOutletTable.seated}
                    </Text>
                  </View>
                </View>
              </View>

              <View
                style={{
                  alignItems: 'center',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  width: '100%',
                }}>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 162,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    },
                    switchMerchant
                      ? {
                        width: windowWidth * 0.13,
                        height: windowHeight * 0.08,
                      }
                      : {},
                  ]}
                  onPress={() => {
                    setSelectTable('existed');
                    setOpenSelectTable(false);
                    setSeatingModal(false);
                  }}>
                  <Text
                    style={[
                      {
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    SELECT TABLE
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ModalView>

        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={openSelectTable}
          transparent={true}>
          <View
            style={{
              flex: 1,
              backgroundColor: Colors.modalBgColor,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                padding: windowHeight * 0.03,
                height: '90%',
                width: '90%',
                borderRadius: 12,
              }}>
              <TouchableOpacity
                style={[
                  styles.closeButton,
                  switchMerchant
                    ? {
                      position: 'absolute',
                      top: windowHeight * 0.02,
                      right: windowWidth * 0.02,
                    }
                    : {},
                ]}
                onPress={() => {
                  setOpenSelectTable(false);
                }}>
                {switchMerchant ? (
                  <AIcon
                    name="closecircle"
                    size={15}
                    color={Colors.fieldtTxtColor}
                  />
                ) : (
                  <AIcon
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                )}
              </TouchableOpacity>
              <View
                style={{
                  height: 40,
                }}>
                <FlatList
                  data={outletSections}
                  renderItem={renderSection}
                  keyExtractor={(item, index) => String(index)}
                  horizontal={true}
                  showsHorizontalScrollIndicator={false}
                  style={[
                    {
                      width: '93%',
                      // width: 50,
                      height: 100,
                    },
                    switchMerchant
                      ? {
                        // paddingLeft: windowWidth * 0.05
                      }
                      : {},
                  ]}
                  contentContainerStyle={{
                    height: '100%',
                    alignItems: 'center',
                    width: '85%',

                    // marginRight: 100,

                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    // paddingTop: 30,
                    // paddingLeft: 5,
                    // paddingRight: 70
                    paddingRight: 20,
                  }}
                />
              </View>
              <View
                style={[
                  {
                    // paddingLeft: windowWidth * 0.04,
                    paddingTop: windowHeight * 0.01,
                    //marginLeft: windowWidth * 0.04,
                    width: switchMerchant
                      ? windowWidth * 0.8
                      : '100%',
                    height: switchMerchant
                      ? windowHeight * 0.52
                      : windowHeight * 0.6,
                    display: 'flex',
                    justifyContent: 'center',
                    flexDirection: 'row',
                    // backgroundColor: 'red',
                  },
                ]}>
                <FlatList
                  data={filteredOutletTablesForRendered.concat({
                    actionButton: true,
                  })}
                  showsVerticalScrollIndicator={false}
                  renderItem={renderTableLayout}
                  keyExtractor={(item, index) => String(index)}
                  // numColumns={6}
                  contentContainerStyle={{
                    // backgroundColor: 'red',
                    paddingBottom: windowHeight * 0.05,
                    // justifyContent: 'center',
                    // alignItems: 'center',
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    // justifyContent: 'center',
                    // left: switchMerchant? windowWidth * -0.02 : 0,.
                  }}
                  keyboardShouldPersistTaps="handled"
                  maxToRenderPerBatch={1}
                />
              </View>
              <View
                style={{
                  flex: 1,
                  alignItems: 'center',
                }}>
                <View
                  style={{
                    justifyContent: 'space-around',
                    height: '100%',
                    // paddingTop: 10,
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    {/* rectangle */}
                    <View
                      style={{
                        backgroundColor: Colors.primaryColor,
                        color: Colors.whiteColor,
                        width: windowHeight * 0.03,
                        height: windowHeight * 0.03,
                      }}></View>
                    <Text
                      style={{
                        color: Colors.primaryColor,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        paddingLeft: windowWidth * 0.01,
                      }}>
                      Available
                    </Text>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    {/* rectangle */}
                    <View
                      style={{
                        backgroundColor: Colors.secondaryColor,
                        color: Colors.whiteColor,
                        width: windowHeight * 0.03,
                        height: windowHeight * 0.03,
                      }}></View>
                    <Text
                      style={{
                        color: Colors.secondaryColor,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                        paddingLeft: windowWidth * 0.01,
                      }}>
                      {`Seated / Reserved within ${reservationIntervalMin} minutes (default is 120 minutes)`}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </ModalView>

        {/* <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={false}
          transparent={true}>
          <View
            style={{
              flex: 1,
              backgroundColor: Colors.modalBgColor,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <View
              style={[
                {
                  width:
                    windowWidth <= 1133
                      ? windowWidth * 0.45
                      : windowWidth * 0.4,
                  Height: windowHeight * 0.47,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: windowWidth * 0.03,
                  padding: windowWidth * 0.04,
                  paddingBottom: 0,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                },
                switchMerchant
                  ? {
                    padding: windowWidth * 0.005,
                    height: windowHeight * 0.8,
                    paddingBottom: 0,
                  }
                  : {},
              ]}>
              <View
                style={[
                  { justifyContent: 'space-between' },
                  switchMerchant
                    ? {
                      height: '100%',
                    }
                    : {},
                ]}>
                <View style={{}}>
                  <Text
                    style={[
                      {
                        fontSize: 24,
                        justifyContent: 'center',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 16,
                        }
                        : {},
                    ]}>
                    {'Add Reservation'}
                  </Text>
                  <Text
                    style={[
                      {
                        fontSize: 20,
                        justifyContent: 'center',
                        alignSelf: 'center',
                        marginTop: 5,
                        color: Colors.descriptionColor,
                        fontFamily: 'NunitoSans-Regular',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>{`There are currently ${userReservations.length} parties in reservation.`}</Text>
                </View>
                <View
                  style={[
                    { justifyContent: 'center', width: '100%' },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // marginTop: '-30%'
                        bottom: windowHeight * 0.12,
                      }
                      : {},
                  ]}>
                  <View
                    style={[
                      {
                        justifyContent: 'center',
                        alignSelf: 'center',
                        alignContent: 'center',
                        marginTop: 30,
                        flexDirection: 'row',
                        width: '100%',
                      },
                      switchMerchant
                        ? {
                          marginTop: 0,
                          // borderWidth: 1
                        }
                        : {},
                    ]}>
                    <View style={{ justifyContent: 'center', width: '45%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Customer Name:
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TextInput
                        placeholder="John Doe"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                            }
                            : {},
                        ]}
                        onChangeText={(text) => {
                          setReservationCustomerName(text);
                        }}
                        defaultValue={reservationCustomerName}
                      />
                    </View>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 0 : 20,
                      flexDirection: 'row',
                      width: '100%',
                    }}>
                    <View style={{ justifyContent: 'center', width: '45%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Mobile Number:
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TextInput
                        placeholder="012 - 777 777"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                            }
                            : {},
                        ]}
                        onChangeText={(text) => {
                          setReservationPhone(text);
                        }}
                        keyboardType={'decimal-pad'}
                        defaultValue={reservationPhone}
                      />
                    </View>
                  </View>

                  <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 0 : 20,
                      flexDirection: 'row',
                      width: '100%',
                    }}>
                    <View style={{ justifyContent: 'center', width: '45%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Capacity:
                      </Text>

                      {/* <Text style={{ fontSize: switchMerchant ? 10 : 20, color: "", fontFamily: 'Nunitosans-Bold', width: '45%', }}>
                        Next Visit Time
                      </Text>
                      <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'center', width: '50%', justifyContent: 'space-between', marginLeft: 5 }}
                        // disabled={selectedCustomerEdit !== null}
                        onPress={() => { setShowNextVisitTimePicker(true); }}
                      >

                        <Text style={switchMerchant ? { fontSize: 10 } : {}}>{customerNextVisitTime ? moment(customerNextVisitTime).format('hh:mm A') : 'N/A'}</Text>

                        {
                          // selectedCustomerEdit === null
                          true
                            ?
                            <GCalendar width={switchMerchant ? 10 : 22} height={switchMerchant ? 10 : 22} />
                            :
                            <></>
                        }
                      </TouchableOpacity> 
                    </View>

                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TextInput
                        placeholder="0"
                        keyboardType={'decimal-pad'}
                        //placeholderTextColor={Colors.descriptionColor}
                        //placeholderStyle={{color: Colors.descriptionColor, justifyContent: 'center', alignItems: 'center'}}
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 99,
                            textAlign: 'left'
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                              paddingLeft: 10,
                              // textAlign: 'left'
                            }
                            : {},
                        ]}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        onChangeText={(text) => {
                          setReservationPax(
                            text.length > 0 ? text : '',
                          );
                        }}
                        defaultValue={reservationPax}
                      />
                    </View>
                  </View>

                  <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 5 : 20,
                      flexDirection: 'row',
                      width: '100%',
                      zIndex: 1000
                    }}>
                    <View style={{ justifyContent: 'center', width: '45%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Select Table:
                      </Text>


                    </View>

                    <View style={{ justifyContent: 'center', width: '60%', zIndex: 1000 }}>
                      {switchMerchant ?
                        <DropDownPicker
                          arrowSize={switchMerchant ? 13 : 20}
                          arrowStyle={[
                            { fontWeight: 'bold' },
                            switchMerchant
                              ? {
                                top: windowHeight * -0.001,
                                height: '280%',
                                // borderWidth: 1,
                              }
                              : {},
                          ]}
                          containerStyle={{
                            width: windowWidth * 0.2,
                            height: windowHeight * 0.06,
                            borderRadius: 15,
                          }}
                          style={{
                            borderWidth: 1,
                            borderRadius: 15,
                            borderColor: '#E5E5E5',
                            backgroundColor: Colors.fieldtBgColor,
                            paddingVertical: 0,
                            zIndex: 1000,
                            height: 35,
                            fontSize: 10
                            //paddingLeft: 10,
                          }}
                          dropDownStyle={{ zIndex: 1000, fontSize: 10, height: windowHeight * 0.2 }}
                          itemStyle={{
                            justifyContent: 'flex-start',
                            paddingLeft: 5,
                            fontSize: 10
                          }}
                          items={outletTables
                            .filter((table) => {
                              if (table.seated <= 0) {
                                return true;
                              } else {
                                return false;
                              }
                            })
                            .map((table) => {
                              return { label: table.code, value: table.uniqueId };
                            })}
                          onChangeItem={(item) => {
                            setSelectedOutletTableId(item.value);
                          }}
                          defaultValue={selectedOutletTableId}
                        />
                        :
                        <DropDownPicker
                          arrowSize={20}
                          containerStyle={{
                            height: 40,
                            width: 200,
                            borderRadius: 15,
                          }}
                          style={{
                            borderWidth: 1,
                            borderRadius: 15,
                            borderColor: '#E5E5E5',
                            backgroundColor: Colors.fieldtBgColor,
                            paddingVertical: 0,
                            zIndex: 1000,
                            //paddingLeft: 10,
                          }}
                          dropDownStyle={{ zIndex: 1000 }}
                          itemStyle={{
                            justifyContent: 'flex-start',
                            paddingLeft: 5,
                          }}
                          items={outletTables
                            .filter((table) => {
                              if (table.seated <= 0) {
                                return true;
                              } else {
                                return false;
                              }
                            })
                            .map((table) => {
                              return { label: table.code, value: table.uniqueId };
                            })}
                          onChangeItem={(item) => {
                            setSelectedOutletTableId(item.value);
                          }}
                          defaultValue={selectedOutletTableId}
                        />}

                    </View>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 10 : 20,
                      flexDirection: 'row',
                      width: '100%',
                    }}>
                    <View style={{ justifyContent: 'center', width: '45%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Reservation Date:
                      </Text>


                    </View>

                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TouchableOpacity style={[switchMerchant ? {
                        paddingHorizontal: switchMerchant ? 7 : 20,
                        backgroundColor: Colors.fieldtBgColor,
                        width: windowWidth * 0.2,
                        height: windowHeight * 0.06,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        alignContent: 'center',
                        borderColor: '#E5E5E5',
                        borderWidth: 1,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                        borderRadius: 5,
                      } : {
                        height: switchMerchant ? 35 : 40,
                        paddingHorizontal: switchMerchant ? 7 : 20,
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 150 : 200,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        alignContent: 'center',
                        borderColor: '#E5E5E5',
                        borderWidth: 1,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }]}

                        onPress={() => {
                          setShowReservationDatePicker(true)
                        }}
                      >

                        <GCalendar width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} />

                        <Text style={switchMerchant ? { fontSize: 10 } : {}}>{moment(reservationDate).format('DD MMM YYYY')}</Text>

                      </TouchableOpacity>
                    </View>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 10 : 20,
                      flexDirection: 'row',
                      width: '100%',
                    }}>
                    <View style={{ justifyContent: 'center', width: '45%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Reservation Time:
                      </Text>


                    </View>

                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TouchableOpacity style={[switchMerchant ? {
                        paddingHorizontal: switchMerchant ? 7 : 20,
                        backgroundColor: Colors.fieldtBgColor,
                        width: windowWidth * 0.2,
                        height: windowHeight * 0.06,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        alignContent: 'center',
                        borderColor: '#E5E5E5',
                        borderWidth: 1,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                        borderRadius: 5,
                      } : {
                        height: switchMerchant ? 35 : 40,
                        paddingHorizontal: switchMerchant ? 7 : 20,
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 150 : 200,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        alignContent: 'center',
                        borderColor: '#E5E5E5',
                        borderWidth: 1,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                      }]}

                        onPress={() => {
                          setShowReservationTimePicker(true)
                        }}
                      >
                        <EvilIcons name="clock" size={switchMerchant ? 20 : 25} color={Colors.primaryColor} />

                        <Text style={switchMerchant ? { fontSize: 10 } : {}}>{moment(reservationTime).format('hh:mm A')}</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>

                <View
                  style={[
                    {
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginTop: 35,
                      zIndex: -1000
                    },
                    switchMerchant
                      ? {
                        // marginTop: windowHeight * 0.067,
                        // borderWidth: 1,
                        position: 'absolute',
                        bottom: 0,
                        width: windowWidth * 0.45,
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    disabled={isLoading}
                    onPress={() => {
                      createReservation()
                    }}
                    style={[
                      {
                        backgroundColor: Colors.fieldtBgColor,
                        width:
                          windowWidth <= 1133
                            ? '56%'
                            : '57.8%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 60,
                        borderBottomLeftRadius: switchMerchant
                          ? windowWidth * 0.03
                          : 35,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      },
                      switchMerchant
                        ? {
                          height: 35,
                          width: '50%',
                        }
                        : {},
                    ]}>
                    {isLoading ? (
                      <>
                        {switchMerchant ? (
                          <ActivityIndicator
                            size={'small'}
                            color={Colors.primaryColor}
                          />
                        ) : (
                          <ActivityIndicator
                            size={'large'}
                            color={Colors.primaryColor}
                          />
                        )}
                      </>
                    ) : (
                      <Text
                        style={[
                          {
                            fontSize: 22,
                            color: Colors.primaryColor,
                            fontFamily: 'NunitoSans-SemiBold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Confirm
                      </Text>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={isLoading}
                    onPress={() => {
                      // setState({ visible: false });
                      setAddReservationModal(false);
                    }}
                    style={[
                      {
                        backgroundColor: Colors.fieldtBgColor,
                        width: '57.8%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 60,
                        borderBottomRightRadius: switchMerchant
                          ? windowWidth * 0.03
                          : 35,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      },
                      switchMerchant
                        ? {
                          height: 35,
                          width: '50%',
                        }
                        : {},
                    ]}>
                    <Text
                      style={[
                        {
                          fontSize: 22,
                          color: Colors.descriptionColor,
                          fontFamily: 'NunitoSans-SemiBold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </ModalView> */}

        <ModalView
          supportedOrientations={['landscape', 'portrait']}
          style={{ flex: 1 }}
          visible={addReservationModal}
          transparent={true}>
          <KeyboardAvoidingView
            behavior={'padding'}
            style={{
              flex: 1,
              backgroundColor: Colors.modalBgColor,
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10000,
              position: 'absolute',
              height: '100%',
              width: '100%',
              top: Platform.OS == 'android'
                ? 0
                : keyboardHeight > 0
                  ? -keyboardHeight * 0.45
                  : 0,
            }}>
            <View
              style={[
                {
                  width:
                    windowWidth <= 1133
                      ? windowWidth * 0.45
                      : windowWidth * 0.4,
                  height: windowHeight * 0.8,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 10,
                  padding: windowWidth * 0.04,
                  paddingBottom: 0,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                },
                switchMerchant
                  ? {
                    padding: windowWidth * 0.005,
                    height: windowHeight * 0.88,
                    paddingBottom: 0,
                  }
                  : {},
              ]}>
              <View
                style={[
                  { justifyContent: 'space-between' },
                  switchMerchant
                    ? {
                      height: '100%',
                    }
                    : {},
                ]}>
                <View style={{}}>
                  <Text
                    style={[
                      {
                        fontSize: 40,
                        justifyContent: 'center',
                        alignSelf: 'center',
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 20,
                        }
                        : {},
                    ]}>
                    {reservationId ? 'Edit Reservation' : 'Add Reservation'}
                  </Text>
                  <Text
                    style={[
                      {
                        fontSize: 16,
                        justifyContent: 'center',
                        alignSelf: 'center',
                        marginTop: 5,
                        color: Colors.descriptionColor,
                        fontFamily: 'NunitoSans-Regular',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>{`There are currently ${userReservations.filter(reservation => reservation.status !== USER_RESERVATION_STATUS.CANCELED).length} parties in reservation.`}</Text>
                </View>
                <ScrollView
                  style={[
                    { width: '100%', paddingLeft: 2 },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        marginTop: windowHeight * 0.16,
                        bottom: windowHeight * 0.12,
                      }
                      : {},
                  ]}>
                  <View
                    style={[
                      {
                        justifyContent: 'center',
                        alignSelf: 'center',
                        alignContent: 'center',
                        marginTop: 30,
                        flexDirection: 'row',
                        width: '100%',
                        paddingLeft: windowWidth * 0.01,
                      },
                      switchMerchant
                        ? {
                          marginTop: 1,
                          // borderWidth: 1
                        }
                        : {},
                    ]}>
                    <View style={{ justifyContent: 'center', width: '46.5%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 16,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Customer Name
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TextInput
                        placeholder="John Doe"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        placeholderStyle={{
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Regular',
                        }}
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                            }
                            : {},
                        ]}
                        onChangeText={(text) => {
                          setReservationCustomerName(text);
                        }}
                        defaultValue={reservationCustomerName}
                      />
                    </View>
                  </View>
                  <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 0 : 20,
                      flexDirection: 'row',
                      width: '100%',
                      paddingLeft: windowWidth * 0.01,
                    }}>
                    <View style={{ justifyContent: 'center', width: '46.5%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 16,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Mobile Number
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TextInput
                        placeholder="012 - 777 777"
                        placeholderStyle={{
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Regular',
                        }}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                            }
                            : {},
                        ]}
                        onChangeText={(text) => {
                          setReservationPhone(text);
                        }}
                        keyboardType={'decimal-pad'}
                        defaultValue={reservationPhone}
                      />
                    </View>
                  </View>

                  <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 5 : 20,
                      flexDirection: 'row',
                      width: '100%',
                      paddingLeft: windowWidth * 0.01,
                    }}>
                    <View style={{ justifyContent: 'center', width: '46.5%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 16,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Reservation Date
                      </Text>
                    </View>

                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TouchableOpacity
                        style={[
                          switchMerchant
                            ? {
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                              borderRadius: 5,
                            }
                            : {
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              width: switchMerchant ? 150 : 200,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                            },
                        ]}
                        onPress={() => {
                          setShowReservationDatePicker(true);
                        }}>
                        <GCalendar
                          width={switchMerchant ? 15 : 20}
                          height={switchMerchant ? 15 : 20}
                        />

                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Regular',
                          }}>
                          {moment(reservationDate).format('DD MMM YYYY')}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 10 : 20,
                      flexDirection: 'row',
                      width: '100%',
                      paddingLeft: windowWidth * 0.01,
                    }}>
                    <View style={{ justifyContent: 'center', width: '46.5%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 16,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Reservation Time
                      </Text>
                    </View>

                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TouchableOpacity
                        style={[
                          switchMerchant
                            ? {
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                              borderRadius: 5,
                            }
                            : {
                              height: switchMerchant ? 35 : 40,
                              paddingHorizontal: switchMerchant ? 7 : 20,
                              backgroundColor: Colors.fieldtBgColor,
                              width: switchMerchant ? 150 : 200,
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              alignContent: 'center',
                              borderColor: '#E5E5E5',
                              borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 16,
                            },
                        ]}
                        onPress={() => {
                          setShowReservationTimePicker(true);
                        }}>
                        <EvilIcons
                          name="clock"
                          size={switchMerchant ? 20 : 25}
                          color={Colors.primaryColor}
                        />

                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Regular',
                          }}>
                          {moment(reservationTime).format('hh:mm A')}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {/* <View
                    style={{
                      justifyContent: 'center',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 0 : 20,
                      flexDirection: 'row',
                      width: '100%',
                    }}>
                    <View style={{ justifyContent: 'center', width: '45%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 20,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Capacity:
                      </Text>
                    </View>

                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <Text
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 99,
                            paddingTop: windowHeight * 0.01,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                              paddingLeft: 10,
                              // textAlign: 'left'
                            }
                            : {},
                        ]}>
                        {seatingPax === 0 ? '0' : seatingPax}
                      </Text>
                    </View>
                  </View> */}

                  <View
                    style={{
                      justifyContent: 'flex-end',
                      alignSelf: 'center',
                      alignContent: 'center',
                      marginTop: switchMerchant ? 10 : 20,
                      flexDirection: 'row',
                      width: '100%',
                      paddingRight: windowWidth * 0.026,
                      // paddingLeft: windowWidth * 0.01,
                    }}>
                    <View style={{ height: '100%', justifyContent: 'center', width: '45%', position: 'absolute', left: 0, top: 0 }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 16,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.035,
                            }
                            : {},
                        ]}>
                        Select Table
                      </Text>
                    </View>
                    {/* <View></Vie> */}
                    <View
                      style={{
                        justifyContent: 'center',
                        backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant
                          ? windowWidth * 0.2
                          : 200,
                        height: switchMerchant
                          ? windowHeight * 0.06
                          : 40,
                        marginRight:
                          windowWidth <= 1823 &&
                            windowWidth >= 1820
                            ? 75
                            : 0,
                        borderRadius: 5,
                        zIndex: 1000,
                        alignItems: 'center',
                      }}>
                      <TouchableOpacity
                        onPress={() => {
                          setOpenSelectTable(true);
                        }}
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'center',
                          alignItems: 'center',
                          borderRadius: 10,
                          width: switchMerchant
                            ? windowWidth * 0.2
                            : 200,
                          height: switchMerchant
                            ? windowHeight * 0.06
                            : 40,
                          left: 0,
                          backgroundColor: '#0F1A3C',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                        }}>
                        <Text
                          style={[
                            {
                              color: 'white',
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 16,
                            },
                            switchMerchant
                              ? {
                                fontSize: 10,
                              }
                              : {},
                          ]}>
                          {selectTable === ''
                            ? 'SELECT TABLE'
                            : selectedOutletTable.code}
                        </Text>
                      </TouchableOpacity>
                      {/* {switchMerchant ? (
                        <RNPickerSelect
                          //pickerProps={{ style: { height: 160, overflow: 'hidden' } }}
                          placeholder={'Select Table'}
                          useNativeAndroidPickerStyle={false}
                          style={{
                            inputAndroidContainer: {
                              width: '100%',
                              color: 'black',
                              textAlignVertical: 'bottom',
                            },
                            inputIOS: {
                              fontSize: switchMerchant ? 10 : 14,
                              textAlign: 'center',
                              color: 'black',
                              textAlignVertical: 'bottom',
                            },
                            inputAndroid: {
                              fontSize: switchMerchant ? 10 : 14,
                              textAlign: 'center',
                              color: 'black',
                              textAlignVertical: 'bottom',
                            },
                          }}
                          items={outletTables
                            .filter((table) => {
                              if (table.seated <= 0) {
                                return true;
                              } else {
                                return false;
                              }
                            })
                            .map((table) => {
                              return {label: table.code, value: table.uniqueId};
                            })}
                          onValueChange={(value) => {

                            setSelectedOutletTableId(value);
                          }}
                          value={selectedOutletTableId}
                        />
                      ) : (
                        <RNPickerSelect
                          pickerProps={{
                            style: {},
                          }}
                          placeholder={'Select Table'}
                          style={{
                            inputAndroidContainer: {
                              width: '100%',
                              color: 'black'
                            },
                            inputIOS: {
                              fontSize: switchMerchant ? 10 : 14,
                              textAlign: 'center',
                              color: 'black'
                            },
                            inputAndroid: {
                              fontSize: switchMerchant ? 10 : 14,
                              textAlign: 'center',
                              color: 'black'
                            },
                          }}
                          items={outletTables
                            .filter((table) => {
                              if (table.seated <= 0) {
                                return true;
                              } else {
                                return false;
                              }
                            })
                            .map((table) => {
                              return {label: table.code, value: table.uniqueId};
                            })}
                          onValueChange={(value) => {
                            // setSelectedOutletCategoryId(value);

                            setSelectedOutletTableId(value);
                          }}
                          value={selectedOutletTableId}
                        />
                      )} */}
                    </View>
                  </View>

                  <View
                    style={[
                      {
                        justifyContent: 'center',
                        alignSelf: 'center',
                        alignContent: 'center',
                        flexDirection: 'row',
                        width: '100%',
                        paddingLeft: windowWidth * 0.01,
                        marginTop: switchMerchant ? 5 : 15,
                      },
                      switchMerchant
                        ? {
                          // marginTop: '3%',
                          // borderWidth: 1
                        }
                        : {},
                    ]}>
                    <View style={{ justifyContent: 'center', width: '46.5%' }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 16,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.037,
                            }
                            : {},
                        ]}>
                        Remarks
                      </Text>
                    </View>
                    <View style={{ justifyContent: 'center', width: '60%' }}>
                      <TextInput
                        placeholder="Remark..."
                        placeholderStyle={{
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Regular',
                        }}
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Regular',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              height: windowHeight * 0.06,
                              padding: 0,
                              margin: 0,
                            }
                            : {},
                        ]}
                        onChangeText={(text) => {
                          setReservationRemark(text);
                        }}
                        defaultValue={reservationRemark}
                      />
                    </View>
                  </View>
                  {/* <View
                    style={[
                      {
                        justifyContent: 'center',
                        alignSelf: 'center',
                        alignContent: 'flex-start',
                        flexDirection: 'row',
                        width: '100%',
                        marginTop: switchMerchant ? 5 : 15,
                      },
                      switchMerchant
                        ? {
                          // marginTop: '3%',
                          // borderWidth: 1
                        }
                        : {},
                    ]}>
                    <View
                      style={{
                        justifyContent: 'flex-start',
                        width: switchMerchant ? '44.5%' : '45%',
                        paddingTop: 5,
                      }}>
                      <Text
                        style={[
                          {
                            color: 'black',
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 16,
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              left: windowWidth * 0.033,
                            }
                            : {},
                        ]}>
                        Terms & Conditions
                      </Text>
                    </View>
                    <ScrollView
                      nestedScrollEnabled={true}
                      style={{ width: '60%' }}>
                      <Text
                        style={[
                          {
                            backgroundColor: Colors.fieldtBgColor,
                            width: 200,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                            textAlign: 'justify',
                            maxHeight: 300,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Regular',
                            marginTop: '0%',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                              width: windowWidth * 0.2,
                              padding: 0,
                              margin: 0,
                            }
                            : {},
                        ]}>
                        {`Term and conditions`}
                      </Text>
                    </ScrollView>
                  </View> */}
                </ScrollView>

                <View
                  style={[
                    {
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginTop: 35,
                      zIndex: -1000,
                    },
                    switchMerchant
                      ? {
                        // marginTop: windowHeight * 0.067,
                        // borderWidth: 1,
                        position: 'absolute',
                        bottom: 0,
                        width: windowWidth * 0.45,
                        left: windowWidth * 0.002,
                      }
                      : {},
                  ]}>
                  <TouchableOpacity
                    disabled={isLoading}
                    onPress={() => {
                      if (seatingPax <= 0) {
                        Alert.alert(
                          'Info',
                          'Pax cannot be 0',
                        );
                        return;
                      }

                      if (reservationId) {
                        updateReservation();
                      }
                      else {
                        createReservation();
                      }
                    }}
                    style={[
                      {
                        backgroundColor: Colors.fieldtBgColor,
                        width:
                          windowWidth <= 1133
                            ? '56%'
                            : '57.8%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 60,
                        borderBottomLeftRadius: switchMerchant ? 12 : 35,
                        borderRightWidth: StyleSheet.hairlineWidth,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      },
                      switchMerchant
                        ? {
                          height: 35,
                          width: '50%',
                        }
                        : {},
                    ]}>
                    {isLoading ? (
                      <>
                        {switchMerchant ? (
                          <ActivityIndicator
                            size={'small'}
                            color={Colors.primaryColor}
                          />
                        ) : (
                          <ActivityIndicator
                            size={'large'}
                            color={Colors.primaryColor}
                          />
                        )}
                      </>
                    ) : (
                      <Text
                        style={[
                          {
                            fontSize: 22,
                            color: Colors.primaryColor,
                            fontFamily: 'NunitoSans-SemiBold',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        Confirm
                      </Text>
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    disabled={isLoading}
                    onPress={() => {
                      // setState({ visible: false });
                      setAddReservationModal(false);
                    }}
                    style={[
                      {
                        backgroundColor: Colors.fieldtBgColor,
                        width: '57.8%',
                        justifyContent: 'center',
                        alignItems: 'center',
                        alignContent: 'center',
                        height: 60,
                        borderBottomRightRadius: switchMerchant ? 12 : 35,
                        borderTopWidth: StyleSheet.hairlineWidth,
                      },
                      switchMerchant
                        ? {
                          height: 35,
                          width: '50%',
                        }
                        : {},
                    ]}>
                    <Text
                      style={[
                        {
                          fontSize: 22,
                          color: Colors.descriptionColor,
                          fontFamily: 'NunitoSans-SemiBold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalView>

        {tableModalVisibility ? (
          <View
            style={{
              flex: 1,
              backgroundColor: Colors.modalBgColor,
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10000,
              position: 'absolute',
              // borderRadius: windowWidth * 0.03,
              width: switchMerchant
                ? windowWidth * 0.88
                : windowWidth * 0.93,
              height: switchMerchant
                ? windowHeight * 0.8
                : windowHeight * 0.85,
            }}>
            <View style={styles.modalContainerTable}>
              <View style={[styles.modalViewTable]}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    setTableModalVisibility(false);
                  }}>
                  {switchMerchant ? (
                    <AntDesign
                      name="closecircle"
                      size={15}
                      color={Colors.fieldtTxtColor}
                    />
                  ) : (
                    <AntDesign
                      name="closecircle"
                      size={25}
                      color={Colors.fieldtTxtColor}
                    />
                  )}
                </TouchableOpacity>
                <View
                  style={{
                    flexDirection: 'column',
                    paddingTop: switchMerchant
                      ? windowHeight * 0.1
                      : 80,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Text
                    style={[
                      switchMerchant
                        ? {
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                          marginBottom: 30,
                        }
                        : {
                          fontSize: switchMerchant ? 16 : 20,
                          fontFamily: 'NunitoSans-Bold',
                          marginBottom: 30,
                        },
                    ]}>
                    Select Table
                  </Text>

                  {console.log('selectedOutletTableId')}
                  {console.log(selectedOutletTableId)}
                  {selectedOutletTableId !== '' &&
                    outletTables.find(
                      (table) => table.uniqueId === selectedOutletTableId,
                    ) ? (
                    <DropDownPicker
                      arrowSize={20}
                      containerStyle={{
                        height: 40,
                        width: '60%',
                        borderRadius: 15,
                        zIndex: 1000,
                      }}
                      style={{
                        borderWidth: 1,
                        borderRadius: 15,
                        borderColor: '#E5E5E5',
                        backgroundColor: Colors.fieldtBgColor,
                        paddingVertical: 0,
                        zIndex: 1000,
                      }}
                      dropDownStyle={{
                        zIndex: 1000,
                        height: windowHeight * 0.22,
                      }}
                      itemStyle={{ justifyContent: 'flex-start', paddingLeft: 5 }}
                      items={outletTables
                        .filter((table) => {
                          if (table.seated <= 0) {
                            return true;
                          } else {
                            return false;
                          }
                        })
                        .map((table) => {
                          return { label: table.code, value: table.uniqueId };
                        })}
                      onChangeItem={(item) => {
                        setSelectedOutletTableId(item.value);
                      }}
                      defaultValue={selectedOutletTableId}
                    />
                  ) : (
                    <></>
                  )}

                  <View style={{ zIndex: -1000 }}>
                    <TouchableOpacity
                      style={{
                        marginTop: 20,
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#0F1A3C',
                        borderRadius: 5,
                        width: 100,
                        paddingHorizontal: 10,
                        height: 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1000,
                      }}
                      onPress={() => {
                        seatedReservation();
                      }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Seated
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </View>
        ) : null}

        <ModalView
          style={
            {
              // flex: 1
            }
          }
          visible={false}
          supportedOrientations={['landscape', 'portrait']}
          transparent={true}
          animationType={'slide'}>
          <View style={styles.modalContainerTable}>
            <View style={[styles.modalViewTable]}>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => {
                  setTableModalVisibility(false);
                }}>
                {switchMerchant ? (
                  <AntDesign
                    name="closecircle"
                    size={15}
                    color={Colors.fieldtTxtColor}
                  />
                ) : (
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                )}
              </TouchableOpacity>
              <View
                style={{
                  flexDirection: 'column',
                  paddingTop: switchMerchant
                    ? windowHeight * 0.1
                    : 80,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text
                  style={[
                    switchMerchant
                      ? {
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                        marginBottom: 30,
                      }
                      : {
                        fontSize: switchMerchant ? 16 : 20,
                        fontFamily: 'NunitoSans-Bold',
                        marginBottom: 30,
                      },
                  ]}>
                  Select Table
                </Text>

                {console.log('selectedOutletTableId')}
                {console.log(selectedOutletTableId)}
                {selectedOutletTableId !== '' &&
                  outletTables.find(
                    (table) => table.uniqueId === selectedOutletTableId,
                  ) ? (
                  <DropDownPicker
                    arrowSize={20}
                    containerStyle={{
                      height: 40,
                      width: '60%',
                      borderRadius: 15,
                      zIndex: 1000,
                    }}
                    style={{
                      borderWidth: 1,
                      borderRadius: 15,
                      borderColor: '#E5E5E5',
                      backgroundColor: Colors.fieldtBgColor,
                      paddingVertical: 0,
                      zIndex: 1000,
                    }}
                    dropDownStyle={{ zIndex: 1000 }}
                    itemStyle={{ justifyContent: 'flex-start', paddingLeft: 5 }}
                    items={outletTables
                      .filter((table) => {
                        if (table.seated <= 0) {
                          return true;
                        } else {
                          return false;
                        }
                      })
                      .map((table) => {
                        return { label: table.code, value: table.uniqueId };
                      })}
                    onChangeItem={(item) => {
                      setSelectedOutletTableId(item.value);
                    }}
                    defaultValue={selectedOutletTableId}
                  />
                ) : (
                  <></>
                )}

                <View style={{ zIndex: -1000 }}>
                  <TouchableOpacity
                    style={{
                      marginTop: 20,
                      justifyContent: 'center',
                      flexDirection: 'row',
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: '#0F1A3C',
                      borderRadius: 5,
                      width: 100,
                      paddingHorizontal: 10,
                      height: 40,
                      alignItems: 'center',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1000,
                    }}
                    onPress={() => {
                      seatedReservation();
                    }}>
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      Seated
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </ModalView>

        <View
          style={[
            {
              alignItems: switchMerchant ? 'flex-end' : 'center',
              flexDirection: 'row',
              alignSelf: 'center',
              marginVertical: switchMerchant ? 0 : 10,
              marginBottom: switchMerchant
                ? windowHeight * 0.08
                : 20,
              width: switchMerchant
                ? windowWidth * 0.76
                : windowWidth * 0.86,
              justifyContent: 'space-between',
              zIndex: 1000,
              // backgroundColor: 'red',
              // borderWidth: 1,

              // right: switchMerchant? 0: windowWidth * 0.016
              // paddingRight: windowWidth * 0.035,
              // paddingLeft: switchMerchant
              //   ? windowWidth * 0.03
              //   : windowWidth * 0.01,
              // paddingLeft: windowWidth * 0.045,
              // marginLeft: -windowWidth * 0.1,
            },
            switchMerchant
              ? {
                height: 35,
                marginTop: -2,
                marginLeft: windowWidth * -0.075,
                // paddingRight: windowWidth * 0.0295,
                paddingLeft: windowWidth * 0.01,
              }
              : {},
          ]}>
          <View
            style={{
              flexDirection: 'row',
              // paddingLeft: '10%',
              // borderWidth: 1
              //left: Platform.OS == 'ios' ? 130 : 0
            }}>
            <Text
              style={[
                {
                  // fontWeight: 'bold',
                  fontSize: 26,
                  marginRight: 0,
                  textAlign: 'center',
                  fontFamily: 'NunitoSans-Bold',
                },
                switchMerchant
                  ? {
                    // fontSize: 15,
                    //follow dashboard
                    fontSize: 20,
                    // borderWidth: 1,
                    // top: windowHeight * -0.05,
                  }
                  : {
                    marginLeft: -2,
                  },
              ]}>
              {userReservations.length} Reservation
            </Text>
            <View
              style={[
                {
                  flexDirection: 'column',
                  //right: Platform.OS == 'ios' ? 40 : 0,
                  top: Platform.OS == 'ios' ? 3 : 0,
                  marginLeft: '5%',
                  // height: '15%',
                },
                switchMerchant
                  ? {
                    // borderWidth: 1,
                    // top: windowHeight * -0.045,
                  }
                  : {},
              ]}>
              {isLoading ? (
                <View
                  style={
                    {
                      // paddingTop: Platform.OS == 'ios' ? 10 : 0,
                    }
                  }>
                  {switchMerchant ? (
                    <ActivityIndicator
                      size={'small'}
                      color={Colors.secondaryColor}
                    />
                  ) : (
                    <ActivityIndicator
                      size={'large'}
                      color={Colors.secondaryColor}
                    />
                  )}
                </View>
              ) : (
                <>
                  {switchMerchant ? (
                    <Switch
                      value={currOutlet.reservationStatus}
                      onSyncPress={(value) => {
                        // setState({ newReservationStatus: item }, () => {
                        //   switchReservationStatus();
                        // });
                        switchReservationStatus(value);
                        //// console.log(userReservations.length)
                      }}
                      width={20}
                      height={10}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  ) : (
                    <Switch
                      value={currOutlet.reservationStatus}
                      onSyncPress={(value) => {
                        // setState({ newReservationStatus: item }, () => {
                        //   switchReservationStatus();
                        // });
                        switchReservationStatus(value);
                        //// console.log(userReservations.length)
                      }}
                      width={42}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  )}

                  <Text
                    style={[
                      {
                        fontSize: 18,
                        marginTop: 0,
                        color: currOutlet.reservationStatus
                          ? Colors.primaryColor
                          : Colors.fieldtTxtColor,
                        textAlign: 'center',
                        right: Platform.OS == 'ios' ? 1 : 0,
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {currOutlet.reservationStatus ? 'ON' : 'OFF'}
                  </Text>
                </>
              )}
            </View>
          </View>

          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'flex-end',
              width: windowWidth * 0.38,
              alignItems: 'center',
              borderRadius: 10,
              // borderWidth: 1,
              height: switchMerchant
                ? 35
                : windowHeight * 0.055,
              // left: windowWidth * 0.005,
              //left: 310
              // borderWidth: 1
              // paddingLeft: switchMerchant ? 0 : windowWidth * 0.05
            }}>
            <MemoizedTouchableOpacity
              style={[
                styles.submitText,
                {
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: 10,
                  height: 40,
                  left: 0,
                  backgroundColor: '#0F1A3C',
                  borderWidth: 1,
                  borderColor: Colors.primaryColor,
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  paddingHorizontal: windowWidth * 0.005,

                  marginRight: windowWidth * 0.01,
                },
                switchMerchant
                  ? {
                    // top: windowHeight * -0.046,
                    height: 35,
                    width: windowWidth * 0.12,
                  }
                  : {
                    // paddingLeft: windowWidth * 0.05
                  },
              ]}
              onPress={() => {
                setAddReservationModal(true);
                setReservationCustomerName('');
                setSeatingPax(0);
                setReservationPhone('');
                setSelectTable('');
                setReservationRemark('');

                setReservationId('');
              }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {switchMerchant ? (
                  <AntDesign name="pluscircle" size={10} color="#FFFFFF" />
                ) : (
                  <AntDesign name="pluscircle" size={20} color="#FFFFFF" />
                )}

                <Text
                  style={[
                    {
                      marginLeft: 5,
                      color: Colors.primaryColor,
                      fontSize: 16,
                      color: '#FFFFFF',
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                        // paddingLeft: '10%',
                      }
                      : {},
                  ]}>
                  RESERVATION
                </Text>
              </View>
            </MemoizedTouchableOpacity>
            <View
              style={[
                {
                  marginRight: 10,
                  paddingHorizontal: 15,
                  flexDirection: 'row',
                  alignItems: 'center',
                  borderRadius: 10,
                  paddingVertical: 10,
                  justifyContent: 'center',
                  backgroundColor: Colors.whiteColor,
                  shadowOpacity: 0,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 1,
                  zIndex: 1,
                },
                switchMerchant
                  ? {
                    height: 35,
                    width:
                      Platform.OS === 'ios'
                        ? windowWidth * 0.19
                        : windowWidth * 0.2,
                    // top: windowHeight * -0.075,
                  }
                  : {},
              ]}>
              <View
                style={{ alignSelf: 'center', marginRight: 5 }}
                onPress={() => {
                  setState({ pickerMode: 'date', showDateTimePicker: true });
                }}>
                {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                {switchMerchant ? (
                  <GCalendar width={13} height={13} />
                ) : (
                  <GCalendar width={20} height={20} />
                )}
              </View>

              <TouchableOpacity
                onPress={() => {
                  setShowDateTimePicker(true);
                  setShowDateTimePicker1(false);
                }}
                style={{
                  marginHorizontal: 4,
                }}>
                <Text
                  style={[
                    { fontFamily: 'NunitoSans-Regular' },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {moment(rev_date).format('DD MMM yyyy')}
                </Text>
              </TouchableOpacity>

              <Text
                style={[
                  { fontFamily: 'NunitoSans-Regular' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}>
                -
              </Text>

              <TouchableOpacity
                onPress={() => {
                  setShowDateTimePicker(false);
                  setShowDateTimePicker1(true);
                  // // console.log('running');
                }}
                style={{
                  marginHorizontal: 4,
                }}>
                <Text
                  style={[
                    { fontFamily: 'NunitoSans-Regular' },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>
                  {moment(rev_date1).format('DD MMM yyyy')}
                </Text>
              </TouchableOpacity>
            </View>
            <View
              style={[
                {
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                  paddingLeft: 10,
                  borderRadius: 5,
                  height: 40,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  backgroundColor: 'white',
                  //marginLeft: 15,
                  //marginRight: '6%',
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                  //right: Platform.OS == 'ios' ? 50 : 0
                },
                switchMerchant
                  ? {
                    height: 35,
                    width: windowWidth * 0.18,
                    // marginTop: windowHeight * -0.04,
                    // right: windowWidth * 0.065,
                    borderWidth: 2,
                  }
                  : {},
              ]}>
              <Text
                style={[
                  {
                    fontSize: 16,
                    paddingRight: Platform.OS == 'ios' ? 20 : 20,
                    borderColor: Colors.fieldtTxtColor,
                    fontFamily: 'NunitoSans-Bold',
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                      // paddingLeft: '0%',
                      // left: windowWidth * -0.02
                    }
                    : {},
                ]}>
                Filter
              </Text>
              <DropDownPicker
                // controller={instance => setController1(instance)}
                arrowColor={Colors.primaryColor}
                arrowSize={switchMerchant ? 13 : 23}
                arrowStyle={[
                  { fontWeight: 'bold' },
                  switchMerchant
                    ? {
                      top: windowHeight * -0.001,
                      height: '280%',
                      // borderWidth: 1,
                    }
                    : {},
                ]}
                dropDownStyle={[switchMerchant ? {} : {}]}
                labelStyle={[
                  { fontFamily: 'NunitoSans-Regular' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                itemStyle={[
                  { justifyContent: 'flex-start', paddingLeft: 6 },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                placeholderStyle={[
                  { color: 'black' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}
                style={[
                  {
                    width: 140,
                    borderWidth: 0,
                    paddingHorizontal: 5,
                    paddingVertical: 0,
                    borderRadius: 5,
                    borderColor: '#E5E5E5',
                    borderWidth: 0,
                    borderLeftWidth: 0,
                  },
                  switchMerchant
                    ? {
                      fontSize: 10,
                      width: windowWidth * 0.113,
                    }
                    : {},
                ]}
                items={[
                  { label: 'All', value: 0 },
                  { label: 'Pending', value: 1 },
                  { label: 'Accepted', value: 2 },
                  { label: 'Seated', value: 3 },
                  // { label: 'Served', value: 4 },
                  { label: 'Rejected', value: 5 },
                  { label: 'No Show', value: 6 },
                ]} //Awaiting Authorization
                // placeholder={"Pending"}
                defaultValue={filterType}
                onChangeItem={(selectedFilter) => {
                  // filterOrders(selectedFilter);
                  setFilterType(selectedFilter.value);
                }}
              //onOpen={() => controller.close()}
              />
            </View>
          </View>
        </View>
        {/* <View
          style={
            switchMerchant
              ? {
                top: windowHeight * -0.08,
                width: windowWidth * 0.8,
                zIndex: -1,
              }
              : {
                zIndex: -1,
              }
          }>
          <FlatList
            numColumns={2}
            style={{
              paddingTop: 5,
              paddingRight: 0,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 1,
              zIndex: -1,
            }}
            showsVerticalScrollIndicator={false}
            data={userReservations}
            renderItem={renderRow}
            keyExtractor={(item, index) => String(index)}
            contentContainerStyle={[
              {
                // paddingHorizontal: 4,
                paddingBottom: windowHeight * 0.2,
                // paddingLeft: 6,
                paddingTop: 4,
                alignSelf: 'center',
                // backgroundColor: 'red',
                // width: '100%',
                // width: windowWidth * 0.9,
                // borderWidth: 1,
                // paddingLeft: windowWidth * 0.045,
              },
              switchMerchant
                ? {
                  // top: windowHeight * -0.07,
                  width: windowWidth * 0.78,
                  left: windowWidth * 0.01,
                }
                : {},
            ]}
          />
        </View> */}
      </View>
    </View >)
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',
    borderRadius: 20,
    paddingVertical: 30,
    paddingHorizontal: 20,
    marginRight: 10,
    marginBottom: 10,
    width: (Dimensions.get('window').width - 150) / 2,
  },
  tablebox: {
    backgroundColor: Colors.whiteColor,
    shadowColor: '#c4c4c4',
    shadowOffset: {
      width: 8,
      height: 8,
    },
    shadowOpacity: 0.55,
    shadowRadius: 10.32,
    width: 100,
    height: 100,
    marginRight: 25,
    borderRadius: 10,
    marginBottom: 30,
  },

  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    padding: 16,
    // backgroundColor: 'red',
  },
  tableSlotDisplay: {
    width: Dimensions.get('window').width * 0.12,
    height: Dimensions.get('window').width * 0.12,
    margin: 12,
    borderRadius: 8,
    padding: Dimensions.get('window').width * 0.01,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: Colors.fieldtTxtColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTableDisplay: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.12,
    height: Dimensions.get('window').width * 0.12,
    margin: 10,
    //borderRadius: windowWidth * 0.02,
    padding: Dimensions.get('window').width * 0.01,
  },
  tableCode: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 18,
  },
  countIndicator: {
    backgroundColor: Colors.primaryColor,
    width: 30,
    height: 30,
    borderRadius: 20,
    position: 'absolute',
    right: -15,
    marginVertical: -10,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionAreaButton: {
    marginLeft: 10,
    // width: windowWidth * 0.085,
    backgroundColor: Colors.whiteColor,
    height: Dimensions.get('window').height * 0.04,
    borderRadius: 8,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',

    paddingLeft: 15,

    paddingRight: 35,

    elevation: 0,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  sectionAreaButtonTxt: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 14,
    color: Colors.primaryColor,
    textAlign: 'center',
  },
  rowItem: {
    padding: 16,
    // width:
    //   Platform.OS == 'ios'
    //     ? windowWidth / 2 - 135
    //     : windowWidth / 2 - 150,
    width: Dimensions.get('window').width * 0.42,
    //minHeight: 115,
    //width: windowWidth * 0.4,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
    // borderRadius: 15,
    borderRadius: 5,
    backgroundColor: Colors.whiteColor,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#c4c4c4',

    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },

  topPart: {
    paddingHorizontal: 10,
    justifyContent: 'center',
    flex: 3,
    alignSelf: 'center',
  },

  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainerTable: {
    flex: 1,
    // backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalViewTable: {
    height: Dimensions.get('window').width * 0.3,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 20,
    paddingTop: 25,
    //alignItems: 'center',
    //justifyContent: 'center'
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.02,
    top: Dimensions.get('window').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },

});
export default ReservationScreen;
